<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  	
  <modelVersion>4.0.0</modelVersion>
  	
  <parent>
        
    <artifactId>mielo.www</artifactId>
        
    <groupId>it.ariaspa.mielo.www</groupId>
        
    <version>1.0.0</version>
      
  </parent>
  	
  <artifactId>mielo.www-angular</artifactId>
  	
  <packaging>zip</packaging>
  	
  <name>mielo.www-angular</name>
  	
  <!-- This configuration will download and install the node and npm as configured -->
  	
  <!-- in the pom file and npm install will install all modules listed as -->
  	
  <!-- dependencies in package.json and in the execution phase the final sources -->
  	
  <!-- will be pushed to dist folder as per the configuration in package.json. -->
  	
  <build>
    		
    <pluginManagement>
      			
      <plugins>
        				
        <plugin>
          					
          <groupId>org.apache.maven.plugins</groupId>
          					
          <artifactId>maven-clean-plugin</artifactId>
          					
          <version>${maven-clean-plugin.version}</version>
          				
        </plugin>
        				
        <plugin>
          					
          <groupId>com.github.eirslett</groupId>
          					
          <artifactId>frontend-maven-plugin</artifactId>
          					
          <version>${frontend-maven-plugin.version}</version>
          				
        </plugin>
        			
      </plugins>
      		
    </pluginManagement>
    		
    <plugins>
       		  
      <plugin>
        				
        <groupId>com.github.eirslett</groupId>
        				
        <artifactId>frontend-maven-plugin</artifactId>
        				
        <executions>
          					
          <execution>
            						
            <id>install node and npm</id>
            						
            <goals>
              							
              <goal>install-node-and-npm</goal>
              						
            </goals>
            						
            <configuration>
              						    
              <npmInheritsProxyConfigFromMaven>false</npmInheritsProxyConfigFromMaven>
              							
              <nodeVersion>${node.version}</nodeVersion>
              							
              <noProxy>true</noProxy>
              							
              <maven.test.failure.ignore>true</maven.test.failure.ignore>
              						    
              <nodeDownloadRoot>https://sqgate.lispa.local/nexus/content/repositories/node-repository-maven/</nodeDownloadRoot>
                            				
              <npmDownloadRoot>https://sqgate.lispa.local/nexus/content/groups/npm-all/npm/-/</npmDownloadRoot>
                                          
              <arguments>install</arguments>
              						
            </configuration>
            					
          </execution>
          					
          <execution>
            						
            <id>npm install</id>
            						
            <goals>
              							
              <goal>npm</goal>
              						
            </goals>
            						
            <configuration>
              							
              <arguments>install</arguments>
              						
            </configuration>
            					
          </execution>
          					
          <execution>
            						
            <id>npm run-script prod</id>
            						
            <goals>
              							
              <goal>npm</goal>
              						
            </goals>
            						
            <configuration>
              							
              <arguments>run-script prod</arguments>
              						
            </configuration>
            					
          </execution>
          				
        </executions>
        			
      </plugin>
      			
      <!-- FONT INIZIO -->
      			
      <plugin>
        				
        <groupId>org.apache.maven.plugins</groupId>
        				
        <artifactId>maven-dependency-plugin</artifactId>
        				
        <version>3.1.1</version>
        				
        <executions>
          					
          <execution>
            						
            <id>copy-boostrap-less</id>
            						
            <phase>validate</phase>
            						
            <goals>
              							
              <goal>copy</goal>
              		 				
            </goals>
            						
            <configuration>
              							
              <artifactItems>
                								
                <artifactItem>
                  									
                  <groupId>it.ariaspa.bootstrap</groupId>
                  									
                  <artifactId>bootstrap-less</artifactId>
                  									
                  <version>1.0.0</version>
                  									
                  <type>zip</type>
                  									
                  <overWrite>true</overWrite>
                  									
                  <destFileName>bootstrap-less.zip</destFileName>
                  									
                  <outputDirectory>${project.basedir}/src/assets/images</outputDirectory>
                  								
                </artifactItem>
                 							
              </artifactItems>
              						
            </configuration>
            					
          </execution>
          					
          <execution>
            						
            <id>unpack</id>
            						
            <phase>validate</phase>
            						
            <goals>
              							
              <goal>unpack</goal>
              						
            </goals>
            						
            <configuration>
              							
              <artifactItems>
                                				
                <artifactItem>
                  									
                  <groupId>it.ariaspa.fonts</groupId>
                  									
                  <artifactId>Titillium_Web</artifactId>
                  									
                  <version>2.0.2</version>
                  									
                  <type>zip</type>
                  									
                  <overWrite>true</overWrite>
                  									
                  <outputDirectory>${project.build.directory}/dependency/fonts</outputDirectory>
                  								
                </artifactItem>
                								
                <artifactItem>
                  									
                  <groupId>it.lispa.fonts</groupId>
                  									
                  <artifactId>springwiz-www-font</artifactId>
                  									
                  <version>1.0.0</version>
                  									
                  <type>zip</type>
                  									
                  <overWrite>true</overWrite>
                  									
                  <outputDirectory>${project.build.directory}/dependency/fonts</outputDirectory>
                  								
                </artifactItem>
                							
              </artifactItems>
              						
            </configuration>
            					
          </execution>
          				
        </executions>
        			
      </plugin>
      			
      <plugin>
        				
        <artifactId>maven-resources-plugin</artifactId>
        				
        <executions>
          				
          <execution>
            						
            <id>copy-fonts-assets-bootstrap</id>
            						
            <phase>validate</phase>
            						
            <goals>
              							
              <goal>copy-resources</goal>
              						
            </goals>
            						
            <configuration>
              							
              <nonFilteredFileExtensions>
                								
                <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                							
              </nonFilteredFileExtensions>
              							
              <outputDirectory>${project.basedir}/src/assets/bootstrap/fonts</outputDirectory>
              							
              <overwrite>true</overwrite>
              							
              <resources>
                								
                <resource>
                  									
                  <directory>${project.build.directory}/dependency/fonts</directory>
                  									
                  <includes>
                    										
                    <include>glyphicons-halflings-regular.eot</include>
                    										
                    <include>glyphicons-halflings-regular.svg</include>
                    										
                    <include>glyphicons-halflings-regular.ttf</include>
                    										
                    <include>glyphicons-halflings-regular.woff</include>
                    									
                  </includes>
                  								
                </resource>
                							
              </resources>
              						
            </configuration>
            					
          </execution>
          					
					
          <execution>
            						
            <id>copy-fonts-assets-Titillium</id>
            						
            <phase>validate</phase>
            						
            <goals>
              							
              <goal>copy-resources</goal>
              						
            </goals>
            						
            <configuration>
              							
              <nonFilteredFileExtensions>
                								
                <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                								
                <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                							
              </nonFilteredFileExtensions>
              							
              <outputDirectory>${project.basedir}/src/assets/bootstrap-lombardia/fonts/Titillium_Web</outputDirectory>
              							
              <overwrite>true</overwrite>
              							
              <resources>
                								
                <resource>
                  									
                  <directory>${project.build.directory}/dependency/fonts</directory>
                  									
                  <includes>
                    										
                    <include>Titillium*</include>
                    									
                  </includes>
                  								
                </resource>
                							
              </resources>
              						
            </configuration>
            					
          </execution>
          			
          <!-- PER I FONT 'Lora' usati da Bootstrap Lombardia 0.3.5 rivolgersi al gruppo di CM così da far predisporre i font tramite PLUGIN -->
          				
        </executions>
        			
      </plugin>
      			
      <!-- FONT FINE -->
      	        
      <plugin>
        	            
        <groupId>it.lispa.siss.maven-plugins</groupId>
            	        
        <artifactId>custom-package-plugin</artifactId>
        	            
        <extensions>true</extensions>
        	            
        <version>${custom-package-plugin.version}</version>
        	            
        <configuration>
          	                
          <sourceDirectory>${project.basedir}/dist</sourceDirectory>
          	            
        </configuration>
        	            
        <executions>
          	                
          <execution>
            	                    
            <id>scripts-rest-service</id>
            	                    
            <phase>prepare-package</phase>
            	                    
            <goals>
              	                        
              <goal>zip</goal>
              	                    
            </goals>
            	                
          </execution>
          	             
        </executions>
        	        
      </plugin>
           		
      <plugin>
        				
        <groupId>org.apache.maven.plugins</groupId>
        				
        <artifactId>maven-clean-plugin</artifactId>
        				
        <configuration>
          					
          <filesets>
            						
            <fileset>
              							
              <directory>${project.build.directory}/dependency/fonts</directory>
              							
              <includes>
                								
                <include>**/*</include>
                							
              </includes>
              						
            </fileset>
            						
            <fileset>
              							
              <directory>${basedir}/dist</directory>
              							
              <includes>
                								
                <include>**</include>
                							
              </includes>
              						
            </fileset>
            						
            <fileset>
              							
              <directory>${basedir}/node</directory>
              							
              <includes>
                								
                <include>**</include>
                							
              </includes>
              						
            </fileset>
            						
            <fileset>
              							
              <directory>${basedir}/node_modules</directory>
              							
              <includes>
                								
                <include>**</include>
                							
              </includes>
              						
            </fileset>
            					
          </filesets>
          				
        </configuration>
        			
      </plugin>
      		
    </plugins>
    	
  </build>
  
  <licenses>
    <license>
      <name>Ariaspa License, Version 1.0</name>
      <url>https://www.ariaspa.it/wps/portal/site/aria</url>
      <distribution>repo</distribution>
      <comments>Licenza di prodotti erogati da Ariaspa per Regione Lombardia</comments>
    </license>
  </licenses>
</project>
