/* You can add global styles to this file, and also import other style files */
@use './assets/styles/_mat-lombardia-variables.scss' as *;

label {
  margin-bottom: 0 !important;
}

.mat-mdc-input-element{
  font-family: "Titillium Web", Geneva, Tahoma, sans-serif !important;
}
.mdc-button{
  outline: $primary !important;
  letter-spacing: normal !important;
  font-weight: 700 !important;
  font-size: 18px !important;
  height: 44px !important;
}

.mat-mdc-button{
  height: 44px !important;
}

.mat-mdc-raised-button[disabled], .mat-mdc-raised-button.mat-mdc-button-disabled{
  color: white !important;
  background-color: #979797 !important;
}

.mat-stroked-button{
  border: 2px solid $primary !important;
  color: $primary !important;
  height: 44px !important;
  font-size: 18px !important;
}

mat-label {
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
  color: $secondary;
}

.mat-mdc-form-field-subscript-wrapper, .mat-mdc-form-field-bottom-align::before{
  letter-spacing: normal !important;
}

.mdc-text-field--outlined{
  height: 44px !important;
}
.mat-mdc-form-field{
  //font-family: "Titillium Web", Geneva, Tahoma, sans-serif !important;
  height: 44px !important;
  letter-spacing: normal !important;
}
//--mdc-outlined-text-field-input-text-placeholder-color:
//--mdc-outlined-text-field-outline-color

.mdc-text-field--outlined .mat-mdc-form-field-infix, .mdc-text-field--no-label .mat-mdc-form-field-infix{
  min-height: 44px !important;
  padding-top: 10px !important;
}

.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control, .mat-mdc-select{
  font-size: 18px !important;
  letter-spacing: normal !important;
}

/* FIX PER ICONE DATEPICKER */
.mat-datepicker-toggle {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding-right: 8px !important;
  padding-bottom: 3px !important;
}

.mat-datepicker-toggle .mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  margin-top: 0 !important;
  padding: 0 !important;
}

.mat-mdc-icon-button.mat-mdc-button-base {
  padding: 0 !important;
}

/* Stile icona calendario */
.icon-calendar {
  fill: #003354 !important;
  width: 24px !important;
  height: 24px !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

//.mat-mdc-form-field-appearance-outline:not(.mat-mdc-form-field-disabled) {

//  /*input filed con sfondo bianco*/
//  .mat-mdc-form-field-outline {
//    background-color: white;
//    border-radius: 5px;
//    -webkit-border-radius: 5px;
//    -moz-border-radius: 5px;
//  }
//}

.button-underline{
  text-decoration: underline;
}

.export-div {
    margin-right: 5px;
}

.upload-button {
    margin-top: 25px;
    margin-left: 10px;
}

.footer {
    bottom: 0;
    width: 100%;
    position: fixed;
    z-index: 3;
    padding-bottom: 8px !important;
    padding-top: 8px !important;
    padding-left: 16px;
    padding-right: 16px;
}

.footer .container-fluid .row .col-12 {
    margin-top: 5px !important;
    //margin-bottom: 5px !important;
}


/* Chrome, Safari, Edge, Opera */

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}


/* Firefox */



/* FONT SIZE */

.f-18 {
  font-size: 18px;
}

.f-20 {
  font-size: 20px;
}

.f-24 {
  font-size: 24px;
}

.font-weight-medium {
  font-weight: 600!important
}
/* END FONT SIZE */

html, body {
  height: 100%;
  letter-spacing: normal !important;
}

p{
  letter-spacing: normal !important;
}

h2{
  font-size: 32px !important;
  color: $secondary !important;
  font-weight: 700 !important;
}
h3{
  font-size: 28px !important;
  color: $secondary !important;
  font-weight: 600 !important;
}

h5{
  font-size: 20px !important;
  color: $secondary !important;
  font-weight: 600 !important;
}

h6{
  font-size: 22px !important;
  color: $secondary !important;
  font-weight: 700 !important;
}


mat-error {
  font-weight: 700 !important;
  color: #CD0000 !important;
}

.icon-primary{
  fill: $primary !important;
  color: $primary !important;
}


/*ACCORDION*/

.panel-title{
  color: $secondary !important;
  font-size: 24px;
  font-weight: 600;
}

.mat-expansion-panel {
  box-shadow: none !important;
  border: 1px solid #E6E9F2 !important;
}

.mat-expansion-panel.mat-expanded {
  background-color: #F5F6FA !important;
}

/*fine accordion*/

/* Radio button styles */
.mat-mdc-radio-button {
  --mdc-radio-selected-focus-icon-color: $primary !important;
  --mdc-radio-selected-hover-icon-color: $primary !important;
  --mdc-radio-selected-icon-color: $primary !important;
  --mdc-radio-selected-pressed-icon-color: $primary !important;
  --mdc-radio-unselected-focus-icon-color: $primary !important;
  --mdc-radio-unselected-hover-icon-color: $primary !important;
  --mdc-radio-unselected-icon-color: $primary !important;
  --mdc-radio-unselected-pressed-icon-color: $primary !important;
  --mat-radio-ripple-color: $primary !important;
}

//.mat-mdc-radio-group {
//  display: flex !important;
//  align-items: center !important;
//}

.mdc-radio .mdc-radio__native-control:enabled:checked + .mdc-radio__background .mdc-radio__outer-circle {
  border-color: $primary !important;
}

.mdc-radio .mdc-radio__native-control:enabled + .mdc-radio__background .mdc-radio__inner-circle {
  border-color: $primary !important;
}

.mdc-radio .mdc-radio__native-control:enabled:not(:checked) + .mdc-radio__background .mdc-radio__outer-circle {
  border-color: $secondary !important;
}

.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before {
  background-color: $primary !important;
}

/* Datepicker icon styles */
.mat-datepicker-toggle {
  .mat-mdc-button-touch-target {
    /* display: none !important; */ /* Rimosso per consentire interazioni */
  }
}

.mat-mdc-form-field-icon-suffix, [dir=rtl] .mat-mdc-form-field-icon-prefix {
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding-right: 8px !important;
}

.mat-datepicker-toggle-default-icon {
  width: 24px !important;
  height: 24px !important;
}

.mat-mdc-icon-button.mat-mdc-button-base {
  --mdc-icon-button-state-layer-size: 28px !important;
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Rimosso per consentire la visualizzazione di contenuti generati */
  /*
  &::after {
    display: none !important;
  }

  &::before {
    display: none !important;
  }
  */

  &:focus {
    outline: none !important;
  }

  /* Rimosso per consentire effetti ripple e rendere visibili i bottoni */
  /*
  .mat-mdc-button-persistent-ripple {
    display: none !important;
  }

  .mat-mdc-button-ripple {
    display: none !important;
  }
  */
}

.mat-datepicker-toggle .mat-mdc-icon-button {
  /* Rimosso per consentire effetti ripple e rendere visibili i bottoni */
  /*
  --mat-mdc-button-persistent-ripple-color: transparent !important;
  --mat-mdc-button-ripple-color: transparent !important;
  */
}

/* Calendar selected date styles */
.mat-calendar-body-selected {
  border: none !important;
}

.mat-calendar-body-cell.mat-calendar-body-active {
  &::before {
    border: none !important;
  }
}

.mat-calendar-body-cell:not(.mat-calendar-body-disabled).mat-calendar-body-active > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected),
.mat-calendar-body-cell:not(.mat-calendar-body-disabled).mat-calendar-body-active:hover > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected) {
  border: none !important;
}

button:focus {
  outline: none !important;
  -webkit-focus-ring-color: transparent !important;
}

.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
  border: none !important;
  border-color: transparent !important;
}

.mat-calendar-body-cell-content.mat-calendar-body-today {
  border: none !important;
}

/* Stili per garantire che i bottoni del calendario siano sempre visibili */
button.mat-calendar-previous-button,
button.mat-calendar-next-button,
.mat-calendar-previous-button,
.mat-calendar-next-button {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  border: none !important;
  background-color: transparent !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  justify-content: center !important;
  align-items: center !important;
  position: relative !important;
  z-index: 100 !important;
  min-width: 32px !important;
  padding: 0 !important;
  margin: 0 4px !important;
}

/* Icone per le frecce */
.mat-calendar-previous-button::before,
.mat-calendar-next-button::before {
  content: "";
  display: block !important;
  width: 8px !important;
  height: 8px !important;
  border-style: solid !important;
  border-width: 2px 2px 0 0 !important;
  border-color: #666 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
}

.mat-calendar-previous-button::before {
  transform: translate(-30%, -50%) rotate(-135deg) !important;
}

.mat-calendar-next-button::before {
  transform: translate(-70%, -50%) rotate(45deg) !important;
}

/* Rimuovo eventuali contenuti after precedenti */
.mat-calendar-previous-button::after,
.mat-calendar-next-button::after {
  content: none !important;
  display: none !important;
}

/* Fix specifico per i bottoni del calendario con attributi ARIA */
[aria-label="Previous 24 years"],
[aria-label="Next 24 years"],
[aria-label="Previous month"],
[aria-label="Next month"],
[aria-label="Previous year"],
[aria-label="Next year"] {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: transparent !important;
  border: none !important;
}

.no-padding-dialog .mat-mdc-dialog-surface {
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* Container per i dati comuni a più componenti */
.container-data {
  margin-left: 2rem !important;
  margin-right: 2rem !important;
}

/* Stili per l'expansion panel e indicatore */
.mat-expansion-panel {
  padding: 12px !important;
}

.mat-expansion-indicator::after {
  color: #297A38 !important;
  height: 22px !important;
  width: 22px !important;
}

/* Correzione allineamento e sfondo per i form fields */
.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper,
.mat-mdc-text-field-wrapper {
  background-color: white !important;
}

.mat-internal-form-field {
  //align-items: end !important;
}

/* Stile icona calendario */
.icon-calendar {
  fill: #003354 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
}

/* Allineamento negativo comune */
.mar-l7minus {
  margin-left: -7px;
}

/* Stile per valori dati */
.value-dati {
  margin-top: 0.5rem;
  text-transform: uppercase;
  font-size: 16px;
  color: #003354;
}

/* Rimuove outline su tutti i focus */
.mdc-icon-button:focus,
.mat-ripple:focus,
.mat-mdc-button-ripple:focus,
.mat-mdc-focus-indicator {
  outline: none !important;
}

/* Stile per radio button */
//.mat-mdc-radio-button {
//  display: inline-flex;
//  align-items: center !important;
//
//  //.mdc-form-field {
//  //  display: flex;
//  //  align-items: center;
//  //}
//
//  .mdc-radio {
//    margin: 0;
//    padding: 0;
//  }
//
//  .f-18 {
//    display: flex;
//    align-items: center !important;
//    height: 100%;
//    margin-left: 8px;
//    line-height: normal;
//  }
//}

//.mat-mdc-radio-button .mat-mdc-radio-label {
//  margin: 0 !important;
//}

/* Stile comune per contenitore radio */
.radio-container {
  //display: flex;
  //align-items: center;
  //width: 100%;
  //margin-top: 1rem !important;

  //.mdc-radio {
  //  .mdc-label {
  //    margin-bottom: 0 !important;
  //  }
  //}
}

/* Stili per i pannelli expansion */
.mat-expansion-panel-header-title {
  display: block;
  margin-right: 0;
  width: 100%;
}

.mat-expansion-panel-header-description {
  display: none;
}

.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover {
  background: transparent !important;
}

/* Bordo inferiore per righe */
.row.border-bottom {
  border-bottom: 1px solid #D9DBE9;
  padding-bottom: 1.5rem;
}

/* Stile label */
.field-label, .mdc-label {
  font-family: 'Titillium Web', sans-serif;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
  color: #003354;
  //margin-bottom: 0 !important;
}

.mat-button-ripple.mat-ripple,
.mat-button-focus-overlay {
  visibility: visible !important;
  opacity: 1 !important;
}

.mat-mdc-button-ripple,
.mat-mdc-button-ripple.mat-ripple-loader-centered {
  visibility: visible !important;
  opacity: 1 !important;
}

/* Stile globale per bottoni disabilitati */
.mat-mdc-button[disabled],
.mat-mdc-raised-button[disabled],
.mat-mdc-unelevated-button[disabled],
.mat-mdc-outlined-button[disabled],
.mat-mdc-flat-button[disabled],
.mat-mdc-button.mat-mdc-button-disabled,
.mat-mdc-raised-button.mat-mdc-button-disabled,
.mat-mdc-unelevated-button.mat-mdc-button-disabled,
.mat-mdc-outlined-button.mat-mdc-button-disabled,
.mat-mdc-flat-button.mat-mdc-button-disabled {
    background-color: #A0A0A0 !important;
    color: #E0E0E0 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* Stile specifico per input disabilitati come mostrato nell'immagine */
.mdc-text-field--outlined.mdc-text-field--no-label.mdc-text-field--disabled {
  background-color: #EEEEEE !important;
}

/* Stile aggiuntivo per garantire che il colore venga applicato correttamente */
.mat-mdc-text-field-wrapper.mdc-text-field--outlined.mdc-text-field--no-label.mdc-text-field--disabled {
  background-color: #EEEEEE !important;
}

/* Stile per i bottoni note */

:host ::ng-deep {
  .note-button.mat-mdc-button.mat-unthemed.mat-mdc-button-disabled,
  .note-button.mat-mdc-button.mat-primary.mat-mdc-button-disabled {
      background-color: transparent !important;
      opacity: 1 !important;
  }
}

.pointer-events-none {
  pointer-events: none;
}

.form-field-container {
  display: flex;
  flex-direction: column;

  .radio-group-container {
    margin-top: 1.3rem;
    padding: 0;
    min-height: 40px;
    display: flex;
    align-items: center;
    width: 100%;
    background-color: transparent;
    border: none;

    .mat-radio-group {
      display: flex;
      gap: 2rem;
    }
  }

  .d-flex {
    gap: 8px;

    mat-form-field {
      flex: 1;
      min-width: 0;
    }
  }

  .d-flex.align-items-center.h-100 {
    margin-top: 28px;

    button {
      padding: 0;
      line-height: 40px;
    }
  }
}

.mat-mdc-tooltip {
  --mdc-plain-tooltip-supporting-text-size: 18px;
}

.mdc-tooltip__surface {
  max-width: 400px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
}