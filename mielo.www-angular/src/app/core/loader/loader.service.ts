import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoaderService {
  // Contatore delle richieste attive
  private requestCount = 0;

  // BehaviorSubject per lo stato di caricamento
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  isLoading$ = this.isLoadingSubject.asObservable();

  constructor() {}

  // Metodo per mostrare il loader
  showLoader() {
    this.requestCount++;
    if (this.requestCount === 1) {
      this.isLoadingSubject.next(true);
    }
  }

  // Metodo per nascondere il loader
  hideLoader() {
    if (this.requestCount > 0) {
      this.requestCount--;
    }
    if (this.requestCount === 0) {
      this.isLoadingSubject.next(false);
    }
  }
}
