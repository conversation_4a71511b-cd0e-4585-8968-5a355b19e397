.loader {
  z-index: 9999;
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modale-loader {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  background: #fff;
  width: 90%;
  max-width: 400px;
  padding: 2rem;
  justify-content: center;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.loader h2 {
  text-align: center;
  font-weight: 700;
  color: #033565;
  margin: 0;
}

.loader .spinner-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.loader .spinner-container .mat-spinner::ng-deep circle {
  stroke: #348d45;
}

@media (max-width: 768px) {
  .modale-loader {
    width: 90%;
    padding: 1.5rem;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .modale-loader {
    width: 95%;
    padding: 1rem;
    gap: 1rem;
  }
}
