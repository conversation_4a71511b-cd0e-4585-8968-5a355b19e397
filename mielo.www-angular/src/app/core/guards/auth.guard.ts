import { inject } from '@angular/core';
import { OperatoreService } from '../services/operatore.service';
import { ErrorService } from '../../shared/components/dialog-error/error.service';

export const authGuard = () => {
  const operatoreService = inject(OperatoreService);
  const errorService = inject(ErrorService);

  const operatore = operatoreService.getOperatore();

  if (operatore) {
    return true;
  } else {
    errorService.showError('Operatore non trovato. Si prega di accedere.')
    return false;
  }
};

