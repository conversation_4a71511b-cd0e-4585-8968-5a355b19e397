import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatFormFieldModule, MAT_FORM_FIELD_DEFAULT_OPTIONS } from "@angular/material/form-field";
import { MatTabsModule, MAT_TABS_CONFIG } from "@angular/material/tabs";
import { MatSortModule } from "@angular/material/sort";
import { MatDialogModule } from '@angular/material/dialog';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatRadioModule } from '@angular/material/radio';
import { MatNativeDateModule } from "@angular/material/core";
import { MatDividerModule } from '@angular/material/divider';



@NgModule({
    imports: [
        CommonModule,
        MatButtonModule,
        MatDialogModule,
        MatToolbarModule,
        MatIconModule,
        MatSidenavModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatRadioModule,
        MatDatepickerModule,
        MatTooltipModule,
        MatTableModule,
        MatPaginatorModule,
        MatRadioModule,
        MatMenuModule,
        MatExpansionModule,
        MatCheckboxModule,
        MatAutocompleteModule,
        MatProgressSpinnerModule,
        MatTabsModule,
        MatSortModule,
        MatToolbarModule,
        MatNativeDateModule,
        MatDividerModule,
        MatButtonToggleModule
    ],
    exports: [
        MatButtonModule,
        MatDialogModule,
        MatToolbarModule,
        MatIconModule,
        MatSidenavModule,
        MatTooltipModule,
        MatInputModule,
        MatFormFieldModule,
        MatSelectModule,
        MatRadioModule,
        MatDatepickerModule,
        MatTableModule,
        MatPaginatorModule,
        MatRadioModule,
        MatMenuModule,
        MatExpansionModule,
        MatCheckboxModule,
        MatAutocompleteModule,
        MatProgressSpinnerModule,
        MatTabsModule,
        MatSortModule,
        MatToolbarModule,
        MatDividerModule,
        MatButtonToggleModule
    ],
    providers: [
        MatDatepickerModule,
        {
            provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
            useValue: {appearance: 'outline'}
        },
        { provide: MAT_TABS_CONFIG, useValue: { animationDuration: '0ms' } } //Fix IE11 resizing content
    ]
})

export class MaterialModule { }
