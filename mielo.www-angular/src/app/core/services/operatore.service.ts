// src/app/services/info.service.ts
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, Observable, of, shareReplay, throwError } from 'rxjs';
import { map } from "rxjs/operators";
import { ErrorService } from "../../shared/components/dialog-error/error.service";
import { BASE_URL } from '../config';
import { InfoOperatoreDTO } from "../interfaces/info-operatore.interface";

@Injectable({
  providedIn: 'root'
})
export class OperatoreService {

  private baseUrl = `${BASE_URL}`;
  private operatore = new BehaviorSubject<InfoOperatoreDTO>({});
  operatore$ = this.operatore.asObservable();
  
  // Aggiungo una variabile per memorizzare la chiamata API
  private infoOperatoreCache$: Observable<InfoOperatoreDTO> | null = null;

  constructor(private http: HttpClient, private errorService: ErrorService) {}

  setOperatore(operatore: InfoOperatoreDTO): void {
    this.operatore.next(operatore);
  }

  getOperatore(): InfoOperatoreDTO{
    return this.operatore.value;
  }


  getInfoOperatore(): Observable<InfoOperatoreDTO> {
    // Se esiste già un operatore con dati, restituisco quelli
    if (this.operatore.value.nomeOperatore) {
      return of(this.operatore.value);
    }
    
    // Se esiste già una richiesta in corso, restituisco quella
    if (this.infoOperatoreCache$) {
      return this.infoOperatoreCache$;
    }
    
    // Altrimenti effettuo la chiamata API e la memorizzo
    this.infoOperatoreCache$ = this.http.get(BASE_URL + `/operatore/info`, {
      headers: { accept: 'application/json', 'Content-Type': 'application/json' }, observe: 'response'
    }).pipe(
      map((res) => {
        if (res && res.status === 200) {
          const response = res.body as InfoOperatoreDTO;
          this.setOperatore(response);
          return response;
        } else {
          throw new Error('Errore nella risposta');
        }
      }),
      catchError((error) => {
        return throwError(() => new Error('Errore nel recupero delle informazioni operatore'));
      }),
      // Condividi la risposta tra tutti i subscriber
      shareReplay(1)
    );
    
    return this.infoOperatoreCache$;
  }
}
