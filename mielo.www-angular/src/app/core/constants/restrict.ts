export const RESTRICT = {
    CF_REGEX: /^(?:[A-Z][AEIOU][AEIOUX]|[B-DF-HJ-NP-TV-Z]{2}[A-Z]){2}(?:[\dLMNP-V]{2}(?:[A-EHLMPR-T](?:[04LQ][1-9MNP-V]|[15MR][\dLMNP-V]|[26NS][0-8LMNP-U])|[DHPS][37PT][0L]|[ACELMRT][37PT][01LM]|[AC-EHLMPR-T][26NS][9V])|(?:[02468LNQSU][048LQU]|[13579MPRTV][26NS])B[26NS][9V])(?:[A-MZ][1-9MNP-V][\dLMNP-V]{2}|[A-M][0L](?:[1-9MNP-V][\dLMNP-V]|[0L][1-9MNP-V]))[A-Z]$/,
    regExp_CF_MIELO: /^(?:[A-Z][AEIOU][AEIOUX]|[B-DF-HJ-NP-TV-Z]{2}[A-Z]){2}(?:[\\dLMNP-V]{2}(?:[A-EHLMPR-T](?:[04LQ][1-9MNP-V]|[15MR][\\dLMNP-V]|[26NS][0-8LMNP-U])|[DHPS][37PT][0L]|[ACELMRT][37PT][01LM]|[AC-EHLMPR-T][26NS][9V])|(?:[02468LNQSU][048LQU]|[13579MPRTV][26NS])B[26NS][9V])(?:[A-MZ][1-9MNP-V][\\dLMNP-V]{2}|[A-M][0L](?:[1-9MNP-V][\\dLMNP-V]|[0L][1-9MNP-V]))[A-Z]$/,
    regexp_codicePrescrittore: /^([0-9]{6})$/, //usata per il form di ricerca semplice
    regexp_codiceFiscale: /^([a-zA-Z0-9]{16})$/, //usata per il form di ricerca semplice
    regexp_cognomeNome: /^((?!\s)(?![\s\S]*\s$)[a-zA-Z'\s()-]+){2,50}$/,
    regexp_telefonoFisso: /^([0-9]{1,11})$/,
    regexp_telefonoMobile: /^([0-9]{1,11})$/,
    regExp_indirizzo_2_80: /^((?!\s)(?![\s\S]*\s$)[a-zA-Z0-9'\s().-]+){2,80}$/,
    regExp_civico_1_11:    /^((?!\s)(?![\s\S]*\s$)[a-zA-Z0-9'\s()\\\/.-]+){1,11}$/,
    regExp_cap: /^([0-9]{5})$/,
    regExp_CF_DETAIL: /^[A-Za-z]{6}[0-9LMNPQRSTUV]{2}[A-Za-z]{1}[0-9LMNPQRSTUV]{2}[A-Za-z]{1}[0-9LMNPQRSTUV]{3}[A-Za-z]{1}$/,
    regExp_CF_gen: /^(?:[A-Z][A-Z][A-Z]|A-Z]{2}[A-Z]){2}(?:[\dLMNP-V]{2}(?:[A-EHLMPR-T](?:[04LQ][1-9MNP-V]|[15MR][\dLMNP-V]|[26NS][0-8LMNP-U])|[DHPS][37PT][0L]|[ACELMRT][37PT][01LM]|[AC-EHLMPR-T][26NS][9V])|(?:[02468LNQSU][048LQU]|[13579MPRTV][26NS])B[26NS][9V])(?:[A-MZ][1-9MNP-V][\dLMNP-V]{2}|[A-M][0L](?:[1-9MNP-V][\dLMNP-V]|[0L][1-9MNP-V]))[A-Z]$/,  //validazione puntuale sulla sequenza alfanumerica di 16 caratteri
    regExp_CF_16: /^(?:[A-Z][AEIOU][AEIOUX]|[B-DF-HJ-NP-TV-Z]{2}[A-Z]){2}(?:[\dLMNP-V]{2}(?:[A-EHLMPR-T](?:[04LQ][1-9MNP-V]|[15MR][\dLMNP-V]|[26NS][0-8LMNP-U])|[DHPS][37PT][0L]|[ACELMRT][37PT][01LM]|[AC-EHLMPR-T][26NS][9V])|(?:[02468LNQSU][048LQU]|[13579MPRTV][26NS])B[26NS][9V])(?:[A-MZ][1-9MNP-V][\dLMNP-V]{2}|[A-M][0L](?:[1-9MNP-V][\dLMNP-V]|[0L][1-9MNP-V]))[A-Z]$/,  //validazione puntuale sulla sequenza alfanumerica di 16 caratteri
    regExp_CF_MEF16: /^([a-zA-Z]{6}[0-9]{2}[a-zA-Z]{1}[0-9]{2}[a-zA-Z]{1}[0-9]{3}[a-zA-Z]{1})$/,
    regExp_CF_MEF11 : /^([0-9]{11})$/, //cf MEF a 11 caratteri numerici, usata per il form di ricerca semplice e validare coerenza formale anagrafica
    regExp_CF_ENI: /^((ENI|eni)[0-9]{13})$/,
    regExp_CF_STP: /^((STP|stp)[0-9]{13})$/,
    regExp_CF_TEAM: /^([a-zA-Z0-9]{20})$/,
    // regExp_decimal3_2: /^([0-9]{1,3}(,|\.)[0-9]{2})$/,
    // regExp_decimal2_2: /^([0-9]{1,2}(,|\.)[0-9]{2})$/,
    // regExp_decimal2_1: /^[-]?[0-9]{1,2}((,|\.)[05])?$/,
    regExp_only_numbers: /^-?(0|[1-9]\d*)?$/,
    regExp_data: /^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/,
}
