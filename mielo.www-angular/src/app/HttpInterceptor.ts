import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { JwtService } from './shared/jwt-service';
import { Observable, switchMap } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

    constructor(private jwtService: JwtService) { }

    intercept(req: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
        return this.jwtService.getJwt().pipe(
            switchMap((token: string) => {
                return next.handle(req.clone({
                    setHeaders: {
                        Authorization: `Bearer ${token}`,
                    },
                }));
            })
        );

    }

}