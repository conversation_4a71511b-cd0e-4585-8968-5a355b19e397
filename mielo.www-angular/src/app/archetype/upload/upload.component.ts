import { HttpClientModule, HttpHeaders } from '@angular/common/http';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { BootStrapLombardiaUtils } from '../../shared/bootstrap-lombardia.service';
import { FileIOService } from '../../shared/file-io.service';
import { BreadcrumbsComponent } from '../breadcrumbs/breadcrumbs.component';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-upload',
  standalone: true,
  imports: [BreadcrumbsComponent, CommonModule, HttpClientModule],
  providers: [FileIOService, BootStrapLombardiaUtils],
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.css']
})
export class UploadComponent implements OnInit {

  constructor(
    private fileIOService: FileIOService,
    private bootStrapLombardiaUtils: BootStrapLombardiaUtils) { }

  @ViewChild('octet') octetInputRef: ElementRef;
  @ViewChild('multipart') multipartInputRef: ElementRef;

  fileOctet = '';
  fileOctetSize = 0;
  fileMultipart = '';
  fileMultipartSize = 0;
  errorMsg = '';

  ngOnInit(): void { }

  allegatoOctetChange(event: Event) {
    this.fileOctetChange(event);
  }

  allegatoMultipartChange(event: Event) {
    this.fileMultipartChange(event);
  }

  private fileOctetChange(event: Event) {
    this.bootStrapLombardiaUtils.setLoadingModal(true);
    const fileList: FileList = (event.target as HTMLInputElement).files as any;
    if (fileList.length > 0) {
      const file: File = fileList[0];
      this.fileOctetSize = file.size;
      this.fileIOService.uploadFileAllegatoOctet(file, {
        params: {
          'fileName': file.name
        },
        headers: new HttpHeaders({
          'Content-Type': 'application/octet-stream'
        }),
        responseType: 'text'
      }).subscribe(
        data => {
          this.bootStrapLombardiaUtils.setLoadingModal(false);
          this.fileOctet = data;
        }, () => {
          this.errorMsg = 'Non è stato possibile salvare il file';
          this.bootStrapLombardiaUtils.setLoadingModal(false);
          this.bootStrapLombardiaUtils.activateModalAlert("modalFIleIOCenter");
        });
    }
  }

  private fileMultipartChange(event: Event) {
    this.bootStrapLombardiaUtils.setLoadingModal(true);
    const fileList: FileList = (event.target as HTMLInputElement).files as any;
    if (fileList.length > 0) {
      const file: File = fileList[0];
      this.fileMultipartSize = file.size;
      let formData: FormData = new FormData();
      formData.append('fileName', file.name);
      formData.append('file', file);
      let headers = new HttpHeaders();
      headers.append('enctype', 'multipart/form-data');
      this.fileIOService.uploadFileAllegatoMultipart(formData, {
        responseType: 'text'
      }).subscribe(
        data => {
          this.bootStrapLombardiaUtils.setLoadingModal(false);
          this.fileMultipart = data;
        }, (err) => {
          console.log(err);
          this.errorMsg = 'Non è stato possibile salvare il file';
          this.bootStrapLombardiaUtils.setLoadingModal(false);
          this.bootStrapLombardiaUtils.activateModalAlert("modalFIleIOCenter");
        });
    }
  }

  rimuoviFile(typeUpload: string) {
    this.bootStrapLombardiaUtils.setLoadingModal(true);
    this.fileIOService.deleteAllegato({
      params: {
        'fileName': typeUpload === 'octet' ? this.fileOctet : this.fileMultipart
      },
      responseType: 'text'
    }).subscribe(
      () => {
        this.bootStrapLombardiaUtils.setLoadingModal(false);
        if (typeUpload === 'octet') {
          this.fileOctet = '';
          this.octetInputRef.nativeElement.value = null;
        } else {
          this.fileMultipart = '';
          this.multipartInputRef.nativeElement.value = null;
        }
      }, (errore) => {
        this.errorMsg = errore.error;
        this.bootStrapLombardiaUtils.setLoadingModal(false);
        this.bootStrapLombardiaUtils.activateModalAlert("modalFIleIOCenter");
      }
    );
  }

}
