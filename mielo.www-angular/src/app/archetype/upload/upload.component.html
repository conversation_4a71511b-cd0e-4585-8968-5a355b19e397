<app-breadcrumbs [currentPage]="'Upload'"></app-breadcrumbs>
<form method="post" action="" enctype="multipart/form-data">
    <input #octet type="file" name="uploadOctet" id="uploadOctet" class="upload" (change)="allegatoOctetChange($event)" />
    <label for="uploadOctet">
        <svg class="icon icon-sm" aria-hidden="true">
            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-upload"></use>
        </svg>
        <span>Carica file in Octet-Stream</span>
    </label>
    <ul *ngIf="fileOctet.length" class="upload-file-list">
        <li class="upload-file success">
            <svg class="icon icon-sm" aria-hidden="true">
                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-file"></use>
            </svg>
            <p>
                {{fileOctet}} <span class="upload-file-weight">{{fileOctetSize}} B</span>
            </p>
            <button disabled>
                <span class="sr-only">Caricamento ultimato</span>
                <svg class="icon" aria-hidden="true">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-check"></use>
                </svg>

            </button>
            <button disabled>
                <span class="sr-only">Cancella</span>
                <svg class="icon" aria-hidden="true" (click)="rimuoviFile('octet')">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-delete"></use>
                </svg>
            </button>
        </li>
    </ul>


</form>


<form method="post" action="" enctype="multipart/form-data">
    <input #multipart type="file" name="uploadMultipart" id="uploadMultipart" class="upload" (change)="allegatoMultipartChange($event)" />
    <label for="uploadMultipart">
        <svg class="icon icon-sm" aria-hidden="true">
            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-upload"></use>
        </svg>
        <span>Carica file in forma Multipart</span>
    </label>
    <ul *ngIf="fileMultipart.length" class="upload-file-list">
        <li class="upload-file success">
            <svg class="icon icon-sm" aria-hidden="true">
                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-file"></use>
            </svg>
            <p>
                {{fileMultipart}} <span class="upload-file-weight">{{fileMultipartSize}} B</span>
            </p>
            <button disabled>
                <span class="sr-only">Caricamento ultimato</span>
                <svg class="icon" aria-hidden="true">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-check"></use>
                </svg>
            </button>
            <button disabled>
                <span class="sr-only">Cancella</span>
                <svg class="icon" aria-hidden="true" (click)="rimuoviFile('multipart')">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-delete"></use>
                </svg>
            </button>
        </li>
    </ul>


</form>


<!-- Modal -->
<div class="modal fade" tabindex="-1" role="dialog" id="modalFIleIOCenter">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Operazione non completata !</h5>
                <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                    <svg class="icon">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-close"></use>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p>{{errorMsg}}</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary btn-sm" data-dismiss="modal" type="button">Ok</button>
            </div>
        </div>
    </div>
</div>