import {IPageableResponse} from "../shared/interfaces/decoder.interface";

export interface IEventiAssociatiReq {
  idScheda: number;
  pageNumber: number;
  pageSize: number;
}

export interface  IEventiResponse extends IPageableResponse{
  content: IEventoAssociato[];
}

export interface IEventoAssociato {
  codiceIdentificativo: string;
  cognome: string;
  dataNascitaPaziente: number;
  dataRicovero: number;
  idNosologico: string;
  idPaziente: number;
  idScheda: number;
  medicoCompilatore: string;
  nome: string;
  presidioMedicoCompilatore: string;
  statoEvento: {
    idDizionario: number;
    categoria: string;
    descrizione: string;
  };
  tipoEvento: {
    idTipoEvento: number;
    descrizione: string;
  };
  isRientro: boolean;
}
