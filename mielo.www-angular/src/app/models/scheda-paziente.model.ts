export class SchedaPaziente {
  idPaziente: number;
  idScheda?: number ;
  nome: string;
  cognome: string;
  idNosologico: string;
  descLesione: string;
  medicoCompilatore: string;
  codiceIdentificativo: string;
  dataNascita: string;
  descStato: string;
  numeroEventi: number;
  ultimoEvento?: boolean;
  fkStatoUltimoEvento?: number;
  fkStato: number;
  isRientro: boolean;
  strutturaDestinazione?: StrutturaDestinazioneModel;
}

export interface StrutturaDestinazioneModel {
  codLivello2: string; 
  descrizioneLivello2: string
}