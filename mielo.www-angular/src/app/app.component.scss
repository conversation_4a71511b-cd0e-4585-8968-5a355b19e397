.vertical-line {
  width: 1px;
  height: 60px;
  background-color: #ccc;
  margin: 0 15px;
}

.p-presidio{
  line-height: 14px !important;
  margin-bottom: 7px !important;
  font-size: 14px !important;
}

.h-cust {
  //min-height: calc(100vh);
  max-height: inherit;
  display: block;
}

/**per impedire all'header di rimpicciolirsi durante lo scroll */
.it-header-slim-wrapper-content {
  display: flex !important;
}

.w-320 {
  width: 320px;
}

.back-button {
  padding: 0;

  .back-link {
    display: flex;
    align-items: center;
    color: #026C3C;
    font-size: 1rem;
    font-weight: 600;
  }

  .icon-left {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    fill: #026C3C;
  }

  .text-green {
    color: #026C3C;
    white-space: nowrap;
  }
}

.menu-container {
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
  width: 100%;
}

.note-button {
  color: #297A38 !important;
  padding: 0 !important;
  min-width: auto !important;
  display: flex !important;
  align-items: center !important;
  background-color: transparent !important;
  justify-content: flex-start !important;

  .icon {
    width: 32px !important;
    height: 32px !important;
    fill: #297A38 !important;
    margin-right: 0.5rem !important;
    margin-bottom: 0.25rem !important;
  }

  mat-icon {
    color: inherit !important;
    margin-right: 4px !important;
  }

  &:disabled {
    color: #999 !important;
    cursor: not-allowed !important;
    background: transparent !important;

    .icon {
      fill: #999 !important;
    }

    mat-icon {
      color: #999 !important;
    }
  }

  &[class*="remove"], &.remove-button {
    color: #D92828;

    mat-icon {
      color: #D92828;
    }

    span {
      color: #D92828 !important;
    }

    &:disabled {
      color: #B0B0B0 !important;
      mat-icon {
        color: #B0B0B0 !important;
      }
      span {
        color: #B0B0B0 !important;
      }
      .icon {
        fill: #B0B0B0 !important;
        color: #B0B0B0 !important;
      }
    }
  }
}