import { HttpErrorResponse, HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ErrorService } from './shared/components/dialog-error/error.service';

export function errorInterceptor(
    request: HttpRequest<unknown>,
    next: HttpHandlerFn
): Observable<HttpEvent<unknown>> {
    const errorService = inject(ErrorService);

    return next(request).pipe(
        catchError((error: HttpErrorResponse) => {
            errorService.manageError(error);
            return throwError(() => error);
        })
    );
}