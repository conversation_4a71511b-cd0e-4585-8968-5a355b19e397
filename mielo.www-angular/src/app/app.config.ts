import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptors } from '@angular/common/http';
import { ApplicationConfig } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withHashLocation } from '@angular/router';
import { routes } from './app.routes';
import { loaderInterceptor } from "./core/loader/loader.interceptor";
import { AuthInterceptor } from './HttpInterceptor';
import {registerLocaleData} from "@angular/common";
import localeIt from '@angular/common/locales/it';
import { errorInterceptor } from './ErrorInterceptor';
import {hTTPinterceptorDateInterceptor} from "./httpinterceptor-date.interceptor";

registerLocaleData(localeIt);

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withHashLocation()),
    provideHttpClient(
      withInterceptors([loaderInterceptor,errorInterceptor, hTTPinterceptorDateInterceptor])
    ),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    provideAnimationsAsync()
  ]
};
