import {HttpInterceptorFn} from '@angular/common/http';
import moment from "moment/moment";
import 'moment/locale/it';

export const hTTPinterceptorDateInterceptor: HttpInterceptorFn = (req, next) => {
  const excludedEndpoint = ['find','campiObbligatoriMancanti','getSchedeNuovoEvento','codicefiscale']
  const isURLexcluded = excludedEndpoint.some(sub =>
      req.url.toLowerCase().includes(sub.toLowerCase())
  );

  if (isURLexcluded || req.method === 'GET' || req.method === 'DELETE') return next(req)

  let reqFormatted = req
  if (req.body) {
    reqFormatted = req.clone({body:formatDatesInBEformat(req.body)})
  }



// dato in input un oggetto(che può contenere anche altri oggetti annidati) lo scorre tutto e trova i campi data e li formatta
  function formatDatesInBEformat(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    const newObj: any = Array.isArray(obj) ? [] : {};

    Object.keys(obj).forEach(key => {
      let value = obj[key];

      if ((key.toLowerCase().includes('data') || key.toLowerCase().includes('date')) && value) {
        if (typeof value === 'string' || typeof value === 'object') {
          const parsedDate = moment(value, ['YYYY-MM-DD', 'DD/MM/YYYY', 'MM-DD-YYYY', moment.ISO_8601], true).clone()

          if (key.toLowerCase().includes('datatrauma') || key.toLowerCase().includes('datadimissione')) { //workaround
            value = moment(parsedDate.toLocaleString()).format('YYYY-MM-DD')
          } else {
            if (parsedDate.isValid()) {
              if (key.toLowerCase().includes('ora') || key.toLowerCase().includes('ore') || key.toLowerCase().includes('datacreazione')) {
                value = parsedDate.format('YYYY-MM-DD HH:mm:ss');
              } else {
                value = parsedDate.format('YYYY-MM-DD');
              }
            }
          }
        }
        else if (typeof value === 'number') {
          if (key.toLowerCase().includes('ora') || key.toLowerCase().includes('ore') || key.toLowerCase().includes('datacreazione')) {
            value = moment(value).format('YYYY-MM-DD HH:mm:ss')
          } else {
            value = moment(value).format('YYYY-MM-DD')
          }
        }
      } else if (typeof value === 'object') {
        value = formatDatesInBEformat(value);
      }

      newObj[key] = value;
    });

    return newObj;
  }


  return next(reqFormatted)
};

