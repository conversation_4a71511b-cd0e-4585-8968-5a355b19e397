import { SceltaSchedaEnum } from "../../shared/enums/scelta-scheda.enum";
import { DatiCliniciComponent } from "../components/dati/dati-clinici/dati-clinici.component";
import { DatiGeneraliComponent } from "../components/dati/dati-generali/dati-generali.component";
import { DatiPazienteComponent } from "../components/dati/dati-paziente/dati-paziente.component";

export interface MenuListInterface {
    title: string,
    principal: boolean,
    tipoScheda: SceltaSchedaEnum,
    hidden: boolean,
    componente: any
}

export class Menu {
    static buildMenuCreazione(): Array<MenuListInterface> {
        let menuList: Array<MenuListInterface> = [
            {
              title: "Dati generali",
              principal: true,
              tipoScheda: SceltaSchedaEnum.GENERALI,
              hidden: false,
              componente: DatiGeneraliComponent
            },
            {
              title: 'Dati paziente',
              principal: false,
              tipoScheda: SceltaSchedaEnum.PAZIENTE,
              hidden: false,
              componente: DatiPazienteComponent,
            },
        ];
        return menuList;
    }

    static buildMenuEdit(): Array<MenuListInterface> {
        let menuList: Array<MenuListInterface> = [
            {
              title: "Dati generali",
              principal: true,
              tipoScheda: SceltaSchedaEnum.GENERALI,
              hidden: false,
              componente: DatiGeneraliComponent
            },
            {
              title: 'Dati clinici',
              principal: false,
              tipoScheda: SceltaSchedaEnum.CLINICI,
              hidden: false,
              componente: DatiCliniciComponent,
            },
        ];
        return menuList;
    }
}
