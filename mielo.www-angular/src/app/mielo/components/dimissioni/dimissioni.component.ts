import { CommonModule } from '@angular/common';
import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, Query<PERSON>ist, ViewChildren } from '@angular/core';
import { MatExpansionPanel } from "@angular/material/expansion";
import { Subscription } from "rxjs";
import { MaterialModule } from "../../../core/material.module";
import { ComplicanzeComponent } from "../../../shared/components/complicanze/complicanze.component";
import { TipoRicoveroEnum } from "../../../shared/enums/tipo-ricovero.enum";
import { SchedaDimissioneModel } from "../../../shared/interfaces/dati-dimissione.interface";
import { SchedaRicoveroModel } from "../../../shared/interfaces/scheda-ricovero.interface";
import { TIPO_EVENTO_RIENTRO_CENSITO, TIPO_EVENTO_RIENTRO_NON_CENSITO } from '../../../shared/utils/const';
import { SchedaRicoveroService } from "../../services/scheda-ricovero.service";
import {
  NecessitaAssistenzialiComponent
} from "../dati/dati-clinici/necessita-assistenziali/necessita-assistenziali.component";
import { QuadroNeurologicoComponent } from "../dati/dati-clinici/quadro-neurologico/quadro-neurologico.component";
import { ValutazioneComponent } from "../dati/dati-clinici/valutazione/valutazione.component";
import { AspettiSocioassistenzialiComponent } from './aspetti-socioassistenziali/aspetti-socioassistenziali.component';
import { AusiliOrtesiComponent } from "./ausili-ortesi/ausili-ortesi.component";
import { DatiGeneraliDimissioneRientroComponent } from "./dati-generali-dimissione-rientro/dati-generali-dimissione-rientro.component";
import { DatiGeneraliDimissioniComponent } from "./dati-generali-dimissioni/dati-generali-dimissioni.component";
import { InterventiEffettuatiRientroComponent } from './interventi-effettuati-rientro/interventi-effettuati-rientro.component';
import {
  QuadroFunzionaleDimissioneComponent
} from './quadro-funzionale-dimissione/quadro-funzionale-dimissione.component';
import { IdOperatoreEnum } from '../../../shared/enums/enum';
import { OperatoreService } from '../../../core/services/operatore.service';

@Component({
  selector: 'app-dimissioni',
  standalone: true,
  imports: [
    CommonModule,
    DatiGeneraliDimissioniComponent,
    DatiGeneraliDimissioneRientroComponent,
    InterventiEffettuatiRientroComponent,
    MaterialModule,
    ValutazioneComponent,
    NecessitaAssistenzialiComponent,
    QuadroNeurologicoComponent,
    NecessitaAssistenzialiComponent,
    ComplicanzeComponent,
    QuadroFunzionaleDimissioneComponent,
    AusiliOrtesiComponent,
    AspettiSocioassistenzialiComponent
  ],
  templateUrl: './dimissioni.component.html',
  styleUrl: './dimissioni.component.scss'
})
export class DimissioniComponent implements OnInit, OnDestroy {
  @Input() repartoType?: TipoRicoveroEnum

  @ViewChildren(MatExpansionPanel) panels: QueryList<MatExpansionPanel>;
  @ViewChildren(DatiGeneraliDimissioniComponent) datiGeneraliComponent: QueryList<DatiGeneraliDimissioniComponent>;
  @ViewChildren(ValutazioneComponent) valutazioneComponent: QueryList<ValutazioneComponent>;
  @ViewChildren(NecessitaAssistenzialiComponent) necessitaComponent: QueryList<NecessitaAssistenzialiComponent>;
  @ViewChildren(QuadroNeurologicoComponent) quadroNeuoroComponent: QueryList<QuadroNeurologicoComponent>;
  @ViewChildren(ComplicanzeComponent) complicanzeComponent: QueryList<ComplicanzeComponent>;
  @ViewChildren(QuadroFunzionaleDimissioneComponent) quadroFunzionaleComponent: QueryList<QuadroFunzionaleDimissioneComponent>;
  @ViewChildren(AusiliOrtesiComponent) ausiliOrtesiComponent: QueryList<AusiliOrtesiComponent>;
  @ViewChildren(AspettiSocioassistenzialiComponent) aspettiSocioassistenzialiComponent: QueryList<AspettiSocioassistenzialiComponent>;

  datiDimissione: SchedaDimissioneModel;
  idReparto: number;
  dataRicovero: string;

  ausiliOrtesiReadOnly: boolean = false;
  quadroFunzionaleReadOnly: boolean = false;
  aspettiSocioassistenzialiReadOnly: boolean = false;
  datiGenDimissReadOnly: boolean = false
  complicanzeReadOnly: boolean = false
  valutazioneReadOnly: boolean = false
  necessitaReadOnly: boolean = false
  quadroNeurologicoReadOnly: boolean = false
  datiGenRientroReadOnly: boolean = false
  interventiEffettuatiReadOnly: boolean = false

  isRientro: boolean = false;
  hasBeenOpenedQuadroFunzionale: boolean = true;
  hasBeenOpenedAspettiSocioassistenziali: boolean = true;
  private allDimissioniFormChanges$: Subscription

  subscriptions: Subscription[] = [];

  constructor(
    private schedaRicoveroService: SchedaRicoveroService,
    private operatoreService: OperatoreService
  ) {
  }

  ngOnInit(): void {
    this.schedaRicoveroService.schedaDettagliata$.subscribe((value: SchedaRicoveroModel) => {
      // CONTROLLO SE È UN RIENTRO CENSITO O NON CENSITO
      this.isRientro = value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento ||
        value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_RIENTRO_NON_CENSITO.idTipoEvento ? true : false;

      this.datiDimissione = value?.datiDimissioni;

      if (value.scheda.unitaOperativa && value.scheda.dataRicovero) {
        this.idReparto = value.scheda.unitaOperativa.idUnitaOperativa;
        this.dataRicovero = value.scheda.dataRicovero as string;
      }

      if (this.isReadOnly(value)) {
        this.datiGenDimissReadOnly = true
        this.complicanzeReadOnly = true
        this.valutazioneReadOnly = true
        this.necessitaReadOnly = true
        this.quadroNeurologicoReadOnly = true
        this.ausiliOrtesiReadOnly = true
        this.aspettiSocioassistenzialiReadOnly = true
        this.quadroFunzionaleReadOnly = true
        this.datiGenRientroReadOnly = true
        this.interventiEffettuatiReadOnly = true
      }
    })
  }

  isReadOnly(value: SchedaRicoveroModel, field?: string): boolean {
    const corrispondenzaPresidi = value.codPresidioOspedaliero !== null && 
                                  value.codPresidioOspedaliero !== this.operatoreService.getOperatore().codLivello2Operatore;
    const isEventoChiuso = value.evento.stato?.descrizione.toUpperCase() === 'CHIUSO';
    const isNotOspedaliero = this.operatoreService.getOperatore().idRuolo !== IdOperatoreEnum.OSP;
                                  
    return corrispondenzaPresidi || isEventoChiuso || isNotOspedaliero;
  }

  onOpened(accordion: string) {

    switch (accordion) {

      case 'quadroFunzionale':
        this.hasBeenOpenedQuadroFunzionale = true;
        break;

      case 'aspettiSocioassistenziali':
        this.hasBeenOpenedAspettiSocioassistenziali = true;
        break;

      default:
        break;
    }

  }

  ngOnDestroy() {
    this.subscriptions?.forEach(sub => sub?.unsubscribe());
    this.allDimissioniFormChanges$?.unsubscribe()
  }

}
