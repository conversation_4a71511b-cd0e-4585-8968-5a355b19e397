import { CommonModule } from "@angular/common";
import { Component, Input, OnInit } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatRadioModule } from "@angular/material/radio";
import { MatSelectModule } from "@angular/material/select";
import { DizionarioModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import { SchedaQuadroFunzionaleDimissioneModel } from "../../../../shared/interfaces/dati-dimissione.interface";
import { distinctUntilChanged, forkJoin, Subscription } from "rxjs";
import { DecoderService } from "../../../../shared/services/decoder.service";
import { DatiDimissioneService } from "../../../services/dati-dimissione.service";
import { ModalService } from "../../../../shared/services/modal.service";
import { CheckBoxSempliceModel } from "../../../../shared/interfaces/shared/shared.interface";
import { CapitalizePipe } from "../../../../shared/pipes/capitalize.pipe";
import { TipoRicoveroEnum } from "../../../../shared/enums/tipo-ricovero.enum";
import {MatTooltip} from "@angular/material/tooltip";

@Component({
    selector: 'app-quadro-funzionale-dimissione',
    standalone: true,
    templateUrl: './quadro-funzionale-dimissione.component.html',
    styleUrls: ['./quadro-funzionale-dimissione.component.scss'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatCheckboxModule,
    MatButtonModule,
    MatTooltip,
  ]
})
export class QuadroFunzionaleDimissioneComponent implements OnInit {

    @Input() readOnly: boolean = false;
    @Input() repartoType?: TipoRicoveroEnum;

    formReady: boolean = false;
    datiQuadroFunzionale: SchedaQuadroFunzionaleDimissioneModel;

    optionsBoolean: Array<DizionarioModel>;
    optionsTrattamentiEseguiti: Array<DizionarioModel>;

    ashworthValues: number[];

    trattamentiEseguitiMap: { [id: number]: { check: string } } = {};

    quadroFunzionaleForm: FormGroup;

    private subscriptions: Subscription[] = [];

    get formGroup(): FormGroup {
      return this.quadroFunzionaleForm;
    }
        constructor(
        private fb: FormBuilder,
        private decoderService: DecoderService,
        private modalService: ModalService,
        public datiDimissioneService: DatiDimissioneService
    ) {
        this.createForm();
    }

    ngOnInit(): void {
        this.ashworthValues = this.generateRange(0, 4);

        if (this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI) {
            this.quadroFunzionaleForm.get('scim')?.setValidators(Validators.required);
            this.quadroFunzionaleForm.get('scim')?.updateValueAndValidity();
            this.quadroFunzionaleForm.get('spasticita')?.setValidators(Validators.required);
            this.quadroFunzionaleForm.get('spasticita')?.updateValueAndValidity();
        }

        const forkJoinSources: any = {
            trattamentiEseguiti: this.decoderService.getDcodTrattamentiSpasticitaDimissione(),
            boolean: this.decoderService.getDcodBoolean()
        };
        forkJoin(forkJoinSources).subscribe((options: any) => {
            this.optionsTrattamentiEseguiti = options.trattamentiEseguiti;
            this.optionsBoolean = options.boolean;

            this.generateTrattamentiEseguitiMap();

            let dati = this.datiDimissioneService.getQuadroFunzionaleValue();
            // Ora che le opzioni sono pronte, posso popolare il form
            if (dati) {
                this.datiQuadroFunzionale = dati;
                this.populateForm(dati);
            }

            this.formReady = true;

            if (this.readOnly) {
                Object.keys(this.quadroFunzionaleForm.controls).forEach(controlName => {
                    if (!controlName.toLowerCase().includes('note')) {
                        this.quadroFunzionaleForm.get(controlName)?.disable({ emitEvent: false });
                    }
                });
            }
        });

        if (!this.readOnly) {

            this.quadroFunzionaleForm.get('scim')?.valueChanges.subscribe(val => {
                this.manageScimValue();
            })
            this.quadroFunzionaleForm.get('spasticita')?.valueChanges.subscribe(val => {
                this.manageSpasticitaValue();
            })
            this.quadroFunzionaleForm.get('altroCheck')?.valueChanges.subscribe(val => {
                this.manageAltroCheckValue();
            })

            const formChanges = this.quadroFunzionaleForm.valueChanges.pipe(
                distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
            ).subscribe(() => {
                const formValue = this.quadroFunzionaleForm.getRawValue();

                const convertBooleanValue = (val: DizionarioModel | null): boolean | null => {
                    if (!val || !val.descrizione) return null;
                    if (val.descrizione.toUpperCase() === 'SI') return true;
                    if (val.descrizione.toUpperCase() === 'NO') return false;
                    return null;
                };

                this.datiDimissioneService.setQuadroFunzionale({
                    ...formValue,
                    idScheda: this.datiQuadroFunzionale.idScheda,
                    nomeScheda: this.datiQuadroFunzionale.nomeScheda,
                    scim: convertBooleanValue(formValue.scim),
                    spasticita: convertBooleanValue(formValue.spasticita),
                    consulenzaDisfunzionaleSessuale: convertBooleanValue(formValue.consulenzaDisfunzionaleSessuale),
                    trattamentiQuadroFunzionaleLista: this.getTrattamentiEseguitiList(),
                    scimCuraSe: formValue.scimCuraSe ? Number(formValue.scimCuraSe) : null,
                    scimRespirazioneSfinteri: formValue.scimRespirazioneSfinteri ? Number(formValue.scimRespirazioneSfinteri) : null,
                    scimMobilita: formValue.scimMobilita ? Number(formValue.scimMobilita) : null,
                    wisci: formValue.wisci ? Number(formValue.wisci) : null
                });
              this.quadroFunzionaleForm.updateValueAndValidity()

                this.datiDimissioneService.setQuadroFunzionaleValido(this.quadroFunzionaleForm.valid);

            });

            this.subscriptions.push(formChanges);
        }

    }

    createForm() {
        this.quadroFunzionaleForm = this.fb.group({
            scim: [null],
            scimCuraSe: [null],
            scimRespirazioneSfinteri: [null],
            scimMobilita: [null],
            wisci: [null, [Validators.min(0), Validators.max(20)]],
            spasticita: [null],
            noteSpasticita: [null],
            punteggioScalaAshworth: [null],
            notePunteggioScalaAshworth: [null],
            farmaciOraliCheck: [null],
            tossinaBotulinicaCheck: [null],
            blocchiDiNervoCheck: [null],
            baclofeneIntratecaleCheck: [null],
            altroCheck: [null],
            altroTrattamentiSpasticita: [null],
            noteTrattamentiSpasticita: [null],
            consulenzaDisfunzionaleSessuale: [null],
            noteConsulenzaDisfunzionaleSessuale: [null],
        })
    }

    private toCamelCase(text: string): string {
        return text
            .toLowerCase()
            .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
            .replace(/^[A-Z]/, c => c.toLowerCase());
    }

    restrictToTwoDigits(event: any): boolean {
        return this.datiDimissioneService.restrictToTwoDigits(event);
    }

    populateForm(val: SchedaQuadroFunzionaleDimissioneModel) {
        this.populateBooleanValues(val);
        this.populateNumberAndText(val);
        this.populateNote(val);
        this.populateTrattamentiEseguiti(val.trattamentiQuadroFunzionaleLista);
        this.manageScimValue();
        this.manageSpasticitaValue();
        this.manageAltroCheckValue();
    }

    // Funzione che popola i controlli con radio button Sì e No
    populateBooleanValues(val: SchedaQuadroFunzionaleDimissioneModel): void {
        const booleanFields: Array<keyof SchedaQuadroFunzionaleDimissioneModel> = [
            'scim',
            'spasticita',
            'consulenzaDisfunzionaleSessuale'
        ];

        booleanFields.forEach(field => {
            const formControl = this.quadroFunzionaleForm.get(field as string);
            const value = val[field];

            if (formControl !== null && formControl !== undefined) {
                if (value === true) {
                    formControl.setValue(this.optionsBoolean[0], { emitEvent: false });
                } else if (value === false) {
                    formControl.setValue(this.optionsBoolean[1], { emitEvent: false });
                } else {
                    formControl.setValue(null, { emitEvent: false });
                }
            }
        });
    }

    populateNote(val: SchedaQuadroFunzionaleDimissioneModel) {
        const noteFields = [
            'noteSpasticita',
            'notePunteggioScalaAshworth',
            'noteTrattamentiSpasticita',
            'noteConsulenzaDisfunzionaleSessuale'
        ] as const;

        noteFields.forEach(field => {
            const value = val[field];
            const control = this.quadroFunzionaleForm.get(field);
            control?.setValue(value, { emitEvent: false });
        });
    }

    populateTrattamentiEseguiti(trattamentiEseguiti: CheckBoxSempliceModel[]) {
        if (!trattamentiEseguiti) return;

        trattamentiEseguiti.forEach(({ idDizionario }) => {
            const mapping = this.trattamentiEseguitiMap[idDizionario];
            const control = mapping.check + 'Check';
            if (mapping) {
                this.quadroFunzionaleForm.get(control)?.setValue(true, { emitEvent: false });
            }
        });
    }

    populateNumberAndText(val: SchedaQuadroFunzionaleDimissioneModel) {
        const fields: (keyof SchedaQuadroFunzionaleDimissioneModel)[] = [
            'scimCuraSe',
            'scimRespirazioneSfinteri',
            'scimMobilita',
            'wisci',
            'altroTrattamentiSpasticita',
            'punteggioScalaAshworth'
        ];

        fields.forEach(field => {
            this.quadroFunzionaleForm.get(field)?.setValue(val[field], { emitEvent: false });
        });
    }

    private generateRange(min: number, max: number): number[] {
        return Array.from({ length: max - min + 1 }, (_, i) => i + min);
    }

    generateTrattamentiEseguitiMap(): void {
        this.trattamentiEseguitiMap = {};

        this.optionsTrattamentiEseguiti.forEach(opt => {
            const checkControl = this.toCamelCase(opt.descrizione).trim();

            this.trattamentiEseguitiMap[opt.idDizionario] = {
                check: checkControl
            }
        })
    }

    getTrattamentiEseguitiList(): CheckBoxSempliceModel[] {
        const result: CheckBoxSempliceModel[] = [];

        this.optionsTrattamentiEseguiti.forEach(opt => {
            const mapping = this.trattamentiEseguitiMap[opt.idDizionario];
            const control = mapping.check + 'Check';
            if (mapping && this.quadroFunzionaleForm.get(control)?.value === true) {
                result.push({ idDizionario: opt.idDizionario });
            }
        });

        return result;
    }

    // Attiva e assegna validators obbligatori
    private enableControls(controls: string[], required = false) {
        controls.forEach(name => {
            const ctrl = this.quadroFunzionaleForm.get(name);
            ctrl?.enable({ emitEvent: false });
            if (required) {
                ctrl?.setValidators([Validators.required]);
                ctrl?.markAsUntouched();
            } else {
                ctrl?.clearValidators();
            }
            ctrl?.updateValueAndValidity({ emitEvent: false });
        });
    }

    // Disattiva e rimuove validators
    private disableControls(controls: string[], resetValue = true) {
        controls.forEach(name => {
            const ctrl = this.quadroFunzionaleForm.get(name);
            ctrl?.disable({ emitEvent: false });
            if (resetValue) {
                ctrl?.setValue(null, { emitEvent: false });
            }
            ctrl?.clearValidators();
            ctrl?.updateValueAndValidity({ emitEvent: false });
        });
    }

    private setControlsValidators(controls: string[]) {
        controls.forEach(name => {
            const ctrl = this.quadroFunzionaleForm.get(name)
            ctrl?.addValidators([Validators.pattern('^[0-9]{1,2}$')]);

            if (name == 'scimCuraSe' || name == 'wisci') {
                ctrl?.addValidators([Validators.min(0), Validators.max(20)])
            } else {
                ctrl?.addValidators([Validators.min(0), Validators.max(40)])
            }
        })
    }

    manageScimValue() {
        const scim = this.quadroFunzionaleForm.get('scim')?.value as DizionarioModel;

        if (!scim || scim.descrizione == 'NO') {
            this.disableControls([
                'scimCuraSe',
                'scimRespirazioneSfinteri',
                'scimMobilita'
            ])
        } else {
            this.enableControls([
                'scimCuraSe',
                'scimRespirazioneSfinteri',
                'scimMobilita'
            ], this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI);
            this.setControlsValidators([
                'scimCuraSe',
                'scimRespirazioneSfinteri',
                'scimMobilita'
            ])
        }
    }

    manageSpasticitaValue() {
        const spasticita = this.quadroFunzionaleForm.get('spasticita')?.value as DizionarioModel;

        if (!spasticita || spasticita.descrizione == 'NO') {
            this.disableControls([
                'punteggioScalaAshworth'
            ])
        } else {
            this.enableControls([
                'punteggioScalaAshworth'
            ], this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI)
        }
    }

    manageAltroCheckValue() {
        const altroCheck = this.quadroFunzionaleForm.get('altroCheck')?.value;

        if (altroCheck) {
            this.enableControls([
                'altroTrattamentiSpasticita'
            ], true)
        } else {
            this.disableControls([
                'altroTrattamentiSpasticita'
            ])
        }
    }

    // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno 
    isNoteDisabled(controlName: string): boolean {
        const control = this.quadroFunzionaleForm.get(controlName);
        return control?.disabled || false;
    }


    openPopupNote(field: string): void {
        let noteField = '';
        noteField = `note${field.charAt(0).toUpperCase() + field.slice(1)}`;
        const noteControl = this.quadroFunzionaleForm.get(noteField);

        if (noteControl) {
            const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

            this.modalService.note(configNote)?.subscribe((res: any) => {
                if (res != noteControl.value) {
                    noteControl.setValue(res);
                    noteControl.markAsDirty();
                    noteControl.updateValueAndValidity();
                }
                this.datiDimissioneService.adjustDialogHeight();
            });
        }
    }

}