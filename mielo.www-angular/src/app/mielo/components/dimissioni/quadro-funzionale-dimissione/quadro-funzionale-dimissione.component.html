<form [formGroup]="quadroFunzionaleForm" class="container-fluid mt-4">

    <div class="row d-flex w-100">
        <div class="col-2">
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <mat-label class="field-label mb-2">SCIM<span
                            *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('scim'))">*</span></mat-label>
                    <div class="d-flex align-items-center">
                        <div class="col p-0">
                            <div class="radio-group-container">
                                <mat-radio-group formControlName="scim" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(quadroFunzionaleForm.get('scim'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-2">
            <mat-label class="field-label">
                SCIM - cura di sé<span
                    *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('scimCuraSe'))">*</span>
            </mat-label>
            <mat-form-field appearance="outline">
                <input matInput formControlName="scimCuraSe" placeholder="Inserisci" (keydown)="restrictToTwoDigits($event)" />
                <mat-error *ngIf="quadroFunzionaleForm.get('scimCuraSe')?.hasError('required')">
                    Campo obbligatorio
                </mat-error>
                <mat-error *ngIf="quadroFunzionaleForm.get('scimCuraSe')?.hasError('max') || quadroFunzionaleForm.get('scimCuraSe')?.hasError('min')">
                    Minimo 0 massimo 20
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-4">
            <mat-label class="field-label">
                SCIM - Respirazione/gestione sfinteri<span
                    *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('scimRespirazioneSfinteri'))">*</span>
            </mat-label>
            <mat-form-field appearance="outline">
                <input matInput formControlName="scimRespirazioneSfinteri" placeholder="Inserisci" (keydown)="restrictToTwoDigits($event)" />
                <mat-error *ngIf="quadroFunzionaleForm.get('scimRespirazioneSfinteri')?.hasError('required')">
                    Campo obbligatorio
                </mat-error>
                <mat-error *ngIf="quadroFunzionaleForm.get('scimRespirazioneSfinteri')?.hasError('max') || quadroFunzionaleForm.get('scimRespirazioneSfinteri')?.hasError('min')">
                    Minimo 0 massimo 40
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-2">
            <mat-label class="field-label">
                SCIM - Mobilità<span
                    *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('scimMobilita'))">*</span>
            </mat-label>
            <mat-form-field appearance="outline">
                <input matInput formControlName="scimMobilita" placeholder="Inserisci" (keydown)="restrictToTwoDigits($event)" />
                <mat-error *ngIf="quadroFunzionaleForm.get('scimMobilita')?.hasError('required')">
                    Campo obbligatorio
                </mat-error>
                <mat-error *ngIf="quadroFunzionaleForm.get('scimMobilita')?.hasError('max') || quadroFunzionaleForm.get('scimMobilita')?.hasError('min')">
                    Minimo 0 massimo 40
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-2">
            <mat-label class="field-label">
                WISCI<span *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('wisci'))">*</span>
            </mat-label>
            <mat-form-field appearance="outline">
                <input matInput formControlName="wisci" placeholder="Inserisci" (keydown)="restrictToTwoDigits($event)"/>
                <mat-error *ngIf="quadroFunzionaleForm.get('wisci')?.hasError('required')">
                    Campo obbligatorio
                </mat-error>
                <mat-error *ngIf="quadroFunzionaleForm.get('wisci')?.hasError('max') || quadroFunzionaleForm.get('wisci')?.hasError('min')">
                    Minimo 0 massimo 20
                </mat-error>
            </mat-form-field>
        </div>
    </div>

    <!-- Separatore -->
    <div class="col-12">
        <hr class="my-3">
    </div>

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Spasticità - Scala Ashworth<span *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('spasticita'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="spasticita" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(quadroFunzionaleForm.get('spasticita'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-5"
                                    [disabled]="isNoteDisabled('spasticita')" (click)="openPopupNote('spasticita')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Punteggio Scala Ashworth
                            <span
                                *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('punteggioScalaAshworth'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="punteggioScalaAshworth" class="d-flex">
                                    <mat-radio-button *ngFor="let option of ashworthValues" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(quadroFunzionaleForm.get('punteggioScalaAshworth'), option, $event)">
                                        {{ option }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-5"
                                    [disabled]="isNoteDisabled('punteggioScalaAshworth')" (click)="openPopupNote('punteggioScalaAshworth')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Separatore -->
    <div class="col-12">
        <hr class="my-3">
    </div>

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Trattamenti eseguiti per la spasticità
                            <span
                                *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('trattamentiSpasticita'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <button mat-button type="button" class="note-button ml-5"
                                (click)="openPopupNote('trattamentiSpasticita')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row d-flex w-100">

        <div class="d-flex w-100 flex-wrap mb-3">
            <div class="col-3">
                <mat-checkbox formControlName="farmaciOraliCheck" color="primary">Farmaci orali</mat-checkbox>
            </div>
            <div class="col-3">
                <mat-checkbox formControlName="tossinaBotulinicaCheck" color="primary">Tossina botulinica</mat-checkbox>
            </div>
            <div class="col-3">
                <mat-checkbox formControlName="blocchiDiNervoCheck" color="primary">Blocchi di nervo</mat-checkbox>
            </div>
            <div class="col-3">
                <mat-checkbox formControlName="baclofeneIntratecaleCheck" color="primary">Baclofene
                    intratecale</mat-checkbox>
            </div>
        </div>
        <div class="d-flex w-100">
            <div class="col-2">
                <mat-checkbox formControlName="altroCheck" color="primary">Altro</mat-checkbox>
            </div>
            <div class="col-10">
                <mat-form-field appearance="outline">
                    <input matInput formControlName="altroTrattamentiSpasticita"
                           [matTooltip]="quadroFunzionaleForm.get('altroTrattamentiSpasticita')?.value || ''"
                        [placeholder]="datiDimissioneService.isRequired(quadroFunzionaleForm.get('altroTrattamentiSpasticita')) ? 'Inserisci*' : 'Inserisci'"
                           maxlength="250" />
                    <mat-error *ngIf="quadroFunzionaleForm.get('altroTrattamentiSpasticita')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>
        </div>

    </div>

    <div class="row d-flex w-100">

        <div class="col mt-3">
            <div class="d-flex flex-column">
                <mat-label class="field-label mb-2">Eseguita consulenza disfunzione sessuale<span
                        *ngIf="datiDimissioneService.isRequired(quadroFunzionaleForm.get('consulenzaDisfunzionaleSessuale'))">*</span></mat-label>
                <div class="d-flex align-items-center">
                    <div class="p-0 mr-2">
                        <div class="radio-group-container">
                            <mat-radio-group formControlName="consulenzaDisfunzionaleSessuale" class="d-flex">
                                <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" color="primary"
                                    (mouseup)="datiDimissioneService.toggleRadioSelection(quadroFunzionaleForm.get('consulenzaDisfunzionaleSessuale'), option, $event)">
                                    {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                    <button mat-button type="button" class="note-button ml-5"
                        [disabled]="isNoteDisabled('consulenzaDisfunzionaleSessuale')" (click)="openPopupNote('consulenzaDisfunzionaleSessuale')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div>

    </div>

</form>