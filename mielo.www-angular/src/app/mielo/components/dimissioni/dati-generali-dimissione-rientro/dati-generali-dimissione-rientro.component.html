<form [formGroup]="form">
    <div class="row mb-4">
        <div class="col-4">
            <mat-label class="field-label">
                Data dimissione<span>*</span>
            </mat-label>
            <mat-form-field class="w-100 mt-2">
                <input [max]="todayISO" [min]="dataRicovero" matInput formControlName="dataDimissione" [matDatepicker]="datePickerRientro"
                    placeholder="GG/MM/AAAA">
                <mat-datepicker-toggle matSuffix [for]="datePickerRientro">
                    <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                </mat-datepicker-toggle>
                <mat-datepicker #datePickerRientro></mat-datepicker>
                <mat-error *ngIf="form.get('dataDimissione')?.hasError('required')">
                    {{ ERROR_MESSAGE.REQUIRED }}
                </mat-error>
                <mat-error *ngIf="form.get('dataDimissione')?.hasError('matDatepickerMax')">
                    {{ ERROR_MESSAGE.MAX_DATE }}
                </mat-error>
                <mat-error *ngIf="form.get('dataDimissione')?.hasError('dateBeforeArrival')">
                    {{ ERROR_MESSAGE.DATE_BEFORE_ARRIVAL }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-3">
            <mat-label class="field-label">
                Ora dimissione<span>*</span>
            </mat-label>
            <mat-form-field appearance="outline" class="mt-2">
                <input matInput type="time" formControlName="oraDimissione" placeholder="--:--" />
                <button type="button" mat-icon-button matSuffix class="pointer-events-none"
                    [disabled]="form.get('oraDimissione')?.disabled">
                    <mat-icon class="d-flex">access_time</mat-icon>
                </button>
                <mat-error *ngIf="form.get('oraDimissione')?.hasError('required')">
                    {{ ERROR_MESSAGE.REQUIRED }}
                </mat-error>
                <mat-error *ngIf="form.get('oraDimissione')?.hasError('pattern')">
                    {{ ERROR_MESSAGE.INVALID_TIME }}
                </mat-error>
                <mat-error *ngIf="form.get('oraDimissione')?.hasError('maxTime')">
                    {{ ERROR_MESSAGE.MAX_TIME }}
                </mat-error>
            </mat-form-field>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-4">
            <mat-label class="field-label">
                Destinazione<span *ngIf="false">*</span>
            </mat-label>
            <mat-form-field class="w-100 mt-2">
                <mat-select formControlName="destinazione" placeholder="Seleziona">
                    <mat-option [value]="null"></mat-option>
                    <mat-option *ngFor="let option of optionsDestinazioneRientro" [value]="option">
                        {{ option.descrizione | capitalizeFirst }}
                    </mat-option>
                </mat-select>
                <mat-error *ngIf="form.get('destinazione')?.hasError('required')">
                    {{ ERROR_MESSAGE.REQUIRED }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-3">
            <mat-label class="field-label">
                Altro<span *ngIf="isRequired('altroDestinazione')">*</span>
            </mat-label>
            <mat-form-field class="w-100 mt-2">
                <input matInput formControlName="altroDestinazione" placeholder="Inserisci" maxlength="250" />
                <mat-error *ngIf="form.get('altroDestinazione')?.hasError('required')">
                    {{ ERROR_MESSAGE.REQUIRED }}
                </mat-error>
            </mat-form-field>
        </div>
        <div class="col-4 align-items-end d-flex">
            <button [disabled]="form.get('note')?.disabled" mat-button type="button" class="p-0 note-button"
                (mousedown)="openNotePopup()">
                <svg class="icon icon-primary mr-1">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                </svg>
                <span class="ms-1">Note</span>
            </button>
        </div>
    </div>
</form>