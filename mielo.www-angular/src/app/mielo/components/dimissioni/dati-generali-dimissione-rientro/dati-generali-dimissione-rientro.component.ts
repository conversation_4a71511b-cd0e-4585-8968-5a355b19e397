import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { Subscription } from 'rxjs';
import { MaterialModule } from '../../../../core/material.module';
import { ERROR_MESSAGE } from '../../../../shared/enums/enum';
import { SchedaGeneraleDimissioniModel } from '../../../../shared/interfaces/dati-dimissione.interface';
import { DizionarioModel } from '../../../../shared/interfaces/scheda-ricovero.interface';
import { CapitalizePipe } from '../../../../shared/pipes/capitalize.pipe';
import { DecoderService } from '../../../../shared/services/decoder.service';
import { ModalService } from '../../../../shared/services/modal.service';
import { DatiDimissioneRientroService } from '../../../services/dati-dimissione-rientro.service';
import { SchedaRicoveroService } from '../../../services/scheda-ricovero.service';

@Component({
    selector: 'app-dati-generali-dimissione-rientro',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, MaterialModule, MatDatepickerModule, MatInputModule, CapitalizePipe],
    templateUrl: './dati-generali-dimissione-rientro.component.html',
    styleUrl: './dati-generali-dimissione-rientro.component.scss'
})
export class DatiGeneraliDimissioneRientroComponent implements OnInit, OnDestroy {
    @Input() readOnly: boolean = false;

    form!: FormGroup;
    subscriptions: Subscription[] = [];
    todayISO: string = new Date().toISOString();
    optionsDestinazioneRientro: DizionarioModel[] = [];
    ALTRO_ID = 524;
    ERROR_MESSAGE = ERROR_MESSAGE;
    maxTime: string = '23:59';

    datiGeneraliDimissioneRientro: SchedaGeneraleDimissioniModel;

    dataRicovero: Date | null = null;

    constructor(
        private fb: FormBuilder,
        private datiDimissioneRientroService: DatiDimissioneRientroService,
        private schedaRicoveroService: SchedaRicoveroService,
        private modalService: ModalService,
        private decoderService: DecoderService
    ) { }

    // Custom validator per data futura e data di dimissione
    // Se la data è futura o maggiore della data di dimissione e della data attuale, ritorna errore
    static dataValidator(
        dataRicovero: Date | null
    ): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const selected = new Date(control.value);
            const now = new Date();
            if (selected > now) {
                return { matDatepickerMax: true };
            } else if (dataRicovero && dataRicovero > selected) {
                return { dateBeforeArrival: true };
            }
            return null;
        };
    }


    // Custom validator per ora futura se la data è oggi
    static oraNonFuturaValidator: ValidatorFn = (control: AbstractControl) => {
        if (!control.parent) return null;
        const dataCtrl = control.parent.get('dataDimissione');
        if (!dataCtrl || !dataCtrl.value || !control.value) return null;
        const today = new Date();
        const selectedDate = new Date(dataCtrl.value);
        if (selectedDate.toDateString() !== today.toDateString()) return null;
        const [h, m] = control.value.split(':');
        const selectedTime = new Date(selectedDate);
        selectedTime.setHours(+h, +m, 0, 0);
        if (selectedTime > today) {
            return { maxTime: true };
        }
        return null;
    };

    ngOnInit(): void {
        this.schedaRicoveroService.schedaCreation$.subscribe(dati => {
            if (dati && dati.scheda.dataRicovero) {
                this.dataRicovero = new Date(new Date(dati.scheda.dataRicovero).setHours(0,0,0));
            } else {
                this.dataRicovero = null;
            }
        }).unsubscribe();

        this.form = this.fb.group({
            dataDimissione: [null, [Validators.required, DatiGeneraliDimissioneRientroComponent.dataValidator(this.dataRicovero)]],
            oraDimissione: [null, [Validators.required, Validators.pattern(/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/), DatiGeneraliDimissioneRientroComponent.oraNonFuturaValidator]],
            destinazione: [null],
            altroDestinazione: [{ value: null, disabled: true }, [Validators.maxLength(250)]],
            noteGeneraliDimissione: [{ value: null, disabled: false }, [Validators.maxLength(3000)]]
        });

        this.decoderService.getDcodDestinazioneDimissioneRientro().subscribe(options => {
            this.optionsDestinazioneRientro = options;

            const dati: SchedaGeneraleDimissioniModel | null = this.datiDimissioneRientroService.getGeneraleRientroValue();
            if (dati) {
                const patchData: any = { ...dati };
                this.datiGeneraliDimissioneRientro = dati;

                if (dati.destinazione && dati.destinazione.idDizionario !== undefined && this.optionsDestinazioneRientro.length > 0) {
                    const selectedDestinazioneOption = this.optionsDestinazioneRientro.find(
                        option => option.idDizionario === dati.destinazione!.idDizionario
                    );
                    if (selectedDestinazioneOption) {
                        patchData.destinazione = selectedDestinazioneOption;
                    } else {
                        patchData.destinazione = null;
                    }
                }

                this.form.patchValue({
                    ...patchData,
                    dataDimissione: this.manageDataToShow(patchData.dataOraDimissione),
                    oraDimissione: this.manageOraToShow(patchData.dataOraDimissione)
                });

                const dataDimissione = this.form.get('dataDimissione'); 
                if (dataDimissione?.value && dataDimissione.enabled) dataDimissione?.markAsTouched();

                const patchedDestinazione = this.form.get('destinazione')?.value;
                if (patchedDestinazione) {
                    this.handleAltroDestinazioneChange(patchedDestinazione);
                }
            }
        });

        this.form.get('destinazione')?.valueChanges.subscribe((val: DizionarioModel) => {
            this.handleAltroDestinazioneChange(val);
        });

        this.form.get('dataDimissione')?.valueChanges.subscribe((val: string) => {
            const today = new Date();
            const selected = val ? new Date(val) : null;
            if (selected && selected.toDateString() === today.toDateString()) {
                // Se la data è oggi, l'ora massima è l'ora attuale
                const now = today;
                this.maxTime = now.toTimeString().slice(0, 5);
            } else {
                this.maxTime = '23:59';
            }
        });

        if (!this.readOnly) {
            const sub = this.form.valueChanges.subscribe(() => {
                const formValue = this.form.getRawValue();
                this.datiDimissioneRientroService.setGeneraleRientro({
                    ...this.schedaRicoveroService.getSchedaCorrente()?.datiDimissioni.schedaGeneraleDimissioni,
                    ...formValue,
                    dataOraDimissione: this.manageDataOraToSave(formValue.dataDimissione)
                });
                this.datiDimissioneRientroService.setGeneraleRientroValido(this.form.valid);
            });
            this.subscriptions.push(sub);
        } else {
            this.form.disable({ emitEvent: false });
        }
    }

    private handleAltroDestinazioneChange(val: DizionarioModel): void {
        if (this.readOnly) return;
        if (val && val.idDizionario === this.ALTRO_ID) {
            this.form.get('altroDestinazione')?.enable({ emitEvent: false });
            this.form.get('altroDestinazione')?.setValidators([Validators.required, Validators.maxLength(250)]);
        } else {
            this.form.get('altroDestinazione')?.setValue(null, { emitEvent: false });
            this.form.get('altroDestinazione')?.disable();
            this.form.get('altroDestinazione')?.clearValidators();
        }
        this.form.get('altroDestinazione')?.updateValueAndValidity({ emitEvent: false });
    }

    openNotePopup(): void {
        const noteControl = this.form.get('noteGeneraliDimissione');
        if (noteControl) {
            const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);
            this.modalService.note(configNote)?.subscribe((res) => {
                if (res !== noteControl.value) {
                    noteControl.setValue(res);
                    noteControl.markAsDirty();
                    noteControl.updateValueAndValidity();
                }
            });
        }
    }

    /* Verifica validatore required */
    isRequired(controlName: string): boolean {
        const control = this.form.get(controlName);
        return this.datiDimissioneRientroService.isRequired(control);
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(s => s.unsubscribe());
    }

    // Metodo per inserire dati arrivati da BE nel form control data trauma
    private manageDataToShow(dataCompleta: Date): Date | null {
        // Estrai solo la data (oggetto Date)
        if (dataCompleta) {
            let dataSola: Date;
            dataSola = new Date(dataCompleta)
            dataSola.setHours(0, 0, 0, 0); // azzeri ora/min/sec/ms
            return dataSola;
        } else return null
    }

    private manageOraToShow(dataCompleta: Date): string | null {
        if (!dataCompleta) return null;
        dataCompleta = new Date(dataCompleta);
        const ore = dataCompleta.getHours();
        const minuti = dataCompleta.getMinutes();

        if (ore === 0 && minuti === 0 && dataCompleta.getSeconds() === 0) {
            return null;
        }

        const oreStr = ore.toString().padStart(2, '0');
        const minutiStr = minuti.toString().padStart(2, '0');
        return `${oreStr}:${minutiStr}`;
    }

    // Metodo per inserire dati digitati in data trauma e ora trauma
    // in un unico oggetto Date da passare poi al BE
    private manageDataOraToSave(data: Date): number | null {
        if (!data) return null;

        const ora = this.form.get('oraDimissione')?.value;
        const dataConOra = new Date(data);

        if (ora && ora.includes(':')) {
            const [ore, minuti] = ora.split(':').map(Number);
            if (!isNaN(ore) && !isNaN(minuti)) {
                dataConOra.setHours(ore, minuti, 1, 1); //mettendo 1 secondo controllo l ora per mostrarla o no
            }
        } else {
            dataConOra.setHours(0, 0, 0, 0); //se 0 secondi non mostro  l ora
        }

        return dataConOra.getTime();
    }
} 