:host {
    .icon-color {
        color: #666666 !important;
    }
    .form-field-container {
        display: flex;
        flex-direction: column;

        .radio-group-container {
            margin-top: 1.3rem;
            padding: 0;
            min-height: 40px;
            display: flex;
            align-items: center;
            width: 100%;
            background-color: transparent;
            border: none;

            .mat-radio-group {
                display: flex;
                gap: 2rem;
            }
        }

        .d-flex {
            gap: 8px;

            mat-form-field {
                flex: 1;
                min-width: 0;
            }
        }

        .d-flex.align-items-center.h-100 {
            margin-top: 28px;

            button {
                padding: 0;
                line-height: 40px;
            }
        }
    }

    ::ng-deep {
        //.mdc-label {
        //    margin-bottom: 0.45rem !important;
        //}

        .mat-radio-button {
            .mat-radio-container {
                height: 16px;
                width: 16px;
            }

            .mat-radio-outer-circle,
            .mat-radio-inner-circle {
                height: 16px;
                width: 16px;
            }

            .mat-radio-label {
                display: flex;
                align-items: center;
            }

            .mat-radio-label-content {
                padding-left: 8px;
                line-height: normal;
                display: flex;
                align-items: center;
            }
        }

        .mat-form-field-appearance-outline {
            .mat-form-field-wrapper {
                margin: 0;
                padding: 0;
                width: 100%;
            }

            .mat-form-field-flex {
                background-color: #ffffff !important;
                min-height: 40px;
                padding: 0 0.75rem !important;
                position: relative;
            }

            .mat-form-field-outline {
                color: #e0e0e0;
                opacity: 0.5;
            }

            .mat-form-field-outline-start,
            .mat-form-field-outline-end,
            .mat-form-field-outline-gap {
                border-width: 1px !important;
            }

            &.mat-focused .mat-form-field-outline-thick {
                color: #4caf50;
            }

            .mat-form-field-infix {
                padding: 0.5em 0;
                border-top: none;
                width: calc(100% - 24px);
                background-color: #ffffff;
            }

            .mat-datepicker-toggle {
                position: relative;
                width: 24px;
                height: 24px;

                .mat-icon {
                    color: #666666;
                    fill: #666666;
                }
            }

            .mat-icon-button {
                width: 24px;
                height: 24px;
                line-height: 24px;

                .mat-icon {
                    color: #666666;
                    fill: #666666;
                    width: 18px;
                    height: 18px;
                    font-size: 18px;
                    line-height: 18px;
                }
            }
        }

        .mat-mdc-form-field-flex {
            display: inline-flex;
            align-items: baseline;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
        }

        .timepicker-overlay {
            z-index: 1000;
        }

        // Colore blu di default
        .mat-datepicker-toggle .mat-icon,
        .mat-icon-button .mat-icon {
            color: #003354 !important;
            fill: #003354 !important;
        }

        // Colore grigio solo se disabilitato
        .mat-form-field-disabled .mat-datepicker-toggle .mat-icon,
        .mat-form-field-disabled .mat-icon-button .mat-icon,
        .mat-mdc-form-field-disabled .mat-datepicker-toggle .mat-icon,
        .mat-mdc-form-field-disabled .mat-icon-button .mat-icon {
            color: #666666 !important;
            fill: #666666 !important;
        }
    }
}

.row.border-bottom {
    border-bottom: 1px solid #e9e9eab5 !important;
    padding-bottom: 1.5rem;
}

input {
    padding: 0 !important;
}

.mdc-icon-button {

    color: rgba(0, 0, 0, 0.54) !important;

    &:disabled {
        color: rgba(0, 0, 0, 0.3) !important;
    }
}