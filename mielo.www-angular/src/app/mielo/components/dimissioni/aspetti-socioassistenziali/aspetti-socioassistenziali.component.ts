import { CommonModule } from "@angular/common";
import { Component, Input, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatCheckboxChange, MatCheckboxModule } from "@angular/material/checkbox";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatRadioModule } from "@angular/material/radio";
import { SchedaAspettiSocioassistenzialiDimissioneModel } from "../../../../shared/interfaces/dati-dimissione.interface";
import { DizionarioModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import { distinctUntilChanged, forkJoin, Subscription } from "rxjs";
import { DecoderService } from "../../../../shared/services/decoder.service";
import { ModalService } from "../../../../shared/services/modal.service";
import { DatiDimissioneService } from "../../../services/dati-dimissione.service";
import { CheckBoxSempliceModel } from "../../../../shared/interfaces/shared/shared.interface";
import { TipoRicoveroEnum } from "../../../../shared/enums/tipo-ricovero.enum";
import {MatTooltip} from "@angular/material/tooltip";

@Component({
    selector: 'app-aspetti-socioassistenziali',
    standalone: true,
    templateUrl: './aspetti-socioassistenziali.component.html',
    styleUrls: ['./aspetti-socioassistenziali.component.scss'],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatRadioModule,
    MatCheckboxModule,
    MatButtonModule,
    MatTooltip,
  ]
})
export class AspettiSocioassistenzialiComponent implements OnInit {

    @Input() readOnly: boolean = false;
    @Input() repartoType?: TipoRicoveroEnum;

    formReady: boolean = false;
    datiAspettiSocioassistenziali: SchedaAspettiSocioassistenzialiDimissioneModel;
    caregiverRequired: boolean = false;

    optionsBoolean: Array<DizionarioModel>;
    optionsCaregiver: Array<DizionarioModel>;

    caregiverMap: { [id: number]: { check: string } } = {};

    aspettiForm: FormGroup;

    private subscriptions: Subscription[] = [];

    get formGroup(): FormGroup {
        return this.aspettiForm;
    }
    constructor(
        private fb: FormBuilder,
        private decoderService: DecoderService,
        private modalService: ModalService,
        public datiDimissioneService: DatiDimissioneService
    ) {
        this.createForm();
    }

    ngOnInit(): void {

        if (this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI) {
            const controls = [
                'attivazioneCureDomiciliari',
                'progettoIndividualeConTerritorio',
                'addestramentoCaregiver',
                'previstoControlloUnitaSpinale'
            ]
            controls.forEach(control => {
                this.aspettiForm.get(control)?.setValidators(Validators.required);
                this.aspettiForm.get(control)?.updateValueAndValidity();
            })

            this.caregiverRequired = true;
        }

        const forkJoinSources: any = {
            caregiver: this.decoderService.getDcodCaregiver(),
            boolean: this.decoderService.getDcodBoolean()
        };
        forkJoin(forkJoinSources).subscribe((options: any) => {
            this.optionsCaregiver = options.caregiver;
            this.optionsBoolean = options.boolean;

            this.generateCaregiverMap();

            let dati = this.datiDimissioneService.getSocioassistenzialiValue();

            // Ora che le opzioni sono pronte, posso popolare il form
            if (dati) {
                this.datiAspettiSocioassistenziali = dati;
                this.populateForm(dati);
            }

            this.formReady = true;

            if (this.readOnly) {
                Object.keys(this.aspettiForm.controls).forEach(controlName => {
                    if (!controlName.toLowerCase().includes('note')) {
                        this.aspettiForm.get(controlName)?.disable({ emitEvent: false });
                    }
                });
            }
        });

        if (!this.readOnly) {

            const formChanges = this.aspettiForm.valueChanges.pipe(
                distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
            ).subscribe(() => {
                const formValue = this.aspettiForm.getRawValue();

                const convertBooleanValue = (val: DizionarioModel | null): boolean | null => {
                    if (!val || !val.descrizione) return null;
                    if (val.descrizione.toUpperCase() === 'SI') return true;
                    if (val.descrizione.toUpperCase() === 'NO') return false;
                    return null;
                };

                this.datiDimissioneService.setSocioassistenziali({
                    ...formValue,
                    idScheda: this.datiAspettiSocioassistenziali.idScheda,
                    nomeScheda: this.datiAspettiSocioassistenziali.nomeScheda,
                    attivazioneCureDomiciliari: convertBooleanValue(formValue.attivazioneCureDomiciliari),
                    progettoIndividualeConTerritorio: convertBooleanValue(formValue.progettoIndividualeConTerritorio),
                    addestramentoCaregiver: convertBooleanValue(formValue.addestramentoCaregiver),
                    previstoControlloUnitaSpinale: convertBooleanValue(formValue.previstoControlloUnitaSpinale),
                    caregiverLista: this.getCaregiverList(),
                    noteCureDomiciliari: formValue.noteCureDomiciliari || null,
                    noteProgettoIndividualeConTerritorio: formValue.noteProgettoIndividualeConTerritorio || null,
                    noteCaregiver: formValue.noteCaregiver || null,
                    noteAddestramentoCaregiver: formValue.noteAddestramentoCaregiver || null,
                    notePrevistoControlloUnitaSpinale: formValue.notePrevistoControlloUnitaSpinale || null

                });
                this.aspettiForm.updateValueAndValidity()

                this.datiDimissioneService.setSocioAssistenzialiValid(this.aspettiForm.valid);

            });

            this.subscriptions.push(formChanges);
        }

    }

    private toCamelCase(text: string): string {
        return text
            .toLowerCase()
            .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
            .replace(/^[A-Z]/, c => c.toLowerCase());
    }

    createForm() {
        this.aspettiForm = this.fb.group({
            attivazioneCureDomiciliari: [null],
            noteCureDomiciliari: [null],
            progettoIndividualeConTerritorio: [null],
            noteProgettoIndividualeConTerritorio: [null],
            familiareCheck: [null],
            nessunoCheck: [null],
            altroCheck: [null],
            altroCaregiver: [null],
            noteCaregiver: [null],
            addestramentoCaregiver: [null],
            noteAddestramentoCaregiver: [null],
            previstoControlloUnitaSpinale: [null],
            notePrevistoControlloUnitaSpinale: [null]
        })
    }

    populateForm(val: SchedaAspettiSocioassistenzialiDimissioneModel) {
        this.populateBooleanValues(val);
        this.populateNote(val);
        this.populateCaregiver(val.caregiverLista);
        this.aspettiForm.get('altroCaregiver')?.setValue(val.altroCaregiver, { emitEvent: false });
        this.manageAltroCheckValue();

    }

    // Funzione che popola i controlli con radio button Sì e No
    populateBooleanValues(val: SchedaAspettiSocioassistenzialiDimissioneModel): void {
        const booleanFields: Array<keyof SchedaAspettiSocioassistenzialiDimissioneModel> = [
            'attivazioneCureDomiciliari',
            'progettoIndividualeConTerritorio',
            'addestramentoCaregiver',
            'previstoControlloUnitaSpinale'
        ];

        booleanFields.forEach(field => {
            const formControl = this.aspettiForm.get(field as string);
            const value = val[field];

            if (formControl !== null && formControl !== undefined) {
                if (value === true) {
                    formControl.setValue(this.optionsBoolean[0], { emitEvent: false });
                } else if (value === false) {
                    formControl.setValue(this.optionsBoolean[1], { emitEvent: false });
                } else {
                    formControl.setValue(null, { emitEvent: false });
                }
            }
        });
    }

    populateNote(val: SchedaAspettiSocioassistenzialiDimissioneModel) {
        const noteFields = [
            'noteCureDomiciliari',
            'noteProgettoIndividualeConTerritorio',
            'noteCaregiver',
            'noteAddestramentoCaregiver',
            'notePrevistoControlloUnitaSpinale'
        ] as const;

        noteFields.forEach(field => {
            const value = val[field];
            const control = this.aspettiForm.get(field);
            control?.setValue(value, { emitEvent: false });
        });
    }

    populateCaregiver(caregiver: CheckBoxSempliceModel[]) {
        if (!caregiver) return;

        caregiver.forEach(({ idDizionario }) => {
            const mapping = this.caregiverMap[idDizionario];
            const control = mapping.check + 'Check';
            if (mapping) {
                this.aspettiForm.get(control)?.setValue(true, { emitEvent: false });
                if (control === 'altroCheck') {
                    this.manageAltroCheckValue();
                }
            }
        });

        // Valuta abilitazione caregiver dopo popolamento
        this.aggiornaStatoAddestramentoCaregiver();
    }

    generateCaregiverMap(): void {
        this.caregiverMap = {};

        this.optionsCaregiver.forEach(opt => {
            const checkControl = this.toCamelCase(opt.descrizione).trim();

            this.caregiverMap[opt.idDizionario] = {
                check: checkControl
            }
        })
    }

    getCaregiverList(): CheckBoxSempliceModel[] {
        const result: CheckBoxSempliceModel[] = [];

        this.optionsCaregiver.forEach(opt => {
            const mapping = this.caregiverMap[opt.idDizionario];
            const control = mapping.check + 'Check';
            if (mapping && this.aspettiForm.get(control)?.value === true) {
                result.push({ idDizionario: opt.idDizionario });
            }
        });

        return result;
    }

    private aggiornaStatoAddestramentoCaregiver(): void {
        const familiare = this.aspettiForm.get('familiareCheck')?.value;
        const altro = this.aspettiForm.get('altroCheck')?.value;
        const altroInput = this.aspettiForm.get('altroCaregiver');
        const nessuno = this.aspettiForm.get('nessunoCheck')?.value;

        const addestramentoCtrl = this.aspettiForm.get('addestramentoCaregiver');
        const noteCtrl = this.aspettiForm.get('noteAddestramentoCaregiver');

        // Se almeno uno tra familiare o altro è selezionato
        if ((familiare || altro) && !nessuno) {
            addestramentoCtrl?.enable({ emitEvent: false });
            if (this.caregiverRequired) {
                addestramentoCtrl?.setValidators(Validators.required);
            }
        } else {
            addestramentoCtrl?.setValue(null, { emitEvent: false });
            addestramentoCtrl?.disable({ emitEvent: false });
            addestramentoCtrl?.clearValidators();

            // disabilitiamo anche il campo note
            noteCtrl?.setValue(null, { emitEvent: false });
        }

        addestramentoCtrl?.updateValueAndValidity({ emitEvent: false });
        noteCtrl?.updateValueAndValidity({ emitEvent: false });
    }


    private manageAltroCheckValue() {
        const altroCheck = this.aspettiForm.get('altroCheck')?.value;

        if (altroCheck) {
            this.enableControls([
                'altroCaregiver'
            ], true)
        } else {
            this.disableControls([
                'altroCaregiver'
            ])
        }
    }

    manageCaregiverCheckboxChange(changedName: string, event: MatCheckboxChange): void {

        const altroField = this.aspettiForm.get('altroCaregiver');
        const enableAltroField = () => {
            altroField?.enable({ emitEvent: false });
            altroField?.setValidators(Validators.required);
            altroField?.updateValueAndValidity();
        }
        const disableAltroField = () => {
            altroField?.setValue(null);
            altroField?.disable({ emitEvent: false });
            altroField?.setValidators(null);
            altroField?.updateValueAndValidity();
        }

        // Gestione campo "altro"
        if (changedName === 'altroCheck') {
            if (event.checked) {
                enableAltroField()
            } else {
                disableAltroField()
            }
        }

        // Se clicchi "nessuno", deseleziona gli altri
        if (changedName === 'nessunoCheck' && event.checked) {
            ['familiareCheck', 'altroCheck'].forEach(name => {
                if (name !== changedName) {
                    this.aspettiForm.get(name)?.setValue(false, { emitEvent: false });
                }
            });
            disableAltroField();
        }

        // Se clicchi uno qualunque degli altri, deseleziona "nessuno"
        if (['familiareCheck', 'altroCheck'].includes(changedName) && event.checked) {
            const nessunoCtrl = this.aspettiForm.get('nessunoCheck');
            if (nessunoCtrl?.value) {
                nessunoCtrl.setValue(false, { emitEvent: false });
            }
        }

        // Aggiorna lo stato di addestramento caregiver dopo ogni change
        this.aggiornaStatoAddestramentoCaregiver();
    }

    // Attiva e assegna validators obbligatori
    private enableControls(controls: string[], required = false) {
        controls.forEach(name => {
            const ctrl = this.aspettiForm.get(name);
            ctrl?.enable({ emitEvent: false });
            if (required) {
                ctrl?.setValidators([Validators.required]);
                ctrl?.markAsUntouched();
            } else {
                ctrl?.clearValidators();
            }
            ctrl?.updateValueAndValidity({ emitEvent: false });
        });
    }

    // Disattiva e rimuove validators
    private disableControls(controls: string[], resetValue = true) {
        controls.forEach(name => {
            const ctrl = this.aspettiForm.get(name);
            ctrl?.disable({ emitEvent: false });
            if (resetValue) {
                ctrl?.setValue(null, { emitEvent: false });
            }
            ctrl?.clearValidators();
            ctrl?.updateValueAndValidity({ emitEvent: false });
        });
    }

    // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno 
    isNoteDisabled(controlName: string): boolean {
        const control = this.aspettiForm.get(controlName);
        const value = control?.value;
        return value === null || value === undefined;
    }


    openPopupNote(field: string): void {
        if (field === 'attivazioneCureDomiciliari')
            field = 'cureDomiciliari';
        let noteField = '';
        noteField = `note${field.charAt(0).toUpperCase() + field.slice(1)}`;
        const noteControl = this.aspettiForm.get(noteField);

        if (noteControl) {
            const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

            this.modalService.note(configNote)?.subscribe((res: any) => {
                if (res != noteControl.value) {
                    noteControl.setValue(res);
                    noteControl.markAsDirty();
                    noteControl.updateValueAndValidity();
                }
                this.datiDimissioneService.adjustDialogHeight();
            });
        }
    }

}