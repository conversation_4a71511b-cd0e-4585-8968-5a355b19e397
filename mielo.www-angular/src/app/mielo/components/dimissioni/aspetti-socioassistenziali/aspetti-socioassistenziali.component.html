<form [formGroup]="aspettiForm" class="container-fluid mt-4">

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Attivazione cure domiciliari<span *ngIf="datiDimissioneService.isRequired(aspettiForm.get('attivazioneCureDomiciliari'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="attivazioneCureDomiciliari" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(aspettiForm.get('attivazioneCureDomiciliari'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-5"
                                        (click)="openPopupNote('attivazioneCureDomiciliari')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Progetto individuale condiviso con il territorio<span 
                            *ngIf="datiDimissioneService.isRequired(aspettiForm.get('progettoIndividualeConTerritorio'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="progettoIndividualeConTerritorio" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(aspettiForm.get('progettoIndividualeConTerritorio'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-5"
                                    (click)="openPopupNote('progettoIndividualeConTerritorio')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Separatore -->
    <div class="col-12">
        <hr class="my-3">
    </div>

    <div class="row d-flex w-100 mb-3">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">Caregiver<span
                                *ngIf="caregiverRequired">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <button mat-button type="button" class="note-button ml-5"
                                (click)="openPopupNote('caregiver')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row d-flex w-100 mb-4">

        <div class="d-flex w-100 flex-wrap mb-3">
            <div class="col-2">
                <mat-checkbox formControlName="familiareCheck" color="primary" (change)="manageCaregiverCheckboxChange('familiareCheck', $event)">
                    Familiare
                </mat-checkbox>
            </div>
            <div class="col-2">
                <mat-checkbox formControlName="nessunoCheck" color="primary" (change)="manageCaregiverCheckboxChange('nessunoCheck', $event)">
                    Nessuno
                </mat-checkbox>
            </div>
        </div>
        <div class="d-flex w-100">
            <div class="col-2">
                <mat-checkbox formControlName="altroCheck" color="primary" (change)="manageCaregiverCheckboxChange('altroCheck', $event)">
                    Altro
                </mat-checkbox>
            </div>
            <div class="col-8">
                <mat-form-field appearance="outline">
                    <input matInput
                           [matTooltip]="aspettiForm.get('altroCaregiver')?.value || ''"
                           formControlName="altroCaregiver"
                           [placeholder]="datiDimissioneService.isRequired(aspettiForm.get('altroCaregiver')) ? 'Inserisci*' : 'Inserisci'"
                           maxlength="250" />
                    <mat-error *ngIf="aspettiForm.get('altroCaregiver')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>
        </div>

    </div>

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Addestramento caregiver?<span 
                            *ngIf="datiDimissioneService.isRequired(aspettiForm.get('addestramentoCaregiver'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="addestramentoCaregiver" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(aspettiForm.get('addestramentoCaregiver'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-5"
                                        [disabled]="aspettiForm.get('addestramentoCaregiver')?.disabled" (click)="openPopupNote('addestramentoCaregiver')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Separatore -->
    <div class="col-12">
        <hr class="my-3">
    </div>

    <div class="row d-flex w-100">

        <div class="col-12 mb-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <mat-label class="field-label mb-0 me-4">
                            Previsto controllo successivo presso l’Unità Spinale?<span 
                            *ngIf="datiDimissioneService.isRequired(aspettiForm.get('previstoControlloUnitaSpinale'))">*</span>
                        </mat-label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="previstoControlloUnitaSpinale" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiDimissioneService.toggleRadioSelection(aspettiForm.get('previstoControlloUnitaSpinale'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-5"
                                     (click)="openPopupNote('previstoControlloUnitaSpinale')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

</form>