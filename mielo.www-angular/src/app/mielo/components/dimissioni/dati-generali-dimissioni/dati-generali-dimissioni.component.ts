import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { MaterialModule } from "../../../../core/material.module";
import { CommonModule } from "@angular/common";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatInputModule } from "@angular/material/input";
import { StruttureRiabilitativeModel, UnitaOperativaModel } from "../../../../shared/interfaces/shared/shared.interface";
import {
  CONDIZIONE_DIMISSIONE_PROGRAMMATA,
  MODALITA_DIMISSIONE_ALTRO_ISTITUTO_ACUTI,
  MODALITA_DIMISSIONE_ALTRO_ISTITUTO_RIABILITAZIONE,
  MODALITA_DIMISSIONE_DECESSO,
  MODALITA_DIMISSIONE_ORDINARIA,
  MODALITA_DIMISSIONE_PROTETTA_ADI,
  MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI,
  PROBLEMATICHE_DIMISSIONE_PROGRAMMATA_ALTRO,
} from "../../../../shared/utils/const";
import { DecoderService } from "../../../../shared/services/decoder.service";
import { CentriRicoveroModel, DizionarioModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import {catchError, debounceTime, distinctUntilChanged, filter, forkJoin, of, startWith, Subscription, switchMap, tap} from "rxjs";
import { CapitalizePipe } from "../../../../shared/pipes/capitalize.pipe";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { ModalService } from "../../../../shared/services/modal.service";
import { TipoRicoveroEnum } from "../../../../shared/enums/tipo-ricovero.enum";
import { DatiDimissioneService } from "../../../services/dati-dimissione.service";
import moment from "moment";
import {
  compareBycodLivello2,
  compareByIdCentro,
  compareByIdDizionario,
  compareByUnitaOperativa,
  toggleRadioSelection
} from "../../../../shared/utils/utils";
import { ERROR_MESSAGE } from "../../../../shared/enums/enum";
import { timeValidator } from "../../../../shared/validators/date-time.validator";
import { SchedaRicoveroService } from '../../../services/scheda-ricovero.service';

@Component({
  selector: 'app-dati-generali-dimissioni',
  standalone: true,
  imports: [MaterialModule, CommonModule, ReactiveFormsModule, MatDatepickerModule, MatInputModule, CapitalizePipe],
  templateUrl: './dati-generali-dimissioni.component.html',
  styleUrl: './dati-generali-dimissioni.component.scss'
})
export class DatiGeneraliDimissioniComponent implements OnInit, OnDestroy {
  @Input() idReparto!: TipoRicoveroEnum;
  @Input() readOnly: boolean = false;

  datiGeneraliForm!: FormGroup;
  optionsModalitaDimissione: DizionarioModel[];
  optionsDestinazione: DizionarioModel[];
  loadData$: Subscription;
  subscriptions: Subscription[] = [];
  strutturaSub: Subscription | undefined;
  optionsStrutturaDestinazione: StruttureRiabilitativeModel[];
  optionsRicoveroIn: UnitaOperativaModel[];
  optionsRepartoDest: CentriRicoveroModel[];
  optionsCondizioneDimissione: DizionarioModel[];
  optionsProblematicheDimissione: DizionarioModel[];
  optionsBoolean: DizionarioModel[];
  optionsTipologiaRiabilitativa: DizionarioModel[];

  problematicheDimissioneRequired: boolean = false;

  dataRicovero: Date | null = null;

  protected readonly moment = moment;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;
  protected readonly CONDIZIONE_DIMISSIONE_PROGRAMMATA = CONDIZIONE_DIMISSIONE_PROGRAMMATA;
  protected readonly MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI = MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI;
  protected readonly compareByIdDizionario = compareByIdDizionario;
  protected readonly compareBycodLivello2 = compareBycodLivello2;
  protected readonly compareByUnitaOperativa = compareByUnitaOperativa;
  protected readonly compareByIdCentro = compareByIdCentro;
  protected readonly toggleRadioSelection = toggleRadioSelection;

  get formGroup(): FormGroup {
    return this.datiGeneraliForm;
  }

  constructor(
    private decoderService: DecoderService,
    private fb: FormBuilder,
    private modalService: ModalService,
    private datiDimissioneService: DatiDimissioneService,
    private schedaRicoveroService: SchedaRicoveroService
  ) {
  }

  ngOnInit(): void {

    this.schedaRicoveroService.schedaCreation$.subscribe(dati => {
      if (dati && dati.scheda.dataRicovero) {
        this.dataRicovero = new Date(dati.scheda.dataRicovero);
      } else {
        this.dataRicovero = null;
      }
    }).unsubscribe();

    this.initForm();

    this.loadData$ = forkJoin({
      modalitaDimissioneSdo: this.decoderService.getDcodModalitaDimissioneSdo(),
      destinazione: this.decoderService.getDcodDestinazioneDimissione(),
      strutturaDestinazione: this.decoderService.getDcodStrutturelivello2(),
      repartoRicovero: this.decoderService.getDcodUnitaOperative(),
      condizioneDimissione: this.decoderService.getDcodCondizioneDimissione(),
      tipologiaRiabilitativa: this.decoderService.getDcodTipologiaRiabilitativa(),
      problematicheDimissione: this.decoderService.getDcodProblematicheDimissioneProgrammata(),
      boolean: this.decoderService.getDcodBoolean(),
    }).pipe(
      tap(options => {
        this.optionsModalitaDimissione = options.modalitaDimissioneSdo;
        this.optionsDestinazione = options.destinazione;
        this.optionsStrutturaDestinazione = options.strutturaDestinazione;
        this.optionsRicoveroIn = options.repartoRicovero;
        this.optionsProblematicheDimissione = options.problematicheDimissione;
        this.optionsCondizioneDimissione = options.condizioneDimissione;
        this.optionsCondizioneDimissione = options.condizioneDimissione;
        this.optionsBoolean = options.boolean
        this.optionsTipologiaRiabilitativa = options.tipologiaRiabilitativa;

        let dati;
        dati = this.datiDimissioneService.getGeneraleValue();

        if (dati) {
          this.datiGeneraliForm.patchValue(dati);

          if (this.isOptionSelected(PROBLEMATICHE_DIMISSIONE_PROGRAMMATA_ALTRO.idDizionario)) {
            this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.enable({ emitEvent: false });
          }

          if (dati.indicazioniRiabilitativeDimissione) {
            this.datiGeneraliForm.get('tipologiaRiabilitativa')?.enable({ emitEvent: false });
          }


          let dataDimissione;
          let oraDimissione;
          // se la data arriva dal be sarà tutta nel campo dataOraDimissione
          if (typeof dati.dataOraDimissione === 'number') {
            dataDimissione = moment(dati.dataOraDimissione)
            oraDimissione = moment(dati.dataOraDimissione).second() === 1 ? moment(dati.dataOraDimissione).format('HH:mm') : null; //workaround per non compilare campo ora con secondi
          } else {
            //se la data arriva dal subject sarà divisa in dataDimissione e oraDimissione
            dataDimissione = (dati.dataOraDimissione as any)?.dataDimissione ? moment((dati.dataOraDimissione as any)?.dataDimissione) : null
            oraDimissione = (dati.dataOraDimissione as any)?.oraDimissione || null
          }

          this.datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.setValue(oraDimissione, { emitEvent: false });
          this.datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.setValue(dataDimissione, { emitEvent: false });


          const dataDimissioneControl = this.datiGeneraliForm.get('dataOraDimissione.dataDimissione');
          if (dataDimissioneControl?.value && dataDimissioneControl.enabled) dataDimissioneControl?.markAsTouched();

          let dataDecesso;
          let oraDecesso;
          if (typeof dati.dataOraDecesso === 'number') {
            dataDecesso = moment(dati.dataOraDecesso)
            oraDecesso = moment(dati.dataOraDecesso).second() === 1 ? moment(dati.dataOraDecesso).format('HH:mm') : null;
          } else {
            dataDecesso = (dati.dataOraDecesso as any)?.dataDecesso ? moment((dati.dataOraDecesso as any)?.dataDecesso) : null
            oraDecesso = (dati.dataOraDecesso as any)?.oraDecesso || null
          }


          this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.setValue(dataDecesso, { emitEvent: false });
          this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.setValue(oraDecesso, { emitEvent: false });
        }

        if (!this.readOnly) {
          const formChanges = this.datiGeneraliForm.valueChanges
            .pipe(
              debounceTime(1),
              distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b))
            )
            .subscribe(() => {
              const formValue = this.datiGeneraliForm.getRawValue();

              this.datiDimissioneService.setGenerale(formValue);
              this.datiGeneraliForm.updateValueAndValidity();
              this.datiDimissioneService.setGeneraleValido(this.datiGeneraliForm.valid);
            })

          const strutturaChanges = this.datiGeneraliForm.get('strutturaDestinazione')?.valueChanges.pipe(
            // Partiamo dal valore iniziale del form control salvato, se esiste
            startWith(this.datiGeneraliForm.get('strutturaDestinazione')?.value),
            tap(value => {
              // Attiva/disattiva reparto visivamente in base al valore
              this.checkEnableRepartoDest(!!value);
            }),
            filter(value => !!value), // ignora valori nulli o falsy
            switchMap(value =>
              this.decoderService.getDcodRepartoRicovero(value.codLivello2, true).pipe(
                catchError(error => {
                  this.checkEnableRepartoDest(false);
                  return of([]); // ritorna array vuoto così la subscribe finale può gestirlo
                })
              )
            )
          ).subscribe({
            next: (reparti) => {
              this.optionsRepartoDest = reparti;
            }
          });

          this.subscriptions.push(formChanges);
          if (strutturaChanges) this.subscriptions.push(strutturaChanges);

        } else {
          Object.keys(this.datiGeneraliForm.controls).forEach(controlName => {
            if (!controlName.toLowerCase().includes('note') || !controlName.toLowerCase().includes('nota')) {
              this.datiGeneraliForm.get(controlName)?.disable({ emitEvent: false });
            }
          });
        }
      })
    ).subscribe()
  }

  private initForm(): void {
    this.datiGeneraliForm = this.fb.group({
      modalitaDimissioneSdo: [null, Validators.required],
      causa: [{ value: undefined, disabled: true }],
      dataOraDecesso: this.fb.group({
        oraDecesso: [{
          value: undefined,
          disabled: true
        }, [timeValidator(() => this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.value)]],
        dataDecesso: [{ value: undefined, disabled: true }]
      }),
      dataOraDimissione: this.fb.group({
        oraDimissione: [{
          value: undefined,
          disabled: true
        }, [timeValidator(() => this.datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.value)]],
        dataDimissione: [{ value: undefined, disabled: true }],
      }),
      dataCreazione: undefined,
      dataUltimoAggiornamento: undefined,
      destinazione: [{ value: undefined, disabled: true }],
      idScheda: undefined,
      idSchedaDimissioni: undefined,
      nomeScheda: undefined,
      noteCondizioneDimissione: undefined,
      noteDestinazione: undefined,
      noteTipologiaRiabilitativa: undefined,
      noteDimissioneProgrammata: undefined,
      noteGeneraliDimissione: undefined,
      noteProblematicheDimProgrammata: undefined,
      problematicheLista: [],
      repartoDestinazione: [{ value: undefined, disabled: true }],
      ricoveroIn: [{ value: undefined, disabled: true }],
      strutturaDestinazione: [{ value: undefined, disabled: true }], //campo select
      strutturaDestinazioneAlternativo: [{ value: undefined, disabled: true }], //campo input
      tipologiaRiabilitativa: [{ value: undefined, disabled: true }],
      altroRepartoDestinazione: [{ value: undefined, disabled: true }],
      condizioneDimissione: [{ value: undefined, disabled: true }],
      indicazioniRiabilitativeDimissione: [{ value: undefined, disabled: true }],
      noteIndicazioniRiabilitativeDimissione: [{ value: undefined, disabled: true }],
      altroCondizioneDimissione: undefined,
      altroProblematicheDimProgrammata: [{ value: undefined, disabled: true }]
    });

    const modalitaDimissioneSdo$ = this.datiGeneraliForm.get('modalitaDimissioneSdo')?.valueChanges
      .pipe(
        tap((value) => {
          if (!value) {
            this.datiGeneraliForm.get('dataOraDimissione')?.reset()
          }
          this.enableDestinazione();
          this.enableStrutturaDestinazione();
          this.enableRicoveroIn();
          this.enableCondizioneDimissione();
          this.gestisciCampiDecesso();
          this.gestisciIndicazioniRiabilitative();
        })
      )
      .subscribe() as Subscription
    this.subscriptions.push(modalitaDimissioneSdo$);

    const repartoDestinazione$ = this.datiGeneraliForm.get('repartoDestinazione')?.valueChanges
      .pipe(
        tap((value) => {
          this.datiGeneraliForm.get('altroRepartoDestinazione')?.setValue(null, { emitEvent: false });

          if (value?.descrizioneLivello2.toUpperCase() === 'ALTRO') {
            this.datiGeneraliForm.get('altroRepartoDestinazione')?.enable({ emitEvent: false });
            this.datiGeneraliForm.get('altroRepartoDestinazione')?.setValidators(Validators.required);
          } else {
            this.datiGeneraliForm.get('altroRepartoDestinazione')?.removeValidators(Validators.required);
            this.datiGeneraliForm.get('altroRepartoDestinazione')?.disable({ emitEvent: false });
          }

          this.datiGeneraliForm.get('altroRepartoDestinazione')?.updateValueAndValidity()
        })
      )
      .subscribe() as Subscription

    const indicazioniRiabilitativeDimissione$ = this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.valueChanges
      .pipe(
        tap((value) => {
          if (value) {
            this.datiGeneraliForm.get('noteIndicazioniRiabilitativeDimissione')?.enable({ emitEvent: false });
            this.datiGeneraliForm.get('tipologiaRiabilitativa')?.enable({ emitEvent: false });
            if (this.idReparto !== TipoRicoveroEnum.ACUTI) {
              this.datiGeneraliForm.get('tipologiaRiabilitativa')?.setValidators(Validators.required);
              this.datiGeneraliForm.get('tipologiaRiabilitativa')?.updateValueAndValidity();
            }
          } else {
            this.datiGeneraliForm.get('noteTipologiaRiabilitativa')?.setValue(null, { emitEvent: false });
            this.datiGeneraliForm.get('noteIndicazioniRiabilitativeDimissione')?.setValue(null, { emitEvent: false });
            this.datiGeneraliForm.get('noteIndicazioniRiabilitativeDimissione')?.disable({ emitEvent: false });
            this.datiGeneraliForm.get('tipologiaRiabilitativa')?.setValue(null, { emitEvent: false });
            this.datiGeneraliForm.get('tipologiaRiabilitativa')?.disable({ emitEvent: false });
            this.datiGeneraliForm.get('tipologiaRiabilitativa')?.clearValidators();
            this.datiGeneraliForm.get('tipologiaRiabilitativa')?.updateValueAndValidity();
          }
        })
      )
      .subscribe() as Subscription

    const dataDimissione$ = this.datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.valueChanges
      .pipe(
        tap(() => {
          this.datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.updateValueAndValidity({ emitEvent: false });
          this.datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.markAsTouched()
        })
      )
      .subscribe() as Subscription

    const dataDecesso$ = this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.valueChanges
      .pipe(
        tap(() => {
          this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.updateValueAndValidity({ emitEvent: false });
          this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.markAsTouched()
        })
      )
      .subscribe() as Subscription

    const altroProblematicheDimProgrammata$ = this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.statusChanges
      .subscribe(value => {
          if (value === 'DISABLED') {
            this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.removeValidators(Validators.required);
          } else {
            this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.addValidators(Validators.required);
          }
        }) as Subscription


    this.subscriptions = [repartoDestinazione$, indicazioniRiabilitativeDimissione$, dataDimissione$, dataDecesso$, altroProblematicheDimProgrammata$];

    this.gestisciCampiDecesso();

  }

  isRequired(controlName: string, formGroup?: any): boolean {
    if (!formGroup) {
      formGroup = this.datiGeneraliForm;
    }

    const control = formGroup.get(controlName);
    if (!control) {
      return false;
    }

    if (!control.validator) {
      return false;
    }

    // Eseguiamo il validator manualmente simulando un control "vuoto"
    const errors = control.validator({} as AbstractControl);

    return errors ? !!errors['required'] : false;
  }

  toggleRadioSelectionComponent(controlName: string, option: DizionarioModel): void {
    const control = this.datiGeneraliForm.get(controlName);
    if (control?.disabled) return;

    if (control?.value === option) {
      control?.setValue(null); // toggle off
    } else {
      control?.setValue(option);
    }

    // Gestione specifica per condizione dimissione programmata
    if (controlName === 'condizioneDimissione') {
      if (option.idDizionario === CONDIZIONE_DIMISSIONE_PROGRAMMATA.idDizionario && control?.value) {
        this.datiGeneraliForm.get('problematicheLista')?.enable({ emitEvent: false });
        if (this.idReparto !== TipoRicoveroEnum.ACUTI) {
          this.problematicheDimissioneRequired = true;
          // this.datiGeneraliForm.get('problematicheLista')?.setValidators
        }
      } else {
        this.datiGeneraliForm.get('problematicheLista')?.setValue([], { emitEvent: false });
        this.datiGeneraliForm.get('problematicheLista')?.disable({ emitEvent: false });
        this.datiGeneraliForm.get('noteProblematicheDimProgrammata')?.setValue(null, { emitEvent: false });
        this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.setValue(undefined, { emitEvent: false });
        this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.disable();
      }
    }
  }

  isDeceduto(): boolean {
    return this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_DECESSO.idDizionario;
  }

  /**
   * Gestisce i campi relativi al decesso del paziente
   * Abilita/disabilita i campi e imposta i validatori in base alla modalità di dimissione
   */
  gestisciCampiDecesso(): void {
    if (this.isDeceduto()) {
      // Disabilita i campi di dimissione
      this.datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('dataOraDimissione')?.disable({ emitEvent: false });

      // Abilita i campi relativi al decesso
      this.datiGeneraliForm.get('causa')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('causa')?.enable({ emitEvent: false });
      this.datiGeneraliForm.get('causa')?.setValidators(Validators.required);
      this.datiGeneraliForm.get('causa')?.updateValueAndValidity({ emitEvent: false });

      this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.enable({ emitEvent: false });
      this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.addValidators(Validators.required);
      this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.updateValueAndValidity({ emitEvent: false });

      this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.enable({ emitEvent: false });
      this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.setValidators([timeValidator(() => this.datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.value), Validators.required]);
      this.datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.updateValueAndValidity({ emitEvent: false });

      // Disabilita il campo condizione dimissione in caso di decesso
      this.datiGeneraliForm.get('condizioneDimissione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('condizioneDimissione')?.disable({ emitEvent: false });
    } else {
      // Abilita i campi di dimissione
      const dataDimissControl = this.datiGeneraliForm.get('dataOraDimissione.dataDimissione');
      const oraDimissControl = this.datiGeneraliForm.get('dataOraDimissione.oraDimissione');
      const dataDecessControl = this.datiGeneraliForm.get('dataOraDecesso.dataDecesso');
      const oraDecessControl = this.datiGeneraliForm.get('dataOraDecesso.oraDecesso');

      // Disabilita i campi relativi al decesso
      dataDimissControl?.enable({ emitEvent: false });
      dataDimissControl?.addValidators(Validators.required);
      oraDimissControl?.enable({ emitEvent: false });
      oraDimissControl?.addValidators(Validators.required);

      this.datiGeneraliForm.get('causa')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('causa')?.disable({ emitEvent: false });

      this.datiGeneraliForm.get('causa')?.updateValueAndValidity({ emitEvent: false });
      dataDecessControl?.setValue(null, { emitEvent: false });
      dataDecessControl?.disable({ emitEvent: false });
      dataDecessControl?.updateValueAndValidity({ emitEvent: false });

      oraDecessControl?.setValue(null, { emitEvent: false });
      oraDecessControl?.disable({ emitEvent: false });
      oraDecessControl?.updateValueAndValidity({ emitEvent: false });
      // La condizione dimissione viene gestita dal metodo enableCondizioneDimissione()
    }
  }

  enableDestinazione() {
    const isEnabled = this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_PROTETTA_ADI.idDizionario ||
      this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_ORDINARIA.idDizionario;

    if (isEnabled) {
      this.datiGeneraliForm.get('destinazione')?.setValidators(Validators.required);
      this.datiGeneraliForm.get('destinazione')?.enable({ emitEvent: false });
    } else {
      this.datiGeneraliForm.get('destinazione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('noteDestinazione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('destinazione')?.disable({ emitEvent: false });
      this.datiGeneraliForm.get('destinazione')?.removeValidators(Validators.required);
    }
  }

  enableStrutturaDestinazione() {
    const isEnabled = this.isDimissioneVerso()
      || this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI.idDizionario

    const strutturaDest = this.datiGeneraliForm.get('strutturaDestinazione'); 
    const strutturaDestAlt = this.datiGeneraliForm.get('strutturaDestinazioneAlternativo'); 
    strutturaDest?.setValue(null, { emitEvent: false });
    strutturaDestAlt?.setValue(null, { emitEvent: false });


    if (isEnabled) {
      // gestisco  select
      if (this.isDimissioneVerso()) {
        strutturaDestAlt?.removeValidators(Validators.required)
        strutturaDest?.enable({ emitEvent: false });
        strutturaDest?.addValidators(Validators.required);
        // gestisco input
      } else if (this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI.idDizionario) {
        strutturaDestAlt?.addValidators(Validators.required)
        strutturaDestAlt?.enable()
        strutturaDest?.removeValidators(Validators.required);
        this.checkEnableRepartoDest(false);
      }
    } else {
      strutturaDestAlt?.removeValidators(Validators.required)
      strutturaDestAlt?.disable({ emitEvent: false });
      strutturaDest?.removeValidators(Validators.required);
      strutturaDest?.disable({ emitEvent: false });
      this.checkEnableRepartoDest(false);
    }
  }

  enableRicoveroIn() {
    const isEnabled = this.isDimissioneVerso()

    if (isEnabled) {
      this.datiGeneraliForm.get('ricoveroIn')?.enable({ emitEvent: false });
      this.datiGeneraliForm.get('ricoveroIn')?.setValidators(Validators.required);
    } else {
      this.datiGeneraliForm.get('ricoveroIn')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('ricoveroIn')?.disable({ emitEvent: false });
      this.datiGeneraliForm.get('ricoveroIn')?.removeValidators(Validators.required);
    }
  }

  checkEnableRepartoDest(enable: boolean) {
    const reparto = this.datiGeneraliForm.get('repartoDestinazione');
    const altro = this.datiGeneraliForm.get('altroRepartoDestinazione');
    if (enable) {
      reparto?.enable({ emitEvent: false });
      reparto?.setValidators(Validators.required);
    } else {    
      altro?.setValue(null, { emitEvent: false });
      altro?.disable({ emitEvent: false });
      altro?.removeValidators(Validators.required);
      reparto?.setValue(null, { emitEvent: false });
      reparto?.disable({ emitEvent: false });
      reparto?.removeValidators(Validators.required);
    }
  }

  /**
   * Gestisce l'abilitazione/disabilitazione del campo condizione dimissione
   * in base alla modalità di dimissione selezionata
   */
  enableCondizioneDimissione() {
    const resetProblematiche = () => {
      this.datiGeneraliForm.get('problematicheLista')?.setValue([], { emitEvent: false });
      this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.disable();
      this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.setValue(null, { emitEvent: false });
    }
    // Se il paziente è deceduto, la condizione di dimissione è già gestita nel metodo gestisciCampiDecesso()
    if (this.isDeceduto()) {
      resetProblematiche()
      return;
    }

    if (this.isCondiDimEnabled()) {
      this.datiGeneraliForm.get('condizioneDimissione')?.enable({ emitEvent: false });
      if (this.idReparto !== TipoRicoveroEnum.ACUTI) {
        this.datiGeneraliForm.get('condizioneDimissione')?.setValidators(Validators.required);
        this.datiGeneraliForm.get('condizioneDimissione')?.updateValueAndValidity();
      }
    } else {
      this.datiGeneraliForm.get('condizioneDimissione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('condizioneDimissione')?.disable({ emitEvent: false });
      this.datiGeneraliForm.get('condizioneDimissione')?.clearValidators();
      this.datiGeneraliForm.get('condizioneDimissione')?.updateValueAndValidity();
      resetProblematiche()
    }
  }

  /**
   * Gestisce l'abilitazione/disabilitazione del campo indicazioni riabilitative
   * in base alla modalità di dimissione selezionata
   */
  gestisciIndicazioniRiabilitative(): void {
    if (this.isDeceduto() || !this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value) {
      this.datiGeneraliForm.get('noteIndicazioniRiabilitativeDimissione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.disable({ emitEvent: false });
      this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.clearValidators();
      this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.updateValueAndValidity({ emitEvent: false });
    } else if (!this.isDeceduto()) {
      this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.enable({ emitEvent: false });
      this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.updateValueAndValidity({ emitEvent: false });
      if (this.idReparto !== TipoRicoveroEnum.ACUTI) {
        this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.setValidators(Validators.required);
        this.datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.updateValueAndValidity({ emitEvent: false });
      }
    }
  }

  isCondiDimEnabled(): boolean {
    return this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_ALTRO_ISTITUTO_ACUTI.idDizionario
  }

  isDimissioneVerso(): boolean {
    return (this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_ALTRO_ISTITUTO_RIABILITAZIONE.idDizionario ||
      this.datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_ALTRO_ISTITUTO_ACUTI.idDizionario)
  }

  isOptionSelected(idDizionario: number): boolean {
    return this.datiGeneraliForm.get('problematicheLista')?.value?.map((p: any) => p.idDizionario).includes(idDizionario);
  }

  onCheckboxChange(event: MatCheckboxChange, idDizionario: number): void {
    if (idDizionario === PROBLEMATICHE_DIMISSIONE_PROGRAMMATA_ALTRO.idDizionario && event.checked) {
      this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.enable();
    } else if (idDizionario === PROBLEMATICHE_DIMISSIONE_PROGRAMMATA_ALTRO.idDizionario && !event.checked) {
      this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.setValue(null, { emitEvent: false });
      this.datiGeneraliForm.get('altroProblematicheDimProgrammata')?.disable();
    }

    const problematicheListaCtrl = this.datiGeneraliForm.get('problematicheLista')

    if (event.checked) {
      problematicheListaCtrl?.setValue([...problematicheListaCtrl?.value, { idDizionario }], { emitEvent: true });
    } else {
      problematicheListaCtrl?.setValue(problematicheListaCtrl?.value.filter((p: any) => p.idDizionario !== idDizionario), { emitEvent: true });
    }

    problematicheListaCtrl?.markAsDirty();
    problematicheListaCtrl?.updateValueAndValidity({ emitEvent: true });
  }

  openPopupNote(controlName: string): void {
    const noteControl = this.datiGeneraliForm.get(controlName);

    if (noteControl) {
      let configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != noteControl.value) {
          noteControl.setValue(res);
          noteControl.markAsDirty();
          noteControl.updateValueAndValidity();
        }
      });
    }
  }


  ngOnDestroy() {
    this.loadData$?.unsubscribe();
    this.subscriptions.forEach(sub => sub?.unsubscribe());
  }
}
