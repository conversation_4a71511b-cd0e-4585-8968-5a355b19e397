<form [formGroup]="datiGeneraliForm">
    <!-- Modalità di dimissione SDO -->
    <div class="row my-4">
        <div class="row flex align-items-center mb-4 col-12">
            <div class="col-5">
                <mat-label class="field-label">
                    Modalità di dimissione SDO<span *ngIf="isRequired('modalitaDimissioneSdo')">*</span>
                </mat-label>
                <mat-form-field class="w-100 mt-2">
                    <mat-select
                            [compareWith]="compareByIdDizionario"
                            formControlName="modalitaDimissioneSdo" placeholder="Seleziona">
                        <mat-option [value]="null"></mat-option>
                        <mat-option *ngFor="let option of optionsModalitaDimissione" [value]="option">
                            {{ option.descrizione }}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="datiGeneraliForm.get('modalitaDimissioneSdo')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>
            <!--    DATA ORA DIMISSIONE-->
            <ng-container formGroupName="dataOraDimissione">
                <div class="col-3">
                    <mat-label class="field-label">
                        Data dimissione<span
                            *ngIf="datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.enabled">*</span>
                    </mat-label>
                    <mat-form-field class="w-100 mt-2">
                        <input
                                [min]="dataRicovero"
                                [max]="moment()"
                                matInput
                                formControlName="dataDimissione"
                                [matDatepicker]="datePickerDimissione"
                                placeholder="GG/MM/AAAA">
                        <mat-datepicker-toggle matSuffix [for]="datePickerDimissione">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #datePickerDimissione></mat-datepicker>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.hasError('required')">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.hasError('matDatepickerMax')">
                            {{ ERROR_MESSAGE.MAX_DATE }}
                        </mat-error>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDimissione.dataDimissione')?.hasError('matDatepickerMin')">
                            {{ ERROR_MESSAGE.DATE_BEFORE_ARRIVAL }}
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="col-2">
                    <mat-label class="field-label">
                        Ora dimissione<span
                            *ngIf="datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.enabled">*</span>
                    </mat-label>
                    <mat-form-field appearance="outline" class="w-100 mt-2">
                        <input matInput
                               class="p-0"
                               type="time" formControlName="oraDimissione" placeholder="--:--"/>
                        <button type="button" mat-icon-button matSuffix class="pointer-events-none" [disabled]="datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.disabled">
                            <mat-icon class="d-flex icon-color">access_time</mat-icon>
                        </button>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.hasError('required')">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.hasError('maxTime')">
                            {{ERROR_MESSAGE.MAX_TIME}}
                        </mat-error>
                        <mat-error *ngIf="datiGeneraliForm.get('dataOraDimissione.oraDimissione')?.hasError('pattern')">
                            Formato ora non valido
                        </mat-error>
                    </mat-form-field>
                </div>
            </ng-container>
        </div>

        <!-- Causa, Data Decesso e Ora Decesso -->
        <div class="row flex align-items-center mb-3 border-hr col-12">
            <div class="col-5">
                <mat-label class="field-label">
                    Causa<span *ngIf="datiGeneraliForm.get('causa')?.enabled">*</span>
                </mat-label>
                <mat-form-field class="w-100 mt-2">
                    <input
                            matInput
                            formControlName="causa" placeholder="Inserisci" maxlength="250"/>
                    <mat-error *ngIf="datiGeneraliForm.get('causa')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>
            <!--data ora decesso-->
            <ng-container formGroupName="dataOraDecesso">
                <div class="col-3">
                    <mat-label class="field-label">
                        Data Decesso<span *ngIf="datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.enabled">*</span>
                    </mat-label>
                    <mat-form-field class="w-100 mt-2">
                        <input
                                [max]="moment()"
                                matInput
                                formControlName="dataDecesso"
                                [matDatepicker]="datePickerDecesso"
                                placeholder="GG/MM/AAAA">
                        <mat-datepicker-toggle matSuffix [for]="datePickerDecesso">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #datePickerDecesso></mat-datepicker>
                        <mat-error *ngIf="datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.hasError('required')">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDecesso.dataDecesso')?.hasError('matDatepickerMax')">
                            {{ ERROR_MESSAGE.MAX_DATE }}
                        </mat-error>
                    </mat-form-field>
                </div>

                <div class="col-2">
                    <mat-label class="field-label">
                        Ora Decesso<span *ngIf="datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.enabled">*</span>
                    </mat-label>
                    <mat-form-field class="w-100 mt-2">
                        <input matInput
                               class="p-0"
                               type="time" formControlName="oraDecesso" placeholder="--:--"/>
                        <button type="button" mat-icon-button matSuffix class="pointer-events-none" [disabled]="datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.disabled">
                            <mat-icon class="d-flex icon-color">access_time</mat-icon>
                        </button>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.hasError('maxTime')">
                            {{ERROR_MESSAGE.MAX_TIME}}
                        </mat-error>
                        <mat-error *ngIf="datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.hasError('required')">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                        <mat-error *ngIf="datiGeneraliForm.get('dataOraDecesso.oraDecesso')?.hasError('pattern')">
                            Formato ora non valido
                        </mat-error>
                    </mat-form-field>
                </div>
            </ng-container>
            <!--      note-->
            <div class="align-content-end h-100 col-2">
                <div class="w-100 mt-2 d-flex justify-content-center">
                    <button
                            [disabled]="datiGeneraliForm.get('modalitaDimissioneSdo')?.disabled"
                            mat-button type="button" class="p-0 note-button"
                            (mousedown)="openPopupNote('noteGeneraliDimissione')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Destinazione -->
        <div class="row flex align-items-center mb-3 col-12 border-hr">
            <div class="col-12">
                <mat-label class="field-label">
                    Destinazione<span *ngIf="datiGeneraliForm.get('destinazione')?.enabled">*</span>
                </mat-label>
                <div class="mt-2 row">
                    <div class="col-auto">
                        <mat-radio-group formControlName="destinazione" class="d-flex">
                            <mat-radio-button
                                    [checked]="datiGeneraliForm.get('destinazione')?.value?.idDizionario === option.idDizionario"
                                    (click)="toggleRadioSelectionComponent('destinazione', option)"
                                    *ngFor="let option of optionsDestinazione" [value]="option" color="primary"
                                    class="mb-2 m-1">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('destinazione')?.hasError('required') && datiGeneraliForm.get('destinazione')?.touched">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                    </div>
                    <div class="col-4 d-flex align-items-center">
                        <button
                                [disabled]="datiGeneraliForm.get('destinazione')?.disabled"
                                mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteDestinazione')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!--    strutt destinazione-->
        <div class="row flex align-items-center mb-3 col-12">
            <div class="col-6">
                <mat-label class="field-label">
                    Struttura di destinazione<span *ngIf="isRequired('strutturaDestinazione') || isRequired('strutturaDestinazioneAlternativo')">*</span>
                </mat-label>
                <!-- Input per MODALITA_DIMISSIONE_PROTETTA_ADI -->
                <mat-form-field class="w-100 mt-2"
                                *ngIf="!isDimissioneVerso() || datiGeneraliForm.get('modalitaDimissioneSdo')?.value?.idDizionario === MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI.idDizionario">
                    <input
                            maxlength="250"
                            matInput formControlName="strutturaDestinazioneAlternativo" placeholder="Inserisci"/>
                    <mat-error *ngIf="datiGeneraliForm.get('strutturaDestinazioneAlternativo')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>

                <!-- Select per isDimissioneVerso -->
                <mat-form-field class="w-100 mt-2" *ngIf="isDimissioneVerso()">
                    <mat-select
                            [compareWith]="compareBycodLivello2"
                            formControlName="strutturaDestinazione" placeholder="Seleziona">
                        <mat-option [value]="null"></mat-option>
                        <mat-option *ngFor="let option of optionsStrutturaDestinazione" [value]="option">
                            {{ option.descrizioneLivello2 }}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="datiGeneraliForm.get('strutturaDestinazione')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>

            <div class="col-6">
                <mat-label class="field-label">
                    Ricovero in<span *ngIf="isRequired('ricoveroIn')">*</span>
                </mat-label>
                <mat-form-field class="w-100 mt-2">
                    <mat-select
                            [compareWith]="compareByUnitaOperativa"
                            formControlName="ricoveroIn" placeholder="Seleziona">
                        <mat-option [value]="null"></mat-option>
                        <mat-option *ngFor="let option of optionsRicoveroIn" [value]="option">
                            {{ option.nome }}
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="datiGeneraliForm.get('ricoveroIn')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>
        </div>

        <!--    ricovero in-->
        <div class="row flex align-items-center my-3 col-12 border-hr">

            <!--    reparto dest  -->
            <div class="col-6">
                <mat-label class="field-label">
                    Reparto di destinazione<span *ngIf="isRequired('repartoDestinazione')">*</span>
                </mat-label>
                <mat-form-field class="w-100 mt-2">
                    <mat-select
                            [compareWith]="compareByIdCentro"
                            formControlName="repartoDestinazione" placeholder="Seleziona">
                        <mat-option [value]="null"></mat-option>
                        <mat-option *ngFor="let option of optionsRepartoDest" [value]="option">
                            {{ option.descrizioneLivello3 }}
                        </mat-option>
                    </mat-select>
                    <mat-error>
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>

            <!--      altro reparto dest-->
            <div class="col-6">
                <mat-label class="field-label">
                    Altro<span *ngIf="isRequired('altroRepartoDestinazione')">*</span>
                </mat-label>
                <mat-form-field class="w-100 mt-2">
                    <input
                            [matTooltip]="datiGeneraliForm.get('altroRepartoDestinazione')?.value || ''"
                            matInput
                            formControlName="altroRepartoDestinazione"
                            placeholder="Inserisci"
                            maxlength="250"/>
                    <mat-error *ngIf="datiGeneraliForm.get('altroRepartoDestinazione')?.hasError('required')">
                                {{ERROR_MESSAGE.REQUIRED}}
                    </mat-error>
                </mat-form-field>
            </div>
        </div>

        <!--    cond dimiss-->
        <div class="row flex align-items-center mb-3 col-12 border-hr">
            <div class="col-12">
                <mat-label class="field-label">
                    Condizione della dimissione
                </mat-label>
                <div class="mt-2 row">
                    <div class="col-auto">
                        <mat-radio-group formControlName="condizioneDimissione" class="d-flex">
                            <mat-radio-button
                                    [checked]="option.idDizionario === datiGeneraliForm.get('condizioneDimissione')?.value?.idDizionario"
                                    (click)="toggleRadioSelectionComponent('condizioneDimissione', option)"
                                    *ngFor="let option of optionsCondizioneDimissione"
                                    [value]="option"
                                    color="primary"
                                    class="mb-2 m-1">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <mat-error
                                *ngIf="datiGeneraliForm.get('condizioneDimissione')?.hasError('required') && datiGeneraliForm.get('condizioneDimissione')?.touched">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                    </div>
                    <div class="col-4 d-flex align-items-center">
                        <button
                                [disabled]="datiGeneraliForm.get('condizioneDimissione')?.disabled"
                                mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteCondizioneDimissione')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row flex align-items-center mb-3 col-12 border-hr">
            <div class="col-12">
                <mat-label class="field-label">
                    Problematiche correlate alla dimissione programmata<span *ngIf="problematicheDimissioneRequired">*</span>
                </mat-label>
                <div class="mt-2 row">
                    @for (option of optionsProblematicheDimissione; track option.idDizionario; let i = $index) {
                        <div class="col-3 mt-2">
                            <mat-checkbox
                                    [disabled]="datiGeneraliForm.get('condizioneDimissione')?.value?.idDizionario !== CONDIZIONE_DIMISSIONE_PROGRAMMATA.idDizionario || readOnly"
                                    [checked]="isOptionSelected(option.idDizionario)"
                                    (change)="onCheckboxChange($event, option.idDizionario)"
                                    color="primary">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-checkbox>
                        </div>
                    }
                    <div class="col-6">
                        <mat-form-field class="col mt-2">
                            <input
                                    [matTooltip]="datiGeneraliForm.get('altroProblematicheDimProgrammata')?.value || ''"
                                    matInput
                                    formControlName="altroProblematicheDimProgrammata"
                                    maxlength="250"
                                    placeholder="Inserisci {{isRequired('altroProblematicheDimProgrammata') ? '*' : ''}}"/>
                            <mat-error
                                    *ngIf="datiGeneraliForm.get('altroProblematicheDimProgrammata')?.errors?.['required']">
                            {{ERROR_MESSAGE.REQUIRED}}
                            </mat-error>
                        </mat-form-field>
                    </div>
                    <div class="col-3 d-flex align-items-center">
                        <button
                                [disabled]="datiGeneraliForm.get('condizioneDimissione')?.value?.idDizionario !== CONDIZIONE_DIMISSIONE_PROGRAMMATA.idDizionario"
                                mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteProblematicheDimProgrammata')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <!--    indic riab-->
        <div class="row flex align-items-center col-12">
            <div class="col-12 row">
                <div class="col-4">
                    <mat-label class="field-label">Indicazioni riabilitative alla dimissione<span *ngIf="isRequired('indicazioniRiabilitativeDimissione')">*</span>
                    </mat-label>
                    <div class="mt-2 row">
                        <mat-radio-group
                                formControlName="indicazioniRiabilitativeDimissione" class="d-flex">
                            <mat-radio-button
                                    (click)="toggleRadioSelection(datiGeneraliForm, 'indicazioniRiabilitativeDimissione',true)"
                                    [value]="true" color="primary">Si
                            </mat-radio-button>
                            <mat-radio-button
                                    (click)="toggleRadioSelection(datiGeneraliForm, 'indicazioniRiabilitativeDimissione',false)"
                                    [value]="false" class="ms-4" color="primary">No
                            </mat-radio-button>
                        </mat-radio-group>
                        <!--            note-->
                        <div class="col-auto ml-3 d-flex justify-content-end">
                            <button
                                    [disabled]="datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.disabled"
                                    mat-button type="button" class="p-0 note-button"
                                    (mousedown)="openPopupNote('noteIndicazioniRiabilitativeDimissione')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!--tipologia riab-->
                <div class="col-8">
                    <mat-label class="field-label">Tipologia riabilitativa<span *ngIf="isRequired('tipologiaRiabilitativa')">*</span></mat-label>
                    <div class="mt-2 row">
                        <mat-radio-group
                                formControlName="tipologiaRiabilitativa" class="d-flex">
                            <mat-radio-button
                                    [checked]="option.idDizionario === datiGeneraliForm.get('tipologiaRiabilitativa')?.value?.idDizionario"
                                    (click)="toggleRadioSelectionComponent('tipologiaRiabilitativa', option)"
                                    *ngFor="let option of optionsTipologiaRiabilitativa"
                                    [value]="option"
                                    color="primary"
                                    class="mb-2">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <!--            note-->
                        <div class="col-auto ml-3 d-flex justify-content-center">
                            <button
                                    [disabled]="!datiGeneraliForm.get('indicazioniRiabilitativeDimissione')?.value"
                                    mat-button type="button" class="p-0 note-button"
                                    (mousedown)="openPopupNote('noteTipologiaRiabilitativa')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>

</form>
