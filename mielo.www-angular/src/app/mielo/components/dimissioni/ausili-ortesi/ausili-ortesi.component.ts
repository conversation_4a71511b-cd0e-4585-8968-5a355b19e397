import {CommonModule} from '@angular/common';
import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import {MatButtonModule} from '@angular/material/button';
import {MatCheckboxModule} from '@angular/material/checkbox';
import {MatExpansionModule} from '@angular/material/expansion';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatIconRegistry} from '@angular/material/icon';
import {MatInputModule} from '@angular/material/input';
import {DomSanitizer} from '@angular/platform-browser';
import {debounceTime, forkJoin} from 'rxjs';
import {SchedaAusiliDimissioneModel} from '../../../../shared/interfaces/dati-dimissione.interface';
import {DizionarioModel} from '../../../../shared/interfaces/scheda-ricovero.interface';
import {CapitalizePipe} from '../../../../shared/pipes/capitalize.pipe';
import {DecoderService} from '../../../../shared/services/decoder.service';
import {ModalService} from '../../../../shared/services/modal.service';
import {DatiCliniciService} from '../../../services/dati-clinici.service';
import {DatiDimissioneService} from '../../../services/dati-dimissione.service';
import {ECategoriaEvento, ERROR_MESSAGE} from '../../../../shared/enums/enum';
import {CheckBoxSempliceModel} from '../../../../shared/interfaces/shared/shared.interface';
import {TipoRicoveroEnum} from '../../../../shared/enums/tipo-ricovero.enum';
import {MatTooltip} from "@angular/material/tooltip";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
  selector: 'app-ausili-ortesi',
  templateUrl: './ausili-ortesi.component.html',
  styleUrl: './ausili-ortesi.component.scss',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    CapitalizePipe,
    MatTooltip
  ]
})

export class AusiliOrtesiComponent implements OnInit, OnChanges {
  @Input() repartoType?: TipoRicoveroEnum;
  @Input() readOnly: boolean;

  ausiliForm: FormGroup;

  ausiliMobility: Array<DizionarioModel>;
  ausiliGestione: Array<DizionarioModel>;
  ausiliFunzioniVitali: Array<DizionarioModel>;
  presidi: Array<DizionarioModel>;

  ausiliFormSavedData: SchedaAusiliDimissioneModel;

  ERROR_MESSAGE = ERROR_MESSAGE;

  ausiliMobilitaRequired: boolean = false;
  ausiliUrinariRequired: boolean = false;
  ausiliFunzioniVitaliRequired: boolean = false;
  presidiPosturaliRequired: boolean = false;

  constructor(
    private fb: FormBuilder,
    iconRegistry: MatIconRegistry,
    sanitizer: DomSanitizer,
    public datiCliniciService: DatiCliniciService,
    public datiDimissione: DatiDimissioneService,
    private decoderService: DecoderService,
    private modalService: ModalService
  ) {
    iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));
    this.initForm();
  }

  ngOnInit() {
    const forkJoinSources: any = {
      ausiliMobilita: this.decoderService.getDcodAusiliMobility(),
      ausiliGestione: this.decoderService.getDcodAusiliGestione(),
      ausiliFunzioniVitali: this.decoderService.getDcodAusiliFunzioniVitali(),
      presidi: this.decoderService.getDcodPresidi()
    };

    forkJoin(forkJoinSources).subscribe((options: any) => {
      this.ausiliMobility = this.moveAltroToEnd(options.ausiliMobilita);
      this.ausiliGestione = this.moveAltroToEnd(options.ausiliGestione);
      this.ausiliFunzioniVitali = this.moveAltroToEnd(options.ausiliFunzioniVitali);
      this.presidi = this.moveAltroToEnd(options.presidi);

      this.populateFormArrayWithOptions(this.ausiliForm.get('ausiliMobilitaLista'), this.ausiliMobility, 'altroAusiliMobilita');
      this.populateFormArrayWithOptions(this.ausiliForm.get('ausiliUrinariLista'), this.ausiliGestione, 'altroAusiliUrinari');
      this.populateFormArrayWithOptions(this.ausiliForm.get('ausiliFunzioniVitaliLista'), this.ausiliFunzioniVitali, 'altroAusiliFunzioniVitali');
      this.populateFormArrayWithOptions(this.ausiliForm.get('presidiPosturaliLista'), this.presidi);

      this.ausiliFormSavedData = this.datiDimissione.getAusiliValue()!;
      if (this.ausiliFormSavedData) {
        this.populateFormWithSavedData(this.ausiliFormSavedData);
      }

      this.setupCheckboxSubscriptions();

      if (this.readOnly) {
        Object.keys(this.ausiliForm.controls).forEach(controlName => {
          if (!controlName.toLowerCase().includes('note')) {
            this.ausiliForm.get(controlName)?.disable({ emitEvent: false });
          }
        });
      }

      this.setDatiAusiliOnValuesChanges();
    });
  }

  setupCheckboxSubscriptions(): void {
    const listConfigs = [
      { controlName: 'ausiliMobilitaLista', altro: 'altroAusiliMobilita' },
      { controlName: 'ausiliUrinariLista', altro: 'altroAusiliUrinari' },
      { controlName: 'ausiliFunzioniVitaliLista', altro: 'altroAusiliFunzioniVitali' },
      { controlName: 'presidiPosturaliLista', altro: null }
    ];

    listConfigs.forEach(cfg => {
      const array = this.ausiliForm.get(cfg.controlName) as FormArray;
      array.controls.forEach(group => {
        this.subscribeToNessunAusilioAndNessunoCheckbox(group as FormGroup, array, cfg.altro ?? undefined);
        const label = group.get('label')?.value;
        if (label === 'ALTRO' && cfg.altro) {
          const altroControl = this.ausiliForm.get(cfg.altro);
          this.subscribeToAltroCheckbox(group as FormGroup, altroControl);
        }
      });
    });
  }

  setDatiAusiliOnValuesChanges() {
    this.ausiliForm.valueChanges.pipe(
      debounceTime(1),
    ).subscribe(() => {
      const formValue = this.ausiliForm.getRawValue();
      this.datiDimissione.setAusili({
        ...formValue,
        idScheda: this.ausiliFormSavedData.idScheda,
        nomeScheda: this.ausiliFormSavedData.nomeScheda,
        altroAusiliFunzioniVitali: this.ausiliForm.get('altroAusiliFunzioniVitali')?.value,
        altroAusiliMobilita: this.ausiliForm.get('altroAusiliMobilita')?.value,
        altroAusiliUrinari: this.ausiliForm.get('altroAusiliUrinari')?.value,
        ausiliFunzioniVitaliLista: this.filterCheckboxesToSave(this.ausiliFunzioniVitaliArray),
        ausiliMobilitaLista: this.filterCheckboxesToSave(this.ausiliMobilityArray),
        ausiliUrinariLista: this.filterCheckboxesToSave(this.ausiliGestioneArray),
        noteAusiliFunzioniVitali: (this.ausiliForm.get('noteAusiliFunzioniVitali')?.value),
        noteAusiliMobilita: (this.ausiliForm.get('noteAusiliMobilita')?.value),
        noteAusiliUrinari: (this.ausiliForm.get('noteAusiliUrinari')?.value),
        notePresidiPosturali: (this.ausiliForm.get('notePresidiPosturali')?.value),
        presidiPosturaliLista: this.filterCheckboxesToSave(this.presidiArray)
      });

      this.datiDimissione.setAusiliValido(this.ausiliForm.valid);
    })
  }

  filterCheckboxesToSave(checkboxes: FormArray) {
    return checkboxes.controls.filter(item => !!item.value.value).map((item) => ({
      idDizionario: item.get('key')?.value
    }))
  }

  populateFormArrayWithOptions(formArray: any, data: any[], altroInputControlName?: string): void {
    formArray.clear();
    data.forEach(item => {
      const group = this.fb.group({
        key: [item.idDizionario],
        label: [item.descrizione],
        value: [false]
      });

      formArray.push(group);

      // Aggiungi listener generale per triggerare la validazione
      group.get('value')?.valueChanges.subscribe(() => {
        formArray.updateValueAndValidity();
        formArray.markAsTouched();
        formArray.markAsDirty();
      });

      this.subscribeToNessunAusilioAndNessunoCheckbox(group, formArray, altroInputControlName);

      if (item.descrizione === 'ALTRO' && altroInputControlName) {
        const altroInput = this.ausiliForm.get(altroInputControlName);
        this.subscribeToAltroCheckbox(group, altroInput);
      }
    });

  }

  populateFormWithSavedData(savedData: SchedaAusiliDimissioneModel): void {
    this.ausiliForm.patchValue({
      nomeScheda: savedData.nomeScheda,
      idScheda: savedData.idScheda,
      altroAusiliMobilita: savedData.altroAusiliMobilita,
      noteAusiliMobilita: savedData.noteAusiliMobilita,
      altroAusiliUrinari: savedData.altroAusiliUrinari,
      noteAusiliUrinari: savedData.noteAusiliUrinari,
      altroAusiliFunzioniVitali: savedData.altroAusiliFunzioniVitali,
      noteAusiliFunzioniVitali: savedData.noteAusiliFunzioniVitali,
      notePresidiPosturali: savedData.notePresidiPosturali
    });

    if (savedData.altroAusiliMobilita) this.ausiliForm.get('altroAusiliMobilita')?.enable();
    if (savedData.altroAusiliUrinari) this.ausiliForm.get('altroAusiliUrinari')?.enable();
    if (savedData.altroAusiliFunzioniVitali) this.ausiliForm.get('altroAusiliFunzioniVitali')?.enable();

    const ausiliMobilitaArray = this.populateCheckboxes(savedData.ausiliMobilitaLista, this.ausiliMobility);
    this.ausiliForm.setControl('ausiliMobilitaLista', ausiliMobilitaArray);

    const ausiliUrinariArray = this.populateCheckboxes(savedData.ausiliUrinariLista, this.ausiliGestione);
    this.ausiliForm.setControl('ausiliUrinariLista', ausiliUrinariArray);

    const ausiliFunzioniVitaliArray = this.populateCheckboxes(savedData.ausiliFunzioniVitaliLista, this.ausiliFunzioniVitali);
    this.ausiliForm.setControl('ausiliFunzioniVitaliLista', ausiliFunzioniVitaliArray);

    const presidiArray = this.populateCheckboxes(savedData.presidiPosturaliLista, this.presidi);
    this.ausiliForm.setControl('presidiPosturaliLista', presidiArray);

    this.ausiliForm.updateValueAndValidity();


    if (this.repartoType !== TipoRicoveroEnum.ACUTI) {
      presidiArray.addValidators(this.atLeastOneSelectedValidator());
      ausiliFunzioniVitaliArray.addValidators(this.atLeastOneSelectedValidator());
      ausiliMobilitaArray.addValidators(this.atLeastOneSelectedValidator());
      ausiliUrinariArray.addValidators(this.atLeastOneSelectedValidator());
      // Forza la validazione iniziale di tutti gli array
      ausiliMobilitaArray.updateValueAndValidity();
      ausiliUrinariArray.updateValueAndValidity();
      ausiliFunzioniVitaliArray.updateValueAndValidity();
      presidiArray.updateValueAndValidity();
    }
  }

  populateCheckboxes(savedData: CheckBoxSempliceModel[], options: DizionarioModel[]): FormArray {
    const formArray: FormArray = this.fb.array([]);

    options.forEach(option => {
      const isChecked = savedData?.some(item => item.idDizionario === option.idDizionario);
      const group = this.fb.group({
        key: [option.idDizionario],
        label: [option.descrizione],
        value: [isChecked]
      });

      formArray.push(group);

      // Aggiungi listener generale per triggerare la validazione
      group.get('value')?.valueChanges.subscribe(() => {
        formArray.updateValueAndValidity();
        formArray.markAsTouched();
        formArray.markAsDirty();
      });
    });

    return formArray;
  }


  subscribeToNessunAusilioAndNessunoCheckbox(group: FormGroup, array: FormArray, altroControlName?: string) {
    const valueControl = group.get('value');
    if (!valueControl) return;

    valueControl.valueChanges.subscribe((isSelected: boolean) => {
      const currentLabel = group.get('label')?.value;
      const isNoneSelected = currentLabel === 'NESSUN AUSILIO' || currentLabel === 'NESSUNO';

      if (isNoneSelected && isSelected) {
        array.controls.forEach((ctrl: AbstractControl) => {
          const otherLabel = ctrl.get('label')?.value;

          if (otherLabel !== currentLabel) {
            ctrl.get('value')?.setValue(false); // let it emit
            if (otherLabel === 'ALTRO' && altroControlName) {
              const altroControl = this.ausiliForm.get(altroControlName);
              altroControl?.setValue('');
              altroControl?.disable();
            }
          }
        });
      }

      if (!isNoneSelected && isSelected) {
        const noneGroup = array.controls.find(ctrl => {
          const label = ctrl.get('label')?.value;
          return label === 'NESSUN AUSILIO' || label === 'NESSUNO';
        });

        noneGroup?.get('value')?.setValue(false);
      }

      // Aggiorna la validazione del FormArray per il validatore atLeastOneSelected
      array.updateValueAndValidity();
      array.markAsTouched();
      array.markAsDirty();
    });
  }

  subscribeToAltroCheckbox(group: FormGroup, altroInputControl: AbstractControl | null): void {
    group.get('value')?.valueChanges.subscribe((isChecked: boolean) => {
      if (!altroInputControl) return;

      if (isChecked) {
        altroInputControl.enable();
        altroInputControl.setValidators(Validators.required);
        altroInputControl.updateValueAndValidity();
      } else {
        altroInputControl.setValue(null);
        altroInputControl.disable();
        altroInputControl.clearValidators();
        altroInputControl.updateValueAndValidity();
      }

      // Trova il FormArray parent e aggiorna la sua validazione
      const parentArray = this.findParentFormArray(group);
      if (parentArray) {
        parentArray.updateValueAndValidity();
        parentArray.markAsTouched();
        parentArray.markAsDirty();
      }
    });
  }


  initForm() {
    this.ausiliForm = this.fb.group({
      nomeScheda: [null],
      idScheda: [null],
      ausiliMobilitaLista: this.fb.array([]),
      altroAusiliMobilita: [null],
      noteAusiliMobilita: [null],
      ausiliUrinariLista: this.fb.array([]),
      altroAusiliUrinari: [null],
      noteAusiliUrinari: [null],
      ausiliFunzioniVitaliLista: this.fb.array([]),
      altroAusiliFunzioniVitali: [null],
      noteAusiliFunzioniVitali: [null],
      presidiPosturaliLista: this.fb.array([]),
      notePresidiPosturali: [null]
    });
    this.ausiliForm.get('altroAusiliMobilita')?.disable();
    this.ausiliForm.get('altroAusiliUrinari')?.disable();
    this.ausiliForm.get('altroAusiliFunzioniVitali')?.disable();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes) {
      if (this.repartoType) {
        this.ausiliMobilitaRequired = this.repartoType !== TipoRicoveroEnum.ACUTI
        this.ausiliUrinariRequired = this.repartoType !== TipoRicoveroEnum.ACUTI
        this.ausiliFunzioniVitaliRequired = this.repartoType !== TipoRicoveroEnum.ACUTI
        this.presidiPosturaliRequired = this.repartoType !== TipoRicoveroEnum.ACUTI

        // Aggiungi sempre i validatori, ma la logica interna del validatore gestirà i reparti acuti
        this.ausiliForm.get('ausiliMobilitaLista')?.addValidators(this.atLeastOneSelectedValidator());
        this.ausiliForm.get('ausiliUrinariLista')?.addValidators(this.atLeastOneSelectedValidator());
        this.ausiliForm.get('ausiliFunzioniVitaliLista')?.addValidators(this.atLeastOneSelectedValidator());
        this.ausiliForm.get('presidiPosturaliLista')?.addValidators(this.atLeastOneSelectedValidator());

        // Forza la validazione iniziale
        this.ausiliForm.get('ausiliMobilitaLista')?.updateValueAndValidity();
        this.ausiliForm.get('ausiliUrinariLista')?.updateValueAndValidity();
        this.ausiliForm.get('ausiliFunzioniVitaliLista')?.updateValueAndValidity();
        this.ausiliForm.get('presidiPosturaliLista')?.updateValueAndValidity();
      }
    }
  }

  // Apre il popup per le note
  openPopupNote(controlName: string): void {
    const noteControl = this.ausiliForm.get(controlName);
    if (noteControl) {
      let configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != noteControl.value) {
          noteControl.setValue(res);
          noteControl.markAsDirty();
          noteControl.updateValueAndValidity();
        }
      });
    }
  }

  moveAltroToEnd(list: any[]): any[] {
    const itemsWithoutAltro = list.filter(item => item.label?.toUpperCase() !== 'ALTRO');
    const altroItem = list.find(item => item.label?.toUpperCase() === 'ALTRO');
    return altroItem ? [...itemsWithoutAltro, altroItem] : itemsWithoutAltro;
  }

  /**
   * Validatore che controlla che almeno uno degli elementi nella lista abbia value: true
   */
  atLeastOneSelectedValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      // Per i reparti acuti, la validazione non è obbligatoria
      if (this.repartoType === TipoRicoveroEnum.ACUTI) {
        return null;
      }

      if (!(control instanceof FormArray)) {
        return null;
      }

      const hasAtLeastOneSelected = control.controls.some(item => {
        const valueControl = item.get('value');
        return valueControl && valueControl.value === true;
      });

      if (!hasAtLeastOneSelected) {
        return {
          atLeastOneRequired: {
            message: 'Selezionare almeno un elemento dalla lista'
          }
        };
      }

      return null;
    };
  }

  /**
   * Trova il FormArray parent di un FormGroup
   */
  findParentFormArray(group: FormGroup): FormArray | null {
    const arrays = [
      this.ausiliForm.get('ausiliMobilitaLista') as FormArray,
      this.ausiliForm.get('ausiliUrinariLista') as FormArray,
      this.ausiliForm.get('ausiliFunzioniVitaliLista') as FormArray,
      this.ausiliForm.get('presidiPosturaliLista') as FormArray
    ];

    return arrays.find(array => array.controls.includes(group)) || null;
  }


  get formGroup(): FormGroup {
    return this.ausiliForm;
  }

  get ausiliMobilityArray(): FormArray {
    return this.ausiliForm.get('ausiliMobilitaLista') as FormArray;
  }

  get ausiliGestioneArray(): FormArray {
    return this.ausiliForm.get('ausiliUrinariLista') as FormArray;
  }

  get ausiliFunzioniVitaliArray(): FormArray {
    return this.ausiliForm.get('ausiliFunzioniVitaliLista') as FormArray;
  }

  get presidiArray(): FormArray {
    return this.ausiliForm.get('presidiPosturaliLista') as FormArray;
  }

  get checkboxAltroMobility(): FormGroup | null {
    return this.ausiliMobilityArray.controls.find(ctrl => ctrl.get('label')?.value === 'ALTRO') as FormGroup ?? null;
  }

  get checkboxAltroGestione(): FormGroup | null {
    return this.ausiliGestioneArray.controls.find(ctrl => ctrl.get('label')?.value === 'ALTRO') as FormGroup ?? null;
  }

  get checkboxAltroFunzioniVitali(): FormGroup | null {
    return this.ausiliFunzioniVitaliArray.controls.find(ctrl => ctrl.get('label')?.value === 'ALTRO') as FormGroup ?? null;
  }
}
