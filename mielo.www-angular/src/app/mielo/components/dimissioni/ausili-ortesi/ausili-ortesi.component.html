<form [formGroup]="ausiliForm" class="box-dati mt-4">
  <!-- AUSILI MOBILITÀ -->
  <div class="row mb-4">
    <div class="col-auto">
      <label class="field-label">
        Ausili Mobilità<span *ngIf="ausiliMobilitaRequired">*</span>
      </label>
      <button mat-button type="button" class="ml-3 p-0" (click)="openPopupNote('noteAusiliMobilita')">
        <svg class="icon mr-2 mb-1 icon-primary">
          <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
        </svg>
        <span class="pl-1 icon-primary">Note</span>
      </button>
    </div>

    <div formArrayName="ausiliMobilitaLista" class="row col-12">
      <ng-container *ngFor="let item of ausiliMobilityArray.controls; let i = index">
        <div *ngIf="item.get('label')?.value !== 'ALTRO'" class="col-6 col-md-4 row" [formGroupName]="i">
          <mat-checkbox color="primary" formControlName="value">
            {{ item.get('label')?.value | capitalizeFirst }}
          </mat-checkbox>
        </div>
      </ng-container>
    </div>

    @if (checkboxAltroMobility) {
    <div class="altro-checkbox-block row w-100 my-3" [formGroup]="checkboxAltroMobility">
      <div class="col-1">
        <mat-checkbox color="primary" formControlName="value">
          {{ checkboxAltroMobility.get('label')?.value | capitalizeFirst }}
        </mat-checkbox>
      </div>

      <div class="col-10 mb-3" [formGroup]="ausiliForm">        
        <mat-form-field appearance="outline" class="w-100">
          <input matInput formControlName="altroAusiliMobilita" maxlength="250"
                 [matTooltip]="ausiliForm.get('altroAusiliMobilita')?.value || ''"
            [placeholder]="datiDimissione.isRequired(ausiliForm.get('altroAusiliMobilita')) ? 'Inserisci*' : 'Inserisci'" />
          <mat-error *ngIf="ausiliForm.get('altroAusiliMobilita')?.hasError('required')">
            {{ ERROR_MESSAGE.REQUIRED }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    }
  </div>

  <!-- AUSILI GESTIONE URINARIA -->
  <div class="row mb-4">
    <div class="col-auto">
      <label class="field-label">
        Ausili gestione urinaria/intestinale<span *ngIf="ausiliUrinariRequired">*</span>
      </label>
      <button mat-button type="button" class="ml-3 p-0" (click)="openPopupNote('noteAusiliUrinari')">
        <svg class="icon mr-2 mb-1 icon-primary">
          <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
        </svg>
        <span class="pl-1 icon-primary">Note</span>
      </button>
    </div>
    <div formArrayName="ausiliUrinariLista" class="row col-12">
      <ng-container *ngFor="let item of ausiliGestioneArray.controls; let i = index">
        <div *ngIf="item.get('label')?.value !== 'ALTRO'" class="col-6 col-md-4 row" [formGroupName]="i">
          <mat-checkbox color="primary" formControlName="value">
            {{ item.get('label')?.value | capitalizeFirst }}
          </mat-checkbox>
        </div>
      </ng-container>
    </div>

    @if (checkboxAltroGestione) {
    <div class="altro-checkbox-block row w-100 my-3" [formGroup]="checkboxAltroGestione">
      <div class="col-1">
        <mat-checkbox color="primary" formControlName="value">
          {{ checkboxAltroGestione.get('label')?.value | capitalizeFirst }}
        </mat-checkbox>
      </div>

      <div class="col-10 mb-3" [formGroup]="ausiliForm">
        <mat-form-field appearance="outline" class="w-100">
          <input matInput
                 [matTooltip]="ausiliForm.get('altroAusiliUrinari')?.value || ''"
                 formControlName="altroAusiliUrinari" maxlength="250"
            [placeholder]="datiDimissione.isRequired(ausiliForm.get('altroAusiliUrinari')) ? 'Inserisci*' : 'Inserisci'" />
          <mat-error *ngIf="ausiliForm.get('altroAusiliUrinari')?.hasError('required')">
            {{ ERROR_MESSAGE.REQUIRED }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    }
  </div>

  <!-- AUSILI FUNZIONI VITALI -->
  <div class="row mb-4">
    <div class="col-auto">
      <label class="field-label">
        Ausili funzioni vitali<span *ngIf="ausiliFunzioniVitaliRequired">*</span>
      </label>
      <button mat-button type="button" class="ml-3 p-0" (click)="openPopupNote('noteAusiliFunzioniVitali')">
        <svg class="icon mr-2 mb-1 icon-primary">
          <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
        </svg>
        <span class="pl-1 icon-primary">Note</span>
      </button>
    </div>

    <div formArrayName="ausiliFunzioniVitaliLista" class="row col-12">
      <ng-container *ngFor="let item of ausiliFunzioniVitaliArray.controls; let i = index">
        <div *ngIf="item.get('label')?.value !== 'ALTRO'" class="col-6 col-md-4 row" [formGroupName]="i">
          <mat-checkbox color="primary" formControlName="value">
            {{ item.get('label')?.value | capitalizeFirst }}
          </mat-checkbox>
        </div>
      </ng-container>
    </div>

    @if (checkboxAltroFunzioniVitali) {
    <div class="altro-checkbox-block row w-100 my-3" [formGroup]="checkboxAltroFunzioniVitali">
      <div class="col-1">
        <mat-checkbox color="primary" formControlName="value">
          {{ checkboxAltroFunzioniVitali.get('label')?.value | capitalizeFirst }}
        </mat-checkbox>
      </div>

      <div class="col-10 mb-3" [formGroup]="ausiliForm">
        <mat-form-field appearance="outline" class="w-100">
          <input matInput
                 [matTooltip]="ausiliForm.get('altroAusiliFunzioniVitali')?.value || ''"
                 formControlName="altroAusiliFunzioniVitali" maxlength="250"
            [placeholder]="datiDimissione.isRequired(ausiliForm.get('altroAusiliFunzioniVitali')) ? 'Inserisci*' : 'Inserisci'" />
          <mat-error *ngIf="ausiliForm.get('altroAusiliFunzioniVitali')?.hasError('required')">
            {{ ERROR_MESSAGE.REQUIRED }}
          </mat-error>
        </mat-form-field>
      </div>
    </div>
    }
  </div>

  <!-- PRESIDI POSTURALI -->
  <div class="row mb-4">
    <div class="col-auto">
      <label class="field-label">
        Presidi antidecubito e posturali<span *ngIf="presidiPosturaliRequired">*</span>
      </label>
      <button mat-button type="button" class="ml-3 p-0" (click)="openPopupNote('notePresidiPosturali')">
        <svg class="icon mr-2 mb-1 icon-primary">
          <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
        </svg>
        <span class="pl-1 icon-primary">Note</span>
      </button>
    </div>

    <div formArrayName="presidiPosturaliLista" class="row col-12">
      <ng-container *ngFor="let item of presidiArray.controls; let i = index">
        <div class="col-6 col-md-4" [formGroupName]="i">
          <mat-checkbox color="primary" formControlName="value">
            {{ item.get('label')?.value | capitalizeFirst }}
          </mat-checkbox>
        </div>
      </ng-container>
    </div>
  </div>
</form>