<div class="container-data pt-4">

  <h3 class="ml-3 mb-0">Dimissione</h3>
  <!-- Dati generali dimissione rientro non censito -->
  <mat-accordion *ngIf="isRientro" class="m-2">
    <mat-expansion-panel expanded>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">DATI GENERALI DI DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-dati-generali-dimissione-rientro [readOnly]="datiGenRientroReadOnly"></app-dati-generali-dimissione-rientro>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Interventi Effettuati - Rientro non censito -->
  <mat-accordion *ngIf="isRientro" class="m-2">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">INTERVENTI EFFETTUATI</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-interventi-effettuati-rientro [readOnly]="interventiEffettuatiReadOnly"></app-interventi-effettuati-rientro>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Dati generali dimissione -->
  <mat-accordion *ngIf="!isRientro" class="m-2">
    <mat-expansion-panel expanded>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">DATI GENERALI DI DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-dati-generali-dimissioni
          [readOnly]="datiGenDimissReadOnly"
          [idReparto]="idReparto"/>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Valutazione alla dimissione-->
  <mat-accordion *ngIf="!isRientro" class="m-2">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">VALUTAZIONE ALLA DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-valutazione
                [readOnly]="valutazioneReadOnly"
                [isDimissione]="true">
        </app-valutazione>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Necessità assistenziale in dimissione -->
  <mat-accordion *ngIf="!isRientro" class="m-2">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">NECESSITÀ ASSISTENZIALI IN DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-necessita-assistenziali
                [readOnly]="necessitaReadOnly"
                [isDimissione]="true"
                [repartoType]="repartoType"></app-necessita-assistenziali>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Quadro neurologico in dimissione -->
  <mat-accordion *ngIf="!isRientro" class="m-2">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">QUADRO NEUROLOGICO DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-quadro-neurologico
                [readOnly]="quadroNeurologicoReadOnly"
                [isDimissione]="true" [idReparto]="idReparto">
        </app-quadro-neurologico>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- COMPLICANZE -->
  <mat-accordion *ngIf="!isRientro" class="m-2">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">COMPLICANZE PRESENTI ALLA DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-complicanze [isDimissione]="true"
                         [readOnly]="complicanzeReadOnly"
                         [repartoType]="repartoType" />
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Quadro funzionale alla dimissione -->
  <mat-accordion *ngIf="!isRientro && datiDimissione?.schedaQuadroFunzionaleDimissione" class="m-2">
    <mat-expansion-panel (opened)="onOpened('quadroFunzionale')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">QUADRO FUNZIONALE ALLA DIMISSIONE E TRATTAMENTI SPECIALISTICI</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedQuadroFunzionale">
        <app-quadro-funzionale-dimissione [repartoType]="repartoType" [readOnly]="quadroFunzionaleReadOnly" />
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Ausili-Ortesi alla dimissione -->
  <mat-accordion *ngIf="!isRientro" class="m-2">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">AUSILI - ORTESI PRESCRITTI ALLA DIMISSIONE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container>
        <app-ausili-ortesi [readOnly]="ausiliOrtesiReadOnly" [repartoType]="idReparto">
        </app-ausili-ortesi>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Aspetti socioassistenziali e di continuità -->
  <mat-accordion *ngIf="!isRientro && datiDimissione?.schedaAspettiSocioassistenzialiDimissione" class="m-2">
    <mat-expansion-panel (opened)="onOpened('aspettiSocioassistenziali')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">ASPETTI SOCIOASSISTENZIALI E DI CONTINUITÀ</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedAspettiSocioassistenziali">
        <app-aspetti-socioassistenziali [repartoType]="repartoType" [readOnly]="aspettiSocioassistenzialiReadOnly" />
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

</div>