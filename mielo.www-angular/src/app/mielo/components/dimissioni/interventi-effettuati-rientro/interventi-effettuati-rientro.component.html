<form [formGroup]="form">
    <div class="row mb-4">
        <!-- Specificare interventi -->
        <div class="col-12">
            <div class="d-flex align-items-center">
                <mat-label class="field-label mr-2">Specificare interventi</mat-label>
                <button mat-button type="button" class="p-0 note-button"
                    (mousedown)="openNotePopup('noteInterventiChirurgici')">
                    <svg class="icon icon-primary ml-3 mr-1">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span>Note</span>
                </button>
            </div>
            <!-- Checkboxes specifici -->
            <div class="row">
                <ng-container *ngFor="let option of optionsInterventiSpecifici">
                    <div class="col-6" *ngIf="option.descrizione?.toUpperCase() !== 'ALTRO'">
                        <mat-checkbox [checked]="isInterventoSpecificoChecked(option)" [disabled]="readOnly"
                            (change)="onInterventiSpecificiChange(option, $event.checked)">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-checkbox>
                    </div>
                </ng-container>
            </div>

            <!-- Checkbox 'Altro' con input -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="d-flex align-items-center altro-container">
                        <mat-checkbox [checked]="isInterventoSpecificoChecked(altroInterventiChirurgiciOption)" [disabled]="readOnly"
                            (change)="onInterventiSpecificiChange(altroInterventiChirurgiciOption, $event.checked)">
                            {{ altroInterventiChirurgiciOption.descrizione | capitalizeFirst }}
                        </mat-checkbox>
                        <mat-form-field class="ml-3 w-100">
                            <input matInput formControlName="altroInterventiChirurgici"
                                [placeholder]="isRequired('altroInterventiChirurgici') ? 'Inserisci*' : 'Inserisci'" />
                            <mat-error *ngIf="form.get('altroInterventiChirurgici')?.hasError('required')">
                                {{ ERROR_MESSAGE.REQUIRED }}
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <hr>

    <div class="row mb-4">
        <!-- Interventi respiratori -->
        <div class="col-12">
            <div class="d-flex align-items-center">
                <mat-label class="field-label mr-2">Interventi respiratori</mat-label>
                <button mat-button type="button" class="p-0 note-button"
                    (mousedown)="openNotePopup('noteInterventiRespiratori')">
                    <svg class="icon icon-primary ml-3 mr-1">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span>Note</span>
                </button>
            </div>
            <!-- Checkboxes respiratori -->
            <div class="row">
                <ng-container *ngFor="let option of optionsInterventiRespiratori">
                    <div class="col-6" *ngIf="option.descrizione?.toUpperCase() !== 'ALTRO'">
                        <mat-checkbox [checked]="isInterventoRespiratorioChecked(option)" [disabled]="readOnly"
                            (change)="onInterventiRespiratoriChange(option, $event.checked)">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-checkbox>
                    </div>
                </ng-container>
            </div>

            <!-- Checkbox 'Altro' con input -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="d-flex align-items-center altro-container">
                        <mat-checkbox [checked]="isInterventoRespiratorioChecked(altroInterventiRespiratoriOption)" [disabled]="readOnly"
                            (change)="onInterventiRespiratoriChange(altroInterventiRespiratoriOption, $event.checked)">
                            {{ altroInterventiRespiratoriOption.descrizione | capitalizeFirst }}
                        </mat-checkbox>
                        <mat-form-field class="ml-3 w-100">
                            <input matInput formControlName="altroInterventiRespiratori"
                                [placeholder]="isRequired('altroInterventiRespiratori') ? 'Inserisci*' : 'Inserisci'" />
                            <mat-error *ngIf="form.get('altroInterventiRespiratori')?.hasError('required')">
                                {{ ERROR_MESSAGE.REQUIRED }}
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>