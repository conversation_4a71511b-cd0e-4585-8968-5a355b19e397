.note-button {
    color: #256029;
    font-weight: 600;

    svg {
        width: 1.2em;
        height: 1.2em;
    }
}

.field-label {
    font-weight: 600 !important;
    font-size: 1rem;
}

.altro-container {
    gap: 2rem;

    .altro-input {
        height: 40px;
        padding: 0 16px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #fff;
        font-size: 16px;
        min-width: 300px;
        max-width: 400px;
        width: 100%;

        &:disabled {
            background-color: #f5f5f5;
            border-color: #ddd;
        }
    }
}

::ng-deep {
    .mat-checkbox-layout {
        align-items: flex-start;
    }

    .mat-checkbox-checked .mat-checkbox-background,
    .mat-checkbox-indeterminate .mat-checkbox-background {
        background-color: #2A7A38 !important;
    }

    .mat-checkbox-checked .mat-checkbox-frame {
        border-color: #2A7A38 !important;
    }

    .mat-checkbox-checked .mat-checkbox-checkmark-path {
        stroke: #fff !important;
    }

    .mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,
    .mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background {
        border-color: #2A7A38 !important;
        background-color: #2A7A38 !important;
    }
}