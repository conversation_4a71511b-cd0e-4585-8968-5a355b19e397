import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Subscription, distinctUntilChanged, fork<PERSON>oin, tap } from 'rxjs';
import { MaterialModule } from '../../../../core/material.module';
import { ERROR_MESSAGE } from '../../../../shared/enums/enum';
import { DizionarioModel } from '../../../../shared/interfaces/scheda-ricovero.interface';
import { CapitalizePipe } from '../../../../shared/pipes/capitalize.pipe';
import { DecoderService } from '../../../../shared/services/decoder.service';
import { ModalService } from '../../../../shared/services/modal.service';
import { DatiDimissioneRientroService } from '../../../services/dati-dimissione-rientro.service';
import { SchedaInterventiEffettuatiDimissioniModel } from '../../../../shared/interfaces/dati-dimissione.interface';

@Component({
    selector: 'app-interventi-effettuati-rientro',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, MaterialModule, CapitalizePipe],
    templateUrl: './interventi-effettuati-rientro.component.html',
    styleUrl: './interventi-effettuati-rientro.component.scss'
})
export class InterventiEffettuatiRientroComponent implements OnInit, OnDestroy {
    @Input() readOnly: boolean = false;

    form!: FormGroup;
    subscriptions: Subscription[] = [];
    ERROR_MESSAGE = ERROR_MESSAGE;

    optionsInterventiSpecifici: DizionarioModel[] = [];
    optionsInterventiRespiratori: DizionarioModel[] = [];

    get altroInterventiChirurgiciControl(): AbstractControl | null {
        return this.form.get('altroInterventiChirurgici');
    }

    get altroInterventiRespiratoriControl(): AbstractControl | null {
        return this.form.get('altroInterventiRespiratori');
    }

    constructor(
        private fb: FormBuilder,
        private datiDimissioneRientroService: DatiDimissioneRientroService,
        private modalService: ModalService,
        private decoderService: DecoderService
    ) { }

    ngOnInit(): void {
        this.initForm();
        this.loadOptions();
        this.subscribeToFormChanges();
    }

    private initForm(): void {
        this.form = this.fb.group({
            interventiChirurgiciDimissioniLista: [],
            altroInterventiChirurgici: [{ value: null, disabled: true }, [Validators.maxLength(250)]],
            noteInterventiChirurgici: [{ value: null, disabled: false }, [Validators.maxLength(3000)]],
            interventiRespiratoriLista: [],
            altroInterventiRespiratori: [{ value: null, disabled: true }, [Validators.maxLength(250)]],
            noteInterventiRespiratori: [{ value: null, disabled: false }, [Validators.maxLength(3000)]]
        });
    }

    private loadOptions(): void {
        forkJoin({
            interventiSpecifici: this.decoderService.getDcodInterventiChirurgiciRientro(),
            interventiRespiratori: this.decoderService.getDcodInterventiRespiratoriRientro()
        }).pipe(
            tap(options => {
                this.optionsInterventiSpecifici = options.interventiSpecifici;
                this.optionsInterventiRespiratori = options.interventiRespiratori;

                const dati = this.datiDimissioneRientroService.getInterventiEffettuatiRientroValue();
                if (dati) {
                    this.patchForm(dati);
                } else {
                    if (this.readOnly) {
                        this.form.disable({ emitEvent: false });
                    }
                }

                if (this.readOnly && !dati) {
                    this.form.disable({ emitEvent: false });
                }
            })
        ).subscribe();
    }

    private subscribeToFormChanges(): void {
        if (!this.readOnly) {
            const sub = this.form.valueChanges.pipe(
                distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b))
            ).subscribe(() => {
                const formValue = this.form.getRawValue();

                const dataToSave = formValue;

                this.datiDimissioneRientroService.setInterventiEffettuatiRientro(dataToSave);
                this.datiDimissioneRientroService.setInterventiEffettuatiRientroValido(this.form.valid);
            });
            this.subscriptions.push(sub);

            this.form.get('interventiChirurgiciDimissioniLista')?.valueChanges.subscribe(() => this.handleAltroSpecificiChange());
            this.form.get('interventiRespiratoriLista')?.valueChanges.subscribe(() => this.handleAltroRespiratoriChange());
        }
    }

    private patchForm(dati: SchedaInterventiEffettuatiDimissioniModel): void {

        const patchedInterventiSpecificiLista = dati.interventiChirurgiciDimissioniLista
            ?.map(savedItem => this.optionsInterventiSpecifici.find(option => option.idDizionario === savedItem.idDizionario))
            .filter((option): option is DizionarioModel => !!option) || [];

        const patchedInterventiRespiratoriLista = dati.interventiRespiratoriLista
            ?.map(savedItem => this.optionsInterventiRespiratori.find(option => option.idDizionario === savedItem.idDizionario))
            .filter((option): option is DizionarioModel => !!option) || [];

        this.form.patchValue({
            interventiChirurgiciDimissioniLista: patchedInterventiSpecificiLista,
            altroInterventiChirurgici: dati.altroInterventiChirurgici,
            noteInterventiChirurgici: dati.noteInterventiChirurgici,
            interventiRespiratoriLista: patchedInterventiRespiratoriLista,
            altroInterventiRespiratori: dati.altroInterventiRespiratori,
            noteInterventiRespiratori: dati.noteInterventiRespiratori,
        }, { emitEvent: false });

        this.handleAltroSpecificiChange();
        this.handleAltroRespiratoriChange();
        this.form.updateValueAndValidity();

        if (this.readOnly) {
            this.form.disable({ emitEvent: false });
        }
    }

    handleAltroSpecificiChange(): void {
        const altroControl = this.form.get('altroInterventiChirurgici');
        const interventiChirurgiciDimissioniLista = this.form.get('interventiChirurgiciDimissioniLista')?.value as DizionarioModel[] || [];
        const altroIdSpecifici = this.optionsInterventiSpecifici.find(opt => opt.descrizione?.toUpperCase() === 'ALTRO')?.idDizionario;

        const isAltroSelected = interventiChirurgiciDimissioniLista.some(item => altroIdSpecifici !== undefined && item.idDizionario === altroIdSpecifici);

        this.toggleAltroControl(altroControl, isAltroSelected);
    }

    handleAltroRespiratoriChange(): void {
        const altroControl = this.form.get('altroInterventiRespiratori');
        const interventiRespiratoriLista = this.form.get('interventiRespiratoriLista')?.value as DizionarioModel[] || [];
        const altroIdRespiratori = this.optionsInterventiRespiratori.find(opt => opt.descrizione?.toUpperCase() === 'ALTRO')?.idDizionario;

        const isAltroSelected = interventiRespiratoriLista.some(item => altroIdRespiratori !== undefined && item.idDizionario === altroIdRespiratori);

        this.toggleAltroControl(altroControl, isAltroSelected);
    }

    private toggleAltroControl(control: AbstractControl | null, enable: boolean): void {
        if (!control) return;
        if (enable) {
            control.enable({ emitEvent: false });
            control.setValidators([Validators.required, Validators.maxLength(250)]);
        } else {
            control.setValue(null, { emitEvent: false });
            control.disable({ emitEvent: false });
            control.clearValidators();
        }
        control.updateValueAndValidity({ emitEvent: false });
    }

    openNotePopup(controlName: string): void {
        const noteControl = this.form.get(controlName);
        if (noteControl) {
            const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);
            this.modalService.note(configNote)?.subscribe((res) => {
                if (res !== noteControl.value) {
                    noteControl.setValue(res);
                    noteControl.markAsDirty();
                    noteControl.updateValueAndValidity();
                }
            });
        }
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(s => s.unsubscribe());
    }

    onInterventiSpecificiChange(option: DizionarioModel, checked: boolean): void {
        this.handleCheckboxChange('interventiChirurgiciDimissioniLista', option, checked);
    }

    onInterventiRespiratoriChange(option: DizionarioModel, checked: boolean): void {
        this.handleCheckboxChange('interventiRespiratoriLista', option, checked);
    }

    private handleCheckboxChange(controlName: string, option: DizionarioModel, checked: boolean): void {
        const control = this.form.get(controlName);
        if (!control) return;

        const currentValues = control.value as DizionarioModel[] || [];
        let updatedValues: DizionarioModel[];

        if (checked) {
            if (!currentValues.some(v => v.idDizionario === option.idDizionario)) {
                updatedValues = [...currentValues, option];
            } else {
                updatedValues = currentValues;
            }
        } else {
            updatedValues = currentValues.filter(v => v.idDizionario !== option.idDizionario);
        }

        updatedValues.sort((a, b) => a.idDizionario - b.idDizionario);

        control.setValue(updatedValues);
        control.markAsDirty();
        control.updateValueAndValidity();
    }

    isInterventoSpecificoChecked(option: DizionarioModel): boolean {
        return this.isCheckboxChecked('interventiChirurgiciDimissioniLista', option);
    }

    isInterventoRespiratorioChecked(option: DizionarioModel): boolean {
        return this.isCheckboxChecked('interventiRespiratoriLista', option);
    }

    private isCheckboxChecked(controlName: string, option: DizionarioModel): boolean {
        const control = this.form.get(controlName);
        if (!control || !control.value) return false;
        return (control.value as DizionarioModel[]).some(v => v.idDizionario === option.idDizionario);
    }

    get interventiSpecificiRows(): DizionarioModel[][] {
        const rows: DizionarioModel[][] = [];
        const options = this.optionsInterventiSpecifici.filter(opt => opt.descrizione?.toUpperCase() !== 'ALTRO');
        for (let i = 0; i < options.length; i += 2) {
            rows.push(options.slice(i, i + 2));
        }
        return rows;
    }

    get interventiRespiratoriRows(): DizionarioModel[][] {
        const rows: DizionarioModel[][] = [];
        const options = this.optionsInterventiRespiratori.filter(opt => opt.descrizione?.toUpperCase() !== 'ALTRO');
        for (let i = 0; i < options.length; i += 2) {
            rows.push(options.slice(i, i + 2));
        }
        return rows;
    }

    get altroInterventiChirurgiciOption(): DizionarioModel {
        return this.optionsInterventiSpecifici?.find(opt => opt?.descrizione?.toUpperCase() === 'ALTRO') ||
            { idDizionario: 99, categoria: '', descrizione: 'Altro' };
    }

    get altroInterventiRespiratoriOption(): DizionarioModel {
        return this.optionsInterventiRespiratori?.find(opt => opt?.descrizione?.toUpperCase() === 'ALTRO') ||
            { idDizionario: 99, categoria: '', descrizione: 'Altro' };
    }

    isAltroInterventiSpecificiSelected(): boolean {
        return (this.form.get('interventiChirurgiciDimissioniLista')?.value as DizionarioModel[]).some(o => o.descrizione?.toUpperCase() === 'ALTRO');
    }

    isAltroInterventiRespiratoriSelected(): boolean {
        return (this.form.get('interventiRespiratoriLista')?.value as DizionarioModel[]).some(o => o.descrizione?.toUpperCase() === 'ALTRO');
    }

    /* Verifica validatore required */
    isRequired(controlName: string): boolean {
        const control = this.form.get(controlName);
        return this.datiDimissioneRientroService.isRequired(control);
    }
} 