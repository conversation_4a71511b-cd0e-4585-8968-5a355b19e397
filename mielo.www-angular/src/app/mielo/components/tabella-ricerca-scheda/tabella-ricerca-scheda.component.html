<div
  style="overflow-x: auto"
  *ngIf="dataSource && dataSource.length !== 0 ; else nessunRisultato">
  <table mat-table [dataSource]="dataSource | paginate: {
    id: 'server',
    itemsPerPage: ricercaSchedaService.itemsPerPage,
    currentPage: ricercaSchedaService.currentPage + 1,
    totalItems: totalItems
  }">
    <ng-container matColumnDef="idPaziente">
      <th mat-header-cell *matHeaderCellDef> ID Paziente </th>
      <td mat-cell *matCellDef="let element"> {{element.idPaziente || '/'}} </td>
    </ng-container>

    <ng-container matColumnDef="idNosologico">
      <th mat-header-cell *matHeaderCellDef> ID Nosologico </th>
      <td mat-cell *matCellDef="let element"> {{element.idNosologico || '/'}} </td>
    </ng-container>

    <ng-container matColumnDef="nome">
      <th mat-header-cell *matHeaderCellDef> Nome </th>
      <td mat-cell *matCellDef="let element"> {{element.nome || '/' | capitalizeFirst}} </td>
    </ng-container>

    <ng-container matColumnDef="cognome">
      <th mat-header-cell *matHeaderCellDef> Cognome </th>
      <td mat-cell *matCellDef="let element"> {{element.cognome || '/' | capitalizeFirst}} </td>
    </ng-container>

    <ng-container matColumnDef="codiceIdentificativo">
      <th mat-header-cell *matHeaderCellDef> Codice Identificativo </th>
      <td mat-cell *matCellDef="let element"> {{element.codiceIdentificativo || '/'}} </td>
    </ng-container>

    <ng-container matColumnDef="lesioneMielica">
      <th mat-header-cell *matHeaderCellDef> Lesione Mielica </th>
      <td mat-cell *matCellDef="let element"> {{element.descLesione || '/'}} </td>
    </ng-container>

    <ng-container matColumnDef="medicoCompilatore">
      <th mat-header-cell *matHeaderCellDef> Medico Compilatore </th>
      <td mat-cell *matCellDef="let element"> {{element.medicoCompilatore || '/'}} </td>
    </ng-container>

    <ng-container matColumnDef="statoEvento">
      <th mat-header-cell *matHeaderCellDef> Stato Evento </th>
      <td mat-cell *matCellDef="let element">
        <span class="d-flex align-items-center">
          {{element.descStato || '/'}}
          <mat-icon *ngIf="!element.ultimoEvento" class="ml-2 stato-evento-icon" matTooltip="Sono presenti eventi successivi">
            error_outline
          </mat-icon>
        </span>
      </td>
    </ng-container>

    <ng-container matColumnDef="azioni">
      <th mat-header-cell *matHeaderCellDef> Azioni </th>
      <td mat-cell *matCellDef="let element">
        <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="menu action" class="ml-action">
          <mat-icon>more_vert</mat-icon>
        </button>

        <mat-menu #menu="matMenu">
          <button mat-menu-item
                  *ngIf="newEventVisible"
                  (click)="goToNewEventoPage(element)"
                  [disabled]="isEventoButtonDisabled(element)"
          >
            <svg class="icon mr-2 mb-1">
              <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-plus-circle"></use>
            </svg>
            <span class="menu-item">Nuovo evento</span>
          </button>
          <button mat-menu-item
            (click)="navigaUltimoEvento(element)">
            <svg class="icon mr-2 mb-1">
              <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-inbox"></use>
            </svg>
            <span class="menu-item">Visualizza evento</span>
          </button>
          <button *ngIf="operatore.idRuolo !== catOperatore.PS" mat-menu-item
                  (click)="navigaEventiAssociati(element)"
                  [disabled]="element.numeroEventi == 1">
            <mat-icon svgIcon="it-inbox" class="inbox"
              [matBadge]="element.numeroEventi !== 0? element.numeroEventi : null" matBadgePosition="below before"
              aria-hidden="false" [attr.aria-label]="'Eventi associati: ' + element.numeroEventi">
            </mat-icon>
            <span class="menu-item">Eventi associati</span>
          </button>
        </mat-menu>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row [id]="row.idScheda" *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
</div>

<div class="d-flex align-items-center mt-3 custom-pagination" [ngClass]="showEstrazioniBtn ? 'justify-content-between' : 'justify-content-end'">
  <button mat-stroked-button (click)="navigateToEstrazioni()" class="btn-secondary btn-estrazioni"
    *ngIf="showEstrazioniBtn">
    Estrazioni
  </button>
  <div *ngIf="dataSource && dataSource.length !== 0 && ricercaSchedaService.resultPaginationSchede?.totalPages > 1">
    <pagination-controls (pageChange)="pageChanged($event)" [directionLinks]="true" previousLabel="" nextLabel=""
      [responsive]="true" [maxSize]="5" id="server">
    </pagination-controls>
  </div>
</div>

<ng-template #nessunRisultato>
  <div class="empty-box">
    <div class="mt-5">
      <img src="assets/images/mielo/empty.png" alt="nessun risultato">
    </div>
    <div class="mt-4">
      <h5>Nessun risultato di ricerca presente</h5>
    </div>
  </div>
</ng-template>
