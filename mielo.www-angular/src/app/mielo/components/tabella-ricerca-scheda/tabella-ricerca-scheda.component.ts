import { CommonModule } from "@angular/common";
import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatBadgeModule } from "@angular/material/badge";
import { MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer } from "@angular/platform-browser";
import { Router } from '@angular/router';
import { NgxPaginationModule } from "ngx-pagination";
import { Subject, takeUntil } from 'rxjs';
import { InfoOperatoreDTO } from "../../../core/interfaces/info-operatore.interface";
import { MaterialModule } from "../../../core/material.module";
import { OperatoreService } from "../../../core/services/operatore.service";
import { SchedaPaziente } from "../../../models/scheda-paziente.model";
import { EDescrizioneEvento, IdOperatoreEnum, StatoEventoEnum } from "../../../shared/enums/enum";
import { CapitalizePipe } from "../../../shared/pipes/capitalize.pipe";
import { TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO, TIPO_EVENTO_RIENTRO_CENSITO } from "../../../shared/utils/const";
import { RicercaSchedaPazienteService } from "../../services/ricerca-scheda-paziente.service";
import { SchedaRicoveroService } from '../../services/scheda-ricovero.service';

const IT_INBOX = `<svg viewBox="0 0 24 24" id="it-inbox" xmlns="http://www.w3.org/2000/svg"><path d="M4 2v14h3.7l1 2h6.6l1-2H20V2H4zm15 13h-3.3l-1 2H9.3l-1-2H5V3h14v12z"/><path d="M19 17h1v5H4v-5h1v4h14zM7 5h10v1H7zM7 8h10v1H7zM7 11h10v1H7z"/><g><path fill="none" d="M0 0h24v24H0z"/></g></svg>`;

@Component({
  selector: 'app-tabella-ricerca-scheda',
  standalone: true,
  imports: [MaterialModule, CommonModule, NgxPaginationModule, MatBadgeModule, CapitalizePipe],
  templateUrl: './tabella-ricerca-scheda.component.html',
  styleUrl: './tabella-ricerca-scheda.component.scss'
})
export class TabellaRicercaSchedaComponent implements OnInit, OnDestroy {
  operatore: InfoOperatoreDTO;
  catOperatore = IdOperatoreEnum;

  displayedColumns: string[] = ['idPaziente', 'idNosologico', 'nome', 'cognome', 'codiceIdentificativo', 'lesioneMielica', 'medicoCompilatore', 'statoEvento', 'azioni'];

  @Input() dataSource: SchedaPaziente[] = [];

  // Informazioni di paginazione
  totalItems: number = 0;
  currentPage: number = 0;

  private destroy$ = new Subject<void>();
  private isChangingPage = false;
  statoEventoEnum = StatoEventoEnum;

  showEstrazioniBtn = false;

  newEventVisible: boolean = false;

  constructor(
    public ricercaSchedaService: RicercaSchedaPazienteService,
    private router: Router,
    private schedaRicoveroService: SchedaRicoveroService,
    private operatoreService: OperatoreService,
    iconRegistry: MatIconRegistry,
    sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef,
  ) {
    iconRegistry.addSvgIconLiteral('it-inbox', sanitizer.bypassSecurityTrustHtml(IT_INBOX));
    const idRuolo = this.operatoreService.getOperatore().idRuolo;
    this.showEstrazioniBtn = idRuolo !== IdOperatoreEnum.PS;
    this.newEventVisible = idRuolo === IdOperatoreEnum.OSP;
  }

  ngOnInit() {
    this.schedaRicoveroService.schedaCreation$.next(null)
    this.schedaRicoveroService.idEditPatientSubject.next(null);
    this.schedaRicoveroService.idSchedaSelectedSubject.next(undefined)

    this.operatore = this.operatoreService.getOperatore();
    if (this.operatore.idRuolo === IdOperatoreEnum.PS) {
      this.displayedColumns = this.displayedColumns.filter(col => col !== 'azioni');
    }

    this.resetPaginazione();
    this.ricercaSchedaService.currentPage = 0;

    // Osserva i risultati dal servizio per aggiornare la tabella
    this.ricercaSchedaService.resultSchedePaziente
      .pipe(takeUntil(this.destroy$))
      .subscribe(schede => {
        if (schede && schede.length > 0) {
          this.dataSource = schede;
        }
      });

    // Osserva lo stato della paginazione
    this.ricercaSchedaService.paginationStatus$
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => {
        if (status) {
          this.totalItems = status.totalElements;
          if (!this.isChangingPage) {
            this.currentPage = status.number;
            this.ricercaSchedaService.currentPage = this.currentPage;
          }
          this.isChangingPage = false;
        }
      });

    // Quando si resetta la paginazione
    this.ricercaSchedaService.paginazioneResettata
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resetPaginazione();
      });

    if (this.dataSource && this.dataSource.length > 0) {
      this.visualizzaSchede(0);
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  navigaUltimoEvento(element: SchedaPaziente) {
    const sameStrutturaOperatore = this.operatore.codLivello2Operatore === element.strutturaDestinazione?.codLivello2 ||
                                   !element.strutturaDestinazione?.codLivello2;
    this.schedaRicoveroService.setHasSameStrutturaAsOperatore(sameStrutturaOperatore);
    this.schedaRicoveroService.setIsLastEvent(!!element.ultimoEvento);
    this.schedaRicoveroService.idSchedaSelectedSubject.next(element.idScheda);
    this.schedaRicoveroService.statusSchedaSelectedSubject.next(element.descStato === EDescrizioneEvento["IN LAVORAZIONE"]);
    this.schedaRicoveroService.isRientroSchedaSelectedSubject.next(element.isRientro);
    this.router.navigate([`/scheda-ricovero/details/ricerca-scheda-paziente`]);
  }

  navigaEventiAssociati(element: SchedaPaziente) {
    if (element.idScheda) {
      const sameStrutturaOperatore = this.operatore.codLivello2Operatore === element.strutturaDestinazione?.codLivello2 ||
        !element.strutturaDestinazione?.codLivello2;
      this.schedaRicoveroService.setHasSameStrutturaAsOperatore(sameStrutturaOperatore);
      this.schedaRicoveroService.idSchedaSelectedSubject.next(element.idScheda);
      this.schedaRicoveroService.getSchedaById(element.idScheda)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (schedaData) => {
            this.schedaRicoveroService.setSchedaCorrente(schedaData);
            const datiPaziente = {
              ...schedaData,
              dataNascitaPaziente: schedaData.dataNascitaPaziente,
              datiSocioEconomiciMancanti: schedaData.datiSocioEconomiciMancanti,
            };

            this.router.navigate([`/eventi-associati/${element.idScheda}`], {
              state: { datiPaziente }
            });
          },
          error: (error) => {
            console.error('Errore nel recupero dei dati della scheda:', error);
            this.router.navigate(['/scheda-ricovero']);
          }
        });
    }
  }

  pageChanged($event: number) {
    this.isChangingPage = true;

    const pageNumber = $event - 1;
    this.currentPage = pageNumber;
    this.ricercaSchedaService.currentPage = pageNumber;
    this.visualizzaSchede(pageNumber);
  }

  visualizzaSchede(pageNumber: number) {
    const body = this.ricercaSchedaService.buildBodyVisualizzaSchede(pageNumber);
    this.ricercaSchedaService.getVisualizzaSchedaPaziente(body)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        error: (err) => {
          console.error('Errore durante il caricamento dei dati:', err);
          this.isChangingPage = false;
        }
      });
  }

  resetPaginazione() {
    this.isChangingPage = false;
    this.currentPage = 0;
    this.ricercaSchedaService.currentPage = 0;

    setTimeout(() => {
      this.cdr.detectChanges();
    });
  }

  goToNewEventoPage(element: SchedaPaziente) {
    this.schedaRicoveroService.statusSchedaSelectedSubject.next(true);
    this.schedaRicoveroService.setRientroSchedaSelectedSubject(element.isRientro)
    this.router.navigate(['/scheda-ricovero/nuovo-evento'], {
      state: {
        from: 'nuovo-evento',
        inputScheda: {
          idPaziente: element.idPaziente,
          idScheda: element.idScheda,
          // if event is rientro I set a default unita operativa 
          // because it does not matter for the BE as long as it is not null
          idUnitaOperativa: element.isRientro ? 3 : null,
          idTipoEvento: element.isRientro
            ? TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento
            : TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento
        }
      }
    });
  }

  navigateToEstrazioni() {
    this.router.navigate(['/estrazioni']);
  }

  isEventoButtonDisabled(element: SchedaPaziente): boolean {
    const isChiuso = element.fkStato === this.statoEventoEnum.CHIUSO || element.fkStatoUltimoEvento === this.statoEventoEnum.CHIUSO;
    const isInLavorazione = element.fkStato === this.statoEventoEnum.IN_LAVORAZIONE || element.fkStatoUltimoEvento === this.statoEventoEnum.IN_LAVORAZIONE;
  
    const newEventNotAllowed = !element.isRientro &&
                              !!element.strutturaDestinazione &&
                                element.strutturaDestinazione.codLivello2 !== this.operatore.codLivello2Operatore;
  
    return (isChiuso && newEventNotAllowed) || isInLavorazione;
  }
}
