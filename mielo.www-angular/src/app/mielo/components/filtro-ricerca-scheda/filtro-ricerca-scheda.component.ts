import { CommonModule } from "@angular/common";
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from "@angular/forms";
import { combineLatest, Subscription } from 'rxjs';
import { MaterialModule } from "../../../core/material.module";
import { OperatoreService } from "../../../core/services/operatore.service";
import { IdOperatoreEnum } from "../../../shared/enums/enum";
import { DecodeModel, StatoLesioneModel } from "../../../shared/interfaces/decoder.interface";
import { DecoderService } from "../../../shared/services/decoder.service";
import { CodiceIdentificativoValidators } from "../../../shared/validators/codice-identificativo.validator";
import { NosologicoValidator } from "../../../shared/validators/dati-generali.validator";
import { RicercaSchedaPazienteService } from "../../services/ricerca-scheda-paziente.service";

@Component({
  selector: 'app-filtro-ricerca-scheda',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MaterialModule],
  templateUrl: './filtro-ricerca-scheda.component.html',
  styleUrl: './filtro-ricerca-scheda.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class FiltroRicercaSchedaComponent implements OnInit, OnDestroy {
  filterForm!: FormGroup;
  private subscriptions: Subscription[] = [];
  isResetButtonDisabled = false;
  private clickCounter = 0;

  constructor(
    private fb: FormBuilder,
    private ricercaSchedeService: RicercaSchedaPazienteService,
    public decoderService: DecoderService,
    private operatoreService: OperatoreService
  ) {
    this.createForm();
  }

  private createForm() {
    this.filterForm = this.fb.group({
      nome: [{value:'', disabled: this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM}, [this.minLengthIfNotEmptyValidator(2)]],
      cognome: [{value:'', disabled: this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM}, [this.minLengthIfNotEmptyValidator(2)]],
      codiceIdentificativo: [{value:'', disabled: this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM}, CodiceIdentificativoValidators.codiceIdentificativoUniversale()],
      lesioneMielica: [''],
      medicoCompilatore: ['', [this.minLengthIfNotEmptyValidator(2)]],
      idNosologico: [{value:'', disabled: this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM}, NosologicoValidator.format()],
      idPaziente: [{value:'', disabled: this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM}, Validators.pattern('^[0-9]*$')],
      statoEvento: ['']
    }, { validators: this.almenoUnCampoCompilato() });

    //forza il codice identificativo a maiuscolo
    this.filterForm.get('codiceIdentificativo')?.valueChanges.subscribe(value => {
      if (value) {
        this.filterForm.get('codiceIdentificativo')?.setValue(value.toUpperCase(), { emitEvent: false });
      }
    });
  }

  ngOnInit() {
    if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM) {
      this.decoderService.statoEventoReady$.subscribe(isReady => {
        if (isReady) {
          this.filterForm.get('statoEvento')?.patchValue(this.decoderService.listStatoEvento.find(e => e.descrizione === 'CHIUSO'))
          this.filterForm.get('statoEvento')?.disable();
          this.ricercaSchedeService.currentFilters = this.filterForm.getRawValue();
        }
      });
    }

    if (this.ricercaSchedeService.initFiltersfromBack) {
      this.caricaFiltriSalvati();

      const decoderSub = combineLatest([
        this.decoderService.statoLesioneReady$,
        this.decoderService.statoEventoReady$
      ]).subscribe(([isLesioneReady, isEventoReady]) => {
        if (isLesioneReady && isEventoReady) {
          this.caricaFiltriSalvati();
        }
      });

      this.subscriptions.push(decoderSub);

      const filtersSub = this.ricercaSchedeService.savedFilters$.subscribe(filters => {
        if (filters && Object.keys(filters).length > 0) {
          if (this.decoderService.listStatoLesione && this.decoderService.listStatoLesione.length > 0) {
            this.patchFormWithFilters(filters);
          }
        }
      });
      this.subscriptions.push(filtersSub);
    }

  }

  private caricaFiltriSalvati() {
    const savedFilters = this.ricercaSchedeService.getSavedFilters();
    if (savedFilters && Object.keys(savedFilters).length > 0) {
      this.patchFormWithFilters(savedFilters);
    }
  }

  private patchFormWithFilters(filters: any) {
    const filtriDaPatchare: any = {};

    Object.keys(this.filterForm.controls).forEach(key => {
      if (filters[key] !== undefined) {
        if (key === 'lesioneMielica' && filters[key]) {
          if (typeof filters[key] === 'object' && filters[key].idDizionario !== undefined) {
            const statoTrovato = this.trovaNellaLista(filters[key]);
            filtriDaPatchare[key] = statoTrovato || filters[key];
          } else if (typeof filters[key] === 'string' || typeof filters[key] === 'number') {
            const statoTrovato = this.trovaNellaListaPerId(filters[key]);
            filtriDaPatchare[key] = statoTrovato || filters[key];
          } else {
            filtriDaPatchare[key] = filters[key];
          }
        } else {
          filtriDaPatchare[key] = filters[key];
        }
      }
    });

    if (Object.keys(filtriDaPatchare).length > 0) {
      this.filterForm.patchValue(filtriDaPatchare, { emitEvent: false });
    }
  }

  private trovaNellaLista(statoLesione: any): StatoLesioneModel | null {
    if (!this.decoderService.listStatoLesione || this.decoderService.listStatoLesione.length === 0) {
      return null;
    }

    // Cerca per idDizionario se disponibile
    if (statoLesione.idDizionario !== undefined) {
      const trovato = this.decoderService.listStatoLesione.find(item => {
        const statoItem = item as StatoLesioneModel;
        return statoItem.idDizionario === statoLesione.idDizionario;
      });

      if (trovato) return trovato as StatoLesioneModel;
    }

    // Cerca per codice se disponibile
    if (statoLesione.codice !== undefined) {
      const trovato = this.decoderService.listStatoLesione.find(item => {
        const statoItem = item as StatoLesioneModel;
        return statoItem.codice === statoLesione.codice;
      });

      if (trovato) return trovato as StatoLesioneModel;
    }

    // Cerca per descrizione se disponibile
    if (statoLesione.descrizione !== undefined) {
      const trovato = this.decoderService.listStatoLesione.find(item => {
        const statoItem = item as StatoLesioneModel;
        return statoItem.descrizione === statoLesione.descrizione;
      });

      if (trovato) return trovato as StatoLesioneModel;
    }

    return null;
  }

  private trovaNellaListaPerId(idOCodice: string | number): StatoLesioneModel | null {
    if (!this.decoderService.listStatoLesione || this.decoderService.listStatoLesione.length === 0) {
      return null;
    }

    const trovato = this.decoderService.listStatoLesione.find(item => {
      const statoItem = item as StatoLesioneModel;
      return (statoItem.idDizionario !== undefined && statoItem.idDizionario.toString() === idOCodice.toString()) ||
        (statoItem.codice !== undefined && statoItem.codice.toString() === idOCodice.toString());
    });

    return trovato as StatoLesioneModel || null;
  }

  getOptionValue(option: DecodeModel): string | number {
    const statoOption = option as StatoLesioneModel;
    return statoOption.idDizionario !== undefined ? statoOption.idDizionario : statoOption.codice;
  }

  getDescrizioneStatoLesione(value: any): string {
    if (!value) return '';

    const statoLesione = this.decoderService.listStatoLesione.find(item => {
      const statoItem = item as StatoLesioneModel;
      return (statoItem.idDizionario && statoItem.idDizionario === Number(value)) ||
        (statoItem.codice && statoItem.codice === value);
    });

    return statoLesione ? statoLesione.descrizione : '';
  }

  compareFn = (o1: any, o2: any): boolean => {
    if (!o1 && !o2) return true;

    // Confronto più dettagliato per oggetti complessi
    if (typeof o1 === 'object' && typeof o2 === 'object') {
      // Confronta gli ID o i codici se disponibili
      if (o1.idDizionario !== undefined && o2.idDizionario !== undefined) {
        return o1.idDizionario === o2.idDizionario;
      }

      if (o1.codice !== undefined && o2.codice !== undefined) {
        return o1.codice === o2.codice;
      }

      // Confronta le descrizioni se disponibili
      if (o1.descrizione !== undefined && o2.descrizione !== undefined) {
        return o1.descrizione === o2.descrizione;
      }
    }

    // Confronto per valori primitivi o oggetti non riconosciuti
    return o1 === o2;
  }

  onFiltra() {
    this.ricercaSchedeService.disabilitaNuovaSegn.next(false) //disabilita il pulsante nuova segnalazione
    const filterValues = this.filterForm.getRawValue()
    delete filterValues.statoEvento?.categoria

    for (const key in filterValues) {
      if (!filterValues[key] || (typeof filterValues[key] === 'string' && filterValues[key].trim() === '')) {
        delete filterValues[key];
      }
    }

    this.ricercaSchedeService.filtraSchede(filterValues);
  }

  resetForm() {
    this.ricercaSchedeService.disabilitaNuovaSegn.next(true)
    const currentClick = ++this.clickCounter;
    const isTavTecn = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM
    const statoEvento = this.filterForm.get('statoEvento')?.value;
    this.filterForm.reset();
    if (isTavTecn) { //tav tecnicod deve sempre filtrare per stato evento CHIUSO
       this.filterForm.get('statoEvento')?.patchValue(statoEvento);
    }
    this.ricercaSchedeService.resetAllFilters();
    this.ricercaSchedeService.filtraSchede(isTavTecn ? {statoEvento} : undefined);
    this.isResetButtonDisabled = true;

    setTimeout(() => {
      if (currentClick === this.clickCounter) {
        this.isResetButtonDisabled = false;
      }
    }, 300);
  }

  almenoUnCampoCompilato(): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      if (!(formGroup instanceof FormGroup)) {
        return null;
      }

      const formKeys = Object.keys(formGroup.controls);
      const filteredFormKeys = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM ? formKeys.filter(key => key !== 'statoEvento') : formKeys;
      const almenoUnoCompilato = filteredFormKeys.some(key => {
        const control = formGroup.controls[key];
        return control.value && control.value.toString().trim() !== '';
      });

      return almenoUnoCompilato ? null : { almenoUnCampoRichiesto: true };
    };
  }

  minLengthIfNotEmptyValidator(minLength: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (!value || value.trim() === '') {
        return null;
      }

      return value.length < minLength ? { minLengthIfNotEmpty: { requiredLength: minLength, actualLength: value.length } } : null;
    };
  }

  ngOnDestroy() {
    // Pulizia delle sottoscrizioni
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
