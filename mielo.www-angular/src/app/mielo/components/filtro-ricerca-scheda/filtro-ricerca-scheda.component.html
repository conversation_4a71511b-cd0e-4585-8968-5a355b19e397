<div [formGroup]="filterForm">

  <div class="row mt-3">
    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">Nome</mat-label>
      <mat-form-field class="col">
        <input matInput formControlName="nome" placeholder="Inserisci" />
        <mat-error *ngIf="filterForm.get('nome')?.errors?.['minLengthIfNotEmpty']">
          Inserire almeno 2 caratteri
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">Cognome</mat-label>
      <mat-form-field class="col">
        <input matInput formControlName="cognome" placeholder="Inserisci" />
        <mat-error *ngIf="filterForm.get('cognome')?.errors?.['minLengthIfNotEmpty']">
          Inserire almeno 2 caratteri
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">Codice Identificativo</mat-label>
      <mat-form-field class="col">
        <input matInput formControlName="codiceIdentificativo" placeholder="Inserisci" />
        <mat-error>Formato codice non valido</mat-error>
      </mat-form-field>
    </div>

    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">Lesione mielica</mat-label>
      <mat-form-field class="col">
        <mat-select formControlName="lesioneMielica" [compareWith]="compareFn">
          <mat-option [value]="''">Seleziona</mat-option>
          <mat-option *ngIf="decoderService.listStatoLesione.length === 0" [value]="''" disabled>Caricamento...</mat-option>
          <mat-option *ngFor="let option of decoderService.listStatoLesione" [value]="option">
            {{ option.descrizione }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <div class="row mt-3">
    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">Medico compilatore</mat-label>
      <mat-form-field class="col">
        <input matInput formControlName="medicoCompilatore" placeholder="Inserisci" />
        <mat-error *ngIf="filterForm.get('medicoCompilatore')?.errors?.['minLengthIfNotEmpty']">
          Inserire almeno 2 caratteri
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">ID nosologico</mat-label>
      <mat-form-field class="col">
        <input matInput formControlName="idNosologico" placeholder="Inserisci" maxlength="9" />
        <mat-error *ngIf="filterForm.get('idNosologico')?.errors?.['lunghezzaInvalida']">
          Il formato deve essere NN/NNNNNN (9 caratteri)
        </mat-error>
        <mat-error *ngIf="filterForm.get('idNosologico')?.errors?.['formatoInvalido']">
          Formato non valido (NN/NNNNNN)
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">ID paziente</mat-label>
      <mat-form-field class="col">
        <input matInput formControlName="idPaziente" placeholder="Inserisci" />
        <mat-error *ngIf="filterForm.get('idPaziente')?.errors?.['pattern']">
          Inserire un valore numerico
        </mat-error>
      </mat-form-field>
    </div>

    <div class="col-lg-3 col-md-3">
      <mat-label class="text-overflow mar-l7">Stato evento</mat-label>
      <mat-form-field class="col">
        <mat-select formControlName="statoEvento" [compareWith]="compareFn">
          <mat-option [value]="''">Seleziona</mat-option>
          <mat-option *ngIf="decoderService.listStatoEvento.length === 0" [value]="''" disabled>Caricamento...</mat-option>
          <mat-option *ngFor="let option of decoderService.listStatoEvento" [value]="option">
            {{ option.descrizione }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="button-group w-100 px-2">
      <button mat-button type="button" (click)="resetForm()" color="primary" [disabled]="isResetButtonDisabled">
        <span class="button-underline">Azzera</span>
      </button>
      <button mat-raised-button (click)="onFiltra()" [disabled]="!filterForm.valid" color="primary">Filtra</button>
    </div>
  </div>

</div>
