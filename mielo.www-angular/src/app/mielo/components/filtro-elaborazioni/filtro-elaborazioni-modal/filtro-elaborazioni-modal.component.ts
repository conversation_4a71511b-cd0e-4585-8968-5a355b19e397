import { CommonModule } from "@angular/common";
import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, ValidatorFn } from "@angular/forms";
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from "@angular/material/dialog";
import { MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer } from "@angular/platform-browser";
import { filter, Subject } from "rxjs";
import { MaterialModule } from "../../../../core/material.module";
import { ERROR_MESSAGE, IdOperatoreEnum } from '../../../../shared/enums/enum';
import { DecoderService } from "../../../../shared/services/decoder.service";
import { IT_FORMATS } from '../../../../shared/utils/it-formats';
import { futureDateValidator } from '../../../../shared/validators/data-futura.validator';
import { DizionarioModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import { DatiCliniciService } from "../../../services/dati-clinici.service";
import { OperatoreService } from "../../../../core/services/operatore.service";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
    selector: 'app-filtro-elaborazioni-modal',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        MatDialogModule
    ],
    templateUrl: './filtro-elaborazioni-modal.component.html',
    styleUrl: './filtro-elaborazioni-modal.component.scss',
    providers: [
        { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
        { provide: MAT_DATE_FORMATS, useValue: IT_FORMATS },
        { provide: MAT_DATE_LOCALE, useValue: 'it' }
    ]
})
export class FiltroElaborazioniModalComponent implements OnInit, OnDestroy {
    filterForm!: FormGroup;
    private destroy$ = new Subject<void>();
    today = new Date();
    ERROR_MESSAGE = ERROR_MESSAGE;
    optionsBoolean: Array<DizionarioModel>;
    isTavoloTecnico: boolean = false;

    constructor(
        private fb: FormBuilder,
        public decoderService: DecoderService,
        public datiCliniciService: DatiCliniciService,
        private dialogRef: MatDialogRef<FiltroElaborazioniModalComponent>,
        private operatoreService: OperatoreService,
        private iconRegistry: MatIconRegistry,
        private sanitizer: DomSanitizer,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {
        this.iconRegistry.addSvgIconLiteral('it-calendar', this.sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));
        this.createForm();
        this.isTavoloTecnico = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM;
    }

    ngOnInit() {
        this.decoderService.getDcodBoolean().subscribe((value) => {
            this.optionsBoolean = value
        })
        if (this.data) {
            this.filterForm.patchValue(this.data);
        }
        if (this.isTavoloTecnico) {
            const eventoChiuso = this.decoderService.listStatoEvento.find(el => el.idDizionario === 36);
            this.filterForm.get('statoEvento')?.setValue(eventoChiuso, { emitEvent: false });
            this.filterForm.get('statoEvento')?.disable();
        }
    }

    private createForm() {
        this.filterForm = this.fb.group({
            periodo: this.fb.group({
                start: [null, [futureDateValidator()]],
                end: [null, [futureDateValidator()]]
            }, { validators: [this.dateRangeValidator()] }),
            idElaborazione: [''],
            statoElaborazione: [null],
            statoEvento: [null],
            lesioneMielica: [null],
            lesioneTraumatica: [null],
            tipoEvento: [null]
        });
    }

    onApply() {
        if (this.filterForm.valid) {
            const rawFormValues = this.filterForm.value;
            if (this.isTavoloTecnico) {
                rawFormValues.statoEvento = this.decoderService.listStatoEvento.find(el => el.idDizionario === 36);
            }

            const filterValues = {
                ...rawFormValues,
                lesioneTraumatica: rawFormValues.lesioneTraumatica
                    ? rawFormValues.lesioneTraumatica.idDizionario === 1
                    : null
            };

            Object.keys(filterValues).forEach(key => {
                const value = filterValues[key];

                const isEmptyString = typeof value === 'string' && value.trim() === '';
                const isEmptyObject = typeof value === 'object' && value !== null && Object.keys(value).length === 0;

                // Lasciamo lesioneTraumatica anche se false o null
                if ((key === 'lesioneTraumatica' && value === null) ||
                    key !== 'lesioneTraumatica' &&
                    (!value || isEmptyString || isEmptyObject)
                ) {
                    delete filterValues[key];
                }
            });

            this.dialogRef.close(filterValues);
        }
    }

    onReset() {
        this.filterForm.reset();
        if (this.isTavoloTecnico) {
            const eventoChiuso = this.decoderService.listStatoEvento.find(el => el.idDizionario === 36);
            this.filterForm.get('statoEvento')?.setValue(eventoChiuso, { emitEvent: false });
        }
    }

    compareFn(c1: any, c2: any): boolean {
        return c1 && c2 ? c1.codice === c2.codice : c1 === c2;
    }

    compareFnIdDizionario = (a: any, b: any): boolean => {
        return a && b ? a.idDizionario === b.idDizionario : a === b;
    };

    dateRangeValidator(): ValidatorFn {
        return (formGroup: any): { [key: string]: any } | null => {
            const start = formGroup.get('start')?.value;
            const end = formGroup.get('end')?.value;

            if (!start || !end) {
                return null;
            }

            return start <= end ? null : { rangeDateInvalid: true };
        };
    }

    get periodoGroup() {
        return this.filterForm.get('periodo') as FormGroup;
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }
} 