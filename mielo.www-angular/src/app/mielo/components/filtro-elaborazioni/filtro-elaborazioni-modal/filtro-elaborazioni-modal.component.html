<div class="modal-container" [formGroup]="filterForm">
    <div class="d-flex justify-content-between">
        <h2 mat-dialog-title>Filtra estrazioni</h2>
        <button class="close" type="button" aria-label="Close" mat-dialog-close>
            <svg class="icon">
                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-close"></use>
            </svg>
        </button>
    </div>

    <mat-dialog-content>
        <div class="row d-flex flex-wrap">
            <div class="col-md-6">
                <div class="form-field-container">
                    <mat-label class="text-overflow">ID Elaborazione</mat-label>
                    <mat-form-field class="w-100">
                        <input matInput type="number" formControlName="idElaborazione" placeholder="Inserisci ID" class="pt-0">
                    </mat-form-field>
                </div>
            </div>
            <div class="col-md-6">                
                <div class="form-field-container">
                    <mat-label class="text-overflow">Intervallo Elaborazione</mat-label>
                    <mat-form-field class="w-100" formGroupName="periodo">
                        <mat-date-range-input [rangePicker]="picker" class="text-center" [max]="today">
                            <input matStartDate formControlName="start" placeholder="GG/MM/AAAA" class="periodo-input p-0">
                            <input matEndDate formControlName="end" placeholder="GG/MM/AAAA" class="periodo-input p-0">
                        </mat-date-range-input>
                        <mat-datepicker-toggle matIconSuffix [for]="picker">
                            <mat-icon matDatepickerToggleIcon svgIcon="it-calendar" class="d-flex icon-calendar"></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-date-range-picker #picker></mat-date-range-picker>

                        <mat-error *ngIf="periodoGroup.get('start')?.errors?.['futureDate']">
                            {{ ERROR_MESSAGE.START_DATE_FUTURE }}
                        </mat-error>
                        <mat-error *ngIf="periodoGroup.get('end')?.errors?.['futureDate']">
                            {{ ERROR_MESSAGE.END_DATE_FUTURE }}
                        </mat-error>
                        <mat-error *ngIf="periodoGroup.errors?.['rangeDateInvalid']">
                            {{ ERROR_MESSAGE.DATE_RANGE_INVALID }}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <div class="col-md-6">                
                <div class="form-field-container">
                    <mat-label class="text-overflow">Stato Elaborazione</mat-label>
                    <mat-form-field class="w-100">
                        <mat-select formControlName="statoElaborazione" [compareWith]="compareFn" [placeholder]="'Seleziona'">
                            <mat-option [value]="null"></mat-option>
                            <mat-option *ngIf="!decoderService.listStatoElaborazione?.length" [value]="''" disabled>Caricamento...</mat-option>
                            <mat-option *ngFor="let option of decoderService.listStatoElaborazione" [value]="option">
                                {{ option.descrizione }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div class="col-md-6">                
                <div class="form-field-container">
                    <mat-label class="text-overflow">Tipo evento</mat-label>
                    <mat-form-field class="w-100">
                        <mat-select formControlName="tipoEvento" [compareWith]="compareFn" [placeholder]="'Seleziona'">
                            <mat-option [value]="null"></mat-option>
                            <mat-option *ngFor="let option of decoderService.listTipoEvento" [value]="option">
                                {{ option.descrizione }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-field-container">
                    <mat-label class="text-overflow">Stato evento</mat-label>
                    <mat-form-field class="w-100">
                        <mat-select formControlName="statoEvento" [compareWith]="compareFnIdDizionario" [placeholder]="'Seleziona'">
                            <mat-option [value]="null"></mat-option>
                            <mat-option *ngFor="let option of decoderService.listStatoEvento" [value]="option">
                                {{ option.descrizione }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-field-container">
                    <mat-label class="text-overflow">Lesione mielica</mat-label>
                    <mat-form-field class="w-100">
                        <mat-select formControlName="lesioneMielica" [compareWith]="compareFnIdDizionario" [placeholder]="'Seleziona'">
                            <mat-option [value]="null"></mat-option>
                            <mat-option *ngFor="let option of decoderService.listStatoLesioneEstrazioni" [value]="option">
                                {{ option.descrizione }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-field-container">
                    <mat-label class="field-label">Lesione traumatica</mat-label>
                    <mat-radio-group formControlName="lesioneTraumatica" class="d-flex" style="gap: 2rem;">
                        <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" color="primary"
                            (mouseup)="datiCliniciService.toggleRadioSelection(filterForm.get('lesioneTraumatica'), option, $event)">
                            {{ option.descrizione === 'SI' ? 'SÌ' : 'NO' }}
                        </mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
        </div>
    </mat-dialog-content>

    <mat-dialog-actions align="end">
        <button mat-button color="primary" (click)="onReset()">
            <span class="button-underline">Azzera</span>
        </button>
        <button mat-raised-button color="primary" (click)="onApply()" [disabled]="!filterForm.valid">
            Applica
        </button>
    </mat-dialog-actions>
</div>