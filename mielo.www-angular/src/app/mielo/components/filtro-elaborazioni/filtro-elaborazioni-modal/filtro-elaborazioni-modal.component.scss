.modal-container {
    padding: 24px;
    max-width: 800px;
    width: 100%;
}

.form-field-container {
    margin-bottom: 20px;
}

mat-form-field {
    width: 100%;
}

mat-dialog-actions {
    padding: 8px 0;
    display: flex;
    justify-content: space-between;
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin-bottom: 4px;
}

.w-100 {
    width: 100%;
}

.button-underline {
    text-decoration: underline;
}

.periodo-input {
    height: 1.5rem !important;
    min-height: 1.5rem !important;
    max-height: 1.5rem !important;
    line-height: 1.5rem !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
    width: 100% !important;
    min-width: 0 !important;
    flex: unset !important;
    display: block;
    font-weight: 500 !important;
}

::ng-deep .mat-date-range-input-container {
    display: grid !important;
    grid-template-columns: 1fr auto 1fr !important;
    align-items: center;
    gap: 0 !important;
}

::ng-deep .mat-date-range-input-separator {
    margin: 0 4px;
    color: rgba(0, 0, 0, 0.54);
}

::ng-deep .mat-date-range-input .mat-input-element {
    height: 1.5rem !important;
    line-height: 1.5rem !important;
    padding-top: 0;
    padding-bottom: 0;
    font-size: 1rem;
}

::ng-deep .mat-date-range-input input.mat-input-element[type="text"] {
    height: 1.5rem !important;
    min-height: 1.5rem !important;
    max-height: 1.5rem !important;
    line-height: 1.5rem !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

::ng-deep .mat-date-range-input-wrapper {
    max-width: 100% !important;
}

.icon-calendar {
    color: #003354 !important;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}