<div class="filter-container">
    <h5 class="mb-0 mr-3 mt-2">Stai visualizzando le estrazioni di:</h5>
    <div class="d-flex align-items-center mb-3 filter-row mt-3">
        <div class="active-filters flex-grow-1">
            <mat-chip-listbox *ngIf="getObjectKeys(activeFilters).length > 0">
                <ng-container *ngFor="let filter of getObjectKeys(activeFilters)">
                    <mat-chip
                        *ngIf="getFilterLabel(filter, activeFilters[filter])"
                        [removable]="true"
                        (removed)="removeFilter(filter)"
                        class="chip-blu"
                    >
                        <span>{{ getFilterLabel(filter, activeFilters[filter]) }}</span>
                        <mat-icon matChipRemove *ngIf="checkRemoveChip(filter)">cancel</mat-icon>
                    </mat-chip>
                </ng-container>
            </mat-chip-listbox>
            <p *ngIf="getObjectKeys(activeFilters).length === 0" class="text-muted">
                Nessun filtro applicato
            </p>
        </div>
        <button mat-raised-button (click)="openFilterModal()" class="ml-auto btn-secondary" style="box-shadow: none;">
            <mat-icon>filter_list</mat-icon>
            Filtra
        </button>
        <button mat-raised-button (click)="reloadList()" class="ml-2 btn-secondary" style="box-shadow: none;">
            <mat-icon>refresh</mat-icon>
            Aggiorna risultati
        </button>
    </div>
</div>