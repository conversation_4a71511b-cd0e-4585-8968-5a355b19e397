.filter-container {
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.active-filters {
    min-height: 48px;

    mat-chip-listbox {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
}

button[mat-raised-button] {
    mat-icon {
        margin-right: 8px;
    }
}

.btn-secondary {
  border: 1px solid #2A7A38 !important;
  color: #2A7A38 !important;

  &:hover {
    background-color: #fbfbfb4d !important;
  }
}

.text-muted {
    color: #666;
    font-style: italic;
    margin: 0;
}

.chip-blu,
.chip-blu .mat-chip-content,
.chip-blu span,
.chip-blu strong,
.chip-blu b {
  color: #fff !important;
}

.chip-blu {
  // background-color: #297A38 !important;
  background-color: #003354 !important;
  color: #fff !important;
  font-weight: 500;
  
  .mat-chip-content,
  .mat-chip-ripple {
    color: #fff !important;
  }

  .mat-icon,
  .mat-chip-remove {
    color: #fff !important;
    opacity: 1 !important;
  }
}

.filter-row {
  display: flex;
  align-items: center;

  .active-filters {
    flex-grow: 1;
    min-width: 0;
    display: flex;
    align-items: center;
  }

  button[mat-raised-button] {
    margin-left: auto;
    white-space: nowrap;
  }
}