import { CommonModule } from "@angular/common";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ReactiveFormsModule } from "@angular/forms";
import { MatButtonModule } from "@angular/material/button";
import { MatChipsModule } from "@angular/material/chips";
import { MAT_DATE_FORMATS } from "@angular/material/core";
import { MatDialog, MatDialogModule } from "@angular/material/dialog";
import { MatIconModule, MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer } from "@angular/platform-browser";
import { Observable, Subject, takeUntil } from "rxjs";
import { MaterialModule } from "../../../core/material.module";
import { DecoderService } from "../../../shared/services/decoder.service";
import { IT_FORMATS } from "../../../shared/utils/it-formats";
import { formatDate } from "../../../shared/utils/utils";
import { ActiveFilters, FilterKeys } from "../../interfaces/elabora-estrazioni.interface";
import { EstrazioniService } from "../../services/estrazioni.service";
import { FiltroElaborazioniModalComponent } from "./filtro-elaborazioni-modal/filtro-elaborazioni-modal.component";
import { DizionarioModel } from "../../../shared/interfaces/scheda-ricovero.interface";
import { OperatoreService } from "../../../core/services/operatore.service";
import { IdOperatoreEnum, StatoEventoEnum } from "../../../shared/enums/enum";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
    selector: 'app-filtro-elaborazioni',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        MatDialogModule,
        MatButtonModule,
        MatChipsModule,
        MatIconModule
    ],
    providers: [
        { provide: MAT_DATE_FORMATS, useValue: IT_FORMATS }
    ],
    templateUrl: './filtro-elaborazioni.component.html',
    styleUrl: './filtro-elaborazioni.component.scss'
})
export class FiltroElaborazioniComponent implements OnInit, OnDestroy {
    activeFilters: ActiveFilters = {};
    readonly FilterKeys = FilterKeys;
    private destroy$ = new Subject<void>();

    isTavoloTecnico: boolean = false;

    constructor(
        private dialog: MatDialog,
        private estrazioniService: EstrazioniService,
        private iconRegistry: MatIconRegistry,
        private sanitizer: DomSanitizer,
        private decoderService: DecoderService,
        private operatoreService : OperatoreService
    ) {
        this.iconRegistry.addSvgIconLiteral('it-calendar', this.sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));
        this.isTavoloTecnico = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM;
    }

    ngOnInit() {
        (this.estrazioniService.currentFilters as Observable<ActiveFilters>)
            .pipe(takeUntil(this.destroy$))
            .subscribe(filters => {
                let updatedFilters = { ...filters };

                if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM) {
                    const statoEvento = {
                        "idDizionario": StatoEventoEnum.CHIUSO,
                        "descrizione": "CHIUSO"
                    }
                    
                    updatedFilters[FilterKeys.STATO_EVENTO] = statoEvento;
                }

                this.activeFilters = updatedFilters;
            });
    }

    openFilterModal() {
        const dialogRef = this.dialog.open(FiltroElaborazioniModalComponent, {
            width: '800px',
            data: this.activeFilters
        });

        dialogRef.afterClosed()
            .pipe(takeUntil(this.destroy$))
            .subscribe(result => {
                if (result && Object.keys(result).length > 0) {
                    this.estrazioniService.filtraEstrazioni(result);
                    this.decoderService.aggiornaReportInElaborazione();
                }
            });
    }

    checkRemoveChip(filter: FilterKeys): boolean {
        return !this.isTavoloTecnico ||
                this.isTavoloTecnico && filter !== FilterKeys.STATO_EVENTO          
    }

    updateEstrazioniForTavoloTecnico() {
        let filterBody: any;
        filterBody.statoEvento = this.decoderService.listStatoEvento.find(el => el.idDizionario === StatoEventoEnum.CHIUSO);
        this.estrazioniService.filtraEstrazioni(filterBody);
        this.decoderService.aggiornaReportInElaborazione();
    }

    removeFilter(key: FilterKeys) {
        const updatedFilters = { ...this.activeFilters };
        // @ts-ignore
        delete updatedFilters[key];
        this.estrazioniService.filtraEstrazioni(updatedFilters);
        this.decoderService.aggiornaReportInElaborazione();
    }

    reloadList() {
        this.estrazioniService.filtraEstrazioni(this.activeFilters);
        this.decoderService.aggiornaReportInElaborazione();
    }

    getFilterLabel(key: FilterKeys, value: any): string {
        switch (key) {
            case FilterKeys.PERIODO:
                if (!value?.start || !value?.end) {
                    return '';
                }
                const startDate = formatDate(value.start);
                const endDate = formatDate(value.end);
                return `Intervallo Elaborazione: ${startDate} - ${endDate}`;
            case FilterKeys.ID_ELABORAZIONE:
                return `ID: ${value}`;
            case FilterKeys.STATO_ELABORAZIONE:
                return `Stato Elaborazione: ${value?.descrizione ?? ''}`;
            case FilterKeys.LESIONE_MIELICA:
                return `Lesione mielica: ${value?.descrizione ?? ''}`;
            case FilterKeys.LESIONE_TRAUMATICA:
                return `Lesione traumatica: ${value ? 'SÌ' : 'NO'}`;
            case FilterKeys.STATO_EVENTO:
                return `Stato evento: ${value?.descrizione ?? ''}`;
            case FilterKeys.TIPO_EVENTO:
                return `Tipo evento: ${value?.descrizione ?? ''}`;
            default:
                return `${key}: ${value?.toString?.() ?? ''}`;
        }
    }

    getObjectKeys(obj: ActiveFilters): FilterKeys[] {
        return Object.keys(obj) as FilterKeys[];
    }

    ngOnDestroy() {
        this.estrazioniService.resetAllFilters();
        this.destroy$.next();
        this.destroy$.complete();
    }
} 