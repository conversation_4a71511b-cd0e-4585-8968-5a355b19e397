<!-- Table -->
<div style="overflow-x: auto" *ngIf="dataSource && dataSource.length !== 0; else nessunRisultato">
  <table mat-table [dataSource]="dataSource | paginate: {
    id: 'server',
    itemsPerPage: paginationConfig.itemsPerPage,
    currentPage: paginationConfig.currentPage + 1,
    totalItems: paginationConfig.totalItems
  }">

    <!-- Id elaborazione -->
    <ng-container matColumnDef="idElaborazione">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-id-elab">ID Elab.</th>
      <td mat-cell *matCellDef="let element" class="text-center col-id-elab">
        <div class="id-column">
          {{ element.idElaborazione }}
        </div>
      </td>
    </ng-container>

    <!-- Data elaborazione -->
    <ng-container matColumnDef="dataElaborazione">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-data-elab">Data Elaborazione</th>
      <td mat-cell *matCellDef="let element"class="text-center col-data-elab">
        {{ (element.dataOraElaborazione ? element.dataOraElaborazione : '/') | date: 'dd/MM/yyyy - HH:mm' }}
      </td>
    </ng-container>

    <!-- Stato elaborazione -->
    <ng-container matColumnDef="statoElaborazione">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-stato-elab">Stato Elaborazione</th>
      <td mat-cell *matCellDef="let element" class="text-center col-stato-elab">
        <span
          [ngClass]="{
            'badge-in-elaborazione': element.statoElaborazione?.idDizionario === statoElaborazioneEnum.IN_ELABORAZIONE,
            'badge-elaborato': element.statoElaborazione?.idDizionario === statoElaborazioneEnum.ELABORATO,
            'badge-fallito': element.statoElaborazione?.idDizionario === statoElaborazioneEnum.FALLITO,
            'badge-errori': element.statoElaborazione?.descrizione?.toUpperCase().includes('ERRORI')
          }"
          class="badge-stato-elaborazione"
        >
          {{ element.statoElaborazione?.descrizione || '/' }}
        </span>
      </td>
    </ng-container>

    <!-- Periodo -->
    <ng-container matColumnDef="periodo">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-periodo">Periodo</th>
      <td mat-cell *matCellDef="let element" class="text-center .col-periodo">{{ formatPeriodo(element.dataMin, element.dataMax) }}</td>
    </ng-container>

    <!-- Tipo Evento -->
    <ng-container matColumnDef="tipoEvento">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-tipo-evento">Tipo Evento</th>
      <td mat-cell *matCellDef="let element" class="text-center  col-tipo-evento">
        {{ (element.tipoEvento?.descrizione | uppercase) || '/' }}
      </td>
    </ng-container>

    <!-- Stato Evento -->
    <ng-container matColumnDef="statoEvento">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-stato-evento">Stato Evento</th>
      <td mat-cell *matCellDef="let element" class="text-center  col-stato-evento">
        {{ (element.statoEvento?.descrizione | uppercase) || '/' }}
      </td>
    </ng-container>

    <!-- Tipo Lesione -->
    <ng-container matColumnDef="tipoLesione">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-tipo-lesione">Lesione Mielica</th>
      <td mat-cell *matCellDef="let element" class="text-center  col-tipo-lesione">
        {{ (element.tipoLesione?.descrizione | uppercase) || '/' }}
      </td>
    </ng-container>

    <!-- Lesione traumatica -->
    <ng-container matColumnDef="lesioneTraumatica">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-lesione-traumatica">Traumatica</th>
      <td mat-cell *matCellDef="let element" class="text-center  col-lesione-traumatica">
        <!-- {{ (element.tipoLesione?.descrizione | uppercase) || '/' }} -->
          {{ convertBooleanToString(element.lesioneTraumatica) }}
      </td>
    </ng-container>
    
    <!-- File -->
    <ng-container matColumnDef="file">
      <th mat-header-cell *matHeaderCellDef class="text-center .col-file">File</th>
      <td mat-cell *matCellDef="let element" class="text-center .col-file d-flex justify-content-center download-border">
        <ng-container *ngIf="element.statoElaborazione?.idDizionario === statoElaborazioneEnum.ELABORATO">
          <button mat-icon-button color="primary" (click)="downloadFile(element)" matTooltip="Scarica il file">
            <mat-icon>download</mat-icon>
          </button>
        </ng-container>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
</div>

<div *ngIf="dataSource && dataSource.length !== 0 && paginationConfig.totalItems > paginationConfig.itemsPerPage"
  class="d-flex align-items-center justify-content-end mt-3 custom-pagination">
  <pagination-controls (pageChange)="pageChanged($event)" [directionLinks]="true" previousLabel="" nextLabel=""
    [responsive]="true" [maxSize]="5" id="server">
  </pagination-controls>
</div>

<ng-template #nessunRisultato>
  <div class="empty-box">
    <div class="mt-5">
      <img src="assets/images/mielo/empty.png" alt="nessun risultato">
    </div>
    <div class="mt-4">
      <h5>Nessun risultato di ricerca presente</h5>
    </div>
  </div>
</ng-template>