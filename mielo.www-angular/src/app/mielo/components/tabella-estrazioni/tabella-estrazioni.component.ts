import { CommonModule } from "@angular/common";
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatBadgeModule } from "@angular/material/badge";
import { NgxPaginationModule } from "ngx-pagination";
import { Subject, combineLatest, takeUntil } from "rxjs";
import { MaterialModule } from "../../../core/material.module";
import { StatoEventoEnum } from "../../../shared/enums/enum";
import { StatoElaborazioneEnum } from '../../../shared/enums/stato-elaborazione.enum';
import { CapitalizePipe } from "../../../shared/pipes/capitalize.pipe";
import { FileHandlerService } from '../../../shared/services/download-file-csv.service';
import { formatDateTimeIT } from '../../../shared/utils/utils';
import { PaginationConfig, TableConfig } from "../../interfaces/elabora-estrazioni.interface";
import { MetadatiReportDTO } from "../../interfaces/metadati-report-dto.interface";
import { EstrazioniService } from "../../services/estrazioni.service";

const TABLE_CONFIG: TableConfig = {
    displayedColumns: [
        'idElaborazione',
        'dataElaborazione',
        'statoElaborazione',
        'periodo',
        'tipoEvento',
        'statoEvento',
        'tipoLesione',
        'lesioneTraumatica',
        'file'
    ],
    defaultItemsPerPage: 6
};

@Component({
    selector: 'app-tabella-estrazioni',
    standalone: true,
    imports: [
        CommonModule,
        MaterialModule,
        NgxPaginationModule,
        MatBadgeModule,
        CapitalizePipe
    ],
    templateUrl: './tabella-estrazioni.component.html',
    styleUrl: './tabella-estrazioni.component.scss'
})
export class TabellaEstrazioniComponent implements OnInit, OnDestroy {
    @Input() dataSource: MetadatiReportDTO[] = [];

    displayedColumns = TABLE_CONFIG.displayedColumns;
    paginationConfig: PaginationConfig = {
        totalItems: 0,
        currentPage: 0,
        itemsPerPage: TABLE_CONFIG.defaultItemsPerPage
    };

    statoEventoEnum = StatoEventoEnum;
    statoElaborazioneEnum = StatoElaborazioneEnum;
    private destroy$ = new Subject<void>();
    private isChangingPage = false;

    constructor(
        private estrazioniService: EstrazioniService,
        private fileHandlerService: FileHandlerService
    ) {}

    ngOnInit() {
        this.initializeSubscriptions();
        this.initializeData();
    }

    private initializeSubscriptions() {
        combineLatest([
            this.estrazioniService.paginationStatus$,
            this.estrazioniService.resultEstrazioni
        ]).pipe(
            takeUntil(this.destroy$)
        ).subscribe(([paginationStatus, estrazioni]) => {
            if (paginationStatus) {
                this.handlePaginationStatus(paginationStatus);
            }
            if (estrazioni?.length > 0) {
                this.dataSource = estrazioni;
            }
        });

        this.estrazioniService.paginazioneResettata
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                this.resetPaginazione();
            });
    }

    private handlePaginationStatus(status: any) {
        this.paginationConfig.totalItems = status.totalElements;
        if (!this.isChangingPage) {
            this.paginationConfig.currentPage = status.number;
            this.estrazioniService.currentPage = this.paginationConfig.currentPage;
        }
        this.isChangingPage = false;
        this.paginationConfig.itemsPerPage = this.estrazioniService.itemsPerPage;
    }

    private initializeData() {
        if (this.estrazioniService.resultEstrazioni.value.length > 0) {
            this.dataSource = this.estrazioniService.resultEstrazioni.value;
            this.paginationConfig.currentPage = this.estrazioniService.currentPage;
        } else {
            this.loadData();
        }
    }

    formatPeriodo(dataInizio: string | null | undefined, dataFine: string | null | undefined): string {
        if (!dataInizio && !dataFine) return '/';
        
        if (dataInizio && !dataFine) {
            return `Dal ${this.formatDate(dataInizio)}`;
        }
        
        if (!dataInizio && dataFine) {
            return `Fino al ${this.formatDate(dataFine)}`;
        }
        
        return `${this.formatDate(dataInizio!)} - ${this.formatDate(dataFine!)}`;
    }

    formatDate(dateString: string | null | undefined): string {
        if (!dateString) return '/';
        const date = new Date(dateString);
        return date.toLocaleDateString('it-IT', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    downloadFile(element: MetadatiReportDTO): void {
        this.estrazioniService.downloadFileCsv(element.idElaborazione)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (res) => {
                    const { downloadUrl, filename } = this.fileHandlerService.elaborateBodyBlobAsFile(res);
                    this.fileHandlerService.downloadBodyBlobAsFile(downloadUrl, filename);
                }
            });
    }

    pageChanged($event: number) {
        this.isChangingPage = true;
        const pageNumber = $event - 1;
        this.paginationConfig.currentPage = pageNumber;
        this.estrazioniService.currentPage = pageNumber;
        this.loadData();
    }

    private loadData() {
        const requestBody = this.estrazioniService.buildRequestBody(this.paginationConfig.currentPage);
        this.estrazioniService.getEstrazioni(requestBody)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                error: (err) => {
                    console.error('Errore durante il caricamento dei dati:', err);
                    this.isChangingPage = false;
                }
            });
    }

    private resetPaginazione() {
        this.isChangingPage = false;
        this.paginationConfig.currentPage = 0;
        this.estrazioniService.currentPage = 0;
    }

    formatDateTime(dateInput: string | number | Date | null | undefined): string {
        return formatDateTimeIT(dateInput);
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    convertBooleanToString(value: boolean) : string {
        let convertedString : string = '/'

        if (value === true) {
            convertedString = 'SÌ';
        }

        if (value === false) {
            convertedString = 'NO';
        }

        return convertedString;
    }
} 