.mdc-icon-button:focus{
  outline: none !important;
}

/** MAT TABLE **/
.table{
  width: 100%;
}

.mat-mdc-header-cell {
  border-right: 1px solid white !important;
  font-weight: 700;
  height: 80px !important;
}
.mat-mdc-cell{
  border-right: 1px solid #D8D8D8 !important;
}

.mat-mdc-cell, .mat-mdc-header-cell {
  padding: 0.5vw !important;
  white-space: pre-line;
  font-size: 18px !important;
  font-family: "Titillium Web", Geneva, Tahoma, sans-serif;
  letter-spacing: normal !important;
  vertical-align: middle !important;
  align-items: center !important;
  min-height: 80px !important;
}
.mat-mdc-row {
  min-height: 72px;
  border-radius: 0px !important;
  cursor: auto !important;
  background-color: #FFFFFF !important;
  border-bottom: 1px solid #D8D8D8;
  color: #000000 !important;
}

.mat-mdc-row:nth-child(even) {
  background-color: #F9F9F9 !important;
}

.mat-mdc-header-row,
.mat-mdc-header-row.mat-table-sticky {
  min-height: 72px;
  background-color: #E6E9F0 !important;
  color: #003354 !important;
}
.mat-mdc-header-cell:last-child {
  border-right: none !important;
}

.mat-mdc-cell:first-child {
  border-left: 1px solid #D8D8D8 !important;
}

.no-pointer {
  pointer-events: none;
}

.col-id-elab           { width: 5% !important; }
.col-stato-elab        { width: 10% !important; }
.col-tipo-lesione      { width: 10% !important; }
.col-lesione-traumatica{ width: 10% !important; }
.col-stato-evento      { width: 15% !important; }
.col-tipo-evento       { width: 15% !important; }
.col-file              { width: 5% !important; }
.col-data-elab         { width: 15% !important; }
.col-periodo           { width: 15% !important; }



/** FINE MAT TABLE**/

//ngx-paginator style
.custom-pagination ::ng-deep .ngx-pagination .current {
  border: 2px solid #297A38;
  border-radius: 8px;
  padding: 10px;
  background-color: transparent;
  color: #505051;
  font-weight: 600;
  width: 48px;
  text-align: center;
}
.custom-pagination ::ng-deep .ngx-pagination a{
  width: 48px;
  height: 48px;
  color: #505051 !important;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  padding: 10px;
  text-align: center;
}

.empty-box{
  width: 100%;
  border: 2px solid #BACCD9;
  text-align: center;
  height: 250px;
}

.text-center {
  text-align: center !important;
}

.icon-cell-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.icon-green {
  color: #297A38 !important;
}

.download-border {
  border-bottom: none !important;
  min-height: 74px;
}

.badge-stato-elaborazione {
  display: inline-block;
  padding: 4px 16px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 8px;
  min-width: 120px;
  text-align: center;
  color: #fff;
  letter-spacing: 0.5px;
  margin: 0 auto;
}

.badge-in-elaborazione {
  background-color: #F19937;
}

.badge-elaborato {
  background-color: #62AF60;
}

.badge-fallito {
  background-color: #C83A32;
}

.badge-errori {
  background-color: #e67e22;
}