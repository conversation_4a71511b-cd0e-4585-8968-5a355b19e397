:host ::ng-deep {
    .mat-mdc-radio-button .mdc-form-field {
        display: flex;
        align-items: center !important;
        height: 100%;
        margin-left: 0px;
        margin-bottom: 0px;
        line-height: normal;
    }

    .mat-icon-button {
        width: 40px;
        height: 40px;
        line-height: 40px;

        .mat-icon {
            color: #003354;
            fill: #003354;
        }
    }

    // .mat-datepicker-toggle {
    //     .mat-icon {
    //         color: #003354;
    //         fill: #003354;
    //     }
    // }

    .mat-form-field-disabled {
        .mat-datepicker-toggle {
            .mat-icon {
                color: #666666;
                fill: #666666;
            }
        }

        .mat-icon-button {
            .mat-icon {
                color: #666666;
                fill: #666666;
            }
        }
    }

    .mat-expansion-panel-header {
        height: 64px;
    }
    
    .mdc-text-field--outlined .mat-mdc-form-field-infix, .mdc-text-field--no-label .mat-mdc-form-field-infix {
        display: flex !important;
        align-items: center !important;
    }
    
    input.mat-mdc-input-element {
        height: 22px !important;
        line-height: 22px !important;
    }
    
    .mat-mdc-form-field-flex {
        align-items: center !important;
    }
    
    .mat-mdc-input-element::placeholder {
        line-height: normal !important;
        vertical-align: middle !important;
    }
    
    .mdc-floating-label:not(.mdc-floating-label--float-above) {
        top: 50% !important;
        transform: translateY(-50%) !important;
    }
}

.form-field-container {
    display: flex;
    flex-direction: column;
}

.field-label {
    font-weight: 600;
    margin-bottom: 12px;
}

.radio-group-container {
    margin: 0;
}

.w-100 {
    width: 100%;
}

::ng-deep {
    .mat-expansion-panel-content {
        font-size: 14px;
    }
}