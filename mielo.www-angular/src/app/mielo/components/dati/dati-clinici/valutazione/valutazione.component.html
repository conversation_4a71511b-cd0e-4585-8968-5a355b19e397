<p class="warning-text" *ngIf="isOltreSetteGiorni">
    <svg class="icon icon-danger">
        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-error"></use>
    </svg>
    Sono trascorsi più di 7 giorni dalla data di ricovero
</p>

<form [formGroup]="valutazioneForm" class="flex-wrapbox-dati mt-4">
    <div class="row g-0">
        <div class="col-6">

            <!-- Vigilanza -->
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">
                        Vigilanza<span *ngIf="isRequired('vigilanza')">*</span>
                    </label>
                    <div class="d-flex align-items-center">
                        <div class="col-12 col-md-12 d-flex">
                            <div class="p-0 mr-2">
                                <div class="radio-group-container">
                                    <mat-radio-group formControlName="vigilanza" class="d-flex">
                                        <mat-radio-button *ngFor="let option of optionsValutazione" [value]="option"
                                            color="primary"
                                            (mouseup)="datiCliniciService.toggleRadioSelection(valutazioneForm.get('vigilanza'), option, $event)">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                            </div>
                            <button mat-button type="button" class="note-button ml-3"
                                [disabled]="isNoteDisabled('vigilanza')" (click)="openPopupNote('vigilanza')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orientamento spaziale -->
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">
                        Orientamento spaziale<span *ngIf="isRequired('orientamentoSpaziale')">*</span>
                    </label>
                    <div class="d-flex align-items-center">
                        <div class="col-12 col-md-12 d-flex">
                            <div class="p-0 mr-2">
                                <div class="radio-group-container">
                                    <mat-radio-group formControlName="orientamentoSpaziale" class="d-flex">
                                        <mat-radio-button *ngFor="let option of optionsValutazione" [value]="option"
                                            color="primary"
                                            (mouseup)="datiCliniciService.toggleRadioSelection(valutazioneForm.get('orientamentoSpaziale'), option, $event)">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                            </div>
                            <button mat-button type="button" class="note-button ml-3"
                                [disabled]="isNoteDisabled('orientamentoSpaziale')"
                                (click)="openPopupNote('orientamentoSpaziale')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="col-6">

            <!-- Deficit cognitivi -->
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">
                        Deficit cognitivi<span *ngIf="isRequired('deficitCognitivo')">*</span>
                    </label>
                    <div class="d-flex align-items-center">
                        <div class="col-12 col-md-12 d-flex">
                            <div class="p-0 mr-2">
                                <div class="radio-group-container">
                                    <mat-radio-group formControlName="deficitCognitivo" class="d-flex">
                                        <mat-radio-button *ngFor="let option of optionsValutazione" [value]="option"
                                            color="primary"
                                            (mouseup)="datiCliniciService.toggleRadioSelection(valutazioneForm.get('deficitCognitivo'), option, $event)">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                            </div>
                            <button mat-button type="button" class="note-button ml-3"
                                [disabled]="isNoteDisabled('deficitCognitivo')"
                                (click)="openPopupNote('deficitCognitivo')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orientamento temporale -->
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">
                        Orientamento temporale<span *ngIf="isRequired('orientamentoTemporale')">*</span>
                    </label>
                    <div class="d-flex align-items-center">
                        <div class="col-12 col-md-12 d-flex">
                            <div class="p-0 mr-2">
                                <div class="radio-group-container">
                                    <mat-radio-group formControlName="orientamentoTemporale" class="d-flex">
                                        <mat-radio-button *ngFor="let option of optionsValutazione" [value]="option"
                                            color="primary"
                                            (mouseup)="datiCliniciService.toggleRadioSelection(valutazioneForm.get('orientamentoTemporale'), option, $event)">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                            </div>
                            <button mat-button type="button" class="note-button ml-3"
                                [disabled]="isNoteDisabled('orientamentoTemporale')"
                                (click)="openPopupNote('orientamentoTemporale')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col">
            <!-- Collaborazione -->
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">
                        Collaborazione<span *ngIf="isRequired('collaborazione')">*</span>
                    </label>
                    <div class="d-flex align-items-center">
                        <div class="col-12 col-md-12 d-flex">
                            <div class="p-0 mr-2">
                                <div class="radio-group-container">
                                    <mat-radio-group formControlName="collaborazione" class="d-flex">
                                        <mat-radio-button *ngFor="let option of optionsCollaborazione" [value]="option"
                                            color="primary"
                                            (mouseup)="datiCliniciService.toggleRadioSelection(valutazioneForm.get('collaborazione'), option, $event)">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                            </div>
                            <button mat-button type="button" class="note-button ml-3"
                                [disabled]="isNoteDisabled('collaborazione')" (click)="openPopupNote('collaborazione')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>