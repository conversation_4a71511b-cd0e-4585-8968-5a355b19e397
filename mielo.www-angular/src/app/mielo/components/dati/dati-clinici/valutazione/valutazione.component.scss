:host {
    .field-label {
        color: #003354;
        margin-bottom: 8px;
        display: block;
        font-size: 18px;
        font-weight: 600;
    }

    .radio-group-container {
        padding: 0;
        display: flex;
        align-items: center;
        width: 100%;
        background-color: transparent;
        border: none;
        margin: 0;

        .mat-radio-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: nowrap;
            width: 100%;

            .mat-radio-button {
                white-space: nowrap;
                flex-shrink: 0;
            }
        }
    }

    ::ng-deep {
        .mat-radio-button {
            display: flex;
            align-items: center;
            margin: 0;
            font-size: 14px;

            .mat-radio-container {
                height: 16px;
                width: 16px;
            }

            .mat-radio-outer-circle,
            .mat-radio-inner-circle {
                height: 16px;
                width: 16px;
            }

            .mat-radio-label {
                display: flex;
                align-items: center;
                margin: 0;
                white-space: nowrap;
            }

            .mat-radio-label-content {
                padding-left: 8px;
                line-height: normal;
                display: flex;
                align-items: center;
                white-space: nowrap;
            }
        }

        .mdc-form-field>label {
            margin-left: 0;
            margin-right: auto;
            padding-left: 4px;
            //padding-bottom: 9px;
            padding-right: 0;
            order: 0;
        }
    }

    .row.mb-5 {
        margin-bottom: 1rem !important;
    }

    .col-12.d-flex.flex-column {
        gap: 0;
    }

    .warning-text {
        color: #D90505;
        font-weight: 700;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: .5rem;
    }

    .row {
        width: 95% !important;
    }
}
