import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnDestroy, OnInit, Output, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { MatExpansionModule, MatExpansionPanel } from '@angular/material/expansion';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { OperatoreService } from '../../../../core/services/operatore.service';
import { SceltaSchedaEnum } from '../../../../shared/enums/scelta-scheda.enum';
import { SchedaDatiCliniciModel } from '../../../../shared/interfaces/dati-clinici.interface';
import { SchedaRicoveroModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import { SchedaRicoveroService } from '../../../services/scheda-ricovero.service';
import { EziologiaRientroComponent } from './eziologia-rientro/eziologia-rientro.component';
import { EziologiaComponent } from './eziologia/eziologia.component';
import { LesioneTrattamentoComponent } from './lesione-trattamento/lesione-trattamento.component';
import { NecessitaAssistenzialiComponent } from './necessita-assistenziali/necessita-assistenziali.component';
import { QuadroNeurologicoComponent } from './quadro-neurologico/quadro-neurologico.component';
import { SettingRiabilitativoComponent } from './setting-riabilitativo/setting-riabilitativo.component';
import { ValutazioneComponent } from './valutazione/valutazione.component';
import { ComplicanzeComponent } from "../../../../shared/components/complicanze/complicanze.component";
import {
  TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO,
  TIPO_EVENTO_RIENTRO_NON_CENSITO,
  TIPO_EVENTO_RIENTRO_CENSITO
} from '../../../../shared/utils/const';
import { IdOperatoreEnum, ETipoScheda } from "../../../../shared/enums/enum";
import { CronologiaMenuSimpleComponent } from '../../../../shared/components/cronologia-menu-simple/cronologia-menu-simple.component';
import { OutputHistoryDTO } from "../../../../shared/interfaces/shared/shared.interface";
import { MatButton } from "@angular/material/button";

@Component({
  selector: 'app-dati-clinici',
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    EziologiaComponent,
    LesioneTrattamentoComponent,
    ValutazioneComponent,
    QuadroNeurologicoComponent,
    SettingRiabilitativoComponent,
    NecessitaAssistenzialiComponent,
    ComplicanzeComponent,
    CronologiaMenuSimpleComponent,
    MatButton,
    ComplicanzeComponent,
    EziologiaRientroComponent
  ],
  templateUrl: './dati-clinici.component.html',
  styleUrl: './dati-clinici.component.scss'
})
export class DatiCliniciComponent implements OnInit, OnDestroy {
  @ViewChildren(MatExpansionPanel) panels: QueryList<MatExpansionPanel>;
  @ViewChildren(EziologiaComponent) eziologiaComponent: QueryList<EziologiaComponent>;
  @ViewChildren(LesioneTrattamentoComponent) lesioneComponent: QueryList<LesioneTrattamentoComponent>;
  @ViewChildren(ValutazioneComponent) valutazioneComponent: QueryList<ValutazioneComponent>;
  @ViewChildren(NecessitaAssistenzialiComponent) necessitaComponent: QueryList<NecessitaAssistenzialiComponent>;
  @ViewChildren(QuadroNeurologicoComponent) quadroComponent: QueryList<QuadroNeurologicoComponent>;
  @ViewChildren(SettingRiabilitativoComponent) settingComponent: QueryList<SettingRiabilitativoComponent>;
  @ViewChildren(ComplicanzeComponent) complicanzeComponent: QueryList<ComplicanzeComponent>;

  @ViewChild('valutazionePanel') valutazionePanel: MatExpansionPanel;
  @ViewChild('necessitaPanel') necessitaPanel: MatExpansionPanel;
  @ViewChild('quadroNeuroPanel') quadroNeuroPanel: MatExpansionPanel;


  @Output() menuItemsChange = new EventEmitter<SceltaSchedaEnum[]>();

  datiClinici: SchedaDatiCliniciModel;
  idReparto: number;
  dataRicovero: string;
  menuItems = [SceltaSchedaEnum.GENERALI]
  tipoEvento: number;
  idScheda?: number;
  datiCronologiaValutazione: OutputHistoryDTO | null = null;
  datiCronologiaNecessita: OutputHistoryDTO | null = null;
  datiCronologiaQuadroNeurologico: OutputHistoryDTO | null = null;

  openEziologia = false;
  showEziologia = false;

  eziologiaReadOnly: boolean = false;
  lesioneReadOnly: boolean;
  valutazioneReadOnly: boolean = false;
  necessitaReadOnly: boolean;
  quadroReadOnly: boolean;
  settingReadOnly: boolean;
  complicanzeReadOnly: boolean;

  hasBeenOpenedEziologia: boolean = true;
  hasBeenOpenedLesione: boolean = false;
  hasBeenOpenedValutazione: boolean = false;
  hasBeenOpenedNecessita: boolean = false;
  hasBeenOpenedQuadro: boolean = false;
  hasBeenOpenedSetting: boolean = false;
  hasBeenOpenedComplicanze: boolean = false;

  protected readonly TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO = TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO;
  protected readonly ETipoScheda = ETipoScheda;
  isRientroNonCensito: boolean = false;
  isRientroCensito: boolean = false;

  private destroy$ = new Subject<void>();

  expandedPanels: { [key: string]: boolean } = {
    'eziologia': true,
    'lesione': false,
    'valutazione': false,
    'necessita': false,
    'quadro': false,
    'setting': false,
    'complicanze': false
  };
  private getCronologia$: Subscription;

  showTornaEditor: { [key: string]: boolean } = {
    valutazione: false,
    necessita: false,
    quadroNeuro: false
  };

  sezioneCronologiaAperta: string;

  showCronologiaBtnValutazione: boolean = false;
  showCronologiaBtnNecessita: boolean = false;
  showCronologiaBtnQuadroNeuro: boolean = false;

  constructor(
    public schedaRicoveroService: SchedaRicoveroService,
    private operatoreService: OperatoreService
  ) {
    this.isRientroNonCensito = this.schedaRicoveroService.getEventoRientroNonCensito();
    this.isRientroCensito = this.schedaRicoveroService.getEventoRientroCensito();
  }

  ngOnInit(): void {
    this.schedaRicoveroService.schedaDettagliata$
      .pipe(takeUntil(this.destroy$))
      .subscribe((value: SchedaRicoveroModel | null) => {
        if (!value) return;
        this.tipoEvento = value.evento.tipoEvento.idTipoEvento;
        this.isRientroNonCensito = value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_RIENTRO_NON_CENSITO.idTipoEvento 
        this.isRientroCensito = value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento;
        this.datiClinici = value.datiClinici;
        this.idReparto = value.scheda?.unitaOperativa?.idUnitaOperativa;
        this.dataRicovero = value.scheda?.dataRicovero as string;
        this.eziologiaReadOnly = this.isReadOnly(value, 'eziologia') || value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento;
        this.valutazioneReadOnly = this.isReadOnly(value, 'valutazione');
        this.lesioneReadOnly = this.isReadOnly(value) || value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento;
        this.necessitaReadOnly = this.isReadOnly(value);
        this.quadroReadOnly = this.isReadOnly(value);
        this.settingReadOnly = this.isReadOnly(value);
        this.complicanzeReadOnly = this.isReadOnly(value);
        this.idScheda = value.scheda.id;
      });
  }

  onEziologiaReady() {
    this.openEziologia = true;
  }

  openEziologiaPanel() {
    this.showEziologia = true;       // fa montare il componente
    this.openEziologia = false;      // resetta il flag
  }

  // quando premo su info della modale
  onCronologiaDatiInfoClick = (event: OutputHistoryDTO) => {

    this.schedaRicoveroService.cronologiaOpened$.next(true);

    switch (event.idTipoScheda) {
      case ETipoScheda.VALUTAZIONE_IN_INGRESSO:
        this.datiCronologiaValutazione = event;
        this.showTornaEditor['valutazione'] = true;
        break;
      case ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO:
        this.datiCronologiaNecessita = event;
        this.showTornaEditor['necessita'] = true;
        break;
      case ETipoScheda.QUADRO_NEUROLOGICO:
        this.datiCronologiaQuadroNeurologico = event;
        this.showTornaEditor['quadroNeuro'] = true;
        break;
      default:
        break;
    }
  }

  onBackOnEditor($event: MouseEvent, scheda: 'valutazione' | 'necessita' | 'quadroNeuro') {
    let panel: MatExpansionPanel | undefined;

    switch (scheda) {
      case 'valutazione':
        panel = this.valutazionePanel;
        this.datiCronologiaValutazione = null;
        break;
      case 'necessita':
        panel = this.necessitaPanel;
        this.datiCronologiaNecessita = null;
        break;
      case 'quadroNeuro':
        panel = this.quadroNeuroPanel;
        this.datiCronologiaQuadroNeurologico = null;
        break;
    }

    if (panel && panel.expanded) {
      $event.stopPropagation();
      $event.preventDefault();
    }
    this.sezioneCronologiaAperta = '';
    this.showTornaEditor[scheda] = false;

    const isAllShowTornaEditorHidden =  !this.showTornaEditor['valutazione'] &&
                                        !this.showTornaEditor['necessita'] &&
                                        !this.showTornaEditor['quadroNeuro']

    if (isAllShowTornaEditorHidden) {
      this.schedaRicoveroService.cronologiaOpened$.next(false)
    }
  }

  onOpened(accordion: string) {

    switch (accordion) {
      case 'eziologia':
        this.hasBeenOpenedEziologia = true;
        break;
      case 'lesione':
        this.hasBeenOpenedLesione = true;
        break;
      case 'valutazione':
        this.hasBeenOpenedValutazione = true;
        this.showCronologiaBtnValutazione = true;
        break;
      case 'necessita':
        this.hasBeenOpenedNecessita = true;
        this.showCronologiaBtnNecessita = true;
        break;
      case 'quadro':
        this.hasBeenOpenedQuadro = true;
        this.showCronologiaBtnQuadroNeuro = true;
        break;
      case 'setting':
        this.hasBeenOpenedSetting = true;
        break;
      case 'complicanze':
        this.hasBeenOpenedComplicanze = true;
        break;

      default:
        break;
    }

  }

  onClosed(accordion: string) {

    switch (accordion) {
      case 'valutazione':
        this.showCronologiaBtnValutazione = false;
        break;
      case 'necessita':
        this.showCronologiaBtnNecessita = false;
        break;
      case 'quadro':
        this.showCronologiaBtnQuadroNeuro = false;
        break;

      default:
        break;
    }

  }

  isReadOnly(value: SchedaRicoveroModel, field?: string): boolean {
    const corrispondenzaPresidi = value.codPresidioOspedaliero !== null && 
                                  value.codPresidioOspedaliero !== this.operatoreService.getOperatore().codLivello2Operatore;
    const isEventoChiuso = value.evento.stato?.descrizione.toUpperCase() === 'CHIUSO';
    const isNotOspedaliero = this.operatoreService.getOperatore().idRuolo !== IdOperatoreEnum.OSP;
                                  
    return corrispondenzaPresidi || isEventoChiuso || isNotOspedaliero;
  }

  onShowDimissione(show: boolean) {
    if (show) {
      // Se show è true, aggiungiamo DIMISSIONI se non è già presente
      this.menuItems = [SceltaSchedaEnum.GENERALI, SceltaSchedaEnum.CLINICI, SceltaSchedaEnum.DIMISSIONI];
      this.menuItemsChange.emit(this.menuItems);
    } else {
      // Se show è false, rimuoviamo DIMISSIONI se è presente
      this.menuItems = [SceltaSchedaEnum.GENERALI, SceltaSchedaEnum.CLINICI];
      this.menuItemsChange.emit(this.menuItems);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.getCronologia$?.unsubscribe();
    this.schedaRicoveroService.cronologiaOpened$.next(false);
  }

  onCronologiaOpened(event: boolean, sezione: string) {
    this.sezioneCronologiaAperta = sezione;
  }
}
