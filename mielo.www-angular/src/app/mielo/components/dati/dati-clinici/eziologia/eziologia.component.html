<form [formGroup]="eziologiaForm" class="box-dati mt-4">
  <div>

    <!-- Lesione midollare confermata e Traumatica -->
    <div class="row mb-4">
      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Lesione midollare confermata<span *ngIf="isRequired('lesioneMielicaConfermata')">*</span>
          </mat-label>
          <mat-radio-group formControlName="lesioneMielicaConfermata" class="d-flex">
            <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" 
              color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(eziologiaForm.get('lesioneMielicaConfermata'), option, $event)">
              {{ option.descrizione | capitalizeFirst}}
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Traumatica<span *ngIf="isRequired('lesioneTraumatica')">*</span>
          </mat-label>
          <mat-radio-group formControlName="lesioneTraumatica" class="d-flex">
            <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" 
              color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(eziologiaForm.get('lesioneTraumatica'), option, $event)">
              {{ option.descrizione | capitalizeFirst}}
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
    </div>

    <!-- Non traumatica e Altro -->
    <div class="row mb-4">
      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Non traumatica<span *ngIf="isRequired('nonTraumatica')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <mat-select formControlName="nonTraumatica" placeholder="Seleziona">
              <mat-option [value]="null"></mat-option>
              <mat-option *ngFor="let option of optionsNonTraumatica" [value]="option">
                {{ option.descrizione }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="eziologiaForm.get('nonTraumatica')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Altro<span *ngIf="isRequired('altroTipoLesione')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <input matInput
                   [matTooltip]="eziologiaForm.get('altroTipoLesione')?.value || ''"
                   formControlName="altroTipoLesione" placeholder="Inserisci" maxlength="250" />
            <mat-error *ngIf="eziologiaForm.get('altroTipoLesione')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Data trauma e Ora trauma -->
    <div class="row mb-4">

      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Data trauma<span *ngIf="isRequired('dataTrauma')">*</span>
          </mat-label>
          <mat-form-field>
            <input required matInput formControlName="dataTrauma" [max]="currentDate" [matDatepicker]="picker" placeholder="GG/MM/AAAA" (blur)="eziologiaForm.get('dataTrauma')?.updateValueAndValidity()">
            <mat-error *ngIf="getDateTimeErrorMessage(eziologiaForm.get('dataTrauma'))">
              {{ getDateTimeErrorMessage(eziologiaForm.get('dataTrauma')) }}
            </mat-error>
            <mat-datepicker-toggle matSuffix [for]="picker">
              <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
            </mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
        </div>
      </div>
      
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Ora trauma<span *ngIf="isRequired('oraTrauma')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <input matInput type="time" formControlName="oraTrauma" placeholder="--:--" />
            <button type="button" mat-icon-button matSuffix class="pointer-events-none" [disabled]="eziologiaForm.get('oraTrauma')?.disabled">
              <mat-icon class="d-flex">access_time</mat-icon>
            </button>
            <mat-error *ngIf="getDateTimeErrorMessage(eziologiaForm.get('oraTrauma'))">
              {{ getDateTimeErrorMessage(eziologiaForm.get('oraTrauma')) }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Tipologia trauma e Altro -->
    <div class="row mb-4 border-bottom">
      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Tipologia trauma<span *ngIf="isRequired('tipoTrauma')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <mat-select formControlName="tipoTrauma" placeholder="Seleziona">
              <mat-option [value]="null"></mat-option>
              <mat-option *ngFor="let option of optionsTipoTrauma" [value]="option">
                {{ option.descrizione }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="eziologiaForm.get('tipoTrauma')?.hasError('required')">
                {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Altro<span *ngIf="eziologiaForm.get('tipoTrauma')?.value?.descrizione === 'ALTRO'">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <input
                    [matTooltip]="eziologiaForm.get('altroTipoTrauma')?.value || ''"
                    matInput formControlName="altroTipoTrauma" placeholder="Inserisci"  maxlength="250"/>
            <mat-error *ngIf="eziologiaForm.get('altroTipoTrauma')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-2 col-md-2 ps-md-2 ml-3">
        <div class="form-field-container">
            <div class="d-flex align-items-center h-100">
                <button mat-button type="button" class="p-0 note-button" 
                  [disabled]="isNoteDisabled('tipoTrauma')" (mouseup)="openPopupNote('tipoTrauma')">
                  <svg class="icon icon-primary">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                  </svg>
                  <span class="ms-1">Note</span>
                </button>
            </div>
        </div>
    </div>
    </div>

    <!-- Sport e Altro -->
    <div class="row mb-4 border-bottom mt-2">
      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Sport<span *ngIf="isRequired('sport')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <mat-select formControlName="sport" placeholder="Seleziona">
              <mat-option [value]="null"></mat-option>
              <mat-option *ngFor="let option of optionsSport" [value]="option">
                {{ option.descrizione }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="eziologiaForm.get('sport')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Altro<span *ngIf="isRequired('altroSport')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <input matInput
                   [matTooltip]="eziologiaForm.get('altroSport')?.value || ''"
                   formControlName="altroSport" placeholder="Inserisci"  maxlength="250"/>
            <mat-error *ngIf="eziologiaForm.get('altroSport')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-2 col-md-2 ps-md-2 ml-3">
        <div class="form-field-container">
            <div class="d-flex align-items-center h-100">
                <button mat-button type="button" class="p-0 note-button" 
                  [disabled]="isNoteDisabled('sport')" (mouseup)="openPopupNote('sport')">
                  <svg class="icon icon-primary">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                  </svg>
                  <span class="ms-1">Note</span>
                </button>
            </div>
        </div>
    </div>
    </div>

    <!-- Caduta e Altro -->
    <div class="row mb-4 border-bottom mt-2">
      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Caduta<span *ngIf="isRequired('tipoCaduta')">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <mat-select formControlName="tipoCaduta" placeholder="Seleziona">
              <mat-option [value]="null"></mat-option>
              <mat-option *ngFor="let option of optionsTipoCaduta" [value]="option">
                {{ option.descrizione }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="eziologiaForm.get('tipoCaduta')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Altro<span *ngIf="eziologiaForm.get('tipoCaduta')?.value?.descrizione === 'ALTRO'">*</span>
          </mat-label>
          <mat-form-field appearance="outline">
            <input
                    [matTooltip]="eziologiaForm.get('altroTipoCaduta')?.value || ''"
                    matInput formControlName="altroTipoCaduta" placeholder="Inserisci"  maxlength="250"/>
            <mat-error *ngIf="eziologiaForm.get('altroTipoCaduta')?.hasError('required')">
              {{ ERROR_MESSAGE.REQUIRED }}
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="col-2 col-md-2 ps-md-2 ml-3">
        <div class="form-field-container">
            <div class="d-flex align-items-center h-100">
                <button mat-button type="button" class="p-0 note-button" 
                  [disabled]="isNoteDisabled('tipoCaduta')" (mouseup)="openPopupNote('tipoCaduta')">
                  <svg class="icon icon-primary">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                  </svg>
                  <span class="ms-1">Note</span>
                </button>
            </div>
        </div>
    </div>
    </div>

    <!-- Infortuni e suicidio -->
    <div class="row mb-4">
      <div class="col-12 col-md-4 pe-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Infortunio sul lavoro<span *ngIf="isRequired('infortunioLavoro')">*</span>
          </mat-label>
          <mat-radio-group formControlName="infortunioLavoro" class="d-flex">
            <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" 
              color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(eziologiaForm.get('infortunioLavoro'), option, $event)">
              {{ option.descrizione | capitalizeFirst}}
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
      <div class="col-12 col-md-4 px-md-2 mb-3 mb-md-0">
        <div class="form-field-container">
          <mat-label class="field-label">
            Infortunio domestico<span *ngIf="isRequired('infortunioDomestico')">*</span>
          </mat-label>
          <mat-radio-group formControlName="infortunioDomestico" class="d-flex">
            <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" 
              color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(eziologiaForm.get('infortunioDomestico'), option, $event)">
              {{ option.descrizione | capitalizeFirst}}
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
      <div class="col-12 col-md-4 ps-md-2">
        <div class="form-field-container">
          <mat-label class="field-label">
            Tentato suicidio-autolesione<span *ngIf="isRequired('suicidioAutolesione')">*</span>
          </mat-label>
          <mat-radio-group formControlName="suicidioAutolesione" class="d-flex">
            <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" 
              color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(eziologiaForm.get('suicidioAutolesione'), option, $event)">
              {{ option.descrizione | capitalizeFirst}}
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
    </div>

  </div>
</form>
