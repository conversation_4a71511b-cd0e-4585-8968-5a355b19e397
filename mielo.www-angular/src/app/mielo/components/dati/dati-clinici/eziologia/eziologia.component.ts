import { CommonModule } from "@angular/common";
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { MatNativeDateModule } from "@angular/material/core";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer } from "@angular/platform-browser";
import {distinctUntilChanged, forkJoin, merge, Subject, takeUntil} from "rxjs";
import { MaterialModule } from "../../../../../core/material.module";
import { TipoRicoveroEnum } from '../../../../../shared/enums/tipo-ricovero.enum';
import { SchedaEziologicaModel } from "../../../../../shared/interfaces/dati-clinici.interface";
import { DizionarioModel } from "../../../../../shared/interfaces/scheda-ricovero.interface";
import { CapitalizePipe } from "../../../../../shared/pipes/capitalize.pipe";
import { DecoderService } from "../../../../../shared/services/decoder.service";
import { ModalService } from '../../../../../shared/services/modal.service';
import { dateNotFutureValidator, getDateTimeErrorMessage, timeValidator } from '../../../../../shared/validators/date-time.validator';
import { DatiCliniciService } from "../../../../services/dati-clinici.service";
import { SchedaRicoveroService } from "../../../../services/scheda-ricovero.service";
import { DialogLesioneConfermataComponent } from "../dialog-lesione-confermata/dialog-lesione-confermata.component";
import {ERROR_MESSAGE} from "../../../../../shared/enums/enum";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
  selector: 'app-eziologia',
  standalone: true,
  imports: [
    MaterialModule,
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CapitalizePipe
  ],
  templateUrl: './eziologia.component.html',
  styleUrl: './eziologia.component.scss'
})
export class EziologiaComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;

  @Input() readOnly: boolean = false;
  @Input() idReparto: number;
  @Output() formInitialized = new EventEmitter<void>();

  today: Date = new Date();
  get currentDate(): Date {
    return this.today;
  }
  formReady: boolean = false;
  datiEziologia: SchedaEziologicaModel;

  optionsBoolean: Array<DizionarioModel>;
  optionsNonTraumatica: Array<DizionarioModel>;
  optionsTipoTrauma: Array<DizionarioModel>;
  optionsSport: Array<DizionarioModel>;
  optionsTipoCaduta: Array<DizionarioModel>;

  eziologiaForm: FormGroup;

  get formGroup(): FormGroup {
    return this.eziologiaForm;
  }

  constructor(
    private fb: FormBuilder,
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    public datiCliniciService: DatiCliniciService,
    private decoderService: DecoderService,
    private modalService: ModalService,
    private dialog: MatDialog,
    private schedaRicoveroService: SchedaRicoveroService
  ) {
    this.iconRegistry.addSvgIconLiteral('it-calendar', this.sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

    this.eziologiaForm = this.fb.group({
      lesioneMielicaConfermata: [null, Validators.required],
      lesioneTraumatica: [null],
      nonTraumatica: [null],
      altroTipoLesione: [null],
      dataTrauma: [null, [dateNotFutureValidator(), Validators.required]],
      oraTrauma: [null, [timeValidator(() => this.eziologiaForm?.get('dataTrauma')?.value)]],
      tipoTrauma: [null],
      altroTipoTrauma: [null],
      notaTipoTrauma: [null],
      sport: [null],
      altroSport: [null],
      notaSport: [null],
      tipoCaduta: [null],
      altroTipoCaduta: [null],
      noteTipoCaduta: [null],
      infortunioLavoro: [null],
      infortunioDomestico: [null],
      suicidioAutolesione: [null]
    });
  }

  ngOnInit() {
    this.initializeForm();
    this.setupFormSubscriptions();

    // Aggiorna la validità di oraTrauma ogni volta che cambia dataTrauma
    this.eziologiaForm.get('dataTrauma')?.valueChanges.subscribe(() => {
      this.eziologiaForm.get('oraTrauma')?.updateValueAndValidity();
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm() {
    forkJoin({
      boolean: this.decoderService.getDcodBoolean(),
      nonTraumatica: this.decoderService.getDcodNonTraumatica(),
      tipoTrauma: this.decoderService.getDcodTipoTrauma(),
      sport: this.decoderService.getDcodSport(),
      caduta: this.decoderService.getDcodCaduta(),
    }).pipe(
      takeUntil(this.destroy$)
    ).subscribe(options => {
      this.optionsBoolean = options.boolean;
      this.optionsNonTraumatica = options.nonTraumatica;
      this.optionsTipoTrauma = options.tipoTrauma;
      this.optionsSport = options.sport;
      this.optionsTipoCaduta = options.caduta;

      const dati = this.datiCliniciService.getDatiEziologiaValue();
      if (dati) {
        this.datiEziologia = dati;
        this.populateForm(dati);
      }

      this.formReady = true;
      this.formInitialized.emit();

      if (this.readOnly) {
        Object.keys(this.eziologiaForm.controls).forEach(controlName => {
          if (!controlName.toLowerCase().includes('nota') || !controlName.toLowerCase().includes('note')) {
            this.eziologiaForm.get(controlName)?.disable({ emitEvent: false });
          }
        });
      } else {
        this.manageEziologiaForm(false);
      }
    });
  }

  private setupFormSubscriptions() {
    if (!this.readOnly) {
      this.eziologiaForm.valueChanges.pipe(
        distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
        takeUntil(this.destroy$)
      ).subscribe(() => {
        const formValue = this.eziologiaForm.getRawValue();
        this.updateDatiEziologia(formValue);
      });

      this.manageAltroChanges();
      this.setupLesioneMielicaSubscription();
      this.setupLesioneTraumaticaSubscription();
    }
  }

  private updateDatiEziologia(formValue: any) {
    const convertBooleanValue = (val: DizionarioModel | null): boolean | null => {
      if (!val || !val.descrizione) return null;
      if (val.descrizione.toUpperCase() === 'SI') return true;
      if (val.descrizione.toUpperCase() === 'NO') return false;
      return null;
    };

    this.datiCliniciService.setDatiEziologia({
      ...formValue,
      idScheda: this.datiEziologia.idScheda,
      nomeScheda: this.datiEziologia.nomeScheda,
      copiaEventoPrecedente: this.datiEziologia.copiaEventoPrecedente,
      tipoLesione: this.eziologiaForm.get('nonTraumatica')?.value,
      lesioneMielicaConfermata: convertBooleanValue(this.eziologiaForm.get('lesioneMielicaConfermata')?.value),
      lesioneTraumatica: convertBooleanValue(this.eziologiaForm.get('lesioneTraumatica')?.value),
      infortunioDomestico: convertBooleanValue(this.eziologiaForm.get('infortunioDomestico')?.value),
      infortunioLavoro: convertBooleanValue(this.eziologiaForm.get('infortunioLavoro')?.value),
      suicidioAutolesione: convertBooleanValue(this.eziologiaForm.get('suicidioAutolesione')?.value),
      dataOraTrauma: this.manageDataOraToSave(formValue.dataTrauma)
    });

    this.eziologiaForm.updateValueAndValidity()
    this.datiCliniciService.setDatiEziologiaValido(this.eziologiaForm.valid);
  }

  private setupLesioneMielicaSubscription() {
    this.eziologiaForm.get('lesioneMielicaConfermata')?.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe((value: DizionarioModel) => {
      if (value && value.descrizione === 'NO') {
        this.openPopupLesioneConfermata();
      } else {
        this.manageEziologiaForm(true);
      }
    });
  }

  private setupLesioneTraumaticaSubscription() {
    this.eziologiaForm.get('lesioneTraumatica')?.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.manageEziologiaForm(true);
    });
  }

  // NOTE: Gestito con il setValue perché con il patchValue non funziona.
  populateForm(val: SchedaEziologicaModel) {
    this.populateBooleanValues(val);
    this.eziologiaForm.get('dataTrauma')?.setValue(this.manageDataToShow(val.dataOraTrauma) ?? null, { emitEvent: false });
    this.eziologiaForm.get('oraTrauma')?.setValue(this.manageOraToShow(val.dataOraTrauma), { emitEvent: false });
    this.populateDizionari(val);
    this.populateAltroAndNote(val);
  }

  populateDizionari(val: SchedaEziologicaModel) {

    const findOption = (options: DizionarioModel[], diz: DizionarioModel | null | undefined): DizionarioModel | null => {
      if (!diz || !diz.idDizionario) return null;
      return options.find(opt => opt.idDizionario === diz.idDizionario) ?? null;
    };

    this.eziologiaForm.get('nonTraumatica')?.setValue(findOption(this.optionsNonTraumatica, val.tipoLesione), { emitEvent: false });
    this.eziologiaForm.get('tipoTrauma')?.setValue(findOption(this.optionsTipoTrauma, val.tipoTrauma), { emitEvent: false });
    this.eziologiaForm.get('sport')?.setValue(findOption(this.optionsSport, val.sport), { emitEvent: false });
    this.eziologiaForm.get('tipoCaduta')?.setValue(findOption(this.optionsTipoCaduta, val.tipoCaduta), { emitEvent: false });

  }

  populateAltroAndNote(val: SchedaEziologicaModel) {
    this.eziologiaForm.get('altroTipoLesione')?.setValue(val.altroTipoLesione, { emitEvent: false });
    this.eziologiaForm.get('altroTipoTrauma')?.setValue(val.altroTipoTrauma, { emitEvent: false });
    this.eziologiaForm.get('altroSport')?.setValue(val.altroSport, { emitEvent: false });
    this.eziologiaForm.get('altroTipoCaduta')?.setValue(val.altroTipoCaduta, { emitEvent: false });
    this.eziologiaForm.get('notaTipoTrauma')?.setValue(val.notaTipoTrauma, { emitEvent: false });
    this.eziologiaForm.get('notaSport')?.setValue(val.notaSport, { emitEvent: false });
    this.eziologiaForm.get('noteTipoCaduta')?.setValue(val.noteTipoCaduta, { emitEvent: false });
  }

  populateBooleanValues(val: SchedaEziologicaModel): void {
    const booleanFields: Array<keyof SchedaEziologicaModel> = [
      'lesioneMielicaConfermata',
      'lesioneTraumatica',
      'infortunioLavoro',
      'infortunioDomestico',
      'suicidioAutolesione'
    ];

    booleanFields.forEach(field => {
      const formControl = this.eziologiaForm.get(field as string);
      const value = val[field];

      if (formControl !== null && formControl !== undefined) {
        if (value === true) {
          formControl.setValue(this.optionsBoolean[0], { emitEvent: false });
        } else if (value === false) {
          formControl.setValue(this.optionsBoolean[1], { emitEvent: false });
        } else {
          formControl.setValue(null, { emitEvent: false });
        }
      }
    });
  }

  // Metodo per inserire dati arrivati da BE nel form control data trauma
  manageDataToShow(dataCompleta: Date): Date | null {
    // Estrai solo la data (oggetto Date)
    if (dataCompleta) {
      let dataSola: Date;
      dataSola = new Date(dataCompleta)
      dataSola.setHours(0, 0, 0, 0); // azzeri ora/min/sec/ms
      return dataSola;
    } else return null
  }

  manageOraToShow(dataCompleta: Date): string | null {
    if (!dataCompleta) return null;
    dataCompleta = new Date(dataCompleta);
    const ore = dataCompleta.getHours();
    const minuti = dataCompleta.getMinutes();

    if (ore === 0 && minuti === 0 && dataCompleta.getSeconds() === 0) {
      return null;
    }

    const oreStr = ore.toString().padStart(2, '0');
    const minutiStr = minuti.toString().padStart(2, '0');
    return `${oreStr}:${minutiStr}`;
  }

  // Metodo per inserire dati digitati in data trauma e ora trauma
  // in un unico oggetto Date da passare poi al BE
  manageDataOraToSave(data: Date): number | null {
    if (!data) return null;

    const ora = this.eziologiaForm.get('oraTrauma')?.value;
    const dataConOra = new Date(data);

    if (ora && ora.includes(':')) {
      const [ore, minuti] = ora.split(':').map(Number);
      if (!isNaN(ore) && !isNaN(minuti)) {
        dataConOra.setHours(ore, minuti, 1, 1); //mettendo 1 secondo controllo l ora per mostrarla o no
      }
    } else {
      dataConOra.setHours(0, 0, 0, 0); //se 0 secondi non mostro  l ora
    }

    return dataConOra.getTime();
  }

  private manageAltroChanges() {
    merge(
      this.eziologiaForm.get('nonTraumatica')?.valueChanges || [],
      this.eziologiaForm.get('tipoTrauma')?.valueChanges || [],
      this.eziologiaForm.get('sport')?.valueChanges || [],
      this.eziologiaForm.get('tipoCaduta')?.valueChanges || []
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.manageEnableAltro();
      this.manageSport();
    });
  }

  private manageSport() {
    const traumaValue = this.eziologiaForm.get('tipoTrauma')?.value;
    traumaValue && traumaValue.idDizionario === 56 ? 
    this.enableControls(['sport'], true) : 
      this.disableControls(['sport']);
    this.manageEnableAltro();
  }

  // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno 
  isNoteDisabled(controlName: string): boolean {
    const control = this.eziologiaForm.get(controlName);
    return !control?.value && !!control?.disabled;
  }  

  isRequired(controlName: string): boolean {
    // sono costretto a fare questo controllo perché non è gestito correttamente il formControl.disabled
    if (this.readOnly) {
      if (this.eziologiaForm.get('lesioneMielicaConfermata')?.value?.descrizione === 'SI') {
        const tipoTrauma = this.eziologiaForm.get('tipoTrauma')?.value;
        const isSport = tipoTrauma?.idDizionario === 56;

        this.eziologiaForm.get('lesioneTraumatica')?.setValidators([Validators.required]);

        if (this.eziologiaForm.get('lesioneTraumatica')?.value?.descrizione === 'SI') {
          if (this.idReparto === TipoRicoveroEnum.RIABILITAZIONE) {
            [
              'oraTrauma', 'tipoTrauma', 'tipoCaduta',
              'infortunioLavoro', 'infortunioDomestico', 'suicidioAutolesione'
            ].forEach(controlName => {
              this.eziologiaForm.get(controlName)?.setValidators([Validators.required]);
            })
          } else {
            ['dataTrauma', 'oraTrauma', 'tipoTrauma', 'tipoCaduta',
              'infortunioLavoro', 'infortunioDomestico', 'suicidioAutolesione'].forEach(controlName => {
              this.eziologiaForm.get(controlName)?.setValidators([Validators.required]);
            })
            if (this.eziologiaForm.get('sport')?.value?.descrizione === 'ALTRO') {
              this.eziologiaForm.get('altroSport')?.setValidators([Validators.required]);
            }
            isSport ? this.eziologiaForm.get('sport')?.setValidators([Validators.required]) : null
          }
        } else {
          if (this.eziologiaForm.get('nonTraumatica')?.value?.descrizione === 'ALTRO') {
            this.eziologiaForm.get('altroTipoLesione')?.setValidators([Validators.required]);
          }
          this.eziologiaForm.get('nonTraumatica')?.setValidators([Validators.required]);
          this.eziologiaForm.get('dataTrauma')?.clearValidators();
        }
      }
    }

    const control = this.eziologiaForm.get(controlName);

    return this.datiCliniciService.isRequired(control);
  }

  manageEziologiaForm(resetValue: boolean) {
    const mielica = this.eziologiaForm.get('lesioneMielicaConfermata')?.value;
    const traumatica = this.eziologiaForm.get('lesioneTraumatica')?.value;
    const tipoTrauma = this.eziologiaForm.get('tipoTrauma')?.value;
    const isSport = tipoTrauma?.idDizionario === 56;

    // --- Se lesione mielica è null (non valorizzata) ---
    if (mielica === null || mielica === undefined || (mielica.descrizione && mielica.descrizione.toUpperCase() === 'NO')) {
      this.disableAllExcept(['lesioneMielicaConfermata']);
      this.datiCliniciService.resetNoteFields(this.eziologiaForm, ['notaTipoTrauma', 'notaSport', 'notaTipoCaduta']);
      return;
    }

    // --- Se lesione mielica SI ---
    this.enableControls(['lesioneTraumatica'], true);

    // Forza la coerenza del valore traumaticaDescrizione
    let traumaticaDescrizione: string | null = null;
    if (traumatica && traumatica.descrizione) {
      traumaticaDescrizione = traumatica.descrizione.toUpperCase();
    } else if (traumatica === true) {
      traumaticaDescrizione = 'SI';
    } else if (traumatica === false) {
      traumaticaDescrizione = 'NO';
    }

    if (!traumaticaDescrizione || traumaticaDescrizione === 'NO') {
      this.disableControls([
        'nonTraumatica', 'altroTipoLesione', 'altroTipoTrauma', 'altroSport',
        'dataTrauma', 'oraTrauma', 'tipoTrauma', 'sport', 'tipoCaduta', 'altroCaduta',
        'infortunioLavoro', 'infortunioDomestico', 'suicidioAutolesione','noteTipoCaduta','notaSport','notaTipoTrauma'
      ], resetValue);
      this.datiCliniciService.resetNoteFields(this.eziologiaForm, ['notaTipoTrauma', 'notaSport', 'notaTipoCaduta']);
    }

    if (traumaticaDescrizione === 'SI') {
      if (this.idReparto === TipoRicoveroEnum.RIABILITAZIONE) {
        this.enableControls(['dataTrauma'], true);
        this.enableControls([
          'oraTrauma', 'tipoTrauma', 'tipoCaduta',
          'infortunioLavoro', 'infortunioDomestico', 'suicidioAutolesione'
        ]);
        (tipoTrauma && isSport) ? this.enableControls(['sport']) : this.disableControls(['sport']);
      } else {
        this.enableControls([
          'dataTrauma', 'oraTrauma', 'tipoTrauma', 'tipoCaduta',
          'infortunioLavoro', 'infortunioDomestico', 'suicidioAutolesione'
        ], true);
        (tipoTrauma && isSport) ? this.enableControls(['sport'], true) : this.disableControls(['sport']);
      }

      this.disableControls(['nonTraumatica', 'altroTipoLesione'], resetValue);
    }

    if (traumaticaDescrizione === 'NO') {
      this.enableControls(['nonTraumatica'], true);
      this.disableControls([
        'dataTrauma', 'oraTrauma', 'tipoTrauma', 'sport', 'tipoCaduta',
        'infortunioLavoro', 'infortunioDomestico', 'suicidioAutolesione'
      ], resetValue);
      this.datiCliniciService.resetNoteFields(this.eziologiaForm, ['notaTipoTrauma', 'notaSport', 'notaTipoCaduta']);
    }

    this.manageEnableAltro();
  }

  // Attiva e assegna validators obbligatori
  private enableControls(controls: string[], required: boolean = false) {
    controls.forEach(name => {
      const ctrl = this.eziologiaForm.get(name);
      ctrl?.enable({ emitEvent: false });
      if (required) {
        // Applica i validator custom dove serve!
        if (name === 'dataTrauma') {
          ctrl?.setValidators([dateNotFutureValidator(), Validators.required]);
        } else if (name === 'oraTrauma') {
          ctrl?.setValidators([timeValidator(() => this.eziologiaForm?.get('dataTrauma')?.value), Validators.required]);
        } else {
          ctrl?.setValidators([Validators.required]);
        }
        ctrl?.markAsUntouched();
      } else {
        ctrl?.clearValidators();
      }
      ctrl?.updateValueAndValidity({ emitEvent: false });
    });
  }

  // Disattiva e rimuove validators
  private disableControls(controls: string[], resetValue = true) {
    controls.forEach(name => {
      const ctrl = this.eziologiaForm.get(name);
      ctrl?.disable({ emitEvent: false });
      if (resetValue) {
        ctrl?.setValue(null, { emitEvent: false });
      }
      ctrl?.clearValidators();
      ctrl?.updateValueAndValidity({ emitEvent: false });
    });
  }

  // Lascia attivi solo certi campi
  private disableAllExcept(activeFields: string[]) {
    Object.keys(this.eziologiaForm.controls).forEach(controlName => {
      if (!activeFields.includes(controlName)) {
        this.disableControls([controlName]);
      }
    });
  }

  // Logica "ALTRO" come da specifica
  private manageEnableAltro(): void {
    const checkAltro = (mainControl: string, altroControl: string) => {
      const main = this.eziologiaForm.get(mainControl)?.value;
      const altro = this.eziologiaForm.get(altroControl);

      if (main?.descrizione?.toUpperCase() === 'ALTRO') {
        altro?.enable({ emitEvent: false });
        altro?.setValidators([Validators.required]);
        altro?.markAsUntouched();
      } else {
        altro?.disable({ emitEvent: false });
        altro?.setValue(null, { emitEvent: false });
        altro?.clearValidators();
      }
      altro?.updateValueAndValidity({ emitEvent: false });
    };

    checkAltro('nonTraumatica', 'altroTipoLesione');
    checkAltro('tipoTrauma', 'altroTipoTrauma');
    checkAltro('sport', 'altroSport');
    checkAltro('tipoCaduta', 'altroTipoCaduta');
  }

  openPopupNote(field: string) {
    let noteField = 'nota' + field.charAt(0).toUpperCase() + field.slice(1);
    if (field === 'tipoCaduta') {
      noteField = 'note' + field.charAt(0).toUpperCase() + field.slice(1);
    }

    const noteControl = this.eziologiaForm.get(noteField);
    noteControl?.enable()
    const control = this.eziologiaForm.get(field);

    if (noteControl) {
      const disableNote = this.readOnly || control?.disabled;
      let configNote = this.modalService.createNoteParam(noteControl.value, disableNote);

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != noteControl.value) {
          noteControl.setValue(res);
          noteControl.markAsDirty();
          noteControl.updateValueAndValidity();
        }
      });

      this.datiCliniciService.adjustDialogHeight();
    }
  }

  openPopupLesioneConfermata(): void {

    const dialogRef: MatDialogRef<any> = this.dialog.open(DialogLesioneConfermataComponent, {
      width: '20rem',
      disableClose: true,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(value => {
      if (value === true) {
        // Recupera la scheda corrente dal service
        const schedaCorrente = this.schedaRicoveroService.getSchedaCorrente();
        if (schedaCorrente) {
          // Crea una copia della scheda e imposta schedaAttiva a false
          const schedaAggiornata = {
            ...schedaCorrente,
            scheda: {
              ...schedaCorrente.scheda,
              schedaAttiva: false
            }
          };
          // Aggiorna la scheda
          this.schedaRicoveroService.updateSchedaPaziente(schedaAggiornata);
        }
      } else if (value === false) {
        this.eziologiaForm.get('lesioneMielicaConfermata')?.setValue(null);
      }
    })
  }

  getDateTimeErrorMessage(control: AbstractControl | null): string {
    return getDateTimeErrorMessage(control);
  }
}
