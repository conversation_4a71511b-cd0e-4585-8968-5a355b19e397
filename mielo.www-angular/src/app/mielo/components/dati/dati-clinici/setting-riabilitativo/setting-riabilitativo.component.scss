:host ::ng-deep {
    .mat-mdc-radio-button .mdc-form-field {
        display: flex;
        align-items: center !important;
        height: 100%;
        margin-left: 0px;
        margin-bottom: 0px;
        line-height: normal;
    }

    .mat-icon-button {
        width: 40px;
        height: 40px;
        line-height: 40px;
    }
    
    .mat-form-field-wrapper {
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .mat-form-field-appearance-outline .mat-form-field-outline {
        background-color: white;
    }

    .mat-form-field-disabled .mat-datepicker-toggle .mat-icon,
    .mat-form-field-disabled .mat-icon-button .mat-icon,
    .mat-mdc-form-field-disabled .mat-datepicker-toggle .mat-icon,
    .mat-mdc-form-field-disabled .mat-icon-button .mat-icon {
        color: #666666 !important;
        fill: #666666 !important;
    }
}

.setting-riabilitativo-container {
    background-color: #F5F6FA !important;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.setting-title {
    color: #003354 !important;
    font-size: 24px;
    font-weight: 600;
}

.label-container {
    margin-bottom: 0.5rem;
}

.field-label {
    font-family: 'Titillium Web', sans-serif;
    font-weight: 600;
    font-size: 18px;
    line-height: 23px;
    color: #003354;
}

.input-container {
    max-width: 100%;
}

.w-100 {
    width: 100%;
}


input {
    padding: 0 !important;
}

:host {
    .form-field-container {
        display: flex;
        flex-direction: column;
    }

    .radio-group-container {
        padding: 0;
        min-height: 40px;
        display: flex;
        align-items: center;
        width: 100%;
        background-color: transparent;
        border: none;

        .mat-radio-group {
            display: flex;
            gap: 2rem;
        }
    }

    ::ng-deep {
        .mat-radio-button {
            .mat-radio-container {
                height: 16px;
                width: 16px;
            }

            .mat-radio-outer-circle,
            .mat-radio-inner-circle {
                height: 16px;
                width: 16px;
            }

            .mat-radio-label {
                display: flex;
                align-items: center;
            }

            .mat-radio-label-content {
                padding-left: 8px;
                line-height: normal;
                display: flex;
                align-items: center;
            }
        }

        .mat-form-field-appearance-outline {
            .mat-form-field-wrapper {
                margin: 0;
                padding: 0;
                width: 100%;
            }

            .mat-form-field-flex {
                background-color: #ffffff !important;
                min-height: 40px;
                padding: 0 0.75rem !important;
                position: relative;
            }

            .mat-form-field-outline {
                color: #e0e0e0;
                opacity: 0.5;
            }

            .mat-form-field-outline-start,
            .mat-form-field-outline-end,
            .mat-form-field-outline-gap {
                border-width: 1px !important;
            }

            &.mat-focused .mat-form-field-outline-thick {
                color: #4caf50;
            }

            .mat-form-field-infix {
                padding: 0.5em 0;
                border-top: none;
                width: calc(100% - 24px);
                background-color: #ffffff;
            }

            .mat-datepicker-toggle {
                position: relative;
                width: 24px;
                height: 24px;

                .mat-icon {
                    color: #666666;
                    fill: #666666;
                }
            }

            .mat-icon-button {
                width: 24px;
                height: 24px;
                line-height: 24px;

                .mat-icon {
                    color: #666666;
                    fill: #666666;
                    width: 18px;
                    height: 18px;
                    font-size: 18px;
                    line-height: 18px;
                }
            }
        }

        .mat-mdc-form-field-flex {
            display: inline-flex;
            align-items: baseline;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
        }

        .timepicker-overlay {
            z-index: 1000;
        }

        .mat-datepicker-toggle .mat-icon,
        .mat-icon-button .mat-icon {
            color: #003354 !important;
            fill: #003354 !important;
        }

        .mat-form-field-disabled .mat-datepicker-toggle .mat-icon,
        .mat-form-field-disabled .mat-icon-button .mat-icon,
        .mat-mdc-form-field-disabled .mat-datepicker-toggle .mat-icon,
        .mat-mdc-form-field-disabled .mat-icon-button .mat-icon {
            color: #666666 !important;
            fill: #666666 !important;
        }
    }
} 