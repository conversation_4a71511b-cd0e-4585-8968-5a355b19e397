import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule, MatIconRegistry } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { DomSanitizer } from '@angular/platform-browser';
import { BehaviorSubject, distinctUntilChanged, forkJoin, Subscription } from 'rxjs';
import { filter, map, switchMap, tap } from 'rxjs/operators';
import { TipoRicoveroEnum } from '../../../../../shared/enums/tipo-ricovero.enum';
import { SchedaSettingRiabilitativoModel } from '../../../../../shared/interfaces/dati-clinici.interface';
import { DizionarioModel } from '../../../../../shared/interfaces/scheda-ricovero.interface';
import { StruttureRiabilitativeModel, UnitaOperativaModel } from '../../../../../shared/interfaces/shared/shared.interface';
import { CapitalizePipe } from "../../../../../shared/pipes/capitalize.pipe";
import { DecoderService } from '../../../../../shared/services/decoder.service';
import { ModalService } from '../../../../../shared/services/modal.service';
import { dateNotFutureValidator, getDateTimeErrorMessage } from '../../../../../shared/validators/date-time.validator';
import { DatiCliniciService } from '../../../../services/dati-clinici.service';
import moment from "moment";
import {SchedaRicoveroService} from "../../../../services/scheda-ricovero.service";
import {ERROR_MESSAGE} from "../../../../../shared/enums/enum";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
  selector: 'app-setting-riabilitativo',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatExpansionModule,
    CapitalizePipe
  ],
  templateUrl: './setting-riabilitativo.component.html',
  styleUrls: ['./setting-riabilitativo.component.scss']
})
export class SettingRiabilitativoComponent implements OnInit, OnDestroy, AfterViewInit {

  @Output() expandedChange = new EventEmitter<boolean>();
  @Output() showDimissione = new EventEmitter<boolean>();
  @Input() idReparto: number;
  @Input() readOnly: boolean = false;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;
  protected readonly moment = moment;

  formReady: boolean = false;
  isAcuti: boolean = false;

  datiSettingRiabilitativo: SchedaSettingRiabilitativoModel;

  optionsUnitaSpinale: Array<UnitaOperativaModel> = [];
  optionsStrutture: Array<StruttureRiabilitativeModel> = [];
  filteredStrutture: Array<StruttureRiabilitativeModel> = [];
  strutturaSearchText: string = '';
  optionsTrasferimentoSettingRiabilitativo: Array<DizionarioModel> = [];
  optionsBoolean: Array<DizionarioModel> = [];

  settingRiabilitativoForm: FormGroup;
  dataRicovero: moment.Moment | null

  get formGroup(): FormGroup {
    return this.settingRiabilitativoForm;
  }

  private formReady$ = new BehaviorSubject<boolean>(false);
  private viewInitialized$ = new BehaviorSubject<boolean>(false);

  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
    private decoderService: DecoderService,
    public datiCliniciService: DatiCliniciService,
    private modalService: ModalService,
    private schedaRicoverService: SchedaRicoveroService
  ) {
    this.iconRegistry.addSvgIconLiteral('it-calendar', this.sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

    this.initForm();
  }

  private initForm(): void {
    this.settingRiabilitativoForm = this.fb.group({
      trasferimentoSettingRiabilitativo: [null, Validators.required],
      settingRiabilitativo: [{ value: '', disabled: true }],
      dataTransferimento: [{ value: null, disabled: true }, [dateNotFutureValidator()]],
      strutturaDiDestinazione: [{ value: '', disabled: true }],
      note: ['']
    });
  }

  ngOnInit(): void {
    this.isAcuti = this.idReparto === TipoRicoveroEnum.ACUTI;

    this.loadOptionsAndData();
    this.setupFormViewInitialization();
    this.setupTrasferimentoChanges();

    this.schedaRicoverService.schedaCreation$.subscribe(dati => {
      if (dati && dati.scheda.dataRicovero) {
        this.dataRicovero = moment(dati.scheda.dataRicovero);
      } else {
        this.dataRicovero = null;
      }
    }).unsubscribe();
  }

  private loadOptionsAndData(): void {
    const loadOptions$ = forkJoin({
      unitaOperative: this.decoderService.getDcodUnitaOperative(),
      boolean: this.decoderService.getDcodBoolean(),
      strutture: this.decoderService.getDcodStrutturelivello2(),
      trasferimentoSettingRiabilitativo: this.decoderService.getDcodTrasferimentoSettingRiabilitativo()
    });

    const subscription = loadOptions$
      .pipe(
        tap(options => {
          this.optionsUnitaSpinale = options.unitaOperative;
          this.optionsStrutture = options.strutture || [];
          this.filteredStrutture = [...this.optionsStrutture]; // Inizializza con tutte le strutture
          this.optionsTrasferimentoSettingRiabilitativo = options.trasferimentoSettingRiabilitativo;
          this.optionsBoolean = options.boolean;

          const dati = this.datiCliniciService.getDatiSettingRiabilitativoValue();
          if (dati) {
            this.datiSettingRiabilitativo = dati;
            this.populateForm(dati);
          }

          this.formReady = true;
          this.formReady$.next(true);
        }),
        switchMap(() => {
          return this.settingRiabilitativoForm.valueChanges.pipe(
            distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
            tap(() => this.handleFormChanges())
          );
        })
      )
      .subscribe();

    this.subscriptions.push(subscription);
  }

  private setupFormViewInitialization(): void {
    const viewInitSubscription = this.viewInitialized$
      .pipe(
        filter(Boolean),
        map(() => this.datiCliniciService.getDatiSettingRiabilitativoValue()),
        filter((dati): dati is SchedaSettingRiabilitativoModel => !!dati),
        tap(dati => this.populateForm(dati))
      )
      .subscribe();

    this.subscriptions.push(viewInitSubscription);
  }

  private setupTrasferimentoChanges(): void {
    const trasferimentoControl = this.settingRiabilitativoForm.get('trasferimentoSettingRiabilitativo');
    if (!trasferimentoControl) return;

    const trasferimentoSub = trasferimentoControl.valueChanges.pipe(
      distinctUntilChanged(),
      tap(value => this.handleTrasferimentoChange(value))
    ).subscribe();

    this.subscriptions.push(trasferimentoSub);
  }

  private handleTrasferimentoChange(value: DizionarioModel): void {
    const isSi = value?.descrizione === 'SI';
    const isNo = value?.descrizione === 'NO';

    const controls = {
      settingRiabilitativo: this.settingRiabilitativoForm.get('settingRiabilitativo'),
      dataTransferimento: this.settingRiabilitativoForm.get('dataTransferimento'),
      strutturaDiDestinazione: this.settingRiabilitativoForm.get('strutturaDiDestinazione')
    };

    if (isSi) {
      Object.values(controls).forEach(control => {
        control?.enable();
        if (control === controls.dataTransferimento) {
          control?.addValidators([Validators.required, dateNotFutureValidator()]);
        } else {
          control?.setValidators([Validators.required]);
        }
        control?.updateValueAndValidity({ emitEvent: false });
      });

      const currentValues = this.settingRiabilitativoForm.getRawValue();
      if (currentValues.dataTransferimento || currentValues.strutturaDiDestinazione) {
        this.settingRiabilitativoForm.patchValue(currentValues, { emitEvent: false });
      }

      if (this.isAcuti) {
        this.showDimissione.emit(false);
      }
    } else if (isNo) {
      Object.values(controls).forEach(control => {
        control?.disable();
        // control?.clearValidators();
        control?.setValue('');
        control?.updateValueAndValidity({ emitEvent: false });
      });

      if (this.isAcuti) {
        this.showDimissione.emit(true);
      }
    } else {
      Object.values(controls).forEach(control => {
        control?.disable();
        control?.clearValidators();
        control?.setValue('');
        control?.updateValueAndValidity({ emitEvent: false });
      });

      if (this.isAcuti) {
        this.showDimissione.emit(false);
      }
    }

    this.settingRiabilitativoForm.updateValueAndValidity({ emitEvent: false });
  }

  private applyReadOnly(): void {
    Object.keys(this.settingRiabilitativoForm.controls).forEach(controlName => {
      if (!controlName.toLowerCase().includes('nota')) {
        this.settingRiabilitativoForm.get(controlName)?.disable({ emitEvent: false });
      }
    });
  }

  ngAfterViewInit(): void {
    this.viewInitialized$.next(true);
    this.settingRiabilitativoForm.updateValueAndValidity();
  }

  handleFormChanges(): void {
    const formValue = this.settingRiabilitativoForm.getRawValue();
    const trasferimentoValue = formValue.trasferimentoSettingRiabilitativo;

    this.showDimissione.emit(this.isAcuti && !trasferimentoValue);

    const idUnitaOperativa =
      formValue.settingRiabilitativo === 'UNITA_SPINALE' ? 4 :
        formValue.settingRiabilitativo === 'REPARTO_DI_RIABILITAZIONE' ? 5 :
          undefined;

    const convertBooleanValue = (val: DizionarioModel | null): boolean | null => {
      if (!val || !val.descrizione) return null;
      if (val.descrizione.toUpperCase() === 'SI') return true;
      if (val.descrizione.toUpperCase() === 'NO') return false;
      return null;
    };

    const datiDaSalvare: SchedaSettingRiabilitativoModel = {
      ...this.datiSettingRiabilitativo,
      trasferimentoSettingRiabilitativo: convertBooleanValue(formValue.trasferimentoSettingRiabilitativo),
      settingRiabilitativo: (idUnitaOperativa && this.findUnitaOperativa(idUnitaOperativa)) || null,
      strutturaDestinazione: this.findStruttura(formValue.strutturaDiDestinazione) || null,
      dataTrasferimento: formValue.dataTransferimento ? moment(formValue.dataTransferimento).format('YYYY-MM-DD') : undefined,
      idScheda: this.datiSettingRiabilitativo?.idScheda || null,
      nomeScheda: this.datiSettingRiabilitativo?.nomeScheda || 'SETTING RIABILITATIVO',
      noteSettingRiabilitativo: formValue.note
    };
    this.settingRiabilitativoForm.updateValueAndValidity()

    this.datiCliniciService.setDatiSettingRiabilitativo(datiDaSalvare);
    this.datiCliniciService.setDatiSettingRiabilitativoValido(this.settingRiabilitativoForm.valid);
  }

  populateForm(dati: SchedaSettingRiabilitativoModel): void {
    // Prima abilita i campi se necessario
    if (dati.trasferimentoSettingRiabilitativo) {
      this.settingRiabilitativoForm.get('settingRiabilitativo')?.enable();
      this.settingRiabilitativoForm.get('settingRiabilitativo')?.setValidators([Validators.required]);

      const dataControl = this.settingRiabilitativoForm.get('dataTransferimento');
      dataControl?.enable();
      dataControl?.setValidators([Validators.required, dateNotFutureValidator()]);

      this.settingRiabilitativoForm.get('strutturaDiDestinazione')?.enable();
      this.settingRiabilitativoForm.get('strutturaDiDestinazione')?.setValidators([Validators.required]);
    }

    // Poi imposta i valori
    const settingValue = dati.settingRiabilitativo?.idUnitaOperativa === TipoRicoveroEnum.SPINALE ? 'UNITA_SPINALE' :
      dati.settingRiabilitativo?.idUnitaOperativa === TipoRicoveroEnum.RIABILITAZIONE ? 'REPARTO_DI_RIABILITAZIONE' : '';

    let trasferimento: DizionarioModel | null = null;
    const formControl = this.settingRiabilitativoForm.get('trasferimentoSettingRiabilitativo');
    const value = dati['trasferimentoSettingRiabilitativo'];

    if (formControl !== null && formControl !== undefined) {
      if (value === true) {
        // formControl.setValue(this.optionsBoolean[0], { emitEvent: false });
        trasferimento = this.optionsBoolean[0];
      } else if (value === false) {
        trasferimento = this.optionsBoolean[1];
      }
    }

    this.settingRiabilitativoForm.patchValue({
      trasferimentoSettingRiabilitativo: trasferimento,
      settingRiabilitativo: settingValue,
      dataTransferimento: dati.dataTrasferimento ? new Date(dati.dataTrasferimento) : null,
      strutturaDiDestinazione: value ? dati.strutturaDestinazione?.codLivello2 : null,
      note: dati.noteSettingRiabilitativo
    }, { emitEvent: false });
    // se la data di trasferimento è minor di ricovero resetto la data per evitare che se inserisco data trasf e poi cambio data ricover se questa è minore non è valida
    if (this.dataRicovero && this.settingRiabilitativoForm.get('dataTransferimento')?.value && moment(this.settingRiabilitativoForm.get('dataTransferimento')?.value).isBefore(this.dataRicovero)) {
      this.settingRiabilitativoForm.get('dataTransferimento')?.markAsTouched()
    }

    this.settingRiabilitativoForm.updateValueAndValidity();

    if (this.readOnly) {
      this.applyReadOnly();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.formReady$.complete();
    this.viewInitialized$.complete();
  }

  populateBooleanValues(val: SchedaSettingRiabilitativoModel): void {
    const booleanFields: Array<keyof SchedaSettingRiabilitativoModel> = [
      'trasferimentoSettingRiabilitativo',
    ];

    booleanFields.forEach(field => {
      const formControl = this.settingRiabilitativoForm.get(field as string);
      const value = val[field];

      if (formControl !== null && formControl !== undefined) {
        if (value === true) {
          formControl.setValue(this.optionsBoolean[0], { emitEvent: false });
        } else if (value === false) {
          formControl.setValue(this.optionsBoolean[1], { emitEvent: false });
        } else {
          formControl.setValue(null, { emitEvent: false });
        }
      }
    });
  }

  openPopupNote(): void {
    let configNote = this.modalService.createNoteParam(this.settingRiabilitativoForm.get('note')?.value, this.readOnly);

    this.modalService.note(configNote)?.subscribe((res) => {
      if (res !== undefined) {
        this.settingRiabilitativoForm.get('note')?.setValue(res);
        this.settingRiabilitativoForm.get('note')?.markAsDirty();
        this.settingRiabilitativoForm.get('note')?.updateValueAndValidity();
      }
      this.datiCliniciService.adjustDialogHeight();
    });
  }

  private findUnitaOperativa(id: number): UnitaOperativaModel | undefined {
    const decoderUnit = this.optionsUnitaSpinale.find(item => item.idUnitaOperativa === id);
    if (!decoderUnit) return undefined;

    return {
      idUnitaOperativa: decoderUnit.idUnitaOperativa,
      codice: decoderUnit.codice || '',
      nome: ''
    };
  }

  private findStruttura(id: string): StruttureRiabilitativeModel | undefined {
    return this.optionsStrutture.find(item => item.codLivello2 === id);
  }

  /**
   * Filtra le strutture in base al testo di ricerca
   */
  filterStrutture(): void {
    if (!this.strutturaSearchText || this.strutturaSearchText.trim() === '') {
      this.filteredStrutture = [...this.optionsStrutture];
    } else {
      const searchText = this.strutturaSearchText.toLowerCase().trim();
      this.filteredStrutture = this.optionsStrutture.filter(struttura =>
        struttura.descrizioneLivello2?.toLowerCase().includes(searchText) ||
        struttura.codLivello2?.toLowerCase().includes(searchText)
      );
    }
  }

  /**
   * Gestisce il cambiamento del testo di ricerca
   */
  onStrutturaSearchChange(searchText: string): void {
    this.strutturaSearchText = searchText;
    this.filterStrutture();
  }

  /**
   * Pulisce il campo di ricerca
   */
  clearStrutturaSearch(): void {
    this.strutturaSearchText = '';
    this.filterStrutture();
  }

  getDateTimeErrorMessage(control: AbstractControl | null): string {
    return getDateTimeErrorMessage(control);
  }
}
