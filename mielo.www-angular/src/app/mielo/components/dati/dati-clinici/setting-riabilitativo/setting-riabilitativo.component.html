<form [formGroup]="settingRiabilitativoForm" class="box-dati mt-4" *ngIf="formReady">
    <div class="row">
        <!-- Prima riga - Trasferimento a setting riabilitativo -->
        <div class="col-md-4">
            <div class="label-container">
                <label class="field-label">Trasferimento a setting riabilitativo*</label>
            </div>
            <div class="radio-group-container">
                <mat-radio-group formControlName="trasferimentoSettingRiabilitativo" class="d-flex align-items-center">
                    <mat-radio-button
                        *ngFor="let option of optionsBoolean" [value]="option" color="primary"
                        (mouseup)="datiCliniciService.toggleRadioSelection(settingRiabilitativoForm.get('trasferimentoSettingRiabilitativo'), option, $event)">
                        {{ option.descrizione | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>
        </div>

        <!-- Setting riabilitativo -->
        <div class="col-md-7 d-flex align-items-center">
            <div class="w-100">
                <div class="label-container">
                    <label class="field-label">Setting riabilitativo<span *ngIf="settingRiabilitativoForm.get('settingRiabilitativo')?.enabled">*</span></label>
                </div>
                <div class="d-flex align-items-center justify-content-start" style="gap: 2rem;">
                    <mat-radio-group formControlName="settingRiabilitativo" class="d-flex align-items-center">
                        <mat-radio-button value="UNITA_SPINALE" color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(settingRiabilitativoForm.get('settingRiabilitativo'), 'UNITA_SPINALE', $event)">Unità Spinale</mat-radio-button>
                        <mat-radio-button value="REPARTO_DI_RIABILITAZIONE" class="ms-4" color="primary" (mouseup)="datiCliniciService.toggleRadioSelection(settingRiabilitativoForm.get('settingRiabilitativo'), 'REPARTO_DI_RIABILITAZIONE', $event)">Reparto di riabilitazione</mat-radio-button>
                    </mat-radio-group>
                    <button mat-button type="button" class="col-auto note-button ms-3" (click)="openPopupNote()" style="background-color: transparent !important;">
                        <svg class="icon" [ngClass]="{'icon-primary': true}">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data trasferimento e Struttura di destinazione -->
    <div class="row mt-2">
        <!-- Data trasferimento -->
        <div class="col-md-4 mb-3">
            <div class="label-container">
                <label class="field-label">Data trasferimento<span *ngIf="settingRiabilitativoForm.get('dataTransferimento')?.enabled">*</span></label>
            </div>
            <div class="input-container">
                <mat-form-field appearance="outline" class="w-100">
                    <input matInput [matDatepicker]="dataTrasfPicker" 
                        formControlName="dataTransferimento"
                        [min]="dataRicovero"
                        [max]="moment()"
                        placeholder="GG/MM/AAAA">
                    <mat-datepicker-toggle matSuffix [for]="dataTrasfPicker">
                        <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                    </mat-datepicker-toggle>
                    <mat-datepicker #dataTrasfPicker></mat-datepicker>
                    <mat-error *ngIf="getDateTimeErrorMessage(settingRiabilitativoForm.get('dataTransferimento'))">
                        {{ getDateTimeErrorMessage(settingRiabilitativoForm.get('dataTransferimento')) }}
                    </mat-error>
                    <mat-error *ngIf="settingRiabilitativoForm.get('dataTransferimento')?.hasError('matDatepickerMin')">
                        {{ ERROR_MESSAGE.DATE_BEFORE_ARRIVAL }}
                    </mat-error>
                </mat-form-field>
            </div>
        </div>

        <!-- Struttura di destinazione -->
        <div class="col-md-8 mb-3">
            <div class="label-container">
                <label class="field-label">Struttura di destinazione<span *ngIf="settingRiabilitativoForm.get('strutturaDestinazione')?.enabled">*</span></label>
            </div>
            <div class="input-container">
                <mat-form-field appearance="outline" class="w-100">
                    <mat-label>Seleziona struttura</mat-label>
                    <input matInput
                           placeholder="Cerca e seleziona struttura..."
                           [matAutocomplete]="strutturaAuto"
                           formControlName="strutturaDiDestinazione"
                           (input)="onStrutturaSearchChange($event.target.value)"
                           (blur)="onStrutturaBlur()">

                    <mat-autocomplete #strutturaAuto="matAutocomplete"
                                      [displayWith]="displayStrutturaFn.bind(this)"
                                      (optionSelected)="onStrutturaSelected($event)">
                        <mat-option *ngFor="let struttura of filteredStrutture" [value]="struttura.codLivello2">
                            <span>{{ struttura.descrizioneLivello2 }}</span>
                            <small class="text-muted ml-2">({{ struttura.codLivello2 }})</small>
                        </mat-option>

                        <mat-option disabled *ngIf="filteredStrutture.length === 0 && strutturaSearchText">
                            <em>Nessuna struttura trovata</em>
                        </mat-option>
                    </mat-autocomplete>

                    <mat-error *ngIf="settingRiabilitativoForm.get('strutturaDiDestinazione')?.hasError('required')">
                        Campo obbligatorio
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
    </div>
</form>
<!-- Messaggio di caricamento -->
<div *ngIf="!formReady" class="text-center my-4">
    <p>Caricamento in corso...</p>
</div>
