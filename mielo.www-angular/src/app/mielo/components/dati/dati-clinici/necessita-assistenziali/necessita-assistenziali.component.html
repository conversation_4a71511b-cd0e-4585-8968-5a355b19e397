<form [formGroup]="necessitaForm" class="d-flex flex-wrapbox-dati mt-4">
    <div class="row g-0">
        <!-- Prima riga -->
        <div class="col-4">
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">Tracheostomia<span
                            *ngIf="isRequired('tracheostomia')">*</span></label>
                    <div class="d-flex align-items-center">
                        <div class="col p-0">
                            <div class="radio-group-container">
                                <mat-radio-group formControlName="tracheostomia" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('tracheostomia'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-3 mx-2"
                                    [disabled]="isNoteDisabled('tracheostomia')"
                                    (click)="openPopupNote('tracheostomia')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-4">
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">Accesso venoso<span
                            *ngIf="isRequired('accessoVenoso')">*</span></label>
                    <div class="d-flex align-items-center">
                        <div class="col p-0">
                            <div class="radio-group-container">
                                <mat-radio-group formControlName="accessoVenoso" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('accessoVenoso'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-3"
                                    [disabled]="isNoteDisabled('accessoVenoso')"
                                    (click)="openPopupNote('accessoVenoso')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Separatore -->
        <div class="col-12">
            <hr class="my-3">
        </div>

        <!-- Respirazione -->
        <div class="col-11 mb-3">
            <div class="row d-flex align-items-center mb-3">
                <label class="field-label col-auto">Respirazione<span *ngIf="respirazioneRequired">*</span></label>
                <div class="col-auto ml-3">
                    <button mat-button type="button" class="note-button" (click)="openPopupNote('respirazione')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>

            <!-- Ventilazione invasiva -->
            <div class="row d-flex justify-content-between mb-5">
                <div class="col-5 col-md-6">
                    <div class="row">
                        <div class="col-8 d-flex align-items-end">
                            <mat-checkbox formControlName="ventilazioneInvasiva" color="primary">
                                Ventilazione invasiva
                            </mat-checkbox>
                        </div>
                        <div class="col-4">
                            <mat-label class="field-label">
                                h/die<span *ngIf="isRequired('ventilazioneInvasivaHDie')">*</span>
                            </mat-label>
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput formControlName="ventilazioneInvasivaHDie" placeholder="Inserisci"
                                (keydown)="restrictToTwoDigits($event)" />
                                <mat-error *ngIf="necessitaForm.get('ventilazioneInvasivaHDie')?.hasError('required')">
                                    {{ ERROR_MESSAGE.REQUIRED }}
                                </mat-error>
                                <mat-error *ngIf="necessitaForm.get('ventilazioneInvasivaHDie')?.hasError('min') || necessitaForm.get('ventilazioneInvasivaHDie')?.hasError('max')">
                                    Il valore deve essere compreso tra 1 e 24
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="col-5 d-flex align-items-end">
                    <mat-checkbox formControlName="respiroSpontaneoInAriaAmbiente" color="primary">
                        Respiro spontaneo in aria ambiente
                    </mat-checkbox>
                </div>
            </div>

            <!-- Ventilazione non invasiva -->
            <div class="row d-flex justify-content-between my-5">
                <div class="col-5 col-md-6">
                    <div class="row">
                        <div class="col-8 d-flex align-items-end">
                            <mat-checkbox formControlName="ventilazioneNonInvasiva" color="primary">
                                Ventilazione non invasiva
                            </mat-checkbox>
                        </div>
                        <div class="col-4">
                            <mat-label class="field-label ">
                                h/die<span *ngIf="isRequired('ventilazioneNonInvasivaHDie')">*</span>
                            </mat-label>
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput formControlName="ventilazioneNonInvasivaHDie" placeholder="Inserisci"
                                    (keydown)="restrictToTwoDigits($event)" />
                                <mat-error *ngIf="necessitaForm.get('ventilazioneNonInvasivaHDie')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error *ngIf="necessitaForm.get('ventilazioneNonInvasivaHDie')?.hasError('min') || necessitaForm.get('ventilazioneNonInvasivaHDie')?.hasError('max')">
                                    Il valore deve essere compreso tra 1 e 24
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <div class="col-5 d-flex align-items-end">
                    <mat-checkbox formControlName="respiroSpontaneoConO2" color="primary">
                        Respiro spontaneo con O2
                    </mat-checkbox>
                </div>
            </div>

            <!-- Stimolatore diaframmatico -->
            <div class="row d-flex justify-content-between mt-5">
                <div class="col-5 col-md-6">
                    <div class="row">
                        <div class="col-8 d-flex align-items-end">
                            <mat-checkbox formControlName="stimolatoreDiaframmatico" color="primary" class="">
                                Stimolatore diaframmatico<span *ngIf="isRequired('stimolatoreDiaframmatico')">*</span>
                            </mat-checkbox>
                        </div>
                        <div class="col-4">
                            <mat-label class="field-label ">
                                h/die<span *ngIf="isRequired('stimolatoreDiaframmaticoHDie')">*</span>
                            </mat-label>
                            <mat-form-field appearance="outline"  class="w-100">
                                <input matInput formControlName="stimolatoreDiaframmaticoHDie" placeholder="Inserisci"
                                    (keydown)="restrictToTwoDigits($event)" />
                                <mat-error *ngIf="necessitaForm.get('stimolatoreDiaframmaticoHDie')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error *ngIf="necessitaForm.get('stimolatoreDiaframmaticoHDie')?.hasError('min') || necessitaForm.get('stimolatoreDiaframmaticoHDie')?.hasError('max')">
                                    Il valore deve essere compreso tra 1 e 24
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>

        <!-- Separatore -->
        <div class="col-12">
            <hr class="my-3">
        </div>

        <div class="col-12 d-flex flex-column">
            <!-- Gestione Vescicale -->
            <div class="col-12 mb-3 mt-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">Gestione Vescicale<span
                            *ngIf="isRequired('gestioneVescicale')">*</span></label>
                    <div class="d-flex align-items-center">
                        <div class="col-5 max-width-gestione-vescicale p-0">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-select formControlName="gestioneVescicale" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsGestioneVescicale" [value]="option">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                                <mat-error *ngIf="necessitaForm.get('gestioneVescicale')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <div class="col-3 ml-4">
                            <button mat-button type="button" class="note-button"
                                [disabled]="isNoteDisabled('gestioneVescicale')"
                                (click)="openPopupNote('gestioneVescicale')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Trattamenti effettuati : solo dimissione-->

            <div *ngIf="isDimissione">
                <!-- Separatore -->
                <div class="col-12">
                    <hr class="my-3">
                </div>

                <!-- Trattamenti effettuati -->
                <div class="col-12 mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <label class="field-label mb-0">Trattamenti effettuati<span *ngIf="trattamentiEffettuatiRequired">*</span></label>
                        <div class="ml-3">
                            <button mat-button type="button" class="note-button"
                                (click)="openPopupNote('trattamentiEffettuati')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>

                    <div class="d-flex flex-wrap">
                        <div class="col-4 mb-2">
                            <mat-checkbox formControlName="farmacologico" (change)="manageTrattamentiCheckboxChange('farmacologico', $event)" color="primary">
                                Farmacologico
                            </mat-checkbox>
                        </div>
                        <div class="col-8 mb-2">
                            <mat-checkbox formControlName="btxSfintere" (change)="manageTrattamentiCheckboxChange('btxSfintere', $event)" color="primary">
                                BTX
                                Sfintere</mat-checkbox>
                        </div>
                        <div class="col-4 mb-2">
                            <mat-checkbox formControlName="btxDetrusore" (change)="manageTrattamentiCheckboxChange('btxDetrusore', $event)" color="primary">
                                BTX
                                Detrusore</mat-checkbox>
                        </div>
                        <div class="col-8 mb-2">
                            <mat-checkbox formControlName="nessuno" (change)="manageTrattamentiCheckboxChange('nessuno', $event)" color="primary">
                                Nessuno
                            </mat-checkbox>
                        </div>
                        <div class="col-4 mb-2">
                            <mat-checkbox formControlName="altro" (change)="manageTrattamentiCheckboxChange('altro', $event)" color="primary">
                                Altro
                            </mat-checkbox>
                        </div>
                        <div class="col-8 mb-2">
                            <mat-form-field appearance="outline" class="w-75">
                            <input matInput formControlName="altroTrattamentiEffettuati"
                                   [matTooltip]="necessitaForm.get('altroTrattamentiEffettuati')?.value || ''"
                                [placeholder]="isRequired('altroTrattamentiEffettuati') ? 'Inserisci*' : 'Inserisci'" max="250"/>
                            <mat-error *ngIf="necessitaForm.get('altroTrattamentiEffettuati')?.hasError('required')">
                                Campo obbligatorio
                            </mat-error>
                        </mat-form-field>
                        </div>
                    </div>
                </div>

                <!-- Separatore -->
                <div class="col-12">
                    <hr class="my-3">
                </div>

            </div>

            <!-- Alimentazione -->
            <div class="col-12 mb-3 mt-3">
                <div class="d-flex align-items-center">
                    <label class="field-label">Alimentazione<span *ngIf="isRequired('alimentazione')">*</span></label>
                    <div class="d-flex align-items-center">
                        <div class="col p-0">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="alimentazione" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsAlimentazione" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('alimentazione'), option, $event)">
                                        {{ option.descrizione === 'PARENTERALE' ? (option.descrizione | capitalizeFirst): (option.descrizione | uppercase) }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-4"
                                    [disabled]="isNoteDisabled('alimentazione')"
                                    (click)="openPopupNote('alimentazione')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Separatore -->
        <div class="col-12">
            <hr class="my-3">
        </div>

        <!-- Gestione alvo -->
        <div class="col-12 mb-3">
            <div class="d-flex align-items-center mb-2">
                <label class="field-label mb-0">Gestione alvo<span *ngIf="gestioneAlvoRequired">*</span></label>
                <div class="ml-3">
                    <button mat-button type="button" class="note-button" (click)="openPopupNote('gestioneAlvo')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>

            <div class="d-flex flex-wrap">
                <div class="col-4 mb-2">
                    <mat-checkbox formControlName="spontanea" color="primary">Spontanea</mat-checkbox>
                </div>
                <div class="col-4 mb-2">
                    <mat-checkbox formControlName="indottaDaFarmaciLocali" color="primary">Indotta da farmaci
                        locali</mat-checkbox>
                </div>
                <div class="col-4 mb-2">
                    <mat-checkbox formControlName="incontinente" color="primary">Incontinente</mat-checkbox>
                </div>
                <div class="col-4 mb-2">
                    <mat-checkbox formControlName="indottaDaFarmaciPerOs" color="primary">Indotta da farmaci per
                        OS</mat-checkbox>
                </div>
                <div class="col-4 mb-2">
                    <mat-checkbox formControlName="indottaDaManovraRiflessa" color="primary">Indotta da manovra
                        riflessa</mat-checkbox>
                </div>
                <div class="col-4 mb-2">
                    <mat-checkbox formControlName="irrigazioneTransanale" color="primary">Irrigazione
                        transanale</mat-checkbox>
                </div>
            </div>
        </div>

        <!--Aggiunte alvo solo per dimissione-->
        <div *ngIf="isDimissione" class="col-4">
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">Alvo - autonomia<span
                            *ngIf="isRequired('alvoAutonomia')">*</span></label>
                    <div class="d-flex align-items-center">
                        <div class="col p-0">
                            <div class="radio-group-container">
                                <mat-radio-group formControlName="alvoAutonomia" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('alvoAutonomia'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button mx-3"
                                    (click)="openPopupNote('alvoAutonomia')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div *ngIf="isDimissione" class="col-4">
            <div class="mb-3">
                <div class="d-flex flex-column">
                    <label class="field-label mb-2">Alvo - continente<span
                            *ngIf="isRequired('alvoContinente')">*</span></label>
                    <div class="d-flex align-items-center">
                        <div class="col p-0">
                            <div class="radio-group-container">
                                <mat-radio-group formControlName="alvoContinente" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('alvoContinente'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button mx-3"
                                    (click)="openPopupNote('alvoContinente')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Separatore -->
        <div *ngIf="isDimissione" class="col-12">
            <hr class="my-3">
        </div>


        <!-- Tipologia materasso e Posizionamento in carrozzina -->
        <div class="col-12 my-3">
            <div class="row g-0">
                <div class="col-12">
                    <div class="d-flex align-items-center">
                        <label class="field-label me-2">Tipologia materasso<span
                                *ngIf="isRequired('tipoMaterasso')">*</span></label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container col">
                                <mat-radio-group formControlName="tipoMaterasso" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsTipoMaterasso" [value]="option"
                                        color="primary" class="mx-1"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('tipoMaterasso'), option, $event)">
                                        {{ option.descrizione | capitalizeFirst }}
                                    </mat-radio-button>
                                </mat-radio-group>
                                <button mat-button type="button" class="note-button ml-3"
                                    (click)="openPopupNote('tipoMaterasso')">
                                    <svg class="icon icon-primary">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div *ngIf="!isDimissione" class="col-12 my-3">
            <div class="row g-0">
                <div class="col-6">
                    <div class="d-flex align-items-center">
                        <label class="field-label me-2">Posizionamento in carrozzina<span
                                *ngIf="isRequired('posizionamentoCarrozzina')">*</span></label>
                        <div class="d-flex align-items-center">
                            <div class="radio-group-container">
                                <mat-radio-group formControlName="posizionamentoCarrozzina" class="d-flex">
                                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option"
                                        color="primary"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(necessitaForm.get('posizionamentoCarrozzina'), option, $event)">
                                        {{ option.descrizione === 'SI' ? 'Sì' : 'No' }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>