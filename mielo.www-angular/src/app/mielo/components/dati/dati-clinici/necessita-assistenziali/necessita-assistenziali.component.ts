import {CommonModule} from '@angular/common';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import {Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges} from '@angular/core';
import {MatButtonModule} from '@angular/material/button';
import {MatCheckboxChange, MatCheckboxModule} from '@angular/material/checkbox';
import {MatExpansionModule} from '@angular/material/expansion';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatInputModule} from '@angular/material/input';
import {MatRadioModule} from '@angular/material/radio';
import {MatSelectModule} from '@angular/material/select';
import {debounceTime} from 'rxjs/operators';
import {distinctUntilChanged, forkJoin, Subscription} from 'rxjs';
import {SchedaNecessitaAssistenzialeIngressoModel} from '../../../../../shared/interfaces/dati-clinici.interface';
import {DizionarioModel} from '../../../../../shared/interfaces/scheda-ricovero.interface';
import {CheckBoxNumeroModel, CheckBoxSempliceModel} from '../../../../../shared/interfaces/shared/shared.interface';
import {CapitalizePipe} from '../../../../../shared/pipes/capitalize.pipe';
import {DecoderService} from '../../../../../shared/services/decoder.service';
import {ModalService} from '../../../../../shared/services/modal.service';
import {DatiCliniciService} from '../../../../services/dati-clinici.service';
import {TRATTAMENTI_EFFETTUATI_ALTRO} from "../../../../../shared/utils/const";
import {SchedaNecessitaAssistenzaDimissioneModel,} from "../../../../../shared/interfaces/dati-dimissione.interface";
import {DatiDimissioneService} from "../../../../services/dati-dimissione.service";
import {TipoRicoveroEnum} from '../../../../../shared/enums/tipo-ricovero.enum';
import {ERROR_MESSAGE} from "../../../../../shared/enums/enum";
import {MatTooltip} from "@angular/material/tooltip";
import {disableAllFieldsInForm, enableAllFieldsInForm} from "../../../../../shared/utils/utils";

@Component({
  selector: 'app-necessita-assistenziali',
  templateUrl: './necessita-assistenziali.component.html',
  styleUrls: ['./necessita-assistenziali.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatRadioModule,
    MatCheckboxModule,
    MatSelectModule,
    CapitalizePipe,
    MatTooltip
  ]
})
export class NecessitaAssistenzialiComponent implements OnInit, OnDestroy, OnChanges {

  @Input() readOnly: boolean = false;
  @Input() isDimissione!: boolean;
  @Input() repartoType?: TipoRicoveroEnum;
  @Input()
  set cronologiaInfo(value: any) {
    // Salva il form corrente PRIMA di impostare il nuovo valore
    if (value && !this._cronologiaInfo) {
      this.savedFormBeforCronologia = this.getAllFormValuesDatiClinici();
    }
    this._cronologiaInfo = value;
  }
  get cronologiaInfo(): any {
    return this._cronologiaInfo;
  }

  private _cronologiaInfo: any;
  private savedFormBeforCronologia: any;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;

  formReady: boolean = false;
  datiNecessita: SchedaNecessitaAssistenzialeIngressoModel | SchedaNecessitaAssistenzaDimissioneModel;
  respirazioneRequired: boolean = true;
  gestioneAlvoRequired: boolean = true;
  trattamentiEffettuatiRequired: boolean = false;

  optionsGestioneAlvo: Array<DizionarioModel>;
  optionsGestioneVescicale: Array<DizionarioModel>;
  optionsRespirazione: Array<DizionarioModel>;
  optionsAlimentazione: Array<DizionarioModel>;
  optionsTipoMaterasso: Array<DizionarioModel>;
  optionsBoolean: Array<DizionarioModel>;

  //options solo per dimissione
  optionsTrattamentiEffettuati: Array<DizionarioModel>;

  trattamentiEffetuatiListaSelected: Set<number> = new Set();

  respirazioneMap: { [id: number]: { check: string, input?: string } } = {};
  gestioneAlvoMap: { [id: number]: { check: string } } = {};
  trattamentiEffetuatiMap: { [id: number]: { check: string, input?: string } } = {};

  atLeastOneRespirazione = this.atLeastOneOptionChecked([
    'ventilazioneInvasiva',
    'respiroSpontaneoInAriaAmbiente',
    'ventilazioneNonInvasiva',
    'respiroSpontaneoConO2',
    'stimolatoreDiaframmatico'
  ], 'atLeastOneRespirazione')
  atLeastOneAlvo = this.atLeastOneOptionChecked([
    'spontanea',
    'indottaDaFarmaciLocali',
    'incontinente',
    'indottaDaFarmaciPerOs',
    'indottaDaManovraRiflessa',
    'irrigazioneTransanale'
  ], 'atLeastOneAlvo')
  atLeastOneTrattamento = this.atLeastOneOptionChecked([
    'farmacologico', 'btxSfintere', 'btxDetrusore', 'altro'
  ], 'atLeastOneTrattamento')

  necessitaForm: FormGroup;

  get formGroup(): FormGroup {
    return this.necessitaForm;
  }

  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private modalService: ModalService,
    public datiCliniciService: DatiCliniciService,
    private decoderService: DecoderService,
    private datiDimissioneService: DatiDimissioneService,
  ) {
    this.necessitaForm = this.fb.group({
      tracheostomia: [null, Validators.required],
      noteTracheostomia: [null],
      accessoVenoso: [null, Validators.required],
      noteAccessoVenoso: [null],

      ventilazioneInvasiva: [false],
      ventilazioneInvasivaHDie: [null],
      respiroSpontaneoInAriaAmbiente: [false],
      ventilazioneNonInvasiva: [false],
      ventilazioneNonInvasivaHDie: [null],
      respiroSpontaneoConO2: [false],
      stimolatoreDiaframmatico: [false],
      stimolatoreDiaframmaticoHDie: [null],

      noteRespirazione: [null],

      gestioneVescicale: [null, Validators.required],
      noteGestioneVescicale: [null],

      alimentazione: [null, Validators.required],
      noteAlimentazione: [null],

      spontanea: [false],
      indottaDaFarmaciLocali: [false],
      incontinente: [false],
      indottaDaFarmaciPerOs: [false],
      indottaDaManovraRiflessa: [false],
      irrigazioneTransanale: [false],
      noteGestioneAlvo: [null],
      tipoMaterasso: [null],
      noteTipoMaterasso: [null],
      posizionamentoCarrozzina: [null]
    }, {
      validators: [this.atLeastOneAlvo, this.atLeastOneRespirazione]
    });

  }

  ngOnInit(): void {
    if (this.isDimissione) {
      this.addControlsDimissione();
    }

    const forkJoinSources: any = {
      gestioneAlvo: this.decoderService.getDcodGestioneAlvo(),
      gestioneVescicale: this.decoderService.getDcodGestioneVescicale(),
      respirazione: this.decoderService.getDcodRespirazione(),
      alimentazione: this.decoderService.getDcodAlimentazione(),
      tipoMaterasso: this.decoderService.getDcodTipoMaterasso(),
      boolean: this.decoderService.getDcodBoolean()
    };

    if (this.isDimissione) {
      forkJoinSources.trattamentiEffettuatiLista = this.decoderService.getDcodTrattamentiEffettuati();
    }
    forkJoin(forkJoinSources).subscribe((options: any) => {
      this.optionsGestioneAlvo = options.gestioneAlvo;
      this.optionsGestioneVescicale = options.gestioneVescicale;
      this.optionsRespirazione = options.respirazione;
      this.optionsAlimentazione = options.alimentazione;
      this.optionsTipoMaterasso = options.tipoMaterasso;
      this.optionsBoolean = options.boolean;

      if (this.isDimissione) {
        this.optionsTrattamentiEffettuati = options.trattamentiEffettuatiLista;
        this.generateTrattamentiEffettuatiMap();
      }

      this.generateRespirazioneMap();
      this.generateGestioneAlvoMap();

      let dati;
      if (!this.isDimissione) {
        dati = this.datiCliniciService.getDatiNecessitaAssistenzialiValue();
      }
      else if (this.isDimissione) {
        dati = this.datiDimissioneService.getNecessitaAssistenzaValue();
      }

      // Ora che le opzioni sono pronte, posso popolare il form
      if (dati) {
        this.datiNecessita = dati;
        this.populateForm(dati);
      }

      this.formReady = true;

      // Disabilita gli input h/die se i checkbox non sono flaggati
      this.checkRespirazioneValues();

      if (this.isDimissione) this.checkTrattamentiEffettuatiValues();

      if (this.readOnly) {
        Object.keys(this.necessitaForm.controls).forEach(controlName => {
          if (!controlName.toLowerCase().includes('note')) {
            this.necessitaForm.get(controlName)?.disable({ emitEvent: false });
          }
        });
      }
    });

    if (!this.readOnly) {
      this.manageRespirazioneChanges();

      const formChanges = this.necessitaForm.valueChanges.pipe(
        distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
        debounceTime(1) // Usato per evitare che il form venga salvato prima che tutte le modifiche siano state applicate
      ).subscribe(() => {

        const formValue = this.necessitaForm.getRawValue();

        if (!this.isDimissione) { //dati clinici
          this.datiCliniciService.setDatiNecessitaAssistenziali(this.getAllFormValuesDatiClinici() as SchedaNecessitaAssistenzialeIngressoModel);
          this.datiCliniciService.setDatiNecessitaAssistenzialiValido(this.necessitaForm.valid);
        }
        else {
          this.datiDimissioneService.setNecessitaAssistenza({
            ...formValue,
            idScheda: this.datiNecessita.idScheda,
            nomeScheda: this.datiNecessita.nomeScheda,
            respirazioneDimissioneLista: this.getRespirazioneList(),
            gestioneAlvoDimissioneLista: this.getGestioneAlvoList(),
            trattamentiEffettuatiLista: this.getTrattamentiEffettuatiList(),
            tracheostomia: this.convertBooleanValue(this.necessitaForm.get('tracheostomia')?.value),
            accessoVenoso: this.convertBooleanValue(this.necessitaForm.get('accessoVenoso')?.value),
            alvoAutonomia: this.convertBooleanValue(this.necessitaForm.get('alvoAutonomia')?.value),
            alvoContinente: this.convertBooleanValue(this.necessitaForm.get('alvoContinente')?.value)
          } as SchedaNecessitaAssistenzaDimissioneModel);
          this.datiDimissioneService.setNecessitaValido(this.necessitaForm.valid);
        }

      })
      this.subscriptions.push(formChanges);
    }
  }

  ngOnDestroy() {
    /* Cancella sottoscrizioni */
    this.subscriptions.forEach(sub => sub.unsubscribe());
    // Ripristino i dati reali nel BehaviorSubject, se ero in modalità cronologia
    if (this.cronologiaInfo) {
      if (!this.isDimissione) {
        this.datiCliniciService.setDatiNecessitaAssistenziali(this.savedFormBeforCronologia);
      }
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    // Evita l'esecuzione durante l'inizializzazione del componente
    if (changes['cronologiaInfo'] && !changes['cronologiaInfo'].firstChange) {
      // setto i dati della cronologia
      if (changes['cronologiaInfo'].currentValue) {
        if (!this.savedFormBeforCronologia) {
          this.savedFormBeforCronologia = this.getAllFormValuesDatiClinici();
        }
        //   è l unico modo per sbiancare questi campi per poi ripopolarli
        this.resetRespirazioniList()
        this.resetGestioneAlvo()
        this.populateForm(changes['cronologiaInfo'].currentValue.schedaClinica)
        disableAllFieldsInForm(this.necessitaForm)
        // this.readOnly = true;
        // altrimenti ripristino il form precedente
      } else if (changes['cronologiaInfo'].previousValue) {
        // this.readOnly = false;
        if (!this.readOnly) {
          enableAllFieldsInForm(this.necessitaForm)
        }
        this.resetRespirazioniList()
        this.resetGestioneAlvo()
        this.populateForm(this.savedFormBeforCronologia)
        this.necessitaForm.updateValueAndValidity()
      }
    }
  }

  isRequired(controlName: string): boolean {
    const control = this.necessitaForm.get(controlName);
    return this.datiCliniciService.isRequired(control);
  }

  toCamelCase(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
      .replace(/^[A-Z]/, c => c.toLowerCase());
  }

  generateRespirazioneMap(): void {
    this.respirazioneMap = {};

    this.optionsRespirazione.forEach(opt => {
      const checkControl = this.toCamelCase(opt.descrizione).trim();
      const inputControl = this.necessitaForm.contains(`${checkControl}HDie`)
        ? `${checkControl}HDie`
        : undefined;

      this.respirazioneMap[opt.idDizionario] = {
        check: checkControl,
        input: inputControl
      };
    });
  }

  generateGestioneAlvoMap(): void {
    this.gestioneAlvoMap = {};

    this.optionsGestioneAlvo.forEach(opt => {
      const checkControl = this.toCamelCase(opt.descrizione).trim();

      this.gestioneAlvoMap[opt.idDizionario] = {
        check: checkControl
      }
    })
  }

  generateTrattamentiEffettuatiMap(): void {
    this.trattamentiEffetuatiMap = {};

    this.optionsTrattamentiEffettuati.forEach(opt => {
      const checkControl = this.toCamelCase(opt.descrizione).trim();

      this.trattamentiEffetuatiMap[opt.idDizionario] = {
        check: checkControl
      }
    })
  }

  checkRespirazioneValues() {
    const controls: { check: string; input: string }[] = [
      { check: 'ventilazioneInvasiva', input: 'ventilazioneInvasivaHDie' },
      { check: 'ventilazioneNonInvasiva', input: 'ventilazioneNonInvasivaHDie' },
      { check: 'stimolatoreDiaframmatico', input: 'stimolatoreDiaframmaticoHDie' }
    ];

    controls.forEach(({ check, input }) => {
      const checkValue = this.necessitaForm.get(check)?.value;
      const inputControl = this.necessitaForm.get(input);

      if (checkValue) {
        inputControl?.enable();
        inputControl?.setValidators([
          Validators.required,
          Validators.min(1),
          Validators.max(24)
        ]);
        inputControl?.markAsUntouched();
      } else {
        inputControl?.disable();
        inputControl?.setValue(null);
        inputControl?.clearValidators();
      }

      inputControl?.updateValueAndValidity();
    });
  }

  checkTrattamentiEffettuatiValues() {
    this.checkAltroValue();
    this.checkNessunoValue();
  }

  checkAltroValue() {
    const altroControl = this.necessitaForm.get('altro');
    const altroInput = this.necessitaForm.get('altroTrattamentiEffettuati');
    if (altroControl && altroControl.value) {
      altroInput?.enable();
      altroInput?.addValidators(Validators.required);
      altroInput?.updateValueAndValidity();
    } else {
      altroInput?.disable();
      altroInput?.clearValidators();
      altroInput?.updateValueAndValidity();
    }
  }

  checkNessunoValue() {
    const nessunoControl = this.necessitaForm.get('nessuno');
    const checkControls = [
      'farmacologico',
      'btxSfintere',
      'btxDetrusore',
      'altro'
    ]

    if (nessunoControl && nessunoControl.value) {
      checkControls.forEach(control => {
        this.necessitaForm.get(control)?.setValue(false, { emitEvent: false })
      })
    }
  }

  getAllFormValuesDatiClinici() {
    return {
      ...this.necessitaForm.getRawValue(),
      idScheda: this.datiNecessita.idScheda,
      nomeScheda: this.datiNecessita.nomeScheda,
      respirazioni: this.getRespirazioneList(),
      gestioneAlvo: this.getGestioneAlvoList(),
      tracheostomia: this.convertBooleanValue(this.necessitaForm.get('tracheostomia')?.value),
      accessoVenoso: this.convertBooleanValue(this.necessitaForm.get('accessoVenoso')?.value),
      posizionamentoCarrozzina: this.convertBooleanValue(this.necessitaForm.get('posizionamentoCarrozzina')?.value)
    }
  }

  convertBooleanValue = (val: DizionarioModel | null): boolean | null => {
    if (!val || !val.descrizione) return null;
    if (val.descrizione.toUpperCase() === 'SI') return true;
    if (val.descrizione.toUpperCase() === 'NO') return false;
    return null;
  };

  populateForm(val: SchedaNecessitaAssistenzialeIngressoModel | SchedaNecessitaAssistenzaDimissioneModel) {

    this.populateBooleanValues(val);
    this.populateDizionari(val);
    if (!this.isDimissione) {
      const ingressoVal = val as SchedaNecessitaAssistenzialeIngressoModel;
      this.populateRespirazioni(ingressoVal.respirazioni);
      this.populateGestioneAlvo(ingressoVal.gestioneAlvo);
    }
    else if (this.isDimissione) {
      const dimissioneVal = val as SchedaNecessitaAssistenzaDimissioneModel;
      this.populateTrattamentiEffettuati(dimissioneVal.trattamentiEffettuatiLista);
      this.populateRespirazioni(dimissioneVal.respirazioneDimissioneLista);
      this.populateGestioneAlvo(dimissioneVal.gestioneAlvoDimissioneLista);
      this.populateAltroInput(dimissioneVal.altroTrattamentiEffettuati);
    }
    this.populateNote(val);
    this.checkRespirazioneValues();
    this.checkTrattamentiEffettuatiValues()
  }

  populateBooleanValues(val: SchedaNecessitaAssistenzialeIngressoModel | SchedaNecessitaAssistenzaDimissioneModel): void {
    const booleanFields = this.isDimissione
      ? ([
        'tracheostomia',
        'accessoVenoso',
        'alvoAutonomia',
        'alvoContinente'
      ] as Array<keyof SchedaNecessitaAssistenzaDimissioneModel>)
      : ([
        'tracheostomia',
        'accessoVenoso',
        'posizionamentoCarrozzina',
      ] as Array<keyof SchedaNecessitaAssistenzialeIngressoModel>);

    booleanFields.forEach(field => {
      const formControl = this.necessitaForm.get(field as string);
      const value = (val as any)[field];

      if (formControl !== null && formControl !== undefined) {
        if (value === true || value?.descrizione === 'SI') {
          formControl.setValue(this.optionsBoolean[0], { emitEvent: false });
        } else if (value === false || value?.descrizione === 'NO') {
          formControl.setValue(this.optionsBoolean[1], { emitEvent: false });
        } else {
          formControl.setValue(null, { emitEvent: false });
        }
      }
    });
  }

  populateDizionari(val: SchedaNecessitaAssistenzialeIngressoModel | SchedaNecessitaAssistenzaDimissioneModel) {
    const findOption = (options: DizionarioModel[], diz: DizionarioModel | null | undefined): DizionarioModel | null => {
      if (!diz || !diz.idDizionario) return null;
      return options.find(opt => opt.idDizionario === diz.idDizionario) ?? null;
    };
    this.necessitaForm.get('gestioneVescicale')?.setValue(findOption(this.optionsGestioneVescicale, val.gestioneVescicale), { emitEvent: false });
    this.necessitaForm.get('alimentazione')?.setValue(findOption(this.optionsAlimentazione, val.alimentazione), { emitEvent: false });
    this.necessitaForm.get('tipoMaterasso')?.setValue(findOption(this.optionsTipoMaterasso, val.tipoMaterasso), { emitEvent: false });
    this.necessitaForm.get('gestioneVescicale')?.setValue(findOption(this.optionsGestioneVescicale, val.gestioneVescicale), { emitEvent: false });
  }

  populateNote(val: SchedaNecessitaAssistenzialeIngressoModel | SchedaNecessitaAssistenzaDimissioneModel) {
    this.necessitaForm.get('noteTracheostomia')?.setValue(val.noteTracheostomia, { emitEvent: false });
    this.necessitaForm.get('noteAccessoVenoso')?.setValue(val.noteAccessoVenoso, { emitEvent: false });
    this.necessitaForm.get('noteRespirazione')?.setValue(val.noteRespirazione, { emitEvent: false });
    this.necessitaForm.get('noteGestioneVescicale')?.setValue(val.noteGestioneVescicale, { emitEvent: false });
    this.necessitaForm.get('noteAlimentazione')?.setValue(val.noteAlimentazione, { emitEvent: false });
    this.necessitaForm.get('noteGestioneAlvo')?.setValue(val.noteGestioneAlvo, { emitEvent: false });
    this.necessitaForm.get('noteTipoMaterasso')?.setValue(val.noteTipoMaterasso, { emitEvent: false });

    if (this.isDimissione) {
      const dimissioneVal = val as SchedaNecessitaAssistenzaDimissioneModel;
      this.necessitaForm.get('noteTrattamentiEffettuati')?.patchValue(dimissioneVal.noteTrattamentiEffettuati, { emitEvent: false });
      this.necessitaForm.get('noteAlvoAutonomia')?.patchValue(dimissioneVal.noteAlvoAutonomia, { emitEvent: false });
      this.necessitaForm.get('noteAlvoContinente')?.patchValue(dimissioneVal.noteAlvoContinente, { emitEvent: false });
    }
  }

  populateRespirazioni(respirazioni: CheckBoxNumeroModel[]): void {
    if (!respirazioni) return;

    respirazioni.forEach(({ idDizionario, numero }) => {
      const mapping = this.respirazioneMap[idDizionario];
      if (mapping) {
        this.necessitaForm.get(mapping.check)?.setValue(true, { emitEvent: false });
        if (mapping.input) {
          this.necessitaForm.get(mapping.input)?.setValue(numero, { emitEvent: false });
        }
      }
    });
  }

  resetRespirazioniList() {
    this.optionsRespirazione.forEach(opt => {
      const mapping = this.respirazioneMap[opt.idDizionario];
      if (mapping) {
        this.necessitaForm.get(mapping.check)?.setValue(false, { emitEvent: false });
        if (mapping.input) {
          this.necessitaForm.get(mapping.input)?.setValue(null, { emitEvent: false });
        }
      }
    });
  }

  populateGestioneAlvo(gestioniAlvo: CheckBoxSempliceModel[]) {
    if (!gestioniAlvo) return;

    gestioniAlvo.forEach(({ idDizionario }) => {
      const mapping = this.gestioneAlvoMap[idDizionario];
      if (mapping) {
        this.necessitaForm.get(mapping.check)?.setValue(true, { emitEvent: false });
      }
    });
  }

  resetGestioneAlvo() {
    this.optionsGestioneAlvo.forEach(opt => {
      const mapping = this.gestioneAlvoMap[opt.idDizionario];
      if (mapping) {
        this.necessitaForm.get(mapping.check)?.setValue(false, { emitEvent: false });
      }
    });
  }

  populateTrattamentiEffettuati(trattamenti: CheckBoxSempliceModel[]) {
    if (!trattamenti) return;

    trattamenti.forEach(({ idDizionario }) => {
      const mapping = this.trattamentiEffetuatiMap[idDizionario];
      if (mapping) {
        this.necessitaForm.get(mapping.check)?.setValue(true, { emitEvent: false });
      }
    });
  }

  populateAltroInput(value: string) {
    const altroInput = this.necessitaForm.get('altroTrattamentiEffettuati');
    altroInput?.setValue(value);
  }

  manageRespirazioneChanges() {
    this.necessitaForm.get('ventilazioneInvasiva')?.valueChanges.subscribe(checked => {
      const control = this.necessitaForm.get('ventilazioneInvasivaHDie');
      if (checked) {
        control?.enable();
        control?.setValidators([
          Validators.required,
          Validators.min(1),
          Validators.max(24)
        ]);
      } else {
        control?.disable();
        control?.setValue(null);
        control?.clearValidators();
      }
      control?.updateValueAndValidity();
    });

    this.necessitaForm.get('ventilazioneNonInvasiva')?.valueChanges.subscribe(checked => {
      const control = this.necessitaForm.get('ventilazioneNonInvasivaHDie');
      if (checked) {
        control?.enable();
        control?.setValidators([
          Validators.required,
          Validators.min(1),
          Validators.max(24)
        ]);
      } else {
        control?.disable();
        control?.setValue(null);
        control?.clearValidators();
      }
      control?.updateValueAndValidity();
    });

    this.necessitaForm.get('stimolatoreDiaframmatico')?.valueChanges.subscribe(checked => {
      const control = this.necessitaForm.get('stimolatoreDiaframmaticoHDie');
      if (checked) {
        control?.enable();
        control?.setValidators([
          Validators.required,
          Validators.min(1),
          Validators.max(24)
        ]);
      } else {
        control?.disable();
        control?.setValue(null);
        control?.clearValidators();
      }
      control?.updateValueAndValidity();
    });
  }

  manageTrattamentiCheckboxChange(changedName: string, event: MatCheckboxChange): void {
    const altroField = this.necessitaForm.get('altroTrattamentiEffettuati');

    // Gestione campo "altro"
    if (changedName === 'altro') {
      if (event.checked) {
        altroField?.enable({ emitEvent: false });
        altroField?.addValidators(Validators.required)
      } else {
        altroField?.disable({ emitEvent: false });
        altroField?.setValue(null, { emitEvent: false });
      }
      altroField?.updateValueAndValidity()
    }

    // Se clicchi "nessuno", deseleziona gli altri
    if (changedName === 'nessuno' && event.checked) {
      ['farmacologico', 'btxSfintere', 'btxDetrusore', 'altro'].forEach(name => {
        if (name !== changedName) {
          this.necessitaForm.get(name)?.setValue(false, { emitEvent: false });
        }
      });
      altroField?.disable({ emitEvent: false });
      altroField?.setValue(null, { emitEvent: false });
    }

    // Se clicchi uno qualunque degli altri, deseleziona "nessuno"
    if (['farmacologico', 'btxSfintere', 'btxDetrusore', 'altro'].includes(changedName) && event.checked) {
      const nessunoCtrl = this.necessitaForm.get('nessuno');
      if (nessunoCtrl?.value) {
        nessunoCtrl.setValue(false, { emitEvent: false });
      }
    }
  }

  getRespirazioneList(): CheckBoxNumeroModel[] {
    const result: CheckBoxNumeroModel[] = [];

    this.optionsRespirazione.forEach(opt => {
      const mapping = this.respirazioneMap[opt.idDizionario];
      if (mapping && this.necessitaForm.get(mapping.check)?.value === true) {
        const numero = mapping.input ? this.necessitaForm.get(mapping.input)?.value ?? null : null;
        result.push({ idDizionario: opt.idDizionario, numero });
      }
    });

    return result;
  }

  getGestioneAlvoList(): CheckBoxSempliceModel[] {
    const result: CheckBoxSempliceModel[] = [];

    this.optionsGestioneAlvo.forEach(opt => {
      const mapping = this.gestioneAlvoMap[opt.idDizionario];
      if (mapping && this.necessitaForm.get(mapping.check)?.value === true) {
        result.push({ idDizionario: opt.idDizionario });
      }
    });

    return result;
  }

  getTrattamentiEffettuatiList(): CheckBoxSempliceModel[] {
    const selected: CheckBoxSempliceModel[] = [];

    // 1. Raccogli tutti i selezionati
    this.optionsTrattamentiEffettuati.forEach(opt => {
      const mapping = this.trattamentiEffetuatiMap[opt.idDizionario];
      if (mapping && this.necessitaForm.get(mapping.check)?.value === true) {
        selected.push({ idDizionario: opt.idDizionario });
      }
    });

    // 2. Controlla se "nessuno" è tra i selezionati
    const nessunoId = this.optionsTrattamentiEffettuati.find(opt => this.trattamentiEffetuatiMap[opt.idDizionario]?.check === 'nessuno')?.idDizionario;

    if (nessunoId && selected.some(item => item.idDizionario === nessunoId)) {
      // Ritorna solo "nessuno" se è stato selezionato
      return [{ idDizionario: nessunoId }];
    }

    return selected;
  }


  restrictToTwoDigits(event: KeyboardEvent): boolean {
    return this.datiCliniciService.restrictToTwoDigits(event);
  }

  // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno
  isNoteDisabled(controlName: string): boolean {
    const control = this.necessitaForm.get(controlName);
    return !control?.value && !!control?.disabled;
  }

  openPopupNote(field: string): void {
    let noteField = '';
    noteField = `note${field.charAt(0).toUpperCase() + field.slice(1)}`;
    const noteControl = this.necessitaForm.get(noteField);

    if (noteControl) {
      const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

      this.modalService.note(configNote)?.subscribe((res: any) => {
        if (res != noteControl.value) {
          noteControl.setValue(res);
          noteControl.markAsDirty();
          noteControl.updateValueAndValidity();
        }
        this.datiCliniciService.adjustDialogHeight();
      });
    }
  }

  private addControlsDimissione() {

    const controls = [
      'farmacologico',
      'btxSfintere',
      'btxDetrusore',
      'nessuno',
      'altro',
      'noteTrattamentiEffettuati',
      'altroTrattamentiEffettuati',
      'alvoAutonomia',
      'alvoContinente',
      'noteAlvoAutonomia',
      'noteAlvoContinente'
    ]

    controls.forEach(control => {
      this.necessitaForm.addControl(control, this.fb.control(null));
    })

    this.necessitaForm.removeControl('posizionamentoCarrozina');

    this.applyConditionalValidators();

    if (this.repartoType) {
      this.respirazioneRequired = this.repartoType !== TipoRicoveroEnum.ACUTI;
      this.gestioneAlvoRequired = this.repartoType !== TipoRicoveroEnum.ACUTI;
      this.trattamentiEffettuatiRequired = this.repartoType !== TipoRicoveroEnum.ACUTI;
    }
  }

  private applyConditionalValidators(): void {
    const requiredFields = [
      'alvoAutonomia',
      'alvoContinente',
      'tracheostomia',
      'accessoVenoso',
      'gestioneVescicale',
      'alimentazione',
      'tipoMaterasso'
    ];
    if (this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI) {
       this.necessitaForm.addValidators(this.atLeastOneTrattamento)

      // Elenco dei controlli da rendere required
      requiredFields.forEach(field => {
        const control = this.necessitaForm.get(field);
        if (control && control.validator == null) {
          control.setValidators(Validators.required);
          control.updateValueAndValidity();
        }
      });
    } else {
      this.necessitaForm.clearValidators();

      requiredFields.forEach(field => {
        const control = this.necessitaForm.get(field);
        if (control) {
          control.setValidators(null);
          control.updateValueAndValidity();
        }
      });
    }
  }

  isOptionSelected(idDizionario: number): boolean {
    return this.trattamentiEffetuatiListaSelected.has(idDizionario);
  }

  onCheckboxChange(event: MatCheckboxChange, idDizionario: number): void {
    if (idDizionario === TRATTAMENTI_EFFETTUATI_ALTRO.idDizionario && event.checked) {
      this.necessitaForm.get('altroTrattamentiEffettuati')?.enable({ emitEvent: false });
    } else if (idDizionario === TRATTAMENTI_EFFETTUATI_ALTRO.idDizionario && !event.checked) {
      this.necessitaForm.get('altroTrattamentiEffettuati')?.setValue(null, { emitEvent: false });
      this.necessitaForm.get('altroTrattamentiEffettuati')?.disable({ emitEvent: false });
    }

    if (event.checked) {
      this.trattamentiEffetuatiListaSelected.add(idDizionario);
    } else {
      this.trattamentiEffetuatiListaSelected.delete(idDizionario);
    }
  }

  atLeastOneOptionChecked(fields: string[], errorKey: string): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
      const atLeastOne = fields.some(field => group.get(field)?.value === true);
      return atLeastOne ? null : { [errorKey]: true };
    };
  }

}
