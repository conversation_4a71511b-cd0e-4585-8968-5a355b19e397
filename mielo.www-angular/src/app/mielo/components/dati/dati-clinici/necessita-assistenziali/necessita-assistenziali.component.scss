:host {
    .field-label {
        color: #003354;
        margin-bottom: 8px;
        display: block;
        font-size: 18px;
        font-weight: 600;
    }

    .smaller-label {
        color: #003354;
        font-size: 14px;
        font-weight: 600;
    }

    .max-width-gestione-vescicale{
        max-width: 38% !important;
    }

    .hdie-label {
        color: #003354;
        font-size: 16px;
        font-weight: 600;
        position: absolute;
        top: -20px;
        left: 0;
    }

    .form-control {
        border: 1px solid #E6E9F2;
        border-radius: 4px;
        padding: 10px 12px;
        font-size: 14px;
        height: 46px;
        background-color: #fff;
        margin: 0;

        &::placeholder {
            color: #b5b5b5;
            opacity: 1;
        }

        &:focus {
            border-color: #0073E6;
            box-shadow: 0 0 0 2px rgba(0, 115, 230, 0.25);
            outline: 0;
        }
    }

    .radio-group-container {
        padding: 0;
        display: flex;
        align-items: center;
        width: 100%;
        background-color: transparent;
        border: none;
        margin: 0;

        .mat-radio-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: nowrap;
            width: 100%;

            .mat-radio-button {
                white-space: nowrap;
                flex-shrink: 0;
            }
        }
    }


    hr {
        border-color: #E6E9F2;
    }

    .small-input {
        margin-bottom: 0;
        height: 40px;

        ::ng-deep {
            .mat-mdc-form-field-infix {
                padding-top: 8px;
                padding-bottom: 8px;
                min-height: auto;
            }
        }
    }

    ::ng-deep {
        .mat-radio-button {
            display: flex;
            align-items: center;
            margin: 0;
            font-size: 14px;

            .mat-radio-container {
                height: 16px;
                width: 16px;
            }

            .mat-radio-outer-circle,
            .mat-radio-inner-circle {
                height: 16px;
                width: 16px;
            }

            .mat-radio-label {
                display: flex;
                align-items: center;
                margin: 0;
                white-space: nowrap;
            }

            .mat-radio-label-content {
                padding-left: 8px;
                line-height: normal;
                display: flex;
                align-items: center;
                white-space: nowrap;
            }
        }

        .mat-checkbox {
            font-size: 14px;
        }

        .mdc-form-field>label {
            margin-left: 0;
            margin-right: auto;
            padding-left: 4px;
            //padding-bottom: 9px;
            padding-right: 0;
            order: 0;
        }
    }

    // Stili per la respirazione
    .respirazione-row {
        margin-bottom: 16px;

        .col-3 {
            display: flex;
            align-items: center;
        }
    }

    // Riduzione spaziatura componenti
    .mb-3 {
        margin-bottom: 1rem!important;
    }

    .mb-2 {
        margin-bottom: 0.5rem!important;
    }

    .hdie-container {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        margin-bottom: 1rem;
    }
}

.row.g-0 {
    align-items: center;
}

.no-gap {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.compact-checkbox {
    padding: 0 !important;
    margin: 0 !important;
    min-width: 0 !important;
}

.compact-input {
    padding: 0 !important;
    margin: 0 !important;
    min-width: 0 !important;
}

.odd-checkbox{
    margin-left: -17vw;
}
