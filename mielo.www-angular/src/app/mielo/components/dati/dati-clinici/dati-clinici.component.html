<div class="container-data pt-4">

  <h3 class="ml-3 mb-0">Dati clinici</h3>

  <!-- Eziologia -->
  <ng-container *ngIf="isRientroNonCensito || isRientroCensito; else normaleEziologia">
    <mat-accordion class="m-2">
      <mat-expansion-panel [expanded]="openEziologia">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">EZIOLOGIA</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <app-eziologia-rientro [readOnly]="eziologiaReadOnly" (formInitialized)="onEziologiaReady()"></app-eziologia-rientro>
      </mat-expansion-panel>
    </mat-accordion>

    <!-- Quadro neurologico per rientro non censito -->
    <mat-accordion *ngIf="datiClinici?.schedaQuadroNeurologico" class="m-2">
      <mat-expansion-panel (opened)="onOpened('quadro')">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">QUADRO NEUROLOGICO</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-container *ngIf="hasBeenOpenedQuadro">
          <app-quadro-neurologico [readOnly]="quadroReadOnly"></app-quadro-neurologico>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>
  </ng-container>
  
  <ng-template #normaleEziologia>
    <mat-accordion *ngIf="datiClinici?.schedaEziologica" class="m-2">
      <mat-expansion-panel (opened)="onOpened('eziologia')" [expanded]="openEziologia">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">EZIOLOGIA</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-container *ngIf="hasBeenOpenedEziologia">
          <app-eziologia [readOnly]="eziologiaReadOnly" [idReparto]="idReparto" (formInitialized)="onEziologiaReady()">
          </app-eziologia>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>

    <!-- Lesione e trattamento -->
    <mat-accordion *ngIf="datiClinici?.schedaLesioneTrattamento" class="m-2">
      <mat-expansion-panel (opened)="onOpened('lesione')">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">LESIONE E TRATTAMENTO</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-container *ngIf="hasBeenOpenedLesione">
        <app-lesione-trattamento [readOnly]="lesioneReadOnly"
                                 [idTipoEvento]="tipoEvento"
                                 [idReparto]="idReparto">
          </app-lesione-trattamento>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>

  <!-- Valutazione in ingresso -->
  <mat-accordion *ngIf="datiClinici?.schedaValutazioneIngresso" class="m-2">
    <mat-expansion-panel #valutazionePanel (opened)="onOpened('valutazione')" (closed)="onClosed('valutazione')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">VALUTAZIONE IN INGRESSO <span *ngIf="tipoEvento !== TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento">(ENTRO UNA SETTIMANA DALL'INGRESSO)</span></span>
        </mat-panel-title>
        <!-- torna editor/cronologia buttons-->
        <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
             class="d-flex justify-content-between pr-5" style="gap: 1rem;">
            <div class="col-auto ms-auto" *ngIf="showTornaEditor['valutazione']">
                <button (click)="onBackOnEditor($event, 'valutazione')" mat-stroked-button class="note-button" style="border: none !important;">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-pencil"></use>
                    </svg>
                    Torna all'editor
                </button>
            </div>
            <div class="col-auto d-flex">
                <app-cronologia-menu-simple
                  *ngIf="showCronologiaBtnValutazione"
                  [onInfoClick]="onCronologiaDatiInfoClick"
                  [idTipoScheda]="ETipoScheda.VALUTAZIONE_IN_INGRESSO"
                  (cronologiaOpened)="onCronologiaOpened($event, 'valutazione')">
                </app-cronologia-menu-simple>
            </div>
        </div>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedValutazione">
        <app-valutazione
                [cronologiaInfo]="datiCronologiaValutazione"
                [isDimissione]="false"
                [readOnly]="valutazioneReadOnly"
                [dataRicovero]="dataRicovero">
        </app-valutazione>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

    <!-- Necessità assistenziale in ingresso -->
    <mat-accordion *ngIf="datiClinici?.schedaNecessitaAssistenzialeIngresso" class="m-2">
    <mat-expansion-panel #necessitaPanel (opened)="onOpened('necessita')" (closed)="onClosed('necessita')">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">NECESSITÀ ASSISTENZIALI IN INGRESSO</span>
          </mat-panel-title>
        <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
             class="d-flex justify-content-between pr-5" style="gap: 1rem;">
            <div class="col-auto ms-auto" *ngIf="showTornaEditor['necessita']">
                <button (click)="onBackOnEditor($event, 'necessita')" mat-stroked-button class="note-button" style="border: none !important;">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-pencil"></use>
                    </svg>
                    Torna all'editor
                </button>
            </div>
            <app-cronologia-menu-simple
              *ngIf="showCronologiaBtnNecessita"
              [onInfoClick]="onCronologiaDatiInfoClick"
              [idTipoScheda]="ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO"
              (cronologiaOpened)="onCronologiaOpened($event, 'necessita')">
            </app-cronologia-menu-simple>
        </div>
        </mat-expansion-panel-header>
        <ng-container *ngIf="hasBeenOpenedNecessita">
          <app-necessita-assistenziali
                [cronologiaInfo]="datiCronologiaNecessita"
                  [repartoType]="idReparto"
                  [readOnly]="necessitaReadOnly"
                  [isDimissione]="false"
          ></app-necessita-assistenziali>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>

  <!-- Quadro neurologico -->
  <mat-accordion *ngIf="datiClinici?.schedaQuadroNeurologico" class="m-2">
    <mat-expansion-panel #quadroNeuroPanel (opened)="onOpened('quadro')" (closed)="onClosed('quadro')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">QUADRO NEUROLOGICO</span>
        </mat-panel-title>
        <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
             class="d-flex justify-content-between pr-5" style="gap: 1rem;">
            <div class="col-auto ms-auto" *ngIf="showTornaEditor['quadroNeuro']">
                <button (click)="onBackOnEditor($event, 'quadroNeuro')" mat-stroked-button class="note-button" style="border: none !important;">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-pencil"></use>
                    </svg>
                    Torna all'editor
                </button>
            </div>
            <app-cronologia-menu-simple
              *ngIf="showCronologiaBtnQuadroNeuro"
              [onInfoClick]="onCronologiaDatiInfoClick"
              [idTipoScheda]="ETipoScheda.QUADRO_NEUROLOGICO"
              (cronologiaOpened)="onCronologiaOpened($event, 'quadroNeuro')">
            </app-cronologia-menu-simple>
        </div>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedQuadro">
        <app-quadro-neurologico [readOnly]="quadroReadOnly"
                                [cronologiaInfo]="datiCronologiaQuadroNeurologico"
                                [isDimissione]="false"></app-quadro-neurologico>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

    <!-- Setting riabilitativo -->
    <mat-accordion *ngIf="datiClinici?.schedaSettingRiabilitativo" class="m-2">
      <mat-expansion-panel (opened)="onOpened('setting')">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">SETTING RIABILITATIVO</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-container *ngIf="hasBeenOpenedSetting">
          <app-setting-riabilitativo [readOnly]="settingReadOnly" [idReparto]="idReparto"
            (showDimissione)="onShowDimissione($event)">
          </app-setting-riabilitativo>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>

    <!-- Complicanze -->
    <mat-accordion *ngIf="datiClinici?.schedaComplicanze" class="m-2">
      <mat-expansion-panel (opened)="onOpened('complicanze')">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span class="panel-title">COMPLICANZE</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-container *ngIf="hasBeenOpenedComplicanze">
          <app-complicanze
                  [repartoType]="idReparto"
                  [readOnly]="complicanzeReadOnly"/>
        </ng-container>
      </mat-expansion-panel>
    </mat-accordion>
  </ng-template>
</div>


