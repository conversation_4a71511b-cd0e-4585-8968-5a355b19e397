:host {
    .icon-color {
        color: #003354 !important;
    }

    .icon-calendar {
        color: #003354 !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .form-field-container {
        display: flex;
        flex-direction: column;
    }

    .d-flex.flex-wrap {
        flex-wrap: wrap;
        gap: 1.5rem 2.5rem;
        align-items: flex-start;
    }

    .altro-container {
        gap: 8px;

        .altro-input {
            height: 40px;
            padding: 0 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            font-size: 16px;
            min-width: 300px;
            max-width: 400px;
            width: 100%;

            &:disabled {
                background-color: #f5f5f5;
                border-color: #ddd;
            }
        }
    }

    .field-label {
        font-family: 'Titillium Web', sans-serif;
        font-weight: 600;
        font-size: 18px;
        line-height: 23px;
        color: #003354;
    }

    ::ng-deep {
        .mat-checkbox-layout {
            align-items: flex-start;
        }

        .mat-form-field-appearance-outline .mat-form-field-flex {
            background-color: #fff !important;
        }

        .mat-form-field {
            width: 100%;
        }

        .mat-checkbox-checked .mat-checkbox-background,
        .mat-checkbox-indeterminate .mat-checkbox-background {
            background-color: #2A7A38 !important;
        }

        .mat-checkbox-checked .mat-checkbox-frame {
            border-color: #2A7A38 !important;
        }

        .mat-checkbox-checked .mat-checkbox-checkmark-path {
            stroke: #fff !important;
        }

        .checkbox-verde.mat-checkbox-checked .mat-checkbox-background,
        .checkbox-verde.mat-checkbox-indeterminate .mat-checkbox-background {
            background-color: #2A7A38 !important;
        }

        .checkbox-verde.mat-checkbox-checked .mat-checkbox-frame {
            border-color: #2A7A38 !important;
        }

        .checkbox-verde.mat-checkbox-checked .mat-checkbox-checkmark-path {
            stroke: #fff !important;
        }

        .mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,
        .mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background {
            border-color: #2A7A38 !important;
            background-color: #2A7A38 !important;
        }

        .mat-mdc-form-field {
            .mat-mdc-text-field-wrapper {
                .mat-mdc-form-field-flex {
                    .mat-mdc-form-field-infix {
                        padding-top: 8px;
                        padding-bottom: 8px;
                    }
                }
            }
        }
    }
}

.text-danger {
    color: #003354;
}

mat-error.nowrap {
    white-space: nowrap;
    display: block;
    width: max-content;
    min-width: 100%;
    overflow-x: auto;
    font-size: 0.95em;
}

.centered-placeholder {
    height: 40px;
    display: flex;
    align-items: center;
    &::placeholder {
        line-height: 40px;
        vertical-align: middle;
        font-size: 16px;
        opacity: 1;
    }
}

::ng-deep .mat-mdc-input-element {
    display: flex;
    align-items: center;
    height: 40px;
}

.border-hr {
    border-bottom: 1px solid #E6E9F2 !important;
    padding-bottom: 1.5rem !important;
}