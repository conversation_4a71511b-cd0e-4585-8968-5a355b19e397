<form [formGroup]="eziologiaRientroForm" class="box-dati mt-4">
    <!-- Data esordio complicanze (only if non censito) -->
    <div class="col-4">
        <mat-label class="field-label">Data esordio complicanze<span>*</span></mat-label>
        <mat-form-field appearance="outline" class="w-100">
            <input matInput formControlName="dataEsordioComplicanza" placeholder="Inserisci" maxlength="10" />
            <mat-error *ngIf="eziologiaRientroForm.get('dataEsordioComplicanza')?.hasError('required')">Campo
                obbligatorio</mat-error>
            <mat-error *ngIf="eziologiaRientroForm.get('dataEsordioComplicanza')?.hasError('invalidFormat')"
                class="nowrap">{{ errorMessages.INVALID_DATE_FORMAT }}</mat-error>
            <mat-error *ngIf="eziologiaRientroForm.get('dataEsordioComplicanza')?.hasError('futureDate')">{{
                errorMessages.MAX_DATE }}</mat-error>
        </mat-form-field>
    </div>
    <!-- Causa del rientro -->
    <div class="row mb-4 mt-4">
        <div class="col-12 border-hr">
            <mat-label class="field-label">
                Causa del rientro<span>*</span>
            </mat-label>

            <div class="container-fluid px-0 mt-2">
                <div class="row">
                    <ng-container *ngFor="let option of causeRientroOptions">
                        <div class="col-md-4" *ngIf="option?.descrizione?.toUpperCase() !== 'ALTRO'">
                            <mat-checkbox [checked]="isCausaRientroChecked(option)" [disabled]="readOnly"
                                (change)="onCausaRientroChange(option, $event.checked)">
                                {{ option?.descrizione | capitalizeFirst }}
                            </mat-checkbox>
                        </div>
                    </ng-container>
                </div>

                <div class="row mt-2">
                    <div class="col-12">
                        <div class="d-flex align-items-center altro-container">
                            <ng-container *ngFor="let option of causeRientroOptions">
                                <mat-checkbox *ngIf="option?.descrizione?.toUpperCase() === 'ALTRO'"
                                    [checked]="isCausaRientroChecked(option)" [disabled]="readOnly"
                                    (change)="onCausaRientroChange(option, $event.checked)">
                                    {{ option?.descrizione | capitalizeFirst }}
                                </mat-checkbox>
                            </ng-container>

                            <mat-form-field appearance="outline" class="w-100">
                              <input
                                matInput
                                formControlName="altroCausa"
                                [placeholder]="'Inserisci' + (eziologiaRientroForm.get('altroCausa')?.enabled ? '*' : '')"
                                maxlength="250"
                                [matTooltip]="eziologiaRientroForm.get('altroCausa')?.value"
                                matTooltipPosition="above" />
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="d-flex gap-4 mt-4 mb-4 align-items-start flex-wrap">
              <!-- Lesione midollare traumatica -->
              <div [ngStyle]="{ 'flex': isRientroNonCensito ? '1 1 30%' : '1 1 20%' }">
                <label class="field-label d-block mb-2">Lesione midollare traumatica<span>*</span></label>
                <mat-radio-group formControlName="traumatica" class="d-flex gap-4">
                  <mat-radio-button
                    *ngFor="let option of optionsBoolean"
                    [value]="option"
                    color="primary"
                    (mouseup)="datiCliniciService.toggleRadioSelection(eziologiaRientroForm.get('traumatica'), option, $event)">
                    {{ option.descrizione | capitalizeFirst }}
                  </mat-radio-button>
                </mat-radio-group>
              </div>
          
              <!-- Data trauma midollare (only if non censito) -->
              <ng-container *ngIf="isRientroNonCensito">
                <div style="flex: 1 1 30%;">
                  <label class="field-label d-block mb-2">Data trauma midollare<span>*</span></label>
                  <mat-form-field appearance="outline" class="w-100">
                    <input
                      matInput
                      formControlName="dataTrauma"
                      [max]="currentDate"
                      [matDatepicker]="pickerTrauma"
                      placeholder="GG/MM/AAAA" />
                    <mat-datepicker-toggle matSuffix [for]="pickerTrauma"></mat-datepicker-toggle>
                    <mat-datepicker #pickerTrauma></mat-datepicker>
                  </mat-form-field>
                </div>
              </ng-container>
          
              <!-- Anno esordio (only if non censito) -->
              <ng-container *ngIf="isRientroNonCensito">
                <div style="flex: 1 1 30%;"></div>
                <div style="flex: 1 1 30%;">
                  <label class="field-label d-block mb-2">Anno esordio lesione midollare<span *ngIf="eziologiaRientroForm.get('annoEsordioLesione')?.enabled">*</span></label>
                  <mat-form-field appearance="outline" class="w-100">
                    <input
                      matInput
                      formControlName="annoEsordioLesione"
                      placeholder="AAAA"
                      [max]="currentDate.getFullYear()"
                      maxlength="4" />
                  </mat-form-field>
                </div>
              </ng-container>
          
              <!-- Non traumatica -->
              <div style="flex: 1 1 30%;">
                <label class="field-label d-block mb-2">Non traumatica</label>
                <mat-form-field appearance="outline" class="w-100">
                  <mat-select
                    formControlName="tipoNonTraumatica"
                    placeholder="Seleziona">
                    <mat-option [value]="null"></mat-option>
                    <mat-option *ngFor="let option of nonTraumaticaOptions" [value]="option">
                      {{ option.descrizione }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
          
              <!-- Altro -->
              <div style="flex: 1 1 30%;">
                <label class="field-label d-block mb-2">Altro<span *ngIf="eziologiaRientroForm.get('altroNonTraumatica')?.enabled">*</span></label>
                <mat-form-field appearance="outline" class="w-100">
                  <input
                    matInput
                    formControlName="altroNonTraumatica"
                    [placeholder]="'Inserisci' + (eziologiaRientroForm.get('altroNonTraumatica')?.enabled ? '*' : '')"
                    maxlength="250"
                    [matTooltip]="eziologiaRientroForm.get('altroNonTraumatica')?.value"
                    matTooltipPosition="above" />
                </mat-form-field>
              </div>
            </div>
          </div>
          
        <div class="row mb-4">

        </div>
    </div>
</form>