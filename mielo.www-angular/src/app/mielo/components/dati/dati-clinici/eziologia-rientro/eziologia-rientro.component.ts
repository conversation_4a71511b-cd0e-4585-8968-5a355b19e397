import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, signal } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import {debounceTime, distinctUntilChanged, forkJoin, Subscription} from 'rxjs';
import { MaterialModule } from '../../../../../core/material.module';
import { ERROR_MESSAGE } from '../../../../../shared/enums/enum';
import { DizionarioModel } from '../../../../../shared/interfaces/scheda-ricovero.interface';
import { CheckBoxSempliceModel } from '../../../../../shared/interfaces/shared/shared.interface';
import { CapitalizePipe } from '../../../../../shared/pipes/capitalize.pipe';
import { DecoderService } from '../../../../../shared/services/decoder.service';
import { dataEsordioComplicanzaValidator } from '../../../../../shared/validators/data-esordio-complicanza.validator';
import { futureDateValidator } from '../../../../../shared/validators/data-futura.validator';
import { DatiCliniciService } from '../../../../services/dati-clinici.service';
import { SchedaRicoveroService } from '../../../../services/scheda-ricovero.service';
import { SchedaEziologiaRientroModel } from '../../../../../shared/interfaces/dati-clinici.interface';

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
    selector: 'app-eziologia-rientro',
    standalone: true,
    imports: [
        MaterialModule,
        CommonModule,
        ReactiveFormsModule,
        MatDatepickerModule,
        MatNativeDateModule,
        CapitalizePipe
    ],
    templateUrl: './eziologia-rientro.component.html',
    styleUrl: './eziologia-rientro.component.scss'
})
export class EziologiaRientroComponent implements OnInit, OnDestroy {
    @Input() readOnly = false;
    @Output() formInitialized = new EventEmitter<void>();

    eziologiaRientroForm: FormGroup;
    datiEziologiaRientro: SchedaEziologiaRientroModel;

    formReady = signal(false);
    today: Date = new Date();
    currentDate: Date = new Date();
    errorMessages = ERROR_MESSAGE;

    isRientroNonCensito: boolean = false;
    isRientroCensito: boolean = false;

    // Mock
    causeRientroOptions: DizionarioModel[] = [];

    nonTraumaticaOptions: DizionarioModel[] = [];

    optionsBoolean: DizionarioModel[] = [
        { idDizionario: 1, categoria: '', descrizione: 'SI' },
        { idDizionario: 2, categoria: '', descrizione: 'NO' }
    ];

    private subscriptions: Subscription[] = [];

    constructor(
        private fb: FormBuilder,
        private iconRegistry: MatIconRegistry,
        private sanitizer: DomSanitizer,
        public datiCliniciService: DatiCliniciService,
        private schedaRicoveroService: SchedaRicoveroService,
        private decoderService: DecoderService
    ) {
        this.iconRegistry.addSvgIconLiteral('it-calendar', this.sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

        if (this.datiCliniciService.getDatiEziologiaRientroValue()) this.datiEziologiaRientro = this.datiCliniciService.getDatiEziologiaRientroValue()!;

        this.isRientroCensito = this.schedaRicoveroService.getEventoRientroCensito();
        this.isRientroNonCensito = this.schedaRicoveroService.getEventoRientroNonCensito();

        // Creo i controlli con lo stato disabled iniziale
        const causaRientroLista = this.fb.control([], Validators.required);
        const altroCausa = this.fb.control('', [Validators.maxLength(250)]);

        const dataTraumaControl = this.fb.control(
            { value: null, disabled: true },
            [Validators.required, futureDateValidator()]
        );
        const tipoNonTraumaticaControl = this.fb.control({ value: null, disabled: true });
        const altroNonTraumaticaControl = this.fb.control({ value: '', disabled: true }, [Validators.maxLength(250)]);
        const dataEsordioComplicanzaControl = this.fb.control(
            { value: '', disabled: false },
            [Validators.required, dataEsordioComplicanzaValidator(this.currentDate)]
        );

        this.eziologiaRientroForm = this.fb.group({
            causaRientroLista,
            altroCausa,
            traumatica: [null, Validators.required],
            tipoNonTraumatica: tipoNonTraumaticaControl,
            altroNonTraumatica: altroNonTraumaticaControl,
            dataEsordioComplicanza: dataEsordioComplicanzaControl,
            ...this.isRientroNonCensito ? {
                dataTrauma: dataTraumaControl,
                annoEsordioLesione: [{ value: null, disabled: true }, [
                    Validators.required,
                    (control: AbstractControl) => {
                        const value = control.value?.toString();
                        if (!value) return null;
    
                        // Prima controlla la lunghezza
                        if (value.length !== 4) {
                            return { invalidYearLength: true };
                        }
    
                        // Se la lunghezza è corretta, controlla se è un anno futuro
                        const year = parseInt(value);
                        if (year > new Date().getFullYear()) {
                            return { futureYear: true };
                        }
    
                        return null;
                    }
                ]]
            } : {},
            
        });

        const dataEsordioControl = this.eziologiaRientroForm.get('dataEsordioComplicanza');
        if (dataEsordioControl) {
            this.subscriptions.push(
                dataEsordioControl.valueChanges.subscribe(value => {
                    if (value instanceof Date) {
                        const formattedDate = this.formatDate(value);
                        dataEsordioControl.setValue(formattedDate, { emitEvent: false });
                    }
                })
            );
        }
    }

    ngOnInit(): void {
        forkJoin({
            nonTraumatica: this.decoderService.getDcodNonTraumatica(),
            causaRientroLista: this.decoderService.getDcodCausaRientro()
        }).subscribe(({ nonTraumatica, causaRientroLista }) => {
            this.nonTraumaticaOptions = nonTraumatica;
            this.causeRientroOptions = causaRientroLista;
            this.eziologiaRientroForm.get('causaRientroLista')?.setValue(this.causeRientroOptions.filter(causa =>
                this.datiEziologiaRientro.causaRientroLista.some(
                    (checkbox: CheckBoxSempliceModel) => checkbox.idDizionario === causa.idDizionario
                )
            ) || []);
            this.eziologiaRientroForm.get('causaRientroLista')?.updateValueAndValidity();
            if (this.datiEziologiaRientro) {
                this.populateForm(this.datiEziologiaRientro);
            }

        // Se il form è in sola lettura, disabilito tutto
            if (this.readOnly) {
                Object.keys(this.eziologiaRientroForm.controls).forEach(controlName => {
                    this.eziologiaRientroForm.get(controlName)?.disable({ emitEvent: false });
                });
            }
        });

        if (!this.readOnly) {
            // Gestione abilitazione campi dinamici
            this.subscriptions.push(
                this.eziologiaRientroForm.get('causaRientroLista')!.valueChanges
                    .subscribe((values: DizionarioModel[]) => {
                        const altroId = this.altroCausaRientroId;
                        const altroSelected = values.some(v => v.idDizionario === altroId);
                        const altroCtrl = this.eziologiaRientroForm.get('altroCausa');
                        if (altroSelected) {
                            altroCtrl?.enable();
                            altroCtrl?.setValidators([Validators.required, Validators.maxLength(250)]);
                        } else {
                            altroCtrl?.disable();
                            altroCtrl?.setValue('');
                            altroCtrl?.clearValidators();
                        }
                        altroCtrl?.updateValueAndValidity();
                    })
            );

            // Gestione cambiamenti traumatica
            this.subscriptions.push(
                this.eziologiaRientroForm.get('traumatica')!.valueChanges.subscribe((value: DizionarioModel) => {
                    this.updateControlsBasedOnTraumatica(value);
                })
            );

            // Gestione cambiamenti tipo non traumatica
            this.subscriptions.push(
                this.eziologiaRientroForm.get('tipoNonTraumatica')!.valueChanges.subscribe((option: DizionarioModel) => {
                    const altroCtrl = this.eziologiaRientroForm.get('altroNonTraumatica');
                    if (option?.idDizionario === this.altroNonTraumaticaId) {
                        altroCtrl?.enable();
                        altroCtrl?.setValidators([Validators.required, Validators.maxLength(250)]);
                    } else {
                        altroCtrl?.disable();
                        altroCtrl?.setValue('');
                        altroCtrl?.clearValidators();
                    }
                    altroCtrl?.updateValueAndValidity();
                })
            );

            // Subscription per il salvataggio dei dati
            const formChanges = this.eziologiaRientroForm.valueChanges.pipe(
                debounceTime(1),
                distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b))
            ).subscribe(() => {
                const formValue = this.eziologiaRientroForm.getRawValue();

                // Converti DizionarioModel in boolean per traumatica
                const convertBooleanValue = (val: DizionarioModel | null): boolean | null => {
                    if (!val || !val.descrizione) return null;
                    if (val.descrizione.toUpperCase() === 'SI') return true;
                    if (val.descrizione.toUpperCase() === 'NO') return false;
                    return null;
                };

                // Salva i dati nel service
                this.datiCliniciService.setDatiEziologiaRientro({
                    ...formValue,
                    causaRientroLista: this.eziologiaRientroForm.get('causaRientroLista')?.value.map((item: DizionarioModel) => {
                        return {
                            idDizionario: item.idDizionario
                        };
                    }),
                    traumatica: convertBooleanValue(formValue.traumatica),
                    dataTrauma: this.manageDataToSave(formValue.dataTrauma),
                    dataEsordioComplicanza: formValue.dataEsordioComplicanza,
                    annoEsordioLesione: formValue.annoEsordioLesione,
                    altroCausa: formValue.altroCausa
                });

                // Aggiorna lo stato di validità del form
                this.datiCliniciService.setDatiEziologiaRientroValido(this.eziologiaRientroForm.valid);
            });

            this.subscriptions.push(formChanges);
        }

        this.formReady.set(true);
        this.formInitialized.emit();
    }

    private populateForm(dati: any): void {
        const findOption = (options: DizionarioModel[], diz: DizionarioModel | null | undefined): DizionarioModel | null => {
            if (!diz || !diz.idDizionario) return null;
            return options.find(opt => opt.idDizionario === diz.idDizionario) ?? null;
        };
        // Trova le reference corrette per la lista delle cause rientro
        const listaCausaRientro = Array.isArray(dati.causaRientroLista)
            ? dati.causaRientroLista
                .map((item: DizionarioModel) => this.causeRientroOptions.find(opt => opt.idDizionario === item.idDizionario))
                .filter(Boolean)
            : [];

        // Gestione del campo "Altro" per causa rientro
        const altroSelected = listaCausaRientro.some((v: DizionarioModel) => v?.descrizione?.toUpperCase() === 'ALTRO');
        const altroCtrl = this.eziologiaRientroForm.get('altroCausa');
        if (altroSelected) {
            altroCtrl?.enable();
            altroCtrl?.setValidators([Validators.required, Validators.maxLength(250)]);
            altroCtrl?.setValue(dati.altroCausa || '', { emitEvent: false });
        } else {
            altroCtrl?.disable();
            altroCtrl?.setValue('', { emitEvent: false });
            altroCtrl?.clearValidators();
        }
        altroCtrl?.updateValueAndValidity({ emitEvent: false });

        // Trova la reference corretta per traumatica
        let traumaticaValue: DizionarioModel | null = null;
        if (dati.traumatica === true) {
            traumaticaValue = this.optionsBoolean.find(opt => opt.descrizione === 'SI') ?? null;
        } else if (dati.traumatica === false) {
            traumaticaValue = this.optionsBoolean.find(opt => opt.descrizione === 'NO') ?? null;
        } else if (dati.traumatica && dati.traumatica.idDizionario) {
            traumaticaValue = findOption(this.optionsBoolean, dati.traumatica);
        }

        // Trova la reference corretta per tipoNonTraumatica
        const tipoNonTraumaticaValue = findOption(this.nonTraumaticaOptions, dati.tipoNonTraumatica);

        // PATCHA I CAMPI NON-DATE
        this.eziologiaRientroForm.get('causaRientroLista')?.setValue(listaCausaRientro, { emitEvent: false });

        // Imposto traumatica una prima volta senza emettere eventi
        this.eziologiaRientroForm.get('traumatica')?.setValue(traumaticaValue, { emitEvent: false });

        this.eziologiaRientroForm.get('tipoNonTraumatica')?.setValue(tipoNonTraumaticaValue, { emitEvent: false });
        this.eziologiaRientroForm.get('altroNonTraumatica')?.setValue(dati.altroNonTraumatica, { emitEvent: false });

        // PATCHA I CAMPI DATA
        const dataTraumaCtrl = this.eziologiaRientroForm.get('dataTrauma');
        const dataEsordioComplicanzaCtrl = this.eziologiaRientroForm.get('dataEsordioComplicanza');

        // Gestione data trauma
        if (dati.dataTrauma) {
            const dataTrauma = new Date(dati.dataTrauma);
            dataTraumaCtrl?.setValue(dataTrauma, { emitEvent: false });
        }

        // Gestione data esordio complicanza
        if (dati.dataEsordioComplicanza) {
            let formattedDate: string;
            if (typeof dati.dataEsordioComplicanza === 'string') {
                // Usa la stringa così com'è (può essere GG/MM/AAAA, MM/AAAA, AAAA)
                formattedDate = dati.dataEsordioComplicanza;
            } else {
                const dataEsordio = new Date(dati.dataEsordioComplicanza);
                formattedDate = this.formatDate(dataEsordio);
            }
            dataEsordioComplicanzaCtrl?.setValue(formattedDate, { emitEvent: false });
        }

        this.updateControlsBasedOnTraumatica(traumaticaValue);

        if (tipoNonTraumaticaValue) {
            this.eziologiaRientroForm.get('tipoNonTraumatica')?.setValue(tipoNonTraumaticaValue, { emitEvent: true });
        }

        if (traumaticaValue) {
            this.eziologiaRientroForm.get('traumatica')?.setValue(traumaticaValue, { emitEvent: true });
        }

        // Gestione anno esordio lesione
        if (dati.annoEsordioLesione) {
            this.eziologiaRientroForm.get('annoEsordioLesione')?.setValue(dati.annoEsordioLesione, { emitEvent: false });
        }

        this.eziologiaRientroForm.updateValueAndValidity();
    }

    private formatDate(date: Date): string {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    }

    private manageDataToSave(data: string | Date): number | null {
        if (!data) return null;

        let dateToSave: Date;
        if (typeof data === 'string') {
            const [day, month, year] = data.split('/').map(Number);
            dateToSave = new Date(year, month - 1, day);
        } else {
            dateToSave = new Date(data);
        }

        dateToSave.setHours(0, 0, 0, 1);
        return dateToSave.getTime();
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(s => s.unsubscribe());
    }

    onCausaRientroChange(option: DizionarioModel, checked: boolean): void {
        if (!option) return;

        const current = this.eziologiaRientroForm.get('causaRientroLista')?.value as DizionarioModel[] || [];
        let updated: DizionarioModel[];
        if (checked) {
            updated = [...current, option];
        } else {
            updated = current.filter(o => o?.idDizionario !== option?.idDizionario);
        }
        this.eziologiaRientroForm.get('causaRientroLista')?.setValue(updated);
        this.eziologiaRientroForm.get('causaRientroLista')?.markAsDirty();
        this.eziologiaRientroForm.get('causaRientroLista')?.updateValueAndValidity();
    }

    isCausaRientroChecked(option: DizionarioModel): boolean {
        if (!option || !this.eziologiaRientroForm.get('causaRientroLista')?.value) {
            return false;
        }
        return (this.eziologiaRientroForm.get('causaRientroLista')?.value as DizionarioModel[]).some(o => o?.idDizionario === option?.idDizionario);
    }

    isAltroCausaSelected(): boolean {
        return (this.eziologiaRientroForm.get('causaRientroLista')?.value as DizionarioModel[]).some(o => o.descrizione === 'Altro');
    }

    isAltroNonTraumaticaSelected(): boolean {
        const val = this.eziologiaRientroForm.get('tipoNonTraumatica')?.value;
        return val && val.idDizionario === this.altroNonTraumaticaId;
    }

    get causeRientroRows(): DizionarioModel[][] {
        const rows: DizionarioModel[][] = [];
        const options = this.causeRientroOptions;
        for (let i = 0; i < options.length; i += 3) {
            rows.push(options.slice(i, i + 3));
        }
        return rows;
    }

    get altroOption(): DizionarioModel {
        return this.causeRientroOptions?.find(opt => opt?.descrizione === 'Altro') ||
            { idDizionario: 99, categoria: '', descrizione: 'Altro' };
    }

    private updateControlsBasedOnTraumatica(value: DizionarioModel | null): void {
        // Otteniamo i controlli all'interno del metodo
        const dataTraumaControl = this.eziologiaRientroForm.get('dataTrauma');
        const tipoNonTraumaticaControl = this.eziologiaRientroForm.get('tipoNonTraumatica');
        const altroNonTraumaticaControl = this.eziologiaRientroForm.get('altroNonTraumatica');
        const annoEsordioLesioneControl = this.eziologiaRientroForm.get('annoEsordioLesione');

        const descrizione = value?.descrizione?.toUpperCase();

        if (descrizione === 'SI') {
            dataTraumaControl?.enable();
            tipoNonTraumaticaControl?.setValue(null, { emitEvent: false });
            tipoNonTraumaticaControl?.disable({ emitEvent: false });

            altroNonTraumaticaControl?.setValue('', { emitEvent: false });
            altroNonTraumaticaControl?.disable({ emitEvent: false });

            annoEsordioLesioneControl?.setValue(null, { emitEvent: false });
            annoEsordioLesioneControl?.disable({ emitEvent: false });
        } else if (descrizione === 'NO') {
            dataTraumaControl?.setValue(null, { emitEvent: false });
            dataTraumaControl?.disable({ emitEvent: false });

            tipoNonTraumaticaControl?.enable();
            annoEsordioLesioneControl?.enable();

            // Gestisci 'Altro' 
            const tipoNonTraumatica = tipoNonTraumaticaControl?.value;
            if (tipoNonTraumatica?.idDizionario === this.altroNonTraumaticaId) {
                altroNonTraumaticaControl?.enable();
                altroNonTraumaticaControl?.setValidators([Validators.required, Validators.maxLength(250)]);
            } else {
                altroNonTraumaticaControl?.setValue('', { emitEvent: false });
                altroNonTraumaticaControl?.disable({ emitEvent: false });
                altroNonTraumaticaControl?.clearValidators();
            }
            altroNonTraumaticaControl?.updateValueAndValidity({ emitEvent: false });
        } else {
            dataTraumaControl?.setValue(null, { emitEvent: false });
            dataTraumaControl?.disable({ emitEvent: false });

            tipoNonTraumaticaControl?.setValue(null, { emitEvent: false });
            tipoNonTraumaticaControl?.disable({ emitEvent: false });

            altroNonTraumaticaControl?.setValue('', { emitEvent: false });
            altroNonTraumaticaControl?.disable({ emitEvent: false });

            annoEsordioLesioneControl?.setValue(null, { emitEvent: false });
            annoEsordioLesioneControl?.disable({ emitEvent: false });
        }
    }

    // Trova dinamicamente l'id di 'Altro' per Non Traumatica
    get altroNonTraumaticaId(): number | null {
        const altro = this.nonTraumaticaOptions.find(opt => opt.descrizione?.toUpperCase() === 'ALTRO');
        return altro ? altro.idDizionario : null;
    }

    get altroCausaRientroId(): number | null {
        const altro = this.causeRientroOptions.find(opt => opt.descrizione?.toUpperCase() === 'ALTRO');
        return altro ? altro.idDizionario : null;
    }
} 