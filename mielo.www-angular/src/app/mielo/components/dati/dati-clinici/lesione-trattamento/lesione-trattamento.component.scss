:host ::ng-deep {
    .mat-icon-button {
        width: 40px;
        height: 40px;
        line-height: 40px;

        .mat-icon {
            color: #003354;
            fill: #003354;
        }
    }

    .mat-datepicker-toggle {
        .mat-icon {
            color: #003354;
            fill: #003354;
        }
    }

    .mat-form-field-disabled,
    .mat-mdc-form-field-disabled {
        .mat-datepicker-toggle .mat-icon,
        .mat-icon-button .mat-icon {
            color: #666666 !important;
            fill: #666666 !important;
        }
    }
}

:host {
    .icon-color {
        color: #003354 !important;
    }
    .field-label {
        color: #003354;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 18px;
        font-weight: 600;
    }

    .font-color{
        color: #003354;
        font-weight: bold;
    }

    .small-datepicker {
        width: 180px;
        position: relative;

        ::ng-deep .mat-form-field-flex {
            padding: 0 0.5rem !important;
        }

        .field-label {
            position: absolute;
            top: -27px;
            left: -14px;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: #003354;
            z-index: 1;
        }
    }

    .max-width-trattamento {
        max-width: 28.34% !important;
    }

    .radio-group-container {
        padding: 0;
        min-height: 5em;
        display: flex;
        flex-direction: column;
        width: 100%;
        background-color: transparent;
        border: none;

        .radio-height {
            height: 6em;
        }

        .mat-radio-group {
            display: flex;
            gap: 1rem;

            @media (max-width: 576px) {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    }

    .note-width {
        width: 7em;
    }

    .checkbox-container {
        min-height: 40px;
        display: flex;
        align-items: center;
    }

    .btn-edit {
        color: #297A38;
        padding: 8px 16px;
        display: flex;
        align-items: center;

        .edit-icon {
            margin-right: 8px;
            font-size: 18px;
            height: 18px;
            width: 18px;
        }
    }


    mat-form-field {
        width: 100%;
    }

    .buttons-container {
        .note-btn-container, .remove-btn-container {
            margin-right: 8px;
            min-width: 100px;

            &:last-child {
                margin-right: 0;
            }

            button {
                width: 100%;
                justify-content: flex-start;
            }
        }
    }

    ::ng-deep {
        .mat-radio-button {
            .mat-radio-container {
                height: 16px;
                width: 16px;
            }

            .mat-radio-outer-circle,
            .mat-radio-inner-circle {
                height: 16px;
                width: 16px;
            }

            .mat-radio-label {
                display: flex;
                align-items: center;
            }

            .mat-radio-label-content {
                padding-left: 8px;
                line-height: normal;
                display: flex;
                align-items: center;
            }
        }

        .mat-form-field-appearance-outline {
            .mat-form-field-wrapper {
                margin: 0;
                padding: 0;
                width: 100%;
            }

            .mat-form-field-flex {
                background-color: white;
                min-height: 56px;
                padding: 0 0.75em 0 0.75em;
            }

            .mat-form-field-outline {
                color: #e0e0e0;
                opacity: 0.5;
            }

            .mat-form-field-outline-start,
            .mat-form-field-outline-end,
            .mat-form-field-outline-gap {
                border-width: 1px !important;
            }

            &.mat-focused .mat-form-field-outline-thick {
                color: #297A38;
            }

            .mat-form-field-infix {
                padding: 0.5em 0;
                border-top: none;
                width: calc(100% - 24px);
                background-color: #ffffff;
            }

            .mat-icon-button {
                width: 40px;
                height: 40px;
                line-height: 40px;

                .mat-icon {
                    color: #003354;
                    fill: #003354;
                }
            }

            .mat-datepicker-toggle {
                position: relative;
                width: 24px;
                height: 24px;

                .mat-icon {
                    color: #003354;
                    fill: #003354;
                }
            }

            .mat-form-field-disabled {
                .mat-datepicker-toggle {
                    .mat-icon {
                        color: #666666;
                        fill: #666666;
                    }
                }

                .mat-icon-button {
                    .mat-icon {
                        color: #666666;
                        fill: #666666;
                    }
                }
            }

            .mat-mdc-form-field-error {
                .mat-datepicker-toggle {
                    .mat-icon {
                        color: #666666 !important;
                        fill: #666666 !important;
                    }
                }
            }
        }

        .mat-mdc-form-field-infix > label,
        .mdc-form-field > label {
            margin-left: 0;
            padding-left: 4px;
            padding-right: 0;
            //padding-bottom: 8px;
            margin-right: 8px;
            order: 0;
        }

        .timepicker-overlay {
            z-index: 1000;
        }

        .timepicker-backdrop-overlay {
            z-index: 999;
        }

        .timepicker {
            .timepicker__header {
                background-color: #297A38 !important;
            }

            .clock-face__clock-hand {
                background-color: #297A38 !important;

                &::after {
                    background-color: #297A38 !important;
                }

                &::before {
                    border-color: #297A38 !important;
                }
            }

            .clock-face__number {
                > span.active {
                    background-color: #297A38 !important;
                }
            }

            button:not(.timepicker-button) {
                color: #297A38 !important;
            }
        }

        .mat-expansion-panel-header {
            height: 64px;
        }

        // Impedisce l'andare a capo del testo dei radio button
        .mat-radio-label-content {
            white-space: nowrap;
        }
    }
}

hr {
    opacity: 0.2;
    background-color: #D9DBE9;
}

.row.border-bottom {
    border-bottom: 1px solid #D9DBE9;
    padding-bottom: 1.5rem;
}

.intervento-form-group {
    margin-bottom: 2rem;
}

mat-error {
    white-space: nowrap;
}

.text-bold {
    font-weight: 700 !important;
}

.gap-radio-note {
    gap: 1.2rem;
    flex-wrap: nowrap;
    min-width: 0;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}

.radio-group-custom {
    gap: 2rem;
    align-items: center;
    flex-wrap: nowrap;
}

.note-col {
    min-width: 90px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-left: 0;
}

@media (max-width: 768px) {
    .note-col {
        margin-top: 0.5rem;
        justify-content: flex-start;
    }
    .radio-group-custom {
        gap: 0.7rem;
    }
}

// Impedisce l'andare a capo del testo dei radio button
::ng-deep .radio-button-custom {
  white-space: nowrap;
}

::ng-deep .radio-button-custom {
  min-width: 0;
  max-width: 100%;
}

.remove-button {
    color: #D90505 !important;
    width: auto !important;
}

.mdc-icon-button {

    color: rgba(0, 0, 0, 0.54) !important;

    &:disabled {
        color: rgba(0, 0, 0, 0.3) !important;
    }
}