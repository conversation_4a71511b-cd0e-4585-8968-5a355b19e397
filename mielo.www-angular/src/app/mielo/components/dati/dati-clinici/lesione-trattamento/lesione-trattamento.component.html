<form [formGroup]="lesioneTrattamentoForm" class="box-dati mt-4">
    <div>
        <div class="row mb-3">
            <div class="col-2">
                <label class="field-label">Diagnostica</label>
            </div>
        </div>

        <!-- Sezione Diagnostica -->
        <div class="row d-flex justify-content-between">

            <!-- RX e Data esame -->
            <div class="col-4 d-flex align-items-end">
                <mat-checkbox formControlName="rx" color="primary"
                    class="me-4 pt-2 d-flex align-items-center">RX</mat-checkbox>
                <div class="col">
                    <label class="field-label">Data esame<span
                            *ngIf="lesioneTrattamentoForm.get('dataEsameRx')?.enabled">*</span></label>
                    <mat-form-field appearance="outline">
                        <input matInput formControlName="dataEsameRx" [matDatepicker]="picker1" placeholder="GG/MM/AAAA"
                            [max]="currentDate" (blur)="lesioneTrattamentoForm.get('dataEsameRx')?.markAsTouched()">
                        <mat-datepicker-toggle matSuffix [for]="picker1">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker1></mat-datepicker>
                        <mat-error
                            *ngIf="datiCliniciService.getDateTimeErrorMessage(lesioneTrattamentoForm.get('dataEsameRx'))">
                            {{ datiCliniciService.getDateTimeErrorMessage(lesioneTrattamentoForm.get('dataEsameRx')) }}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>

            <!-- TC e Data esame -->
            <div class="col-4 d-flex align-items-end">
                <mat-checkbox formControlName="tc" color="primary"
                    class="me-4 pt-2 d-flex align-items-center">TC</mat-checkbox>
                <div class="col">
                    <label class="field-label">Data esame<span
                            *ngIf="lesioneTrattamentoForm.get('dataEsameTc')?.enabled">*</span></label>
                    <mat-form-field appearance="outline">
                        <input matInput formControlName="dataEsameTc" [matDatepicker]="picker2" placeholder="GG/MM/AAAA"
                            [max]="currentDate" (blur)="lesioneTrattamentoForm.get('dataEsameTc')?.markAsTouched()">
                        <mat-datepicker-toggle matSuffix [for]="picker2">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker2></mat-datepicker>
                        <mat-error
                            *ngIf="datiCliniciService.getDateTimeErrorMessage(lesioneTrattamentoForm.get('dataEsameTc'))">
                            {{ datiCliniciService.getDateTimeErrorMessage(lesioneTrattamentoForm.get('dataEsameTc')) }}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>

            <!-- RMN e Data esame -->
            <div class="col-4 d-flex align-items-end">
                <mat-checkbox formControlName="rmn" color="primary"
                    class="me-4 pt-2 d-flex align-items-center">RMN</mat-checkbox>
                <div class="col">
                    <label class="field-label">Data esame<span
                            *ngIf="lesioneTrattamentoForm.get('dataEsameRmn')?.enabled">*</span></label>
                    <mat-form-field appearance="outline">
                        <input matInput formControlName="dataEsameRmn" [matDatepicker]="picker3"
                            placeholder="GG/MM/AAAA" [max]="currentDate"
                            (blur)="lesioneTrattamentoForm.get('dataEsameRmn')?.markAsTouched()">
                        <mat-datepicker-toggle matSuffix [for]="picker3">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker3></mat-datepicker>
                        <mat-error
                            *ngIf="datiCliniciService.getDateTimeErrorMessage(lesioneTrattamentoForm.get('dataEsameRmn'))">
                            {{ datiCliniciService.getDateTimeErrorMessage(lesioneTrattamentoForm.get('dataEsameRmn')) }}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
        </div>

        <hr>

        <!-- Sezione Lesione vertebra/e -->
        <div class="row my-3">
            <div class="col-auto d-flex align-items-center">
                <label class="field-label mr-5">Lesione vertebra/e<span *ngIf="!isRiabilitazione">*</span></label>
                <div class="ml-4 pl-1 d-flex align-items-center">
                    <button mat-button class="btn-edit" type="button" (click)="openPopupLesioneVertebra()"
                        [disabled]="readOnly">
                        <mat-icon class="edit-icon">edit</mat-icon>
                        <span>Seleziona</span>
                    </button>
                    <label class="field-label ml-3">
                        Selezionate: <span class="text-bold"
                            *ngIf="vertebreSelezionate && vertebreSelezionate.length > 0">
                            {{ vertebreSelezionateDescrizioni }}
                        </span>
                    </label>
                </div>
                <mat-error
                    *ngIf="lesioneTrattamentoForm.get('lesioneVertebra')?.hasError('required') && lesioneTrattamentoForm.get('lesioneVertebra')?.touched">
                    Campo obbligatorio
                </mat-error>
            </div>
        </div>

        <!-- Sezione Tipo lesione vertebrale -->
        <div class="row my-2 align-items-center">
            <div class="col-auto mr-4 pr-4">
                <label class="field-label">Tipo lesione vertebrale<span *ngIf="!isRiabilitazione">*</span></label>
            </div>
            <div class="col d-flex align-items-center gap-radio-note">
                <mat-radio-group formControlName="tipoLesioneVertebrale" class="d-flex radio-group-custom flex-nowrap">
                    <mat-radio-button *ngFor="let option of optionsTipoLesioneVertebrale; let first = first"
                        [value]="option" color="primary" [class.ms-4]="!first"
                        (mouseup)="datiCliniciService.toggleRadioSelection(lesioneTrattamentoForm.get('tipoLesioneVertebrale'), option, $event)"
                        class="radio-button-custom">
                        {{ option.descrizione | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
                <button mat-button type="button" class="note-button d-flex align-items-center ms-3"
                    [disabled]="isNoteDisabled('tipoLesioneVertebrale')"
                    (click)="openPopupNote('tipoLesioneVertebrale')">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span class="ms-1">Note</span>
                </button>
            </div>
        </div>

        <hr class="my-4">

        <!-- Sezione Intervento chirurgico -->
        <div class="row d-flex align-items-center mb-4">
            <div class="col-auto mr-4 pr-4">
                <label class="field-label">Intervento chirurgico<span
                        *ngIf="isRequired('interventiChirurgici')">*</span></label>
            </div>
            <div class="col-auto ml-3">
                <mat-radio-group formControlName="interventiChirurgici" class="d-flex">
                    <mat-radio-button
                        (click)="toggleRadioSelection(lesioneTrattamentoForm, 'interventiChirurgici',true)"
                        [value]="true" color="primary">Si
                    </mat-radio-button>
                    <mat-radio-button
                        (click)="toggleRadioSelection(lesioneTrattamentoForm, 'interventiChirurgici',false)"
                        [value]="false" class="ms-4" color="primary">No
                    </mat-radio-button>
                </mat-radio-group>
            </div>
            <div class="col d-flex">
                <button mat-button type="button" class="note-button ms-4" [disabled]="!canAddIntervento || readOnly"
                    [matTooltip]="!canAddIntervento ? 'Compila tutti i campi obbligatori dell\'intervento corrente prima di aggiungerne uno nuovo' : ''"
                    (click)="addIntervento()">
                    <mat-icon>add_circle_outline</mat-icon>
                    <span class="ms-1">Aggiungi intervento</span>
                </button>
            </div>
        </div>

        <div *ngFor="let interventoControl of interventiFormArray.controls; let i = index"
            [formGroup]="asFormGroup(interventoControl)" class="intervento-form-group">
            <div class="d-flex align-items-center">
                <!-- Data intervento -->
                <div class="col-3">
                    <label class="field-label mb-1">
                        Data intervento<span *ngIf="interventoControl.get('dataIntervento')?.hasValidator(Validators.required)">*</span>
                    </label>
                    <mat-form-field appearance="outline">
                        <input matInput [matDatepicker]="pickerAgg" formControlName="dataIntervento"
                            placeholder="GG/MM/AAAA" [max]="currentDate">
                        <mat-datepicker-toggle matSuffix [for]="pickerAgg">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #pickerAgg></mat-datepicker>
                        <mat-error
                            *ngIf="datiCliniciService.getDateTimeErrorMessage(asFormGroup(interventoControl).get('dataIntervento'))">
                            {{
                            datiCliniciService.getDateTimeErrorMessage(asFormGroup(interventoControl).get('dataIntervento'))
                            }}
                        </mat-error>
                    </mat-form-field>
                </div>
                <!-- Ora intervento -->
                <div class="col-2">
                    <label class="field-label mb-1">Ora intervento</label>
                    <mat-form-field class="p-0" appearance="outline">
                        <input class="p-0" matInput type="time" formControlName="oraIntervento" placeholder="--:--">
                        <button type="button p-0" mat-icon-button matSuffix class="pointer-events-none" [disabled]="asFormGroup(interventoControl).get('dataIntervento')?.disabled">
                            <mat-icon>access_time</mat-icon>
                        </button>
                        <mat-error
                            *ngIf="datiCliniciService.getDateTimeErrorMessage(asFormGroup(interventoControl).get('oraIntervento'))">
                            {{
                            datiCliniciService.getDateTimeErrorMessage(asFormGroup(interventoControl).get('oraIntervento'))
                            }}
                        </mat-error>
                    </mat-form-field>
                </div>
                <!-- Tipologia intervento -->
                <div style="width: 35% !important;">
                    <div class="d-flex align-items-start">
                        <div class="flex-grow-1">
                            <label class="field-label mb-1">
                                Tipologia intervento<span
                                    *ngIf="interventoControl.get('tipologiaInterventoId')?.hasValidator(Validators.required)">*</span>
                            </label>
                            <mat-form-field appearance="outline">
                                <mat-select formControlName="tipologiaInterventoId" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsTipologiaIntervento"
                                        [value]="option.idDizionario">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                                <mat-error *ngIf="interventoControl.get('tipologiaInterventoId')?.hasError('required')">
                                    {{ERROR_MESSAGE.REQUIRED}}
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
                <!-- Note e Rimuovi -->
                <div class="col-1 d-flex align-self-end buttons-container justify-content-start ml-3">
                    <div class="note-btn-container">
                        <button mat-button type="button" class="note-button" (click)="editInterventoNote(i)"
                            [disabled]="!isInterventoChirurgicoSi">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                    <div class="remove-btn-container" *ngIf="interventiFormArray.controls.length > 1">
                        <button mat-button type="button" class="remove-button" (click)="removeIntervento(i)"
                            [disabled]="!isInterventoChirurgicoSi || readOnly">
                            <mat-icon>highlight_off</mat-icon>
                            <span class="ms-1">Rimuovi</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <hr class="my-4">

        <!-- Sezione Traumi associati -->

        <div class="row d-flex align-items-center" style="gap: 1rem;">
            <label class="field-label col-auto">Traumi associati</label>
            <div class="ml-3">
                <button mat-button type="button" class="note-button" [disabled]="isNoteDisabled('traumiAssociati')"
                    (click)="openPopupNote('traumiAssociati')">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note">
                        </use>
                    </svg>
                    <span class="ms-1">Note</span>
                </button>
            </div>
        </div>
        <div class="row d-flex flex-wrap mt-1 align-items-end">
            <div class="checkbox-container mr-4">
                <mat-checkbox formControlName="nessunoTraumi" color="primary">Nessuno</mat-checkbox>
            </div>
            <div class="checkbox-container mr-4">
                <mat-checkbox formControlName="cranioTraumi" color="primary">Cranio</mat-checkbox>
            </div>
            <div class="checkbox-container mr-4">
                <mat-checkbox formControlName="scheletroTraumi" color="primary">Scheletro</mat-checkbox>
            </div>
            <div class="checkbox-container mr-4">
                <mat-checkbox formControlName="organiInterniTraumi" color="primary">Organi
                    interni</mat-checkbox>
            </div>
            <div class="checkbox-container">
                <mat-checkbox formControlName="altroTraumi" color="primary">Altro</mat-checkbox>
            </div>
            <div class="col-3 col-md-3">
                <mat-form-field appearance="outline">
                    <input matInput [matTooltip]="lesioneTrattamentoForm.get('altroTraumi')?.value || ''"
                        formControlName="altroTraumiDescrizione"
                        [placeholder]="lesioneTrattamentoForm.get('altroTraumi')?.getRawValue() ? 'Inserisci*' : 'Inserisci'"
                        maxlength="250">
                    <mat-error *ngIf="lesioneTrattamentoForm.get('altroTraumiDescrizione')?.hasError('required')">
                        {{ERROR_MESSAGE.REQUIRED}}
                    </mat-error>
                </mat-form-field>
            </div>

        </div>

        <hr class="my-4">

        <!-- Sezione Trattamento medico in acuto -->

        <div class="row">
            <label class="field-label col-auto d-flex align-items-center mr-3">
                Trattamento medico in acuto<span *ngIf="!isRiabilitazione">*</span>
            </label>
            <div class="col radio-group-container d-flex flex-row align-items-center">
                <mat-radio-group formControlName="trattamentoMedicoAcuto" class="d-flex flex-wrap">
                    <mat-radio-button *ngFor="let option of optionsTrattamentoMedicoAcuto" [value]="option"
                        color="primary" class="ms-4"
                        (mouseup)="datiCliniciService.toggleRadioSelection(lesioneTrattamentoForm.get('trattamentoMedicoAcuto'), option, $event)">
                        {{ option.descrizione == 'NASCIS' ? option.descrizione : option.descrizione | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
                <div class="col-3 col-md-5">
                    <mat-form-field appearance="outline">
                        <input matInput formControlName="altroTrattamentoMedicoAcuto" maxlength="250"
                            [placeholder]="lesioneTrattamentoForm.get('trattamentoMedicoAcuto')?.getRawValue()?.descrizione === 'ALTRO' ? 'Inserisci*' : 'Inserisci'">
                        <mat-error
                            *ngIf="lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.hasError('required')">
                            Campo obbligatorio
                        </mat-error>
                    </mat-form-field>
                </div>
                <div class="col-auto ml-3">
                    <button mat-button type="button" class="note-button"
                        [disabled]="isNoteDisabled('trattamentoMedicoAcuto')"
                        (click)="openPopupNote('trattamentoMedicoAcuto')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note">
                            </use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div>
        <!-- <div class="row">
            <div class="radio-group-container d-flex flex-row align-items-end">
                <mat-radio-group formControlName="trattamentoMedicoAcuto" class="d-flex flex-wrap">
                    <mat-radio-button *ngFor="let option of optionsTrattamentoMedicoAcuto" [value]="option"
                        color="primary" class="ms-4"
                        (mouseup)="datiCliniciService.toggleRadioSelection(lesioneTrattamentoForm.get('trattamentoMedicoAcuto'), option, $event)">
                        {{ option.descrizione == 'NASCIS' ? option.descrizione : option.descrizione | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
                <div class="col-3 col-md-3">
                    <mat-form-field appearance="outline">
                        <input matInput
                                           [matTooltip]="lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.value || ''"
                                           formControlName="altroTrattamentoMedicoAcuto" maxlength="250"
                            [placeholder]="lesioneTrattamentoForm.get('trattamentoMedicoAcuto')?.getRawValue()?.descrizione === 'ALTRO' ? 'Inserisci*' : 'Inserisci'">
                        <mat-error
                            *ngIf="lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.hasError('required')">
                            Campo obbligatorio
                        </mat-error>
                    </mat-form-field>
                </div>
                <div class="col-auto ml-3">
                    <button mat-button type="button" class="note-button"
                        [disabled]="isNoteDisabled('trattamentoMedicoAcuto')"
                        (click)="openPopupNote('trattamentoMedicoAcuto')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note">
                            </use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div> -->

        <hr class="mt-4 mb-1">

        <!-- Sezione Presa in carico riabilitativa in fase acuta -->
        <div class="row d-flex align-items-center">
            <div class="col-auto">
                <label class="field-label pt-2">
                    Presa in carico riabilitativa in fase acuta (trattato da fisioterapista)
                </label>
            </div>
            <div class="radio-group-container col-3 col-md-3 justify-content-center mt-2">
                <mat-radio-group formControlName="presaInCaricoRiabilitativa" class="d-flex">
                    <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" color="primary"
                        (mouseup)="datiCliniciService.toggleRadioSelection(lesioneTrattamentoForm.get('presaInCaricoRiabilitativa'), option, $event)">
                        {{ option.descrizione }}
                    </mat-radio-button>
                </mat-radio-group>
            </div>
        </div>
    </div>
</form>