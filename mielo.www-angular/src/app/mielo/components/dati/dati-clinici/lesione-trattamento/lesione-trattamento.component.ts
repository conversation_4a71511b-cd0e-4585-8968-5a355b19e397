import { CommonModule } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms";
import { MatNativeDateModule } from "@angular/material/core";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer } from "@angular/platform-browser";
import { BehaviorSubject, combineLatest, forkJoin, merge, of, Subject, Subscription } from "rxjs";
import { debounceTime, filter, map, startWith, takeUntil, tap } from "rxjs/operators";
import { MaterialModule } from "../../../../../core/material.module";
import { ERROR_MESSAGE } from "../../../../../shared/enums/enum";
import { DiagnosticaType, DizionarioHelper, TrattamentoType, TraumaType } from '../../../../../shared/enums/lesione-trattamento.enum';
import { TipoRicoveroEnum } from '../../../../../shared/enums/tipo-ricovero.enum';
import { SchedaLesioneTrattamentoModel } from "../../../../../shared/interfaces/dati-clinici.interface";
import { DizionarioModel } from "../../../../../shared/interfaces/scheda-ricovero.interface";
import { CapitalizePipe } from "../../../../../shared/pipes/capitalize.pipe";
import { DecoderService } from "../../../../../shared/services/decoder.service";
import { ModalService } from '../../../../../shared/services/modal.service';
import { convertToDate, toggleRadioSelection } from "../../../../../shared/utils/utils";
import { dateNotFutureValidator, dateTimeNotFutureValidator, timeValidator } from '../../../../../shared/validators/date-time.validator';
import { DatiCliniciService } from "../../../../services/dati-clinici.service";
import {TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO} from "../../../../../shared/utils/const";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;
@Component({
  selector: 'app-lesione-trattamento',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    ReactiveFormsModule,
    FormsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CapitalizePipe
  ],
  templateUrl: './lesione-trattamento.component.html',
  styleUrl: './lesione-trattamento.component.scss'
})
export class LesioneTrattamentoComponent implements OnInit, OnDestroy {
  @Input() idReparto: number;
  @Input() idTipoEvento!: number;
  @Input() readOnly: boolean = false;

  formReady: boolean = false;
  vertebreSelezionate: DizionarioModel[] = [];

  optionsTipologiaIntervento: Array<DizionarioModel> = [];
  optionsDiagnostica: Array<DizionarioModel> = [];
  optionsVertebre: Array<DizionarioModel> = [];
  optionsTipoLesioneVertebrale: Array<DizionarioModel> = [];
  optionsTrattamentoMedicoAcuto: Array<DizionarioModel> = [];
  optionsTraumiAssociati: Array<DizionarioModel> = [];
  optionsBoolean: Array<DizionarioModel> = [];

  lesioneTrattamentoForm: FormGroup;
  datiLesioneTrattamento: SchedaLesioneTrattamentoModel;

  get formGroup(): FormGroup {
    return this.lesioneTrattamentoForm;
  }

  /* Array per la gestione degli interventi chirurgici aggiuntivi */
  interventiAggiuntivi: Array<{
    idIntervento: number | null;
    dataIntervento: Date | null;
    oraIntervento: string;
    tipologiaIntervento: string;
    tipologiaInterventoId: number;
    note: string;
  }> = [];

  private subscriptions: Subscription[] = [];
  private readonly destroyed$ = new Subject<void>();

  // Sostituiamo i signal con BehaviorSubject
  private readonly interventoState$ = new BehaviorSubject<{
    idIntervento: number | null;
    dataIntervento: Date | null;
    oraIntervento: string;
    tipologiaIntervento: string;
    tipologiaInterventoId: number;
    note: string;
  } | null>(null);

  private readonly interventiList$ = new BehaviorSubject<Array<{
    idIntervento: number | null;
    dataIntervento: Date | null;
    oraIntervento: string;
    tipologiaIntervento: string;
    tipologiaInterventoId: number;
    note: string;
  }>>([]);


  protected readonly toggleRadioSelection = toggleRadioSelection;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;
  // Computed property per canAddIntervento come Observable
  private canAddIntervento$: any;
  private canAddInterventoValue: boolean = false;

  // Getter per verificare se siamo in modalità RIABILITAZIONE
  get isRiabilitazione(): boolean {
    return this.idReparto === TipoRicoveroEnum.RIABILITAZIONE;
  }

  today: Date = new Date();
  get currentDate(): Date {
    return this.today;
  }

  constructor(
    private fb: FormBuilder,
    iconRegistry: MatIconRegistry,
    sanitizer: DomSanitizer,
    public datiCliniciService: DatiCliniciService,
    private modalService: ModalService,
    private decoderService: DecoderService,
  ) {
    iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

    if (this.datiCliniciService.getDatiLesioneTrattamentoValue()) this.datiLesioneTrattamento = this.datiCliniciService.getDatiLesioneTrattamentoValue()!;

    this.lesioneTrattamentoForm = this.fb.group({
      rx: [false],
      tc: [false],
      rmn: [false],
      dataEsameRx: [null, [Validators.required, dateNotFutureValidator()]],
      dataEsameTc: [null, [Validators.required, dateNotFutureValidator()]],
      dataEsameRmn: [null, [Validators.required, dateNotFutureValidator()]],
      lesioneVertebra: [''],
      tipoLesioneVertebrale: [null],
      noteTipoLesioneVertebrale: [''],
      interventiChirurgici: [null, Validators.required], //radio button
      interventoChirurgico: [null], //array di interventi chirurgici
      idIntervento: [null],
      // dataIntervento: [null, [Validators.required, dateNotFutureValidator()]],
      // oraIntervento: ['', [timeValidator(() => this.lesioneTrattamentoForm.get('dataIntervento')?.value)]],
      tipologiaIntervento: [''],
      noteTipologiaIntervento: [''],
      nessunoTraumi: [false],
      cranioTraumi: [false],
      scheletroTraumi: [false],
      organiInterniTraumi: [false],
      altroTraumi: [false],
      altroTraumiDescrizione: [''],
      noteTraumiAssociati: [''],
      trattamentoMedicoAcuto: [''],
      altroTrattamentoMedicoAcuto: [''],
      noteTrattamentoMedicoAcuto: [''],
      presaInCaricoRiabilitativa: [null],
      interventiAggiuntivi: this.fb.array([])
    });

    if (this.idReparto !== TipoRicoveroEnum.ACUTI) {
      this.lesioneTrattamentoForm.get('interventoChirurgico')?.removeValidators(Validators.required);
      this.lesioneTrattamentoForm.get('interventoChirurgico')?.updateValueAndValidity();
    }

    this.initializeFormState();

    // Inizializziamo canAddIntervento$ dopo la creazione del form
    this.canAddIntervento$ = combineLatest([
      this.interventiList$,
      this.lesioneTrattamentoForm.valueChanges.pipe(
        map(form => ({
          data: form.dataIntervento,
          tipologia: form.tipologiaIntervento,
        }))
      )
    ]).pipe(
      map(([interventi, primoIntervento]) => {
        if (!this.isInterventoChirurgicoSi) {
          return false;
        }

        // Primo intervento: controlliamo il primo elemento del FormArray
        const primoCtrl = this.interventiFormArray.at(0);
        const primoValido = primoCtrl
          ? Boolean(primoCtrl.get('dataIntervento')?.value && primoCtrl.get('tipologiaInterventoId')?.value)
          : false;

        let aggiuntiviValidi = true;
        if (this.interventiFormArray.length > 1) {
          aggiuntiviValidi = this.interventiFormArray.controls.slice(1).every((ctrl, idx) => {
            const valido = ctrl.get('dataIntervento')?.value && ctrl.get('tipologiaInterventoId')?.value;
            return valido;
          });
        }

        const result = primoValido && aggiuntiviValidi;
        return result;
      })
    ).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(value => {
      this.canAddInterventoValue = value;
    });
  }

  // Getter per il template
  get canAddIntervento(): boolean {
    return this.canAddInterventoValue;
  }

  ngOnInit() {
    /* Carica dizionari in parallelo e inizializza il form */
    forkJoin({
      tipologiaIntervento: this.decoderService.getDcodTipologiaIntervento(),
      vertebre: this.decoderService.getDcodVertebre(),
      tipoLesioneVertebrale: this.decoderService.getDcodTipoLesioneVertebrale(),
      trattamentoMedicoAcuto: this.decoderService.getDcodTrattamentoMedicoAcuto(),
      diagnostica: this.decoderService.getDcodDiagnostica(),
      traumiAssociati: this.decoderService.getDcodTraumiAssociati(),
      boolean: this.decoderService.getDcodBoolean()
    }).pipe(
      takeUntil(this.destroyed$),
      tap(options => {
        // Memorizza le opzioni dei dizionari
        this.optionsTipologiaIntervento = options.tipologiaIntervento;
        this.optionsVertebre = options.vertebre;
        this.optionsDiagnostica = options.diagnostica;
        this.optionsTraumiAssociati = options.traumiAssociati;
        this.optionsTipoLesioneVertebrale = options.tipoLesioneVertebrale;
        this.optionsTrattamentoMedicoAcuto = options.trattamentoMedicoAcuto;
        this.optionsBoolean = options.boolean;
        this.formReady = true;

        // Imposta i validatori in base al tipo di ricovero
        this.setValidatorsBasedOnRicovero();

        // Carica dati salvati precedentemente
        this.loadSavedData();

        if (!this.readOnly) {
          // Imposta le dipendenze tra i controlli del form
          this.setupFormDependencies();

          // Configurazione della sottoscrizione per gli interventi
          this.setupInterventoSubscription();

          if (this.interventiFormArray.length === 0) {
            this.interventiFormArray.push(this.createInterventoFormGroup());
          }

          // Imposta la validazione delle date
          this.setupFormValidation();

          // se il FormArray è vuoto, aggiungi un FormGroup vuoto e disabilitalo subito
          if (this.interventiFormArray.length === 0) {
            const formGroup = this.createInterventoFormGroup();
            this.interventiFormArray.push(formGroup);
          }

          this.manageInterventoChirurgicoControls();
        }
      })
    ).subscribe();

    if (!this.readOnly) {
      // Monitoraggio dei cambiamenti del form
      this.lesioneTrattamentoForm.valueChanges.pipe(
        takeUntil(this.destroyed$),
        debounceTime(1),
        tap(() => {
          const formValues = this.lesioneTrattamentoForm.getRawValue();
          this.manageDiagnosticaControls();
          this.manageInterventoChirurgicoControls();
          this.manageTraumiAssociatiControls();
          this.manageTrattamentoMedicoAcutoControls();

          // Aggiornamento del modello e del servizio
          this.updateModelFromForm(formValues);
          this.datiCliniciService.setDatiLesioneTrattamento(this.datiLesioneTrattamento);
          this.lesioneTrattamentoForm.updateValueAndValidity()
          this.datiCliniciService.setDatiLesioneTrattamentoValido(this.lesioneTrattamentoForm.valid);
        })
      ).subscribe();
    }

    if (this.readOnly) {
      Object.keys(this.lesioneTrattamentoForm.controls).forEach(controlName => {
        if (!controlName.toLowerCase().includes('nota')) {
          this.lesioneTrattamentoForm.get(controlName)?.disable({ emitEvent: false });
        }
      });
    }
  }

  // Imposta validatori in base al tipo di ricovero
  private setValidatorsBasedOnRicovero(): void {
    const isRequiredForOtherRicoveri = !this.isRiabilitazione;
    const requiredValidators = isRequiredForOtherRicoveri ? [Validators.required] : [];

    // Campi che necessitano di validazione condizionale
    const campiDaValidare = [
      'lesioneVertebra',
      'tipoLesioneVertebrale',
      // 'interventoChirurgico',
      'trattamentoMedicoAcuto'
    ];

    // Imposta i validatori in base al tipo di ricovero
    campiDaValidare.forEach(campo => {
      const control = this.lesioneTrattamentoForm.get(campo);
      if (control) {
        control.setValidators(requiredValidators);
        control.updateValueAndValidity();
      }
    });
  }

  /* Getter per controllo altroTraumi */
  get isAltroTraumi(): boolean {
    return this.lesioneTrattamentoForm.get('altroTraumi')?.value ?? false;
  }


  /* Getter per controllo interventoChirurgico */
  get isInterventoChirurgicoSi(): boolean {
    return this.lesioneTrattamentoForm.get('interventiChirurgici')?.value;
  }

  /* Getter per FormArray interventi */
  get interventiFormArray(): FormArray {
    return this.lesioneTrattamentoForm.get('interventiAggiuntivi') as FormArray;
  }

  /* Crea FormGroup per intervento */
  createInterventoFormGroup(intervento?: any): FormGroup {
    const disabled = !this.isInterventoChirurgicoSi || this.readOnly
    const formGroup = this.fb.group({
      idIntervento: [intervento?.idIntervento || null],
      dataIntervento: [
        { value: intervento?.dataIntervento || null, disabled },
        []
      ],
      oraIntervento: [{ value: intervento?.oraIntervento || '', disabled }],
      tipologiaInterventoId: [
        { value: intervento?.tipologiaInterventoId || null, disabled }
      ],
      tipologiaIntervento: [intervento?.tipologiaIntervento || ''],
      note: [intervento?.note || '']
    }, {
      validators: dateTimeNotFutureValidator('dataIntervento', 'oraIntervento')
    });
    return formGroup;
  }

  private loadSavedData() {
    if (!this.datiLesioneTrattamento) return;

    // Reset degli array di interventi
    this.interventiAggiuntivi = [];
    this.interventiList$.next([]);

    // Prima rimuoviamo tutti gli elementi dal FormArray
    while (this.interventiFormArray.length) {
      this.interventiFormArray.removeAt(0);
    }

    // Gestione interventi chirurgici
    if (this.datiLesioneTrattamento.interventoChirurgico?.length > 0) {

      // Gestiamo TUTTI gli interventi allo stesso modo
      const tuttiInterventi = this.datiLesioneTrattamento.interventoChirurgico.map(intervento => {

        const dataOra = intervento.dataOraInterventoChirurgico ? new Date(intervento.dataOraInterventoChirurgico) : null;
        const hasTime = dataOra && intervento.dataOraInterventoChirurgico && //in questo modo faccio comparire il campo ora
          (new Date(intervento.dataOraInterventoChirurgico).getSeconds() !== 0 ||
            new Date(intervento.dataOraInterventoChirurgico).getHours() !== 0 ||
            new Date(intervento.dataOraInterventoChirurgico).getMinutes() !== 0);

        return {
          idIntervento: intervento.idIntervento,
          dataIntervento: dataOra,
          oraIntervento: hasTime ? dataOra?.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit', hour12: false }) : '',
          tipologiaIntervento: intervento.tipoInterventoChirurgicoLesione?.descrizione || '',
          tipologiaInterventoId: intervento.tipoInterventoChirurgicoLesione?.idDizionario || 0,
          note: intervento.noteInterventoChirurgico || ''
        };
      });


      // Salva gli interventi nella lista globale
      this.interventiList$.next(tuttiInterventi);
      this.interventiAggiuntivi = [...tuttiInterventi];

      // Aggiungiamo ogni intervento al FormArray
      this.interventiFormArray.clear(); // Rimuovi tutti gli elementi esistenti

      tuttiInterventi.forEach(interventoMappato => {
        const formGroup = this.createInterventoFormGroup(interventoMappato);
        this.interventiFormArray.push(formGroup);
      });

    } else {
      // Reset del form se non ci sono interventi
      this.lesioneTrattamentoForm.patchValue({
        idIntervento: null,
        dataIntervento: null,
        oraIntervento: '',
        tipologiaIntervento: '',
        noteTipologiaIntervento: ''
      }, { emitEvent: false });

      this.interventoState$.next(null);
      this.interventiList$.next([]);
    }

    // Gestiamo le vertebre selezionate
    if (this.datiLesioneTrattamento.lesioneVertebra && Array.isArray(this.datiLesioneTrattamento.lesioneVertebra) && this.datiLesioneTrattamento.lesioneVertebra.length > 0) {
      this.vertebreSelezionate = this.datiLesioneTrattamento.lesioneVertebra
        .map(v => {
          const vertebraOption = this.optionsVertebre.find(ov => ov.idDizionario === v.idDizionario);
          return vertebraOption || null;
        })
        .filter((v): v is DizionarioModel => v !== null);

      // Aggiorniamo anche il form control
      const vertebreString = this.vertebreSelezionate.map(v => v.descrizione).join(', ');
      this.lesioneTrattamentoForm.patchValue({
        lesioneVertebra: vertebreString
      }, { emitEvent: false });
    }

    /* Recupero date esami */
    const dataRx = this.datiLesioneTrattamento.diagnostica?.find(d =>
      this.findInDizionario(this.optionsDiagnostica, DiagnosticaType.RX)?.idDizionario === d.idDizionario
    )?.data;

    const dataTc = this.datiLesioneTrattamento.diagnostica?.find(d =>
      this.findInDizionario(this.optionsDiagnostica, DiagnosticaType.TC)?.idDizionario === d.idDizionario
    )?.data;

    const dataRmn = this.datiLesioneTrattamento.diagnostica?.find(d =>
      this.findInDizionario(this.optionsDiagnostica, DiagnosticaType.RMN)?.idDizionario === d.idDizionario
    )?.data;

    const findOption = (options: DizionarioModel[], diz: DizionarioModel | null | undefined): DizionarioModel | null => {
      if (!diz || !diz.idDizionario) return null;
      return options.find(opt => opt.idDizionario === diz.idDizionario) ?? null;
    };
    /* Prepara dati per form */
    const formData: any = {
      rx: this.someInDizionario(this.datiLesioneTrattamento.diagnostica, this.optionsDiagnostica, DiagnosticaType.RX),
      tc: this.someInDizionario(this.datiLesioneTrattamento.diagnostica, this.optionsDiagnostica, DiagnosticaType.TC),
      rmn: this.someInDizionario(this.datiLesioneTrattamento.diagnostica, this.optionsDiagnostica, DiagnosticaType.RMN),
      dataEsameRx: convertToDate(dataRx),
      dataEsameTc: convertToDate(dataTc),
      dataEsameRmn: convertToDate(dataRmn),
      lesioneVertebra: this.vertebreSelezionate.map(v => v.descrizione).join(', '),
      noteTipoLesioneVertebrale: this.datiLesioneTrattamento.noteTipoLesioneVertebrale || '',
      tipoLesioneVertebrale: findOption(this.optionsTipoLesioneVertebrale, this.datiLesioneTrattamento.tipoLesioneVertebrale) || null,
      nessunoTraumi: this.someInDizionario(this.datiLesioneTrattamento.traumiAssociati, this.optionsTraumiAssociati, TraumaType.NESSUNO),
      interventiChirurgici: this.datiLesioneTrattamento.interventiChirurgici,
      cranioTraumi: this.someInDizionario(this.datiLesioneTrattamento.traumiAssociati, this.optionsTraumiAssociati, TraumaType.CRANIO),
      scheletroTraumi: this.someInDizionario(this.datiLesioneTrattamento.traumiAssociati, this.optionsTraumiAssociati, TraumaType.SCHELETRO),
      organiInterniTraumi: this.someInDizionario(this.datiLesioneTrattamento.traumiAssociati, this.optionsTraumiAssociati, TraumaType.ORGANI_INTERNI),
      altroTraumi: !!this.datiLesioneTrattamento.altroTraumiAssociati,
      altroTraumiDescrizione: this.datiLesioneTrattamento.altroTraumiAssociati || '',
      noteTraumiAssociati: this.datiLesioneTrattamento.noteTraumiAssociati || '',
      trattamentoMedicoAcuto: findOption(this.optionsTrattamentoMedicoAcuto, this.datiLesioneTrattamento.trattamentoMedicoAcuto) || null,
      altroTrattamentoMedicoAcuto: this.datiLesioneTrattamento.altroTrattamentoMedicoAcuto || '',
      noteTrattamentoMedicoAcuto: this.datiLesioneTrattamento.noteTrattamentoMedicoAcuto || '',
    };

    this.lesioneTrattamentoForm.patchValue(formData, { emitEvent: false });

    // Gestione presaInCaricoRiabilitativa
    this.populateBooleanControl('presaInCaricoRiabilitativa', this.datiLesioneTrattamento.presaCaricoRiabilitativaFaseAcuta);

    // Inizializzazione stato form
    this.initializeFormState();

    // Imposta il form come valido o meno in base ai valori attuali
    // controllo idTipoevento xk se è primo ricovero riabilitativo non deve validare poichè il form è DISABLED
    this.datiCliniciService.setDatiLesioneTrattamentoValido(this.lesioneTrattamentoForm.valid || this.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento);

    this.lesioneTrattamentoForm.updateValueAndValidity({ onlySelf: false, emitEvent: true });


  }

  ngOnDestroy() {
    /* Cancella sottoscrizioni */
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  setupFormDependencies() {
    const form = this.lesioneTrattamentoForm;

    // Utilizziamo merge per combinare tutti gli eventi dei controlli diagnostica
    merge(
      form.get('rx')!.valueChanges.pipe(map(value => ({ tipo: 'rx', checked: value }))),
      form.get('tc')!.valueChanges.pipe(map(value => ({ tipo: 'tc', checked: value }))),
      form.get('rmn')!.valueChanges.pipe(map(value => ({ tipo: 'rmn', checked: value })))
    ).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(({ tipo, checked }) => {
      this.manageDiagnosticaControl(tipo, checked);
    });

    // Gestione intervento chirurgico
    form.get('interventoChirurgico')!.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.manageInterventoChirurgicoControls();
    });

    // Gestione traumi
    form.get('nessunoTraumi')!.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(value => {
      if (value) {
        this.clearOtherTraumi();
      }
    });

    // Combiniamo tutti i controlli dei traumi in un unico observable
    const traumiControls = ['cranioTraumi', 'scheletroTraumi', 'organiInterniTraumi', 'altroTraumi'];
    merge(
      ...traumiControls.map(ctrl =>
        form.get(ctrl)!.valueChanges.pipe(
          map(value => ({ ctrl, value }))
        )
      )
    ).pipe(
      takeUntil(this.destroyed$)
    ).subscribe(({ ctrl, value }) => {
      if (value) {
        form.get('nessunoTraumi')!.setValue(false, { emitEvent: false });
      }
      this.manageAltroTraumiControl();
    });

    // Gestione trattamento medico acuto
    form.get('trattamentoMedicoAcuto')!.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(() => {
      this.manageAltroTrattamentoMedicoAcuto();
    });
  }

  /* Verifica validatore required */
  isRequired(controlName: string): boolean {
    const control = this.lesioneTrattamentoForm.get(controlName);
    return this.datiCliniciService.isRequired(control);
  }

  // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno 
  isNoteDisabled(controlName: string): boolean {
    const control = this.lesioneTrattamentoForm.get(controlName);
    return !control?.value && !!control?.disabled;
  }

  /* Gestisce tutti i controlli diagnostica */
  manageDiagnosticaControls() {
    ['rx', 'tc', 'rmn'].forEach(tipo => {
      this.manageDiagnosticaControl(tipo, this.lesioneTrattamentoForm.get(tipo)?.value);
    });
  }

  /* Gestisce singolo controllo diagnostica */
  manageDiagnosticaControl(tipo: string, checked: boolean) {
    const dataField = `dataEsame${tipo.charAt(0).toUpperCase() + tipo.slice(1)}`;
    this.manageConditionalControl(
      dataField,
      checked,
      null
    );
  }

  /* Gestisce controlli intervento chirurgico */
  manageInterventoChirurgicoControls() {
    const isInterventoSi = this.isInterventoChirurgicoSi;

    if (this.lesioneTrattamentoForm.get('interventiChirurgici')?.value === null) {
      if (this.idReparto === TipoRicoveroEnum.RIABILITAZIONE) {
        this.lesioneTrattamentoForm.get('interventiChirurgici')?.removeValidators(Validators.required);
        this.lesioneTrattamentoForm.get('interventiChirurgici')?.updateValueAndValidity();
      }
    }


    if (this.idReparto === TipoRicoveroEnum.RIABILITAZIONE) {
      this.lesioneTrattamentoForm.get('interventiChirurgici')?.removeValidators(Validators.required);
      this.lesioneTrattamentoForm.get('interventiChirurgici')?.updateValueAndValidity();
    }

    if (!isInterventoSi) {
      // Se è NO, svuota il FormArray e aggiungi un solo form vuoto disabilitato
      while (this.interventiFormArray.length > 0) {
        this.interventiFormArray.removeAt(0);
      }
      const formGroup = this.createInterventoFormGroup();
      this.interventiFormArray.push(formGroup);
      formGroup.disable({ emitEvent: false });
      this.interventiList$.next([]);
      this.interventiAggiuntivi = [];
      // Rimuovo i validatori required
      const dataInterventoControl = this.lesioneTrattamentoForm.get('dataIntervento');
      const tipologiaInterventoControl = this.lesioneTrattamentoForm.get('tipologiaIntervento');
      if (dataInterventoControl) {
        dataInterventoControl.clearValidators();
        dataInterventoControl.updateValueAndValidity({ emitEvent: false });
      }
      if (tipologiaInterventoControl) {
        tipologiaInterventoControl.clearValidators();
        tipologiaInterventoControl.updateValueAndValidity({ emitEvent: false });
      }
      this.lesioneTrattamentoForm.get('interventoChirurgico')?.removeValidators(Validators.required)
    } else {
      // this.lesioneTrattamentoForm.get('interventoChirurgico')?.addValidators(Validators.required)
      // In modalità SÌ, non rimuovere i gruppi vuoti: servono per la compilazione
      // Se il FormArray è vuoto, aggiungi un gruppo nuovo
      if (this.interventiFormArray.length === 0) {
        const formGroup = this.createInterventoFormGroup();
        this.interventiFormArray.push(formGroup);
      }
      // Abilita tutti i gruppi e aggiorna i validatori
      this.interventiFormArray.controls.forEach(ctrl => {
        if (!this.readOnly) ctrl.enable({ emitEvent: false });
        if (this.idReparto !== TipoRicoveroEnum.RIABILITAZIONE) { //in riav i campi di intervento sono opzionali
          ctrl.get('dataIntervento')?.setValidators([Validators.required, dateNotFutureValidator()]);
          ctrl.get('dataIntervento')?.updateValueAndValidity({emitEvent: false});
          ctrl.get('tipologiaInterventoId')?.setValidators([Validators.required]);
          ctrl.get('tipologiaInterventoId')?.updateValueAndValidity({emitEvent: false});
        }
      });
      this.interventiFormArray.updateValueAndValidity();
      this.lesioneTrattamentoForm.updateValueAndValidity();
    }
  }

  /* Gestisce controlli traumi */
  manageTraumiAssociatiControls() {
    this.manageAltroTraumiControl();
  }

  /* Gestisce campo "altro" nei traumi */
  manageAltroTraumiControl() {
    if (this.readOnly) return this.lesioneTrattamentoForm.get('altroTraumiDescrizione')?.disable()

    const isAltro = this.isAltroTraumi;
    if (isAltro) {
      this.lesioneTrattamentoForm.get('altroTraumiDescrizione')?.enable()
      this.lesioneTrattamentoForm.get('altroTraumiDescrizione')?.addValidators(Validators.required)
    } else {
      this.lesioneTrattamentoForm.get('altroTraumiDescrizione')?.setValue('');
      this.lesioneTrattamentoForm.get('altroTraumiDescrizione')?.disable()
      this.lesioneTrattamentoForm.get('altroTraumiDescrizione')?.clearValidators();
    }
  }

  /* Deseleziona traumi se "nessuno" */
  clearOtherTraumi() {
    ['cranioTraumi', 'scheletroTraumi', 'organiInterniTraumi', 'altroTraumi'].forEach(ctrl => {
      this.lesioneTrattamentoForm.get(ctrl)?.setValue(false, { emitEvent: false });
    });
    this.manageAltroTraumiControl();
  }

  /* Gestisce controlli trattamento */
  manageTrattamentoMedicoAcutoControls() {
    this.manageAltroTrattamentoMedicoAcuto();
  }

  /* Gestisce campo "altro" nel trattamento */
  manageAltroTrattamentoMedicoAcuto() {
    if (this.readOnly) return this.lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.disable()
    const trattamento = this.lesioneTrattamentoForm.get('trattamentoMedicoAcuto')?.value;
    const isAltroSelected = !!trattamento && typeof trattamento === 'object' && trattamento.descrizione === TrattamentoType.ALTRO;
    if (isAltroSelected) {
      this.lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.enable()
      this.lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.addValidators(Validators.required)
    } else {
      this.lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.disable()
      this.lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.setValue(null, { emitEvent: false });
      this.lesioneTrattamentoForm.get('altroTrattamentoMedicoAcuto')?.clearValidators();
    }
  }

  /* Apre selezione vertebre */
  openPopupLesioneVertebra() {
    // Per il popup inviamo i codici delle vertebre (C1, C2, etc)
    const vertebreCodici = this.vertebreSelezionate.map(v => v.descrizione);
    const configSelectionVertebra = this.modalService.createSelectionVertebraParam(vertebreCodici);

    this.modalService.selectionVertebra(configSelectionVertebra)?.pipe(
      takeUntil(this.destroyed$),
      filter(res => !!res) // Filtriamo risultati null/undefined
    ).subscribe((res) => {
      if (Array.isArray(res)) {
        // Convertiamo i codici (C1, C2, etc) in oggetti vertebra completi
        this.vertebreSelezionate = res
          .map(codice => this.optionsVertebre.find(v => v.descrizione === codice))
          .filter((v): v is DizionarioModel => v !== null && v !== undefined);

        // Aggiorniamo il form control con le descrizioni
        const vertebreString = this.vertebreSelezionate.map(v => v.descrizione).join(', ');
        const lesioneVertebraControl = this.lesioneTrattamentoForm.get('lesioneVertebra');

        if (lesioneVertebraControl) {
          lesioneVertebraControl.setValue(vertebreString);
          lesioneVertebraControl.markAsDirty();
          lesioneVertebraControl.updateValueAndValidity();
        }
      } else {
        // Reset se non ci sono selezioni
        this.vertebreSelezionate = [];
        const lesioneVertebraControl = this.lesioneTrattamentoForm.get('lesioneVertebra');

        if (lesioneVertebraControl) {
          lesioneVertebraControl.setValue('');
          lesioneVertebraControl.markAsDirty();
          lesioneVertebraControl.updateValueAndValidity();
        }
      }

      // Aggiorniamo il modello
      this.updateModelFromForm(this.lesioneTrattamentoForm.value);
    });
  }

  /* Apre popup note */
  openPopupNote(field: string) {
    const noteFieldMap: Record<string, string> = {
      'tipoLesioneVertebrale': 'noteTipoLesioneVertebrale',
      'tipologiaIntervento': 'noteTipologiaIntervento',
      'traumiAssociati': 'noteTraumiAssociati',
      'trattamentoMedicoAcuto': 'noteTrattamentoMedicoAcuto'
    };

    const noteField = noteFieldMap[field] || '';

    if (!noteField) return;

    const noteControl = this.lesioneTrattamentoForm.get(noteField);
    if (!noteControl) return;

    const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

    this.modalService.note(configNote)?.pipe(
      takeUntil(this.destroyed$),
      filter(res => res !== noteControl.value)
    ).subscribe(res => {
      noteControl.setValue(res);
      noteControl.markAsDirty();
      noteControl.updateValueAndValidity();
      this.adjustDialogHeight();
    });
  }

  /* Modifica note intervento */
  editInterventoNote(index: number) {
    if (index < 0 || index >= this.interventiFormArray.length) return;

    const interventoFormGroup = this.interventiFormArray.at(index) as FormGroup;
    const noteControl = interventoFormGroup.get('note');

    if (!noteControl) return;

    const configNote = this.modalService.createNoteParam(noteControl.value || '', this.readOnly);

    this.modalService.note(configNote)?.pipe(
      takeUntil(this.destroyed$),
      filter(res => res !== noteControl.value)
    ).subscribe(res => {
      noteControl.setValue(res);

      if (index < this.interventiAggiuntivi.length) {
        this.interventiAggiuntivi[index].note = res;
      }

      this.datiLesioneTrattamento.interventoChirurgico = this.getInterventiFromFormArray();
      this.datiCliniciService.setDatiLesioneTrattamento(this.datiLesioneTrattamento);
      this.adjustDialogHeight();
    });
  }

  /* Adatta altezza dialog */
  private adjustDialogHeight() {
    this.datiCliniciService.adjustDialogHeight();
  }

  /* Aggiunge intervento alla lista */
  addIntervento() {
    // Aggiungi solo se tutti i gruppi esistenti sono validi
    if (this.interventiFormArray.controls.every(ctrl => ctrl.valid)) {
      const nuovoIntervento = {
        idIntervento: null,
        dataIntervento: null,
        oraIntervento: '',
        tipologiaIntervento: '',
        tipologiaInterventoId: 0,
        note: ''
      };
      const formGroup = this.createInterventoFormGroup(nuovoIntervento);
      this.interventiFormArray.insert(0, formGroup);
      this.interventiAggiuntivi.unshift(nuovoIntervento);
      this.updateModelFromInterventi();
      this.interventiFormArray.updateValueAndValidity();
      this.lesioneTrattamentoForm.updateValueAndValidity();
    }
  }

  private updateModelFromInterventi(): void {
    if (!this.datiLesioneTrattamento) return;

    const interventoChirurgico: any[] = [];

    const primoIntervento = this.buildIntervento(
      this.datiLesioneTrattamento.idScheda,
      this.lesioneTrattamentoForm.get('idIntervento')?.value || 0,
      this.lesioneTrattamentoForm.get('dataIntervento')?.value,
      this.lesioneTrattamentoForm.get('oraIntervento')?.value || '',
      this.lesioneTrattamentoForm.get('tipologiaIntervento')?.value || null,
      this.lesioneTrattamentoForm.get('noteTipologiaIntervento')?.value || ''
    );

    if (primoIntervento) {
      interventoChirurgico.push(primoIntervento);
    }

    const interventiAggiuntivi = this.interventiList$.getValue();

    interventiAggiuntivi.forEach(intervento => {
      const interventoCompleto = this.buildIntervento(
        this.datiLesioneTrattamento.idScheda,
        intervento.idIntervento || 0,
        intervento.dataIntervento,
        intervento.oraIntervento,
        intervento.tipologiaInterventoId,
        intervento.note
      );

      if (interventoCompleto) {
        interventoChirurgico.push(interventoCompleto);
      }
    });

    this.datiLesioneTrattamento.interventoChirurgico = interventoChirurgico;
    this.datiCliniciService.setDatiLesioneTrattamento(this.datiLesioneTrattamento);
  }

  /* Rimuove intervento dalla lista */
  removeIntervento(index: number) {

    if (this.interventiFormArray.length === 1) {
      // Se c'è solo un elemento, resetta i campi senza rimuoverlo
      this.interventiFormArray.at(0).reset();

      // Aggiorna anche interventiList$
      if (this.interventiList$.getValue().length > 0) {
        const interventoVuoto = {
          idIntervento: null,
          dataIntervento: null,
          oraIntervento: '',
          tipologiaIntervento: '',
          tipologiaInterventoId: 0,
          note: ''
        };
        this.interventiList$.next([interventoVuoto]);
        this.interventiAggiuntivi = [interventoVuoto];
      }
    } else if (index >= 0 && index < this.interventiFormArray.length) {
      // Rimuovi dal FormArray
      this.interventiFormArray.removeAt(index);

      // Rimuovi anche da interventiList$
      const nuovaLista = [...this.interventiList$.getValue()];
      nuovaLista.splice(index, 1);
      this.interventiList$.next(nuovaLista);
      this.interventiAggiuntivi = [...nuovaLista];
    }


    // Aggiorna il modello
    this.updateModelFromInterventi();
    this.interventiFormArray.updateValueAndValidity();
    this.lesioneTrattamentoForm.updateValueAndValidity();


  }


  /* Inizializza tutti i controlli del form */
  initializeFormState() {
    this.manageDiagnosticaControls();
    this.manageInterventoChirurgicoControls();
    this.manageTraumiAssociatiControls();
    this.manageTrattamentoMedicoAcutoControls();
  }

  /* Estrae dati da FormArray */
  getInterventiFromFormArray(): any[] {
    const interventoChirurgico: any[] = [];


    // Raccogli i valori da tutti i controlli nel FormArray
    for (let i = 0; i < this.interventiFormArray.length; i++) {
      const interventoValue = this.interventiFormArray.at(i).value;

      // Verifica che il controllo sia abilitato - altrimenti potrebbe essere un intervento "NO"
      if (this.interventiFormArray.at(i).disabled) {
        continue;
      }

      const interventoCompleto = this.buildIntervento(
        this.datiLesioneTrattamento?.idScheda || 0,
        interventoValue.idIntervento || 0,
        interventoValue.dataIntervento,
        interventoValue.oraIntervento,
        interventoValue.tipologiaInterventoId || null,
        interventoValue.note
      );

      if (interventoCompleto) {
        interventoChirurgico.push(interventoCompleto);
      }
    }
    return interventoChirurgico;
  }

  /* Ottiene descrizioni vertebre */
  get vertebreSelezionateDescrizioni(): string {
    if (!this.vertebreSelezionate || this.vertebreSelezionate.length === 0) {
      return '';
    }
    return this.vertebreSelezionate.map(v => v.descrizione).join(', ');
  }

  /* Converte valori dizionario in boolean */
  convertBooleanValue(val: DizionarioModel | null): boolean | null {
    return this.datiCliniciService.convertBooleanValue(val);
  }

  /* Imposta controllo con valore boolean */
  populateBooleanControl(controlName: string, booleanValue: boolean | null | undefined): void {
    const control = this.lesioneTrattamentoForm.get(controlName);
    this.datiCliniciService.populateBooleanControl(control, booleanValue, this.optionsBoolean);
  }

  // Aggiorna modello dai valori form
  private updateModelFromForm(formValues: any) {
    // Salva una copia dei dati correnti
    const datiCorretti = { ...this.datiLesioneTrattamento };

    // Gestione diagnostica
    const diagnostica: any[] = [];
    const diagnosticaTipi = [
      { name: 'rx', label: DiagnosticaType.RX, date: 'dataEsameRx' },
      { name: 'tc', label: DiagnosticaType.TC, date: 'dataEsameTc' },
      { name: 'rmn', label: DiagnosticaType.RMN, date: 'dataEsameRmn' }
    ];

    diagnosticaTipi.forEach(({ name, label, date }) => {
      if (formValues[name]) {
        const option = this.findInDizionario(this.optionsDiagnostica, label);
        if (option) {
          diagnostica.push({
            idScheda: datiCorretti.idScheda,
            idDizionario: option.idDizionario,
            data: formValues[date] ? new Date(formValues[date]).getTime() : null
          });
        }
      }
    });

    // Gestione lesione vertebra
    const lesioneVertebra = [...this.vertebreSelezionate];

    // Gestione tipo lesione vertebrale
    let tipoLesioneVertebrale = formValues.tipoLesioneVertebrale ? formValues.tipoLesioneVertebrale : null;

    // Gestione traumi associati
    const traumiAssociati: any[] = [];
    const traumiTipi = [
      { name: 'nessunoTraumi', label: TraumaType.NESSUNO },
      { name: 'cranioTraumi', label: TraumaType.CRANIO },
      { name: 'scheletroTraumi', label: TraumaType.SCHELETRO },
      { name: 'organiInterniTraumi', label: TraumaType.ORGANI_INTERNI }
    ];

    traumiTipi.forEach(({ name, label }) => {
      if (formValues[name]) {
        const option = this.findInDizionario(this.optionsTraumiAssociati, label);
        if (option) {
          traumiAssociati.push({
            idScheda: datiCorretti.idScheda,
            idDizionario: option.idDizionario
          });
        }
      }
    });

    // Gestione intervento chirurgico
    const interventoChirurgico: any[] = this.getInterventiChirurgiciFromForm(formValues, datiCorretti.idScheda);

    // Gestione trattamento medico acuto
    let trattamentoMedicoAcuto = formValues.trattamentoMedicoAcuto ? formValues.trattamentoMedicoAcuto : null;

    // Gestione presa in carico riabilitativa
    const presaCaricoRiabilitativaFaseAcuta = this.convertBooleanValue(formValues.presaInCaricoRiabilitativa)

    // Aggiornamento completo del modello mantenendo tutti i campi
    this.datiLesioneTrattamento = {
      ...datiCorretti,
      diagnostica,
      lesioneVertebra,
      tipoLesioneVertebrale,
      noteTipoLesioneVertebrale: formValues.noteTipoLesioneVertebrale || '',
      interventoChirurgico,
      interventiChirurgici: formValues.interventiChirurgici,
      traumiAssociati,
      altroTraumiAssociati: formValues.altroTraumiDescrizione || '',
      noteTraumiAssociati: formValues.noteTraumiAssociati || '',
      trattamentoMedicoAcuto,
      altroTrattamentoMedicoAcuto: formValues.altroTrattamentoMedicoAcuto || '',
      noteTrattamentoMedicoAcuto: formValues.noteTrattamentoMedicoAcuto || '',
      presaCaricoRiabilitativaFaseAcuta
    };

    // Aggiornamento del servizio
    this.datiCliniciService.setDatiLesioneTrattamento({ ...this.datiLesioneTrattamento });
  }

  // Metodo di supporto per estrarre gli interventi chirurgici dal form
  private getInterventiChirurgiciFromForm(formValues: any, idScheda: number | null): any[] {
    const interventoChirurgico: any[] = [];

    if (!formValues.interventiChirurgici) {
      return interventoChirurgico;
    }

    // Otteniamo tutti gli interventi direttamente dal FormArray
    if (this.interventiFormArray.length > 0) {

      for (let i = 0; i < this.interventiFormArray.length; i++) {
        const interventoForm = this.interventiFormArray.at(i);

        // Salta gli elementi disabilitati
        if (interventoForm.disabled) {
          continue;
        }

        const interventoValue = interventoForm.value;

        const interventoCompleto = this.buildIntervento(
          idScheda,
          interventoValue.idIntervento,
          interventoValue.dataIntervento,
          interventoValue.oraIntervento,
          interventoValue.tipologiaInterventoId || null,
          interventoValue.note
        );

        if (interventoCompleto) {
          interventoChirurgico.push(interventoCompleto);
        } else {
        }
      }
    }

    return interventoChirurgico;
  }

  // Helper per costruire un singolo intervento
  private buildIntervento(
    idScheda: number | null,
    idIntervento: number,
    dataIntervento: Date | null,
    oraIntervento: string,
    tipologiaInterventoId: number,
    note: string
  ): any | null {

    const intervento = {
      idScheda,
      idIntervento,
      dataOraInterventoChirurgico: null as unknown as number,
      tipoInterventoChirurgicoLesione: {} as DizionarioModel | null,
      noteInterventoChirurgico: ''
    };

    let hasAnyData = false;

    // Gestione data e ora
    let dataOraIntervento = this.combineDateTime(dataIntervento, oraIntervento);
    if (dataOraIntervento) {
      if (!oraIntervento) dataOraIntervento = new Date(dataOraIntervento).setHours(0,0,0,0) // workaround per fix ora scompare quando cambio sezione
      intervento.dataOraInterventoChirurgico = new Date(dataOraIntervento).getTime();
      hasAnyData = true;
    }

    // Gestione tipologia intervento
    if (tipologiaInterventoId) {
      const tipologiaSelezionata = this.optionsTipologiaIntervento.find(
        t => t.idDizionario === tipologiaInterventoId
      );
      if (tipologiaSelezionata) {
        intervento.tipoInterventoChirurgicoLesione = tipologiaSelezionata || null;
        hasAnyData = true;
      }
    } else {
      intervento.tipoInterventoChirurgicoLesione = null;
    }

    // Gestione note
    if (note?.trim()) {
      intervento.noteInterventoChirurgico = note.trim();
      hasAnyData = true;
    }

    // Se è un intervento già salvato con un ID, consideriamolo sempre valido
    if (idIntervento) {
      hasAnyData = true;
    }

    // Restituiamo l'intervento solo se ha almeno un dato
    if (hasAnyData) {
      return intervento;
    } else {
      return null;
    }
  }

  private combineDateTime(date: Date | null, timeString: string): number | null {
    if (!date) return null;

    const result = new Date(date);

    if (timeString && /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeString)) {
      const [hours, minutes] = timeString.split(':').map(Number);
      result.setHours(hours, minutes, 1, 0); //aggiungo 1 secondo per far comparire il campo ora se inserito
    }

    return result.getTime();
  }

  // trovare elementi nel dizionario
  private findInDizionario(options: Array<DizionarioModel>, descrizione: string): DizionarioModel | undefined {
    return DizionarioHelper.findInDizionario(options, descrizione);
  }

  // verificare presenza in dizionario
  private someInDizionario(items: any[] | undefined, options: Array<DizionarioModel>, descrizione: string): boolean {
    return DizionarioHelper.someInDizionario(items, options, descrizione);
  }

  // Metodo per gestire l'abilitazione/disabilitazione
  private manageConditionalControl(
    controlName: string,
    isEnabled: boolean,
    defaultValue: any = null,
    requireValidation: boolean = true
  ): void {
    if (this.readOnly) return;
    const control = this.lesioneTrattamentoForm.get(controlName);
    if (!control) return;

    if (isEnabled) {
      control.enable({ emitEvent: false });
      if (!this.isRiabilitazione && requireValidation) {
        control.setValidators([Validators.required, dateNotFutureValidator()]);
      } else {
        control.setValidators([dateNotFutureValidator()]);
      }
    } else {
      control.disable({ emitEvent: false });
      // Imposta il valore predefinito in base al tipo
      if (typeof defaultValue === 'string') {
        control.setValue('', { emitEvent: false });
      } else {
        control.setValue(defaultValue, { emitEvent: false });
      }
      control.clearValidators();
    }

    control.updateValueAndValidity({ emitEvent: false });
  }

  private setupInterventoSubscription() {
    // Monitoriamo i cambiamenti del primo intervento (form principale)
    const dataInterventoChanges$ = this.lesioneTrattamentoForm.get('dataIntervento')?.valueChanges || of(null);
    const oraInterventoChanges$ = this.lesioneTrattamentoForm.get('oraIntervento')?.valueChanges || of(null);
    const tipologiaInterventoChanges$ = this.lesioneTrattamentoForm.get('tipologiaIntervento')?.valueChanges || of(null);
    const noteInterventoChanges$ = this.lesioneTrattamentoForm.get('noteTipologiaIntervento')?.valueChanges || of(null);

    combineLatest([
      dataInterventoChanges$,
      oraInterventoChanges$,
      tipologiaInterventoChanges$,
      noteInterventoChanges$
    ]).pipe(
      takeUntil(this.destroyed$),
      debounceTime(1),
      tap(() => this.updateInterventoState())
    ).subscribe();

    // Monitoriamo i cambiamenti nei form degli interventi aggiuntivi
    this.interventiFormArray.valueChanges.pipe(
      takeUntil(this.destroyed$),
      debounceTime(1),
      tap((values) => {
        if (!this.datiLesioneTrattamento) {
          this.datiLesioneTrattamento = {
            idScheda: null,
            nomeScheda: '',
            diagnostica: [],
            lesioneVertebra: [],
            tipoLesioneVertebrale: null,
            noteTipoLesioneVertebrale: '',
            interventoChirurgico: [],
            interventiChirurgici: null,
            traumiAssociati: [],
            altroTraumiAssociati: '',
            noteTraumiAssociati: '',
            trattamentoMedicoAcuto: null,
            altroTrattamentoMedicoAcuto: '',
            noteTrattamentoMedicoAcuto: '',
            presaCaricoRiabilitativaFaseAcuta: false
          };
        }

        const interventoFromFormArray = this.getInterventiFromFormArray();

        this.datiLesioneTrattamento.interventoChirurgico = interventoFromFormArray;
        this.datiCliniciService.setDatiLesioneTrattamento(this.datiLesioneTrattamento);

      })
    ).subscribe();

    // Validazione oraIntervento per ogni FormGroup aggiuntivo
    this.interventiFormArray.controls.forEach(ctrl => {
      const fg = ctrl as FormGroup;
      const oraCtrl = fg.get('oraIntervento');
      const dataCtrl = fg.get('dataIntervento');
      if (oraCtrl && dataCtrl) {
        merge(
          oraCtrl.valueChanges.pipe(startWith(oraCtrl.value)),
          dataCtrl.valueChanges.pipe(startWith(dataCtrl.value))
        ).pipe(
          takeUntil(this.destroyed$)
        ).subscribe(() => {
          this.datiCliniciService.validateTime(oraCtrl, oraCtrl.value, dataCtrl.value);
        });
      }
    });
  }

  private updateInterventoState(): void {
    if (!this.isInterventoChirurgicoSi) {
      this.interventoState$.next(null);
      return;
    }

    const idIntervento = this.getFormControlValue<number | null>('idIntervento', null);
    const dataIntervento = this.getFormControlValue<Date | null>('dataIntervento', null);
    const oraIntervento = this.getFormControlValue<string>('oraIntervento', '');
    const tipologiaInterventoId = this.getFormControlValue<number>('tipologiaIntervento', 0);
    const note = this.getFormControlValue<string>('noteTipologiaIntervento', '');
    const tipologiaDescrizione = this.getTipologiaDescrizione(tipologiaInterventoId);

    this.interventoState$.next({
      idIntervento,
      dataIntervento,
      oraIntervento,
      tipologiaIntervento: tipologiaDescrizione,
      tipologiaInterventoId,
      note
    });
  }

  private getTipologiaDescrizione(idTipologia: number): string {
    const tipologia = this.optionsTipologiaIntervento.find(t => t.idDizionario === idTipologia);
    return tipologia?.descrizione || '';
  }

  private getFormControlValue<T>(controlName: string, defaultValue: T): T {
    const control = this.lesioneTrattamentoForm.get(controlName);
    return control ? (control.value ?? defaultValue) : defaultValue;
  }

  asFormGroup(ctrl: AbstractControl<any, any> | null): FormGroup {
    return ctrl as FormGroup;
  }

  private setupFormValidation() {
    if (!this.readOnly) {
      const dataEsameRxControl = this.lesioneTrattamentoForm.get('dataEsameRx');
      const dataEsameTcControl = this.lesioneTrattamentoForm.get('dataEsameTc');
      const dataEsameRmnControl = this.lesioneTrattamentoForm.get('dataEsameRmn');
      const dataInterventoControl = this.lesioneTrattamentoForm.get('dataIntervento');
      const oraInterventoControl = this.lesioneTrattamentoForm.get('oraIntervento');

      [dataEsameRxControl, dataEsameTcControl, dataEsameRmnControl].forEach(control => {
      });
      if (dataInterventoControl) {
        const validators = !this.isRiabilitazione ? [Validators.required, dateNotFutureValidator()] : [dateNotFutureValidator()];
        dataInterventoControl.setValidators(validators);
        dataInterventoControl.updateValueAndValidity();
      }

      // Validazione oraIntervento principale
      if (oraInterventoControl) {
        oraInterventoControl.valueChanges.pipe(
          startWith(oraInterventoControl.value),
          takeUntil(this.destroyed$)
        ).subscribe(value => {
          const dataValue = dataInterventoControl ? dataInterventoControl.value : null;
          const getRelatedDate = () => dataValue;
          const errors = timeValidator(getRelatedDate)(oraInterventoControl);
          oraInterventoControl.setErrors(errors);
        });
      }

      // Validazione oraIntervento per ogni FormGroup aggiuntivo
      this.interventiFormArray.controls.forEach(ctrl => {
        const fg = ctrl as FormGroup;
        const oraCtrl = fg.get('oraIntervento');
        const dataCtrl = fg.get('dataIntervento');
        if (oraCtrl) {
          oraCtrl.valueChanges.pipe(
            startWith(oraCtrl.value),
            takeUntil(this.destroyed$)
          ).subscribe(value => {
            const getRelatedDate = () => dataCtrl ? dataCtrl.value : null;
            const errors = timeValidator(getRelatedDate)(oraCtrl);
            oraCtrl.setErrors(errors);
          });
        }
      });

      dataInterventoControl?.valueChanges.pipe(
        takeUntil(this.destroyed$)
      ).subscribe(() => {
        oraInterventoControl?.updateValueAndValidity();
      });
      this.interventiFormArray.controls.forEach(ctrl => {
        const fg = ctrl as FormGroup;
        const dataCtrl = fg.get('dataIntervento');
        if (dataCtrl) {
          dataCtrl.valueChanges
            .pipe(takeUntil(this.destroyed$))
            .subscribe(() => {
              fg.updateValueAndValidity();
            });
        }
      });
    }
  }


  protected readonly Validators = Validators;
}

