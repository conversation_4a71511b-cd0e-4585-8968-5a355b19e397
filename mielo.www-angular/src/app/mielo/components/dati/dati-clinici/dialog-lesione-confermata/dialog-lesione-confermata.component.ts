// dialog-lesione-confermata.component.ts
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MaterialModule } from '../../../../../core/material.module';

@Component({
  selector: 'app-dialog-lesione-confermata',
  templateUrl: './dialog-lesione-confermata.component.html',
  styleUrl: './dialog-lesione-confermata.component.scss',
  standalone: true,
  imports: [
    MatDialogModule,
    MaterialModule
  ]
})
export class DialogLesioneConfermataComponent {
  constructor(
    public dialogRef: MatDialogRef<DialogLesioneConfermataComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  tornaAllaCompilazione(): void {
    this.dialogRef.close(false);
  }

  annullaScheda(): void {
    this.dialogRef.close(true);
  }
}
