<div [ngClass]="idPatient && idPatient >= 0 ? 'container-data' : 'container-data pt-4'">
  <!--se è update paziente mostro headbar-->
  <div *ngIf="idPatient && idPatient >= 0" class="menu-container">
    <div class="d-flex justify-content-between align-items-center p-3">
      <button mat-icon-button class="back-button" (click)="onBack()">
                      <span class="back-link">
                          <svg class="icon icon-left">
                              <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-arrow-left"></use>
                          </svg>
                          <span class="text-green">Indietro</span>
                      </span>
      </button>

      <button mat-raised-button color="primary"
              (click)="updatePatient()"
              [disabled]="isSaveButtonDisabled"
              class="btn-primary btn-save" *ngIf="showSaveBtn">
        Salva
      </button>
    </div>
  </div>


  <h3 [ngClass]="idPatient && idPatient >= 0 ? 'pt-4' : 'ml-3 mb-0'">Dati paziente</h3>
  <app-dati-anagrafici
    [updatePatientData]="patientInfo"
    class="m-3"/>
  <app-dati-socio-economici
    [updatePatientData]="patientInfo"
    class="m-3"/>
</div>
