<mat-accordion>
    <mat-expansion-panel (expandedChange)="enableAllGetData($event)">
        <mat-expansion-panel-header>
            <mat-panel-title>
                <span class="panel-title">DATI SOCIO-ECONOMICI</span>
            </mat-panel-title>
        </mat-expansion-panel-header>

        <form [formGroup]="datiSocioEconomiciForm" class="box-dati">
            <div class="mt-4">
                <div class="mb-4">
                    <mat-label class="text-overflow">Scolarità{{isUpdate ? '*' : ''}}</mat-label>
                    <div class="mt-2">
                        <mat-radio-group formControlName="scolarita"
                                         aria-label="Seleziona scolarità" class="d-flex flex-wrap">
                            <div
                              *ngFor="let scolarita of scolarita"
                              class="d-flex align-items-center mb-2">
                                <mat-radio-button
                                        (click)="toggleRadioSelection('scolarita', scolarita)"
                                        [value]="scolarita.idDizionario"></mat-radio-button>
                                <label class="f-18 font-weight-medium mb-0">{{scolarita.descrizione | capitalizeFirst}}</label>
                            </div>
                        </mat-radio-group>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-6 mb-3">
                        <mat-label class="text-overflow">Occupazione{{isUpdate ? '*' : ''}}</mat-label>
                        <mat-form-field class="w-100 mt-2">
                            <mat-select formControlName="condizioneProfessionale"
                                           [compareWith]="compareByIdDizionario"
                                        placeholder="Seleziona">
                                <mat-option [value]="null"></mat-option>
                                <mat-option *ngFor="let occupazione of occupazioni" [value]="occupazione">
                                    {{ occupazione.descrizione }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div class="col-12 col-md-6 mb-3">
                        <mat-label class="text-overflow">Altro{{datiSocioEconomiciForm.get('condizioneProfessionale')?.value?.idDizionario === EOccupazione.ALTRO ? '*' : ''}}</mat-label>
                        <mat-form-field class="w-100 mt-2">
                            <input
                              maxlength="250"
                              matInput
                              formControlName="altroProfessione" placeholder="Inserisci"
                              [matTooltip]="datiSocioEconomiciForm.get('altroProfessione')?.value || ''"
                              matTooltipPosition="above" />

                          <mat-error *ngIf="datiSocioEconomiciForm.get('altroProfessione')?.errors?.['required']">
                            Campo obbligatorio
                          </mat-error>
                        </mat-form-field>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-6 mb-3">
                        <mat-label class="text-overflow">Stato civile{{isUpdate ? '*' : ''}}</mat-label>
                        <mat-form-field class="w-100 mt-2">
                            <mat-select formControlName="statoCivile"
                                        [compareWith]="compareByIdDizionario"
                                        placeholder="Seleziona">
                                <mat-option [value]="null"></mat-option>
                                <mat-option *ngFor="let stato of statiCivili" [value]="stato">
                                    {{ stato.descrizione }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </form>
    </mat-expansion-panel>
</mat-accordion>
