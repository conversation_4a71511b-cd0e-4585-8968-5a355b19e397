import {CommonModule} from "@angular/common";
import {Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import {Subscription, tap} from "rxjs";
import {MaterialModule} from "../../../../../core/material.module";
import {DecoderService} from "../../../../../shared/services/decoder.service";
import {
  AggiungiSchedaRicoveroRequest,
  DizionarioModel,
  PazienteModel
} from "../../../../../shared/interfaces/scheda-ricovero.interface";
import {EOccupazione} from "../../../../../shared/enums/enum";
import {SchedaRicoveroService} from "../../../../services/scheda-ricovero.service";
import {compareByIdDizionario} from "../../../../../shared/utils/utils";
import {CapitalizePipe} from "../../../../../shared/pipes/capitalize.pipe";

@Component({
  selector: 'app-dati-socio-economici',
  standalone: true,
  imports: [MaterialModule, CommonModule, ReactiveFormsModule, CapitalizePipe],
  templateUrl: './dati-socio-economici.component.html',
  styleUrl: './dati-socio-economici.component.scss'
})
export class DatiSocioEconomiciComponent implements OnInit, OnDestroy, OnChanges {
  @Input() updatePatientData: PazienteModel
  protected readonly compareByIdDizionario = compareByIdDizionario;

  datiSocioEconomiciForm: FormGroup;
  occupazioni: DizionarioModel[] = [];
  statiCivili: DizionarioModel[] = [];
  scolarita: DizionarioModel[] = [];
  protected readonly EOccupazione = EOccupazione;
  private subscriptions: Subscription[] = [];
  private datiSocioEconomiciForm$: Subscription;
  isUpdate = this.schedaRicService.idSchedaSelectedSubject.getValue();

  canInputsBeEnabled: boolean = false;

  constructor(
    private fb: FormBuilder,
    private schedaRicService: SchedaRicoveroService,
    private decoderService: DecoderService
  ) {
    this.datiSocioEconomiciForm = this.fb.group({
      scolarita: undefined,
      condizioneProfessionale: undefined,
      altroProfessione: [{value: '', disabled: true}],
      statoCivile: undefined
    });
  }

  ngOnInit() {
    // se sono in create non disabilito mai
    this.canInputsBeEnabled = !this.schedaRicService.getSchedaCorrente() ? true : this.schedaRicService.getSchedaCorrente()?.existAtLeastOneEventOnSamePresidio;
    this.datiSocioEconomiciForm$ = this.datiSocioEconomiciForm.valueChanges
      .pipe(
        tap(() => {
          this.schedaRicService.schedaCreation$.next({
            ...this.schedaRicService.schedaCreation$.getValue(),
            paziente: {
              ...this.schedaRicService.schedaCreation$.getValue()?.paziente,
              ...this.datiSocioEconomiciForm.getRawValue(),
            }
          } as AggiungiSchedaRicoveroRequest);

          this.schedaRicService._areFormsCreateSchedaValidSubject$.next({
            ...this.schedaRicService._areFormsCreateSchedaValidSubject$.getValue(),
            formDatiSocioEconomici: {
              // valid is not a stable value, do not use to check form validity
              valid: this.datiSocioEconomiciForm.valid,
              pristine: this.datiSocioEconomiciForm.pristine
            }
          });
        })
      )
      .subscribe()

    const occupazioneChanges = this.datiSocioEconomiciForm.get('condizioneProfessionale')?.valueChanges
      .subscribe((value: DizionarioModel) => {
        if (value?.idDizionario === EOccupazione.ALTRO) {
          this.datiSocioEconomiciForm.get('altroProfessione')?.setValidators([Validators.required]);
          if (!this.canInputsBeEnabled){
            this.datiSocioEconomiciForm.get('altroProfessione')?.disable({emitEvent: false});
          } else {
            this.datiSocioEconomiciForm.get('altroProfessione')?.enable({emitEvent: false});
          }
        } else {
          this.datiSocioEconomiciForm.get('altroProfessione')?.setValue(null, {emitEvent: false});
          this.datiSocioEconomiciForm.get('altroProfessione')?.disable({emitEvent: false});
          this.datiSocioEconomiciForm.get('altroProfessione')?.removeValidators([Validators.required]);
        }
      }) as Subscription
    this.subscriptions.push(occupazioneChanges);

    const scheda = this.schedaRicService.schedaCreation$.getValue() as AggiungiSchedaRicoveroRequest
    if (!this.isUpdate) this.datiSocioEconomiciForm.patchValue({
      ...scheda?.paziente,
    })

    if (!this.canInputsBeEnabled) {
      this.datiSocioEconomiciForm.disable();
    }
  }

  enableAllGetData(event: boolean) {
    if (event) {
      if (!this.occupazioni.length) {
        const occupazioniSub = this.decoderService.getDcodOccupazione()
          .pipe(
            tap((occupazioni) => {
              this.occupazioni = occupazioni;
            })
          )
          .subscribe();
        this.subscriptions.push(occupazioniSub);
      }

      if (!this.statiCivili.length) {
        const statiCiviliSub = this.decoderService.getDcodStatoCivile()
          .pipe(
            tap((statiCivili) => {
              this.statiCivili = statiCivili;
            })
          )
          .subscribe();
        this.subscriptions.push(statiCiviliSub);
      }

      if (!this.scolarita.length) {
        const scolarita$ = this.decoderService.getDcodScolarita()
          .pipe(
            tap((scolarita) => {
              this.scolarita = scolarita;
            })
          )
          .subscribe();
        this.subscriptions.push(scolarita$);
      }
    }
  }

  toggleRadioSelection(controlName: string, option: DizionarioModel): void {
    const control = this.datiSocioEconomiciForm.get(controlName);
    if (control?.disabled) return;
    control?.markAsDirty()

    if (control?.value === option.idDizionario) {
      control?.setValue(null); // toggle off
    } else {
      control?.setValue(option.idDizionario);
    }
  }

    ngOnChanges(changes: SimpleChanges) {
    if (changes) {
      if (this.updatePatientData?.idPaziente) {
        this.datiSocioEconomiciForm.patchValue(this.updatePatientData);
        this.datiSocioEconomiciForm.get('scolarita')?.setValue(this.updatePatientData?.scolarita?.idDizionario, {emitEvent: true});
      }
    }
  }

  ngOnDestroy() {
    this.datiSocioEconomiciForm$?.unsubscribe()
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

}
