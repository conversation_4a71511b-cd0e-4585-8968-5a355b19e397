import { Component, OnDestroy } from '@angular/core';
import { MaterialModule } from "../../../../core/material.module";
import { DatiAnagraficiComponent } from './dati-anagrafici/dati-anagrafici.component';
import { DatiSocioEconomiciComponent } from './dati-socio-economici/dati-socio-economici.component';
import { DialogService } from "../../../../shared/services/dialog.service";
import { Router } from "@angular/router";
import { Subscription, tap } from "rxjs";
import { SchedaRicoveroService } from "../../../services/scheda-ricovero.service";
import { PazienteModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import {CommonModule, Location} from "@angular/common";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { STATO_ESTERO } from "../../../../shared/utils/const";
import moment from "moment";
import { createDomicilioObj, createResidenzaObj } from "../../../../shared/utils/residenza.utils";

//UPDATE COMPONENTE

@Component({
  selector: 'app-dati-paziente',
  standalone: true,
  imports: [MaterialModule, CommonModule, DatiAnagraficiComponent, DatiSocioEconomiciComponent],
  templateUrl: './dati-paziente.component.html',
  styleUrl: './dati-paziente.component.scss'
})
export class DatiPazienteComponent implements OnDestroy {
  idPatient = this.schedaRicService.idEditPatientSubject.getValue();
  patientInfo: PazienteModel
  isSaveButtonDisabled: boolean = true;
  private updatePatient$: Subscription;

  showSaveBtn: boolean = false;

  constructor(
    private dialogService: DialogService,
    private router: Router,
    private schedaRicService: SchedaRicoveroService,
    private location: Location
  ) {
    this.showSaveBtn = this.schedaRicService.getSchedaCorrente()?.existAtLeastOneEventOnSamePresidio;
    schedaRicService._areFormsCreateSchedaValidSubject.pipe(
      takeUntilDestroyed(),
      tap(formValues => {
        const idScheda = this.schedaRicService.idSchedaSelectedSubject?.getValue() ?? -1;
        const isUpdate = idScheda >= 0;

        this.isSaveButtonDisabled = isUpdate
            ? !formValues.formDatiAnagrafici.valid || (formValues.formDatiAnagrafici.pristine && formValues.formDatiSocioEconomici.pristine)
          : !formValues.formDatiAnagrafici.valid;
      })
    ).subscribe()
    if (this.idPatient) {
      this.schedaRicService.getPazienteById(this.idPatient).pipe(
        takeUntilDestroyed(),
        tap(paziente => this.patientInfo = paziente),
      ).subscribe()
    }
  }

  onBack() {
    const canShowPopup = this.schedaRicService.getSchedaCorrente()?.existAtLeastOneEventOnSamePresidio;
    if (canShowPopup) {
      this.dialogService.confermaTornaAllaScheda().subscribe(result => {
        if (result === 'conferma') {
          this.location.back(); // Torna all'URL precedente
        }
      });
    } else {
      this.location.back();
    }
  }

  updatePatient() {
    const patientInfo = {
      ...this.schedaRicService.schedaCreation$.getValue()?.paziente,
      idPaziente: this.idPatient,
      dataNascita: moment(this.schedaRicService.schedaCreation$.getValue()?.paziente.dataNascita).format('YYYY-MM-DD'),
      dataRilascioTeam: moment.isMoment(this.schedaRicService.schedaCreation$.getValue()?.paziente.dataRilascioTeam) ? moment(this.schedaRicService.schedaCreation$.getValue()?.paziente.dataRilascioTeam).format('YYYY-MM-DD') : undefined,
      residenza: createResidenzaObj(this.schedaRicService.schedaCreation$.getValue()?.paziente),
      domicilio: createDomicilioObj(this.schedaRicService.schedaCreation$.getValue()?.paziente),
      genere: { idDizionario: this.schedaRicService.schedaCreation$.getValue()?.paziente.genere } as any,
      scolarita: this.schedaRicService.schedaCreation$.getValue()?.paziente?.scolarita && (this.schedaRicService.schedaCreation$.getValue()?.paziente?.scolarita as any) >= 0 ? { idDizionario: this.schedaRicService.schedaCreation$.getValue()?.paziente.scolarita } as any : null,
      comuneNascita: (this.schedaRicService.schedaCreation$.getValue()?.paziente as any).provinciaNascita?.cdProvinciaISTAT !== STATO_ESTERO.cdProvinciaISTAT ? this.schedaRicService.schedaCreation$.getValue()?.paziente.comuneNascita : null
    } as PazienteModel


    // rendopatientInfo oggetto request uguale al dto
    delete (patientInfo as any).eta
    delete (patientInfo as any).provinciaResidenza
    delete (patientInfo as any).comuneResidenza
    delete (patientInfo as any).indirizzoResidenza
    delete (patientInfo as any).numeroCivico
    delete (patientInfo as any).provinciaDomicilio
    delete (patientInfo as any).comuneDomicilio
    delete (patientInfo as any).indirizzoDomicilio
    delete (patientInfo as any).numeroCivicoDomicilio
    delete (patientInfo as any).provinciaNascita

    this.updatePatient$ = this.schedaRicService.checkCodiceFiscale(patientInfo).pipe(
      tap(res => {
        if (res) {
          this.updatePatientFN(patientInfo).subscribe()
        } else {
          const dialogCheckCodFisc$ = this.dialogService.openConfirmDialog({
            tipo: 'warning',
            titolo: 'Attenzione',
            messaggio: 'Il Codice Fiscale non è coerente con i dati anagrafici inseriti.\nDesideri salvare lo stesso?',
            bottoni: [
              { testo: 'Continua le modifiche', azione: 'annulla' },
              { testo: 'Salva Scheda Paziente', azione: 'conferma' }
            ]
          })
            .pipe(
              tap(res => {
                dialogCheckCodFisc$.unsubscribe()
                if (res === 'conferma') {
                  this.updatePatientFN(patientInfo).subscribe()
                }
              })
            )
            .subscribe()
        }
      }
      ))
      .subscribe()
  }

  updatePatientFN(patientInfo: PazienteModel) {
    return this.schedaRicService.updatePaziente(patientInfo).pipe(
      tap(() => {
        this.schedaRicService.idEditPatientSubject.next(null);
        const dialogUpdatePatient$ = this.dialogService.datiPazienteAggiornati().pipe(
          tap(result => {
            if (result === 'conferma') {
              this.location.back()
              dialogUpdatePatient$.unsubscribe()
            }
          })
        ).subscribe()
      })
    )
  }

  ngOnDestroy() {
    this.updatePatient$?.unsubscribe();
  }
}
