<mat-accordion>
  <mat-expansion-panel expanded>
    <mat-expansion-panel-header>
      <div class="header-container">
        <div class="panel-title">DATI ANAGRAFICI</div>
        <div class="identifica-cittadino" (click)="$event.stopPropagation()">
          <ng-container *ngIf="!isCittadinoIdentificato">
            <button mat-button class="identifica-button" (click)="identificaCittadino($event)"
                    [disabled]="!enableIdentificaCittadino()">
              <div class="button-content">
                <mat-icon class="search-icon">search</mat-icon>
                <span class="identifica-text">Identifica cittadino</span>
              </div>
            </button>
          </ng-container>
          <ng-container *ngIf="isCittadinoIdentificato">
            <button mat-button class="identifica-button"
                    (click)="annullaIdentificazione($event)">
              <div class="button-content">
                <mat-icon class="cancel-icon">close</mat-icon>
                <span class="annulla-text">Annulla identificazione</span>
              </div>
            </button>
          </ng-container>
        </div>
      </div>
    </mat-expansion-panel-header>

    <form [formGroup]="datiAnagraficiForm" class="box-dati">
      <div class="mt-4">
        <div class="row">
          <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Tipologia codice identificativo*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="tipoCodiceIdentificativo"
                          [compareWith]="compareByIdDizionario"
                          placeholder="Seleziona">
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let option of tipologiaCodiceIdentificativo$ | async"
                            [value]="option">{{ option.descrizione }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="datiAnagraficiForm.get('tipoCodiceIdentificativo')?.invalid">
                Campo obbligatorio
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Numero codice*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input matInput formControlName="codiceIdentificativo"
                     [placeholder]="getTipologiaPlaceholder()"
                     [maxLength]=computeMaxLengthCodice()>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['required']  ">
                Campo obbligatorio
              </mat-error>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['codiceFiscaleInvalido']  ">
                Formato codice fiscale non valido
              </mat-error>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['omocodiaInvalida']  ">
                Formato omocodia non valido
              </mat-error>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['codiceFiscaleProvvisorioInvalido']  ">
                Il codice fiscale provvisorio deve contenere 11 cifre
              </mat-error>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['codiceSTPInvalido']  ">
                Il codice STP deve iniziare con 'STP' seguito da 13 cifre
              </mat-error>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['codiceTEAMInvalido']  ">
                Il codice TEAM deve contenere 20 caratteri alfanumerici
              </mat-error>
              <mat-error
                *ngIf="datiAnagraficiForm.get('codiceIdentificativo')?.errors?.['codiceENIInvalido']  ">
                Il codice ENI deve iniziare con 'ENI' seguito da 13 cifre
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-12 col-md-12 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Data di rilascio (STP o TEAM)</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input matInput
                     [max]="moment()"
                     formControlName="dataRilascioTeam" [matDatepicker]="pickerRilascio"
                     placeholder="GG/MM/AAAA">
              <mat-datepicker-toggle matIconSuffix [for]="pickerRilascio">
                <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon
                          class="d-flex icon-calendar"></mat-icon>
              </mat-datepicker-toggle>
              <mat-datepicker #pickerRilascio></mat-datepicker>
                <mat-error *ngIf="datiAnagraficiForm.get('dataRilascioTeam')?.hasError('matDatepickerMax')">
                        {{ERROR_MESSAGE.MAX_DATE}}
                </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="row">
          <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Nome*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input
                maxlength="50"
                minlength="2"
                matInput formControlName="nome" placeholder="Inserisci"/>
              <mat-error>{{ getErrorNomeCognome('nome') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Cognome*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input
                minlength="2"
                maxlength="50"
                matInput formControlName="cognome" placeholder="Inserisci"/>
              <mat-error>{{ getErrorNomeCognome('cognome') }}</mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-12 col-md-12 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Data di nascita*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input matInput [matDatepicker]="picker" placeholder="GG/MM/AAAA"
                     formControlName="dataNascita" [max]="maxDate">
              <mat-datepicker-toggle matSuffix [for]="picker">
                <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon
                          class="d-flex icon-calendar"></mat-icon>
              </mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error>{{ getErrorDataNascita() }}
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="row">
          <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Provincia di nascita</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="provinciaNascita" placeholder="Seleziona"
                          [compareWith]="compareFn">
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let provincia of province"
                            [value]="getProvinciaValue(provincia)">
                  {{ provincia.dsProvincia }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Comune/stato estero di
              nascita{{ datiAnagraficiForm.get('provinciaNascita')?.value?.cdProvinciaISTAT ? '*' : '' }}
            </mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select
                required
                formControlName="comuneNascita"
                placeholder="Seleziona"
                [compareWith]="compareFn">
                <ng-container
                  *ngIf="datiAnagraficiForm.get('provinciaNascita')?.value?.cdProvinciaISTAT === cdProvinciaISTAT_ESTERO">
                  <mat-option [value]="null"></mat-option>
                  <mat-option *ngFor="let nazione of listNazioni"
                              (onSelectionChange)="$event.isUserInput && setNazioneNascita(nazione)"
                              [value]="getNazioneValue(nazione)">
                    {{ nazione.dsNazione }}
                  </mat-option>
                </ng-container>
                <ng-container
                  *ngIf="datiAnagraficiForm.get('provinciaNascita')?.value?.cdProvinciaISTAT !== cdProvinciaISTAT_ESTERO">
                  <mat-option [value]="null"></mat-option>
                  <mat-option *ngFor="let comune of listComuniNascitaFiltered"
                              (onSelectionChange)="setNazioneNascita(STATO_ITALIANO)"
                              [value]="getComuneValue(comune)">
                    {{ comune.dsComune }}
                  </mat-option>
                </ng-container>
              </mat-select>
              <mat-error *ngIf="datiAnagraficiForm.get('comuneNascita')?.errors?.['required']">Campo obbligatorio
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-12 col-sm-12 col-md-12 col-lg-4 col-xl-4 margin-custom-bottom">
            <mat-label class="text-overflow">Cittadinanza*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="cittadinanza" placeholder="Seleziona"
                          [compareWith]="compareByCdNazioneIstat">
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let nazione of listNazioniWithIT"
                            [value]="getNazioneValue(nazione)">
                  {{ nazione.dsNazione }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="datiAnagraficiForm.get('cittadinanza')?.invalid">
                Campo obbligatorio
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="row">
          <div class="col-4 col-sm-2 col-md-2 col-lg-2 col-xl-2 margin-custom-bottom">
            <mat-label class="text-overflow">Età*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input matInput
                     disabled
                     [value]="etaSignal()"/>
            </mat-form-field>
          </div>
          <div class="col-8 col-sm-10 col-md-10 col-lg-10 col-xl-10 margin-custom-bottom">
            <mat-label class="text-overflow">Genere*</mat-label>
            <div class="radio-container mt-2">
              <mat-radio-group
                required
                formControlName="genere" aria-label="Seleziona genere"
                class="d-flex align-items-center w-100">
                <div
                  *ngFor="let sesso of getDcodGenereSessuale$ | async"
                  class="col-12 col-sm-12 col-md-3 col-lg-auto col-xl-auto d-flex align-items-center mb-2">
                  <mat-radio-button [value]="sesso.idDizionario"></mat-radio-button>
                  <label class="f-18 font-weight-medium mb-0">{{ sesso.descrizione | capitalizeFirst }}</label>
                </div>
              </mat-radio-group>
            </div>
            <mat-error
              *ngIf="datiAnagraficiForm.get('genere')?.invalid  && datiAnagraficiForm.get('genere')?.touched">
              Campo obbligatorio
            </mat-error>
          </div>
        </div>

        <div class="row">
          <div class="col-3 margin-custom-bottom">
            <mat-label class="text-overflow">Provincia di residenza*</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="provinciaResidenza" placeholder="Seleziona"
                          [compareWith]="compareFn">
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let provincia of provinceDiResidenza"
                            [value]="getProvinciaValue(provincia)">
                  {{ provincia.dsProvincia }}
                </mat-option>
              </mat-select>
              <mat-error
                *ngIf="datiAnagraficiForm.get('provinciaResidenza')?.invalid">
                Campo obbligatorio
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-4 margin-custom-bottom">
            <mat-label class="text-overflow">Comune/stato estero di residenza{{ datiAnagraficiForm.get('provinciaResidenza')?.value?.cdProvinciaISTAT ? '*' : '' }}</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="comuneResidenza" placeholder="Seleziona"
                          [compareWith]="compareFn">
                <mat-option [value]="null"></mat-option>
                <mat-option
                  *ngIf="!datiAnagraficiForm.get('provinciaResidenza')?.value"
                  disabled>
                  Seleziona prima una provincia
                </mat-option>
                <ng-container
                  *ngIf="datiAnagraficiForm.get('provinciaResidenza')?.value?.cdProvinciaISTAT === cdProvinciaISTAT_ESTERO">
                  <mat-option *ngFor="let nazione of listNazioniResidenza"
                              [value]="getNazioneValue(nazione)">
                    {{ nazione.dsNazione }}
                  </mat-option>
                </ng-container>
                <ng-container
                  *ngIf="datiAnagraficiForm.get('provinciaResidenza')?.value?.cdProvinciaISTAT !== cdProvinciaISTAT_ESTERO">
                  <mat-option *ngFor="let comune of listComuniResidenzaFiltered"
                              [value]="getComuneValue(comune)">
                    {{ comune.dsComune }}
                  </mat-option>
                </ng-container>
              </mat-select>
              <mat-error
                *ngIf="datiAnagraficiForm.get('comuneResidenza')?.invalid">
                Campo obbligatorio
              </mat-error>
            </mat-form-field>
          </div>
          <div class="col-4 margin-custom-bottom">
            <mat-label class="text-overflow">Indirizzo di residenza</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input
                maxlength="150"
                matInput formControlName="indirizzoResidenza" placeholder="Inserisci"/>
            </mat-form-field>
          </div>
          <div class="col-1 margin-custom-bottom">
            <mat-label class="text-overflow">N. civico</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input
                maxlength="11"
                matInput formControlName="numeroCivico" placeholder="Inserisci"/>
            </mat-form-field>
          </div>
        </div>

        <div class="row mt-1 mb-4">
          <div class="col-12">
            <div class="d-flex align-items-center">
              <mat-label class="text-overflow mr-3">L'indirizzo di domicilio coincide con l'indirizzo di
                residenza
              </mat-label>
              <mat-checkbox formControlName="domicilioEqResidenza" color="primary"></mat-checkbox>
            </div>
          </div>
        </div>

        <div class="row mt-2" *ngIf="!datiAnagraficiForm.get('domicilioEqResidenza')?.value">
          <div class="col-3 margin-custom-bottom">
            <mat-label class="text-overflow">Provincia di domicilio</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="provinciaDomicilio" placeholder="Seleziona" [compareWith]="compareFn">
                <mat-option [value]="null"></mat-option>
                <mat-option
                  *ngFor="let provincia of (!provinceDomicilio?.length ? (provDomicilio$ | async) : provinceDomicilio)"
                  [value]="getProvinciaValue(provincia)">
                  {{ provincia.dsProvincia }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-4 margin-custom-bottom">
            <mat-label class="text-overflow">Comune/stato estero di domicilio</mat-label>
            <mat-form-field class="w-100 mt-2">
              <mat-select formControlName="comuneDomicilio" placeholder="Seleziona"
                          [compareWith]="compareFn">
                <mat-option *ngIf="!datiAnagraficiForm.get('provinciaDomicilio')?.value" disabled>
                  Seleziona prima una provincia
                </mat-option>
                <ng-container
                  *ngIf="datiAnagraficiForm.get('provinciaDomicilio')?.value?.cdProvinciaISTAT === cdProvinciaISTAT_ESTERO">
                  <mat-option [value]="null"></mat-option>
                  <mat-option *ngFor="let nazione of listNazioniDomicilio"
                              [value]="getNazioneValue(nazione)">
                    {{ nazione.dsNazione }}
                  </mat-option>
                </ng-container>
                <ng-container
                  *ngIf="datiAnagraficiForm.get('provinciaDomicilio')?.value?.cdProvinciaISTAT !== cdProvinciaISTAT_ESTERO">
                  <mat-option [value]="null"></mat-option>
                  <mat-option *ngFor="let comune of listComuniDomicilioFiltered"
                              [value]="getComuneValue(comune)">
                    {{ comune.dsComune }}
                  </mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-4 margin-custom-bottom">
            <mat-label class="text-overflow">Indirizzo di domicilio</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input
                maxlength="150"
                matInput formControlName="indirizzoDomicilio" placeholder="Inserisci"/>
            </mat-form-field>
          </div>
          <div class="col-1 margin-custom-bottom">
            <mat-label class="text-overflow">N. civico</mat-label>
            <mat-form-field class="w-100 mt-2">
              <input
                maxlength="11"
                matInput formControlName="numeroCivicoDomicilio" placeholder="Inserisci"/>
            </mat-form-field>
          </div>
        </div>
      </div>
    </form>
  </mat-expansion-panel>
</mat-accordion>
