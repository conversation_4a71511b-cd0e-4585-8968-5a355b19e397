.margin-custom-bottom {
    margin-bottom: 40px !important;
}

.mr-3 {
    margin-right: 2rem;
}

.header-container {
    display: flex;
    align-items: center;
    width: 100%;
}

.identifica-cittadino {
    flex: 0 0 auto;
    margin-left: 1rem;
}

.identifica-button {
    color: #297A38 !important;
    display: flex;
    align-items: center;
    background: transparent !important;
    padding: 0 8px;
    line-height: normal;

    &:hover {
        background: transparent !important;
    }
    
    &[disabled] {
        opacity: 1 !important;
        
        .search-icon, .identifica-text {
            color: #9E9E9E !important; 
        }
        
        .identifica-text {
            text-decoration: underline;
        }
    }

    .mat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.button-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
}

.search-icon {
    color: #297A38;
    margin-right: 8px;
    font-size: 20px !important;
    height: 20px !important;
    width: 20px !important;
    display: flex;
    align-items: center;
}

.identifica-text {
    color: #297A38;
    font-weight: bold;
    text-decoration: underline;
    display: inline-flex;
    align-items: center;
    line-height: normal;
}

.cancel-icon {
    color: #D32F2F;
    margin-right: 8px;
    font-size: 20px !important;
    height: 20px !important;
    width: 20px !important;
    display: flex;
    align-items: center;
}

.annulla-text {
    color: #D32F2F;
    font-weight: bold;
    text-decoration: underline;
    display: inline-flex;
    align-items: center;
    line-height: normal;
}