import {CommonModule} from '@angular/common';
import {Component, Input, OnChanges, OnDestroy, OnInit, signal, SimpleChanges} from '@angular/core';
import {FormBuilder, FormGroup, ReactiveFormsModule, ValidatorFn, Validators} from "@angular/forms";
import {MatDatepickerModule} from "@angular/material/datepicker";
import {MatIconRegistry} from "@angular/material/icon";
import {DomSanitizer} from "@angular/platform-browser";
import {catchError, forkJoin, of, Subscription, tap, throwError} from "rxjs";
import {MaterialModule} from "../../../../../core/material.module";
import {DecoderService} from "../../../../../shared/services/decoder.service";
import {CodiceIdentificativoValidators} from "../../../../../shared/validators/codice-identificativo.validator";
import {ERROR_MESSAGE, TipoCodiceIdentEnum} from "../../../../../shared/enums/enum";
import {
  AggiungiSchedaRicoveroRequest,
  ComuneModel,
  DizionarioModel,
  NazioneModel,
  PazienteModel,
  ProvinciaModel,
  SISSModel
} from "../../../../../shared/interfaces/scheda-ricovero.interface";
import moment, {Moment} from "moment";
import {dataNascitaValidator} from "../../../../../shared/validators/data-nascita.validator";
import {cdProvinciaISTAT_ESTERO, STATO_ESTERO, STATO_ITALIANO} from "../../../../../shared/utils/const";
import {SchedaRicoveroService} from "../../../../services/scheda-ricovero.service";
import {
  compareByCdNazioneIstat,
  compareByIdDizionario,
  mapSISStoDatiAnagrafici
} from "../../../../../shared/utils/utils";
import {CapitalizePipe} from "../../../../../shared/pipes/capitalize.pipe";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
  selector: 'app-dati-anagrafici',
  standalone: true,
  imports: [
    MaterialModule,
    CommonModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    CapitalizePipe
  ],
  templateUrl: './dati-anagrafici.component.html',
  styleUrl: './dati-anagrafici.component.scss'
})
export class DatiAnagraficiComponent implements OnInit, OnDestroy, OnChanges {
  @Input() updatePatientData: PazienteModel

  protected readonly cdProvinciaISTAT_ESTERO = cdProvinciaISTAT_ESTERO;
  protected readonly STATO_ITALIANO = STATO_ITALIANO;
  protected readonly moment = moment;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;

  private subscriptions: Subscription[] = [];
  private provSubscriptions: Subscription[] = [];
  private toggleDomicilioEqResidenza$: Subscription;
  private province$: Subscription;
  private tipCodice$: Subscription;
  private listNazioniWithIT$: Subscription;
  private codFiscUppercase$: Subscription;
  private datiAnagraficiForm$: Subscription;
  private provDom$: Subscription;
  provinceDiResidenza$: Subscription

  protected readonly compareByIdDizionario = compareByIdDizionario;
  protected readonly compareByCdNazioneIstat = compareByCdNazioneIstat;

  // @ts-ignore
  isUpdate = this.schedaRicService.idSchedaSelectedSubject.getValue() !== null && this.schedaRicService.idSchedaSelectedSubject.getValue() >= 0;
  tipologiaCodiceIdentificativo$ = this.decoderService.getDcodTipoCodIdentificativo();
  datiAnagraficiForm: FormGroup;
  isCittadinoIdentificato = this.schedaRicService.isCittadinoIdentificatoSubject.getValue()
  maxDate: Date;
  province: ProvinciaModel[];
  provinceDiResidenza: ProvinciaModel[];
  provinceDomicilio: ProvinciaModel[];
  listNazioni: NazioneModel[] = [];
  listComuniNascitaFiltered: ComuneModel[] = [];
  listNazioniWithIT: NazioneModel[] = []
  listComuniResidenzaFiltered: ComuneModel[];
  listComuniDomicilioFiltered: ComuneModel[];
  private generiSessuali: DizionarioModel[];
  etaSignal = signal<string | null>(null);
  today = moment(new Date()).format('YYYY-MM-DD');
  listNazioniResidenza: NazioneModel[];
  listNazioniDomicilio: NazioneModel[];
  provDomicilio$ = this.decoderService.getDcodProvince(this.today, false, true).pipe(
    tap(province => this.provinceDomicilio = province)
  )
  getDcodGenereSessuale$ = this.decoderService.getDcodGenereSessuale().pipe(
    tap(generi => {
      this.generiSessuali = generi
    })
  )

  canInputsBeEnabled: boolean = false;

  constructor(
    iconRegistry: MatIconRegistry,
    sanitizer: DomSanitizer,
    public decoderService: DecoderService,
    private formBuilder: FormBuilder,
    private schedaRicService: SchedaRicoveroService
  ) {
    iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));
    this.maxDate = new Date();
  }


  ngOnInit() {
    // se sono in create non disabilito mai
    this.canInputsBeEnabled = !this.schedaRicService.getSchedaCorrente() ? true : this.schedaRicService.getSchedaCorrente()?.existAtLeastOneEventOnSamePresidio;
    this.initForm();
    this.toggleSubscriptionsNOIdentificaCittadino(true);

    if (!this.isUpdate) this.datiAnagraficiForm.patchValue({
      ...(this.schedaRicService.schedaCreation$.getValue() as AggiungiSchedaRicoveroRequest)?.paziente,
    }, {emitEvent: true})

    this.listNazioniWithIT$ = this.decoderService.getDcodNazioni_ITALIA(this.today).pipe(
      tap(naz => {
        this.listNazioniWithIT = naz
        if (this.isUpdate) this.datiAnagraficiForm.get('nazioneNascita')?.setValue(this.listNazioniWithIT.find(n => n.cdNazioneISTAT === this.updatePatientData?.nazioneNascita?.cdNazioneISTAT), {emitEvent: false});
      })
    ).subscribe() as Subscription

    // se citt è identificato tramite bottone
    if (this.schedaRicService.isCittadinoIdentificatoSubject.getValue()) {
      this.toggleSubscriptionsNOIdentificaCittadino(false);
      this.disableFormAndDomicilio(this.schedaRicService.pazienteSISSIdentificatoSubject.getValue() as SISSModel)
    }

    if (!this.canInputsBeEnabled) {
      this.datiAnagraficiForm.disable();
    }
  }

  identificaCittadino(event: MouseEvent) {
    event.stopPropagation();

    this.schedaRicService.identificaCittadino(this.datiAnagraficiForm.get('codiceIdentificativo')?.value).pipe(
      tap(pazienteSISS => {
        if (pazienteSISS) {
          this.toggleSubscriptionsNOIdentificaCittadino(false);

          this.isCittadinoIdentificato = !this.isCittadinoIdentificato
          this.schedaRicService.isCittadinoIdentificatoSubject.next(true)
          this.schedaRicService.pazienteSISSIdentificatoSubject.next(pazienteSISS)

          const pazienteMapped = mapSISStoDatiAnagrafici(pazienteSISS)
          pazienteMapped.genere = this.generiSessuali?.find(g => g.descrizione[0].toUpperCase() === (pazienteMapped.genere as string)?.toUpperCase())?.idDizionario as number
          const dataNascita = moment(pazienteMapped.dataNascita).format('YYYY-MM-DD');

          const calls$ = {
            province: this.decoderService.getDcodProvince(dataNascita, false, true),
            nazioni: this.decoderService.getDcodNazioni(dataNascita, false),
            comuneNascita: this.decoderService.getDcodComuni(pazienteMapped.comuneNascita?.provincia.cdProvinciaISTAT as string, dataNascita),
            comuneResidenza: this.decoderService.getDcodComuni((pazienteMapped.comuneResidenza as ComuneModel)?.provincia.cdProvinciaISTAT as string, this.today),
            nazioniResidenza: this.decoderService.getDcodNazioni(this.today, false),
            provinceResidenza: this.loadProvince(this.today),
            provinceDomicilio: this.decoderService.getDcodProvince(this.today, false, true),
            statoEsteroDomicilio: this.decoderService.getDcodNazioni(this.today, false)
          };

          forkJoin(calls$).pipe(
            tap(({
                   province,
                   nazioni,
                   comuneNascita,
                   comuneResidenza,
                   nazioniResidenza,
                   provinceResidenza,
                   provinceDomicilio,
                   statoEsteroDomicilio
                 }) => {
              // Popolo le select con i risultati
              this.province = province;

              this.listNazioni = nazioni;
              this.listComuniNascitaFiltered = comuneNascita;
              this.listComuniResidenzaFiltered = comuneResidenza;

              this.listNazioniResidenza = nazioniResidenza;
              this.provinceDiResidenza = provinceResidenza;
              this.provinceDomicilio = provinceDomicilio;
              this.listNazioniDomicilio = statoEsteroDomicilio

              const nazioneNascita = pazienteMapped.provinciaResidenza.cdProvinciaISTAT === STATO_ESTERO.cdProvinciaISTAT ? pazienteMapped.comuneResidenza : STATO_ITALIANO;

              const eta = this.calcolaEtaLabel(moment(pazienteMapped.dataNascita));
              this.etaSignal.set(eta);

              // Popolo il form con i dati del paziente
              this.datiAnagraficiForm.patchValue({
                ...pazienteMapped,
                nazioneNascita
              }, {emitEvent: false});

              this.disableFormAndDomicilio(pazienteSISS)
            }),
            catchError(error => {
              console.error('Errore nel caricamento dei dati:', error);
              return throwError(() => error);
            })
          ).subscribe();
        } else {
          this.schedaRicService.pazienteSISSIdentificatoSubject.next(null)
        }
      }),

      catchError(() => {
        this.schedaRicService.pazienteSISSIdentificatoSubject.next(null)
        this.toggleSubscriptionsNOIdentificaCittadino(true);
        return of(null);
      })
    )
      .subscribe()
  }

  private disableFormAndDomicilio(pazienteSISS: SISSModel): void {
    // Disabilito il form e riabilito i campi necessari
    this.datiAnagraficiForm.disable();
    
    if (this.canInputsBeEnabled) {
      this.datiAnagraficiForm.get('cittadinanza')?.enable();
    }

    // se il paziente ha un domicilio uguale alla residenza
    if (this.checkIfResidenzaEqualsDomicilio(pazienteSISS)) {
      this.datiAnagraficiForm.get('domicilioEqResidenza')?.setValue(true, {emitEvent: false});
      return;
    }

    const calls$ = {
      comuniDomicilio: (pazienteSISS.domicilio?.comune as ComuneModel)?.provincia?.cdProvinciaISTAT ?
        this.decoderService.getDcodComuni((pazienteSISS.domicilio?.comune as ComuneModel).provincia.cdProvinciaISTAT, this.today) :
        of([])
    };

    forkJoin(calls$).pipe(
      tap(({comuniDomicilio}) => {
        this.listComuniDomicilioFiltered = comuniDomicilio;

        // Ora possiamo impostare tutti i valori in sequenza
        this.abilitaEImpostaValore('provinciaDomicilio', (pazienteSISS.domicilio?.comune as ComuneModel)?.provincia);
        this.abilitaEImpostaValore('comuneDomicilio', pazienteSISS.domicilio?.comune);
        this.abilitaEImpostaValore('indirizzoDomicilio', pazienteSISS.domicilio?.indirizzo);
        this.abilitaEImpostaValore('numeroCivicoDomicilio', pazienteSISS.domicilio?.numeroCivico);
      })
    ).subscribe();
  }

  enableIdentificaCittadino() {
    const codFiscControl = this.datiAnagraficiForm.get('codiceIdentificativo');
    const tipologiaValue = this.datiAnagraficiForm.get('tipoCodiceIdentificativo')?.value as DizionarioModel

    return codFiscControl?.valid && (tipologiaValue?.idDizionario === TipoCodiceIdentEnum.CF
      || tipologiaValue?.idDizionario === TipoCodiceIdentEnum.CFP
      || tipologiaValue?.idDizionario === TipoCodiceIdentEnum.STP);
  }

  calcolaEtaLabel(dataNascita: Moment) {
    const oggi = moment();
    const nascita = moment(dataNascita);

    const anni = oggi.diff(nascita, 'years');
    let etaLabel: string;

    if (anni < 2) {
      etaLabel = `${oggi.diff(nascita, 'months')} mesi`;
    } else {
      etaLabel = `${anni} anni`;
    }

    // Aggiorna anche il valore nel form control
    if (this.datiAnagraficiForm) {
      this.datiAnagraficiForm.get('eta')?.setValue(anni, {emitEvent: false});
    }

    return etaLabel;
  }

  annullaIdentificazione(event: Event) {
    event.stopPropagation();
    event.preventDefault();
    this.isCittadinoIdentificato = false;
    const codiceIdentificativo = this.datiAnagraficiForm.get('codiceIdentificativo')?.value;
    const tipoCodiceIdentificativo = this.datiAnagraficiForm.get('tipoCodiceIdentificativo')?.value;

    this.schedaRicService.isCittadinoIdentificatoSubject.next(false)
    this.datiAnagraficiForm.reset()

    this.initForm()

    this.datiAnagraficiForm.patchValue({
      codiceIdentificativo,
      tipoCodiceIdentificativo
    }, {emitEvent: false});
    this.toggleSubscriptionsNOIdentificaCittadino(true);
  }

  // se null carico le nazioni
  getProvinciaValue(provincia: ProvinciaModel) {
    return provincia
  }

  getComuneValue(comune: ComuneModel): ComuneModel {
    return comune
  }

  getNazioneValue(nazione: NazioneModel): NazioneModel {
    return {
      cdNazioneISTAT: nazione.cdNazioneISTAT,
      dsNazione: nazione.dsNazione,
      cdSigla: nazione.cdSigla,
      nazKeyId: nazione.nazKeyId,
    };
  }

  compareFn = (o1: any, o2: any): boolean => {
    if (!o1 && !o2) return true;
    if (!o1 || !o2) return false;

    if (o1.comKeyId && o2.comKeyId) {
      return o1.comKeyId === o2.comKeyId;
    }

    if (o1.cdProvinciaISTAT && o2.cdProvinciaISTAT) {
      return o1.cdProvinciaISTAT === o2.cdProvinciaISTAT;
    }
    if (o1.cdNazioneISTAT && o2.cdNazioneISTAT) {
      return o1.cdNazioneISTAT === o2.cdNazioneISTAT;
    }

    return o1.cdProvinciaISTAT === o2.cdProvinciaISTAT;
  }

  public onProvinciaNascitaChange(provincia: ProvinciaModel): void {
    this.datiAnagraficiForm.get('comuneNascita')?.reset();
    if (!provincia?.cdProvinciaISTAT) {
      this.datiAnagraficiForm.get('nazioneNascita')?.setValue(null, {emitEvent: false});
      this.datiAnagraficiForm.get('comuneNascita')?.disable({emitEvent: false});
      this.datiAnagraficiForm.get('comuneNascita')?.removeValidators(Validators.required);
      return;
    }

    if (this.isUpdate) this.datiAnagraficiForm.get('comuneNascita')?.setValue(this.updatePatientData.comuneNascita, {emitEvent: false});
    if (this.canInputsBeEnabled) {
      this.datiAnagraficiForm.get('comuneNascita')?.enable({emitEvent: false});
    }

    // Imposta sempre il validatore 'required' indipendentemente dalla provincia
    this.datiAnagraficiForm.get('comuneNascita')?.setValidators([Validators.required]);
    const dataNascita = moment(this.datiAnagraficiForm.get('dataNascita')?.value).format('YYYY-MM-DD');

    if (moment(dataNascita).isValid()) {
      if (provincia.cdProvinciaISTAT === cdProvinciaISTAT_ESTERO) {
        this.decoderService.getDcodNazioni(dataNascita, true).pipe(
          tap(naz => {
            this.listNazioni = naz
            if (this.isUpdate) {
              const comuneNascita = {
                // @ts-ignore
                cdNazioneISTAT: this.updatePatientData.nazioneNascita?.cdNazioneISTAT,
                dsNazione: this.updatePatientData.nazioneNascita?.dsNazione,
                cdSigla: this.updatePatientData.nazioneNascita?.cdSigla,
                nazKeyId: this.updatePatientData.nazioneNascita?.nazKeyId
              }
              this.datiAnagraficiForm.get('comuneNascita')?.setValue(comuneNascita, {emitEvent: true});
            }
          })
        )
          .subscribe();
      } else {
        const listComuniNascitaFiltered$ = this.decoderService.getDcodComuni(provincia.cdProvinciaISTAT,
          dataNascita)
          .pipe(
            tap(comuni => {
              this.listComuniNascitaFiltered = comuni;
            })
          )
          .subscribe();

        this.subscriptions.push(listComuniNascitaFiltered$)
      }
    }
  }

  public onProvinciaResidenzaChange(provincia: ProvinciaModel) {
    if (!provincia) {
      this.datiAnagraficiForm.get('comuneResidenza')?.setValue(null, {emitEvent: false});
      this.datiAnagraficiForm.get('comuneResidenza')?.disable({emitEvent: false});
      return;
    }

    if (provincia.cdProvinciaISTAT === cdProvinciaISTAT_ESTERO) {
      this.decoderService.getDcodNazioni(this.today, true).pipe(
        tap(naz => this.listNazioniResidenza = naz)
      )
        .subscribe();
    } else {
      const listComuniResidenzaFiltered$ = this.decoderService.getDcodComuni(provincia.cdProvinciaISTAT,
        this.today)
        .pipe(
          tap(comuni => {
            this.listComuniResidenzaFiltered = comuni;
          })
        )
        .subscribe();

      this.subscriptions.push(listComuniResidenzaFiltered$)
    }

    if (this.canInputsBeEnabled) {
      this.datiAnagraficiForm.get('comuneResidenza')?.enable({emitEvent: false});
    }

    this.datiAnagraficiForm.get('comuneResidenza')?.setValue(null, {emitEvent: false});


    // Aggiorna anche domicilio se coincide con residenza
    if (this.datiAnagraficiForm.get('domicilioEqResidenza')?.value) {
      this.datiAnagraficiForm.get('provinciaDomicilio')?.setValue(provincia, {emitEvent: false});
    }
  }

  public onProvinciaDomicilioChange(provincia: any): void {
    if (this.datiAnagraficiForm.get('domicilioEqResidenza')?.value || !provincia) {
      this.datiAnagraficiForm.get('comuneDomicilio')?.setValue(null, {emitEvent: false});
      this.datiAnagraficiForm.get('comuneDomicilio')?.disable({emitEvent: false});
      return;
    }


    if (provincia.cdProvinciaISTAT === cdProvinciaISTAT_ESTERO) {
      this.decoderService.getDcodNazioni(this.today, true).pipe(
        tap(naz => this.listNazioniDomicilio = naz)
      )
        .subscribe();
    } else {
      const listComuniDomicilioFiltered$ = this.decoderService.getDcodComuni(provincia.cdProvinciaISTAT,
        this.today)
        .pipe(
          tap(comuni => {
            this.listComuniDomicilioFiltered = comuni;
          })
        )
        .subscribe();

      this.subscriptions.push(listComuniDomicilioFiltered$)
    }

    if (this.canInputsBeEnabled) {
      this.datiAnagraficiForm.get('comuneDomicilio')?.enable({emitEvent: false});
    }

    this.datiAnagraficiForm.get('comuneDomicilio')?.setValue(null, {emitEvent: false});
  }


  getTipologiaPlaceholder(): string {
    const tipologia = this.datiAnagraficiForm.get('tipoCodiceIdentificativo')?.value as DizionarioModel;

    switch (tipologia?.idDizionario) {
      case TipoCodiceIdentEnum.CF:
        return 'Inserisci codice fiscale (16 caratteri)';
      case TipoCodiceIdentEnum.CFP:
        return 'Inserisci codice fiscale provvisorio (11 cifre)';
      case TipoCodiceIdentEnum.STP:
        return 'Inserisci codice STP (STP + 13 cifre)';
      case TipoCodiceIdentEnum.TEAM:
        return 'Inserisci codice TEAM (20 caratteri)';
      case TipoCodiceIdentEnum.ENI:
        return 'Inserisci codice ENI (ENI + 13 cifre)';

      default:
        return 'Inserisci';
    }
  }


  getErrorDataNascita() {
    const control = this.datiAnagraficiForm.get('dataNascita');
    if (control?.hasError('invalidFormat')) return 'Formato data non valido (DD/MM/YYYY)';
    if (control?.hasError('futureDate')) return 'La data di nascita non può essere futura';
    if (control?.hasError('required')) return 'Campo obbligatorio';
    return 'Campo non valido';
  }

  getErrorNomeCognome(campo: string): string | null {
    const control = this.datiAnagraficiForm.get(campo);

    if (control?.hasError('required')) {
      return 'Campo obbligatorio';
    }
    if (control?.hasError('minlength')) {
      return 'Inserire almeno 2 caratteri';
    }
    if (control?.hasError('maxlength')) {
      return 'Lunghezza massima 50 caratteri';
    }
    return null;
  }

  computeMaxLengthCodice(): number {
    let tipologia = this.datiAnagraficiForm.get('tipoCodiceIdentificativo')?.value as DizionarioModel

    switch (tipologia?.idDizionario) {
      case TipoCodiceIdentEnum.CF:
        return 16;
      case TipoCodiceIdentEnum.CFP:
        return 11;
      case TipoCodiceIdentEnum.STP:
        return 16;
      case TipoCodiceIdentEnum.TEAM:
        return 20;
      case TipoCodiceIdentEnum.ENI:
        return 16;
    }
    return 16;
  }

  private loadProvince(date: string, empty = false, statoEstero = false) {
    // Cancella eventuale subscription precedente
    if (this.province$) {
      this.province$.unsubscribe();
    }

    return this.decoderService.getDcodProvince(date, empty, statoEstero)
  }

  private initForm(): void {
    // Prima creiamo il form con i controlli base
    this.datiAnagraficiForm = this.formBuilder.group({
      tipoCodiceIdentificativo: ['', [Validators.required]],
      codiceIdentificativo: ['', [Validators.required]],
      dataRilascioTeam: [{value: null, disabled: true}],
      nome: ['', [Validators.required]],
      cognome: ['', [Validators.required]],
      dataNascita: ['', [
        dataNascitaValidator(),
        Validators.required,
      ]],
      provinciaNascita: [{value: null, disabled: true}],
      comuneNascita: [{value: null, disabled: true}],
      cittadinanza: ['', Validators.required],
      genere: [undefined, Validators.required],
      provinciaResidenza: [null, Validators.required],
      comuneResidenza: [{value: null, disabled: true}, Validators.required],
      indirizzoResidenza: [''],
      numeroCivico: [''],
      domicilioEqResidenza: [false],
      provinciaDomicilio: null,
      comuneDomicilio: [{value: null, disabled: true}],
      indirizzoDomicilio: [''],
      numeroCivicoDomicilio: [''],
      nazioneNascita: null
    });

    // attivo value changes sui fields
    this.datiAnagraficiForm$ = this.datiAnagraficiForm.valueChanges.subscribe(data => {
      // salvo la validità del form per controllo save button
      this.schedaRicService._areFormsCreateSchedaValidSubject$.next({
        ...this.schedaRicService._areFormsCreateSchedaValidSubject$.getValue(),
        formDatiAnagrafici: {
          valid: this.datiAnagraficiForm.valid,
          pristine: this.datiAnagraficiForm.pristine
        }
      });

      this.schedaRicService.schedaCreation$.next({
        ...this.schedaRicService.schedaCreation$.getValue(),
        paziente: {
          ...this.schedaRicService.schedaCreation$.getValue()?.paziente,
          ...this.datiAnagraficiForm.getRawValue(),
        }
      } as AggiungiSchedaRicoveroRequest);
    }) as Subscription
    // controllo se c'è almeno un valore popolato nella scheda per controllare validità dei campi
    if (!!Object.values(this.schedaRicService.schedaCreation$.getValue()?.paziente || {}).filter(val => val).length) {
      this.datiAnagraficiForm.markAllAsTouched()
    }

    this.toggleDomicilioEqResidenza$ = this.datiAnagraficiForm.get('domicilioEqResidenza')?.valueChanges
      .pipe(
        tap(val => this.toggleDomicilioEqResidenza(val))
      )
      .subscribe() as Subscription

    this.provinceDiResidenza$ = this.loadProvince(this.today, false, true).subscribe(
      province => {
        this.provinceDiResidenza = province;
      }
    )

    this.tipCodice$ = this.datiAnagraficiForm.get('tipoCodiceIdentificativo')?.valueChanges
      .pipe(
        tap((val: DizionarioModel) => {
          if ((val?.idDizionario === TipoCodiceIdentEnum.STP || val?.idDizionario === TipoCodiceIdentEnum.TEAM) && this.canInputsBeEnabled) {
            this.datiAnagraficiForm.get('dataRilascioTeam')?.enable({emitEvent: false});
          } else {
            this.datiAnagraficiForm.get('dataRilascioTeam')?.setValue(null, {emitEvent: false});
            this.datiAnagraficiForm.get('dataRilascioTeam')?.disable({emitEvent: false});
          }

          this.aggiornaValidatoreCodiceFiscale(val?.idDizionario);
        })
      )
      .subscribe() as Subscription;

    this.provDom$ = this.datiAnagraficiForm.get('provinciaDomicilio')?.valueChanges.subscribe(provincia => {
      this.onProvinciaDomicilioChange(provincia);
    }) as Subscription

    this.codFiscUppercase$ = this.datiAnagraficiForm.get('codiceIdentificativo')?.valueChanges.subscribe(val => {
      const upperCaseValue = val?.toUpperCase();
      if (val !== upperCaseValue) {
        this.datiAnagraficiForm.get('codiceIdentificativo')?.setValue(upperCaseValue, {emitEvent: false});
      }
    }) as Subscription
  }

  // gestisce la chckbx del domicilioeqresidena
  toggleDomicilioEqResidenza(isChecked: boolean) {
    if (isChecked) {
      this.datiAnagraficiForm.get('provinciaDomicilio')?.setValue(this.datiAnagraficiForm.get('provinciaResidenza')?.value);
      this.datiAnagraficiForm.get('comuneDomicilio')?.setValue(this.datiAnagraficiForm.get('comuneResidenza')?.value);
      this.datiAnagraficiForm.get('indirizzoDomicilio')?.setValue(this.datiAnagraficiForm.get('indirizzoResidenza')?.value);
      this.datiAnagraficiForm.get('numeroCivicoDomicilio')?.setValue(this.datiAnagraficiForm.get('numeroCivico')?.value);
    } else {
      this.datiAnagraficiForm.get('provinciaDomicilio')?.reset();
      this.datiAnagraficiForm.get('comuneDomicilio')?.reset();
      this.datiAnagraficiForm.get('indirizzoDomicilio')?.reset();
      this.datiAnagraficiForm.get('numeroCivicoDomicilio')?.reset();
    }
  }

  // attivo/disattivo listener se è premuto identifica citt
  toggleSubscriptionsNOIdentificaCittadino(shouldSubscribe: boolean) {
    // Prima facciamo unsubscribe di tutte le subscription esistenti
    this.provSubscriptions.forEach(sub => sub?.unsubscribe());
    this.provSubscriptions = [];


    if (shouldSubscribe) {
      // Creiamo nuove subscription solo se shouldSubscribe è true
      this.provSubscriptions = [
        // Gestione dataNascita e provinciaNascita
        this.datiAnagraficiForm.get('dataNascita')?.valueChanges
          .pipe(
            tap((dataNascita: Moment | null) => {
              const provinciaNascitaControl = this.datiAnagraficiForm.get('provinciaNascita');
              const comuneNascitaControl = this.datiAnagraficiForm.get('comuneNascita');

              if (!this.datiAnagraficiForm.get('dataNascita')?.errors && dataNascita && moment(dataNascita, 'DD/MM/YYYY', true).isValid()) {
                if (this.canInputsBeEnabled) {
                  provinciaNascitaControl?.enable({emitEvent: false});
                }
                
                this.province$ = this.loadProvince(moment(dataNascita).format("YYYY-MM-DD"), false, true).subscribe(
                  province => {
                    this.province = province;
                  }
                )

                // Calcola età
                const eta = this.calcolaEtaLabel(dataNascita);
                this.etaSignal.set(eta);
              } else {
                // Disabilita e resetta il campo provincia e comune di nascita
                provinciaNascitaControl?.disable({emitEvent: false});
                provinciaNascitaControl?.setValue(null, {emitEvent: false});
                comuneNascitaControl?.disable({emitEvent: false});
                comuneNascitaControl?.setValue(null, {emitEvent: false});
                this.province = [];

                this.etaSignal.set(null);
              }
            })).subscribe(),
        this.datiAnagraficiForm.get('comuneResidenza')?.valueChanges.subscribe(comune => {
          if (this.datiAnagraficiForm.get('domicilioEqResidenza')?.value) {
            if (this.canInputsBeEnabled) {
              this.datiAnagraficiForm.get('comuneDomicilio')?.enable()
            }
            
            this.datiAnagraficiForm.get('comuneDomicilio')?.setValue(comune, {emitEvent: false});
          }
        }),
        this.datiAnagraficiForm.get('indirizzoResidenza')?.valueChanges.subscribe(comune => {
          if (this.datiAnagraficiForm.get('domicilioEqResidenza')?.value) {
            this.datiAnagraficiForm.get('indirizzoDomicilio')?.setValue(comune, {emitEvent: false});
          }
        }),
        this.datiAnagraficiForm.get('numeroCivico')?.valueChanges.subscribe(comune => {
          if (this.datiAnagraficiForm.get('domicilioEqResidenza')?.value) {
            this.datiAnagraficiForm.get('numeroCivicoDomicilio')?.setValue(comune, {emitEvent: false});
          }
        }),
        this.datiAnagraficiForm.get('provinciaNascita')?.valueChanges.subscribe(provincia => {
          this.onProvinciaNascitaChange(provincia);
        }),
        this.datiAnagraficiForm.get('provinciaResidenza')?.valueChanges.subscribe(provincia => {
          this.onProvinciaResidenzaChange(provincia);
        }),

      ].filter(sub => sub !== undefined) as Subscription[];
    }
  }

  // quando recupero da identifica citt devo verificare la totale uguaglianza dei campi di domicilio rispetto residenza
  checkIfResidenzaEqualsDomicilio(pazienteSISS: SISSModel) {
    const provinciaResideSISS = (pazienteSISS.residenza?.comune as ComuneModel)?.provincia
    const comuneResidenzaSISS = pazienteSISS.residenza?.comune
    const indirizzoResidenzaSISS = pazienteSISS.residenza?.indirizzo
    const civicoResidenzaSISS = pazienteSISS.residenza?.numeroCivico

    const comuneDomicilioSISS = pazienteSISS.domicilio?.comune
    const indirizzoDomicilioSISS = pazienteSISS.domicilio?.indirizzo
    const civicoDomicilioSISS = pazienteSISS.domicilio?.numeroCivico
    const provinciaDomicilioSISS = (pazienteSISS.domicilio?.comune as ComuneModel)?.provincia

    return provinciaResideSISS?.cdProvinciaISTAT === provinciaDomicilioSISS?.cdProvinciaISTAT &&
      (comuneResidenzaSISS as ComuneModel)?.comKeyId === (comuneDomicilioSISS as ComuneModel)?.comKeyId &&
      indirizzoResidenzaSISS === indirizzoDomicilioSISS &&
      civicoResidenzaSISS === civicoDomicilioSISS
  }

  setNazioneNascita(nazione: NazioneModel) {
    this.datiAnagraficiForm.get('nazioneNascita')?.setValue(nazione);
  }

  abilitaEImpostaValore(campo: string, valore: any) {
    const controllo = this.datiAnagraficiForm.get(campo);
    
    if (!valore && this.canInputsBeEnabled) {
      controllo?.enable({emitEvent: false});
    } else {
      controllo?.disable({emitEvent: false});
    }
    controllo?.setValue(valore);
  }

// gestisco i campi del form quando recuperati in update
  ngOnChanges(changes: SimpleChanges) {
    if (changes) {
      // mi trovo in edit
      if (this.updatePatientData?.idPaziente) {
        // naz estera
        let comuneNascita = this.updatePatientData.comuneNascita
        let provinciaNascita = this.updatePatientData.comuneNascita?.provincia
        if (!provinciaNascita && this.updatePatientData.nazioneNascita?.cdNazioneISTAT &&
            this.updatePatientData.nazioneNascita?.cdNazioneISTAT !== STATO_ITALIANO.cdNazioneISTAT) {
          provinciaNascita = STATO_ESTERO
          // @ts-ignore
          comuneNascita = this.updatePatientData.nazioneNascita
        }

        let provinciaResidenza = this.updatePatientData.residenza?.comune?.provincia
        if (!provinciaResidenza) {
          if (this.updatePatientData.residenza?.nazione)
            provinciaResidenza = STATO_ESTERO
        }
        let comuneResidenza = this.updatePatientData.residenza?.comune || this.updatePatientData.residenza?.nazione

        let provinciaDomicilio = this.updatePatientData.domicilio?.comune?.provincia
        if (!provinciaDomicilio) {
          if (this.updatePatientData.domicilio?.nazione)
            provinciaDomicilio = STATO_ESTERO
        }
        let comuneDomicilio = this.updatePatientData.domicilio?.comune || this.updatePatientData.domicilio?.nazione

        this.datiAnagraficiForm.patchValue({
          ...this.updatePatientData,
          dataRilascioTeam: moment(this.updatePatientData.dataRilascioTeam).isValid() ? moment(this.updatePatientData.dataRilascioTeam) : null,
          dataNascita: moment(this.updatePatientData.dataNascita),
          provinciaNascita,
          comuneNascita,

          provinciaResidenza,
          comuneResidenza,
          indirizzoResidenza: this.updatePatientData.residenza?.indirizzo,
          numeroCivico: this.updatePatientData.residenza?.numeroCivico,

          nazioneNascita: this.updatePatientData.nazioneNascita,

          provinciaDomicilio: provinciaDomicilio,
          comuneDomicilio: comuneDomicilio,
          indirizzoDomicilio: this.updatePatientData.domicilio?.indirizzo,
          numeroCivicoDomicilio: this.updatePatientData.domicilio?.numeroCivico,
          genere: this.updatePatientData.genere.idDizionario,
          scolarita: this.updatePatientData.scolarita?.idDizionario,
        }, {emitEvent: true});

        this.schedaRicService.schedaCreation$.next({
          paziente: this.datiAnagraficiForm.value
        } as AggiungiSchedaRicoveroRequest);
      }
    }
  }

  private aggiornaValidatoreCodiceFiscale(tipologia: number): void {
    const codiceFiscaleControl = this.datiAnagraficiForm.get('codiceIdentificativo');
    if (!codiceFiscaleControl) return;

    codiceFiscaleControl.clearValidators();
    codiceFiscaleControl.updateValueAndValidity({emitEvent: false});
    let validatore: ValidatorFn[]

    switch (tipologia) {
      case TipoCodiceIdentEnum.CF:
        validatore = [Validators.required, CodiceIdentificativoValidators.codiceFiscale()];
        break;
      case TipoCodiceIdentEnum.CFP:
        validatore = [Validators.required, CodiceIdentificativoValidators.codiceFiscaleProvvisorio()]
        break;
      case TipoCodiceIdentEnum.STP:
        validatore = [Validators.required, CodiceIdentificativoValidators.codiceSTP()]
        break;
      case TipoCodiceIdentEnum.TEAM:
        validatore = [Validators.required, CodiceIdentificativoValidators.codiceTEAM()]
        break;
      case TipoCodiceIdentEnum.ENI:
        validatore = [Validators.required, CodiceIdentificativoValidators.codiceENI()]
        break;

      default:
        validatore = [Validators.required]
        break;
    }

    codiceFiscaleControl.setValidators(validatore);
    codiceFiscaleControl.updateValueAndValidity({emitEvent: false});
  }

  ngOnDestroy() {
    this.toggleSubscriptionsNOIdentificaCittadino(false);

    this.province$?.unsubscribe()
    this.datiAnagraficiForm$?.unsubscribe()
    this.provDom$?.unsubscribe()
    this.listNazioniWithIT$?.unsubscribe()
    this.toggleDomicilioEqResidenza$?.unsubscribe()
    this.tipCodice$?.unsubscribe()
    this.codFiscUppercase$?.unsubscribe()
    this.subscriptions.forEach(sub => sub?.unsubscribe());
  }
}
