import { CommonModule } from "@angular/common";
import { Component, Input, OnChanges, OnDestroy } from '@angular/core';
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from "@angular/forms";
import { DateAdapter } from "@angular/material/core";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconRegistry } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { DomSanitizer } from "@angular/platform-browser";
import moment, {isMoment} from "moment";
import { catchError, forkJoin, of, Subscription, take, tap } from "rxjs";
import { MaterialModule } from "../../../../core/material.module";
import { OperatoreService } from "../../../../core/services/operatore.service";
import { CronologiaMenuSimpleComponent } from "../../../../shared/components/cronologia-menu-simple/cronologia-menu-simple.component";
import { ERROR_MESSAGE, IdOperatoreEnum, StatoEventoEnum } from "../../../../shared/enums/enum";
import {
  AggiungiSchedaRicoveroRequest,
  CentriRicoveroModel,
  DizionarioModel, SchedaRicoveroModel, UnitaOperativaModel
} from "../../../../shared/interfaces/scheda-ricovero.interface";
import { CapitalizePipe } from "../../../../shared/pipes/capitalize.pipe";
import { DecoderService } from "../../../../shared/services/decoder.service";
import { ModalService } from "../../../../shared/services/modal.service";
import { TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO } from "../../../../shared/utils/const";
import { compareByIdDizionario, decodeFromBase64, disableAllFieldsInForm, } from "../../../../shared/utils/utils";
import { NosologicoValidator } from "../../../../shared/validators/dati-generali.validator";
import { SchedaRicoveroService } from "../../../services/scheda-ricovero.service";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
  selector: 'app-dati-generali',
  standalone: true,
  imports: [MaterialModule, CommonModule, ReactiveFormsModule, MatDatepickerModule, MatInputModule, CapitalizePipe, CronologiaMenuSimpleComponent],
  templateUrl: './dati-generali.component.html',
  styleUrl: './dati-generali.component.scss'
})
export class DatiGeneraliComponent implements OnDestroy, OnChanges {

  protected readonly compareByIdDizionario = compareByIdDizionario;

  datiGeneraliForm: FormGroup;
  idNosologico: FormControl;
  dataRicovero: FormControl;
  unitaOperativa: FormControl;
  repartoRicovero: FormControl;
  lesioneMielica: FormControl;
  lesioneNote: FormControl;
  provenienza: FormControl;

  @Input() datiGenerali: SchedaRicoveroModel
  @Input() eventoRientroCensito?: boolean;
  @Input() eventoRientroNonCensito?: boolean;
  @Input() lesioni: DizionarioModel[] = [];

  unitaOperative: UnitaOperativaModel[] = [];
  savedDataBeforeCronologia = null;

  optionsProvenienza: DizionarioModel[] = [];

  protected readonly moment = moment;
  private datiGeneraliForm$: Subscription;
  protected readonly TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO = TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO;

  repartiRicovero: CentriRicoveroModel[];
  medicoCompilatore: string;
  ente: string;
  presidio: string;

  showLesioneMielica: boolean = true;
  private getCronologia$: Subscription;

  showTornaAllEditor: boolean = false;
  isTornaAllEditorClicked: boolean = false;
  showCronologiaBtnGenerali: boolean = true;
  eventoRientroCensitoONon: boolean = false;
  eventoRiabilitativo: boolean = false;

  lesioneMielicaDefaultValue?: DizionarioModel | null;

  dataDimissioneTrasferimentoPrecedente: Date | null = null;

  // Custom validator: data ammissione non puo' essere inferiore a dt dimissione evento precedente
  static dataValidator(
    dataDimissioneTrasferimentoPrecedente: Date | null
  ): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const selected = new Date(control.value);

      if (dataDimissioneTrasferimentoPrecedente && selected <= dataDimissioneTrasferimentoPrecedente) {
        return { dateBeforeDischarge: true };
      }

      return null;
    };
  }

  constructor(public operatoreService: OperatoreService, iconRegistry: MatIconRegistry, sanitizer: DomSanitizer,
    private modalService: ModalService,
    public schedaRicService: SchedaRicoveroService,
    private decoderService: DecoderService,
    private dateAdapter: DateAdapter<any>) {

    const observables: { [key: string]: any } = {
      unitaOperative: this.decoderService.getDcodUnitaOperative().pipe(
        catchError(error => {
          console.error('Errore durante il recupero delle unità operative:', error);
          return of([]);
        })
      )
    };

    const cdLivello2 = this.operatoreService.getOperatore().codLivello2Operatore;

    if (cdLivello2) {
      observables['repartoRicovero'] = this.decoderService.getDcodRepartoRicovero(cdLivello2).pipe(
        catchError(error => {
          console.error('Errore durante il recupero dei reparti:', error);
          return of([]);
        })
      );
    }

    forkJoin(observables).pipe(
      takeUntilDestroyed(),
      tap(result => {
        this.unitaOperative = result['unitaOperative'] as UnitaOperativaModel[];
        this.repartiRicovero = result['repartoRicovero'] as CentriRicoveroModel[];

        this.lesioneMielicaDefaultValue = this.eventoRientroCensitoONon ? this.lesioni?.find(lesione => lesione.idDizionario === 33) : null;
        if (this.lesioneMielicaDefaultValue) this.datiGeneraliForm.get('lesioneMielica')?.setValue(this.lesioneMielicaDefaultValue.idDizionario);
      })
    ).subscribe();


    iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

    this.dateAdapter.setLocale('it-IT');

    this.idNosologico = new FormControl(null, [
      NosologicoValidator.format()
    ]);
    this.dataRicovero = new FormControl('', [
      Validators.required,
      DatiGeneraliComponent.dataValidator(this.dataDimissioneTrasferimentoPrecedente)
    ]);
    this.unitaOperativa = new FormControl(null, Validators.required);
    this.repartoRicovero = new FormControl(undefined);
    this.lesioneMielica = new FormControl('', Validators.required);
    this.lesioneNote = new FormControl('');

    this.datiGeneraliForm = new FormGroup({
      'idNosologico': this.idNosologico,
      'dataRicovero': this.dataRicovero,
      'unitaOperativa': this.unitaOperativa,
      'repartoRicovero': this.repartoRicovero,
      'lesioneMielica': this.lesioneMielica,
      'lesioneNote': this.lesioneNote,
      'provenienza': new FormControl(null)
    });

    this.datiGeneraliForm.valueChanges
      .pipe(
        takeUntilDestroyed(),
        tap(() => {
          const data = this.datiGeneraliForm.getRawValue();

          schedaRicService._areFormsCreateSchedaValidSubject$.next({
            ...schedaRicService._areFormsCreateSchedaValidSubject$.getValue(),
            formDatiGenerali: {
              valid: this.datiGeneraliForm.valid,
              pristine: this.datiGeneraliForm.pristine
            }
          });

          schedaRicService.schedaCreation$.next({
            ...schedaRicService.schedaCreation$.getValue(),
            scheda: {
              ...schedaRicService.schedaCreation$.getValue()?.scheda,
              ...data,
              lesioneMielica: this.lesioni?.find(l => l.idDizionario === (data.lesioneMielica ? data.lesioneMielica : this.datiGenerali?.scheda?.lesioneMielica?.idDizionario)) || null,
              // repartoRicovero: this.repartiRicovero?.find(r => r.idCentro === (data.repartoRicovero ? data.repartoRicovero.idCentro : this.datiGenerali?.scheda?.repartoRicovero?.idCentro)) || null
            }
          } as AggiungiSchedaRicoveroRequest);
        })
      ).subscribe()

    // se sono in creazione carico i dati dal subject in modo da non perderli quando cambio menu della sx
    this.datiGeneraliForm.patchValue({
      ...(schedaRicService.schedaCreation$?.getValue() as AggiungiSchedaRicoveroRequest)?.scheda,
      lesioneMielica: schedaRicService.schedaCreation$.getValue()?.scheda?.lesioneMielica?.idDizionario || this.lesioneMielicaDefaultValue,
    }, { emitEvent: false })

    if (!schedaRicService.idSchedaSelectedSubject.getValue()) {
      // modo per capire quando torno da dati anagrafici a dat generale e fare check campi
      if (Object.keys(schedaRicService.schedaCreation$.getValue()?.paziente || {}).length) {
        this.datiGeneraliForm.markAllAsTouched()
      }
    }

    this.medicoCompilatore = `${this.operatoreService.getOperatore().nomeOperatore} ${this.operatoreService.getOperatore().cognomeOperatore}`
    this.ente = this.operatoreService.getOperatore().descLivello1Operatore || '';
    this.presidio = this.operatoreService.getOperatore().descLivello2Operatore || ''
  }

  isReadOnly(value: SchedaRicoveroModel, field?: string): boolean {
    const corrispondenzaPresidi = value?.codPresidioOspedaliero !== null &&
                                  value?.codPresidioOspedaliero !== this.operatoreService.getOperatore().codLivello2Operatore;
    const isEventoChiuso = value?.evento.stato?.descrizione.toUpperCase() === 'CHIUSO';
    const isNotOspedaliero = this.operatoreService.getOperatore().idRuolo !== IdOperatoreEnum.OSP;
                                  
    return corrispondenzaPresidi || isEventoChiuso || isNotOspedaliero;
  }

  disableFormControls() {
    Object.keys(this.datiGeneraliForm.controls).forEach(controlName => {
      if (!controlName.toLowerCase().includes('nota') || !controlName.toLowerCase().includes('note')) {
        this.datiGeneraliForm.get(controlName)?.disable({ emitEvent: false });
      }
    });
  }

  ngOnChanges() {
    if (this.schedaRicService.getSchedaCorrente()) {
      this.schedaRicService.getSchedaCorrente$()
          .pipe(take(1))
          .subscribe(scheda => {
            const dataDimissionePrecedente = scheda?.dataDimissioneTrasferimentoPrecedente;
            this.dataDimissioneTrasferimentoPrecedente = dataDimissionePrecedente ? new Date(dataDimissionePrecedente) : null;
          });
    }
    if (this.eventoRientroCensito || this.eventoRientroNonCensito) {
      this.provenienza = new FormControl(null);
      this.datiGeneraliForm.addControl('provenienza', this.provenienza);

      if (this.schedaRicService.isNewEvent.getValue() && !this.eventoRientroCensitoONon) {
        this.lesioneMielica.disable();
        this.unitaOperativa.disable();
      }

      this.initRientro();
    } else {
      this.datiGeneraliForm.removeControl('provenienza');
    }

    if (this.datiGenerali) { //update

      this.eventoRiabilitativo = this.datiGenerali.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento;

      this.showLesioneMielica = !this.eventoRiabilitativo && !this.eventoRientroCensitoONon;
      const dataRicovero = moment(this.datiGenerali.scheda?.dataRicovero).isValid() ? moment(this.datiGenerali.scheda?.dataRicovero) : null
      const repartoRicovero = this.datiGenerali.scheda?.repartoRicovero
      const lesioneMielica = this.datiGenerali.scheda?.lesioneMielica?.idDizionario
      const unitaOperativa = this.datiGenerali.scheda?.unitaOperativa
      const lesioneNote = this.datiGenerali.scheda?.lesioneNote
      const provenienza = this.datiGenerali.scheda?.provenienza || this.schedaRicService.schedaCreation$.getValue()?.scheda?.provenienza;
      const idNosologico = this.datiGenerali.scheda?.idNosologico;
      const scheda = this.schedaRicService.schedaCreation$.getValue()?.scheda;

      if (this.isReadOnly(this.datiGenerali, 'repartoRicovero') && repartoRicovero) {
        this.repartiRicovero?.push(repartoRicovero);
      }

      // condizione necessaria perchè eventoRientroCensitoONon ha la scheda valorizzata anche se è vuota 
      if (!scheda?.id && (!scheda || !!(this.datiGenerali.scheda?.id && this.eventoRientroCensitoONon))) {
        if (!this.eventoRientroCensitoONon) {
          this.datiGeneraliForm.get('lesioneMielica')?.disable({ emitEvent: false });
          this.datiGeneraliForm.get('unitaOperativa')?.disable({ emitEvent: false });
        }
        const baseScheda = {
          ...this.datiGenerali.scheda,
          lesioneMielica: this.lesioni?.find(l => l.idDizionario === this.datiGenerali.scheda.lesioneMielica?.idDizionario) || this.lesioneMielicaDefaultValue,
          provenienza: this.eventoRientroCensitoONon ? provenienza : {},
        };

        this.schedaRicService.schedaCreation$.next({
          ...this.schedaRicService.schedaCreation$.getValue(),
          scheda: baseScheda,
        } as AggiungiSchedaRicoveroRequest);

        this.datiGeneraliForm.patchValue({
          ...this.datiGenerali.scheda,
          dataRicovero,
          lesioneMielica,
          repartoRicovero,
          unitaOperativa,
          lesioneNote,
          provenienza: this.eventoRientroCensitoONon ? provenienza : {},
        });
      } else if (!this.datiGenerali.scheda?.id && this.eventoRientroCensitoONon) {
        this.datiGeneraliForm.patchValue({
          ...scheda
        });
      } else if (!this.datiGenerali.scheda?.id && this.eventoRiabilitativo) {
        if (this.datiGenerali.scheda?.id !== this.schedaRicService.schedaCreation$.getValue()?.scheda?.id) {
          this.schedaRicService.schedaCreation$.next({
            ...this.schedaRicService.schedaCreation$.getValue(),
            scheda: {
              ...scheda,
              lesioneMielica: this.lesioni?.find(l => l.idDizionario === this.datiGenerali.scheda?.lesioneMielica.idDizionario) || this.lesioneMielicaDefaultValue
            }
          } as AggiungiSchedaRicoveroRequest);
        }

        this.datiGeneraliForm.patchValue({
          idNosologico,
          lesioneMielica,
          unitaOperativa,
          dataRicovero,
          repartoRicovero,
          provenienza: this.eventoRientroCensitoONon ? provenienza : {},
        });

        if (lesioneMielica) this.datiGeneraliForm.get('lesioneMielica')?.disable({ emitEvent: false });
        if (unitaOperativa) this.datiGeneraliForm.get('unitaOperativa')?.disable({ emitEvent: false });
      }

      if (!this.isReadOnly(this.datiGenerali)) {
        this.medicoCompilatore = `${this.operatoreService.getOperatore().nomeOperatore} ${this.operatoreService.getOperatore().cognomeOperatore}`;
        this.ente = this.operatoreService.getOperatore().descLivello1Operatore || '';
        this.presidio = this.operatoreService.getOperatore().descLivello2Operatore || '';
      } else {
        this.medicoCompilatore = this.datiGenerali.nomeCognomeMedico;
        this.ente = this.datiGenerali.ente;
        this.presidio = this.datiGenerali.presidioOspedaliero;
      }

      if (scheda?.id) {
        this.datiGeneraliForm.patchValue({
          ...this.datiGenerali.scheda,
          dataRicovero: scheda?.dataRicovero,
          idNosologico: scheda?.idNosologico,
          repartoRicovero: scheda?.repartoRicovero ,
          lesioneNote: scheda?.lesioneNote,
          lesioneMielica: scheda?.lesioneMielica.idDizionario,
          unitaOperativa: scheda?.unitaOperativa ,
          provenienza: this.eventoRientroCensitoONon ? scheda.provenienza : {},
        });
      } else {
        this.datiGeneraliForm.patchValue({
          ...this.datiGenerali.scheda,
          dataRicovero: scheda?.dataRicovero || dataRicovero,
          idNosologico: scheda?.idNosologico || idNosologico,
          repartoRicovero: scheda?.repartoRicovero || repartoRicovero,
          lesioneNote: scheda?.lesioneNote || lesioneNote,
          lesioneMielica: scheda?.lesioneMielica.idDizionario || lesioneMielica,
          unitaOperativa: scheda?.unitaOperativa || unitaOperativa,
          provenienza: this.eventoRientroCensitoONon ? provenienza : {},
        });
      }

      if (lesioneMielica) this.datiGeneraliForm.get('lesioneMielica')?.disable({ emitEvent: false });
      if (unitaOperativa) this.datiGeneraliForm.get('unitaOperativa')?.disable({ emitEvent: false });
      if (moment(this.datiGeneraliForm.get('dataRicovero')?.value).isValid()) {
        this.datiGeneraliForm.get('dataRicovero')?.markAsTouched()
        this.datiGeneraliForm.updateValueAndValidity()
      }

      if (this.datiGenerali.evento.stato && this.datiGenerali.evento.stato?.idDizionario !== StatoEventoEnum.IN_LAVORAZIONE) {
        Object.keys(this.datiGeneraliForm.controls).forEach(key => {
          this.datiGeneraliForm.get(key)?.disable({ emitEvent: false });
        })
      }
    }
    else if (!this.datiGenerali && this.schedaRicService.schedaCreation$.getValue()?.scheda) {
      this.datiGeneraliForm.patchValue({
        ...(this.schedaRicService.schedaCreation$?.getValue() as AggiungiSchedaRicoveroRequest)?.scheda,
        lesioneMielica: this.schedaRicService.schedaCreation$.getValue()?.scheda?.lesioneMielica?.idDizionario || this.lesioneMielicaDefaultValue,
        provenienza: this.eventoRientroCensitoONon ?  this.schedaRicService.schedaCreation$.getValue()?.scheda?.provenienza  : {},
      }, { emitEvent: false })
    }

    if (this.datiGenerali && this.isReadOnly(this.datiGenerali)) {
      this.disableFormControls()
    }
  }

  initRientro() {
    this.eventoRientroCensitoONon = !!(this.eventoRientroCensito || this.eventoRientroNonCensito);
    this.lesioneMielicaDefaultValue = this.eventoRientroCensitoONon ? this.lesioni?.find(lesione => lesione.idDizionario === 33) : null;
    if (this.lesioneMielicaDefaultValue) this.datiGeneraliForm.get('lesioneMielica')?.setValue(this.lesioneMielicaDefaultValue.idDizionario);
    if (this.eventoRientroCensitoONon) {
      this.decoderService.getDcodProvenienza().pipe(
        catchError(error => {
          console.error('Errore durante il recupero delle provenienze:', error);
          return of([]);
        })
      ).subscribe(result => {
        this.optionsProvenienza = result as DizionarioModel[];
        this.lesioneMielica.disable();
        if ((this.datiGenerali && this.isReadOnly(this.datiGenerali)) || //
          (this.datiGenerali?.scheda.id && this.eventoRientroNonCensito && this.unitaOperativa.value) || 
          this.datiGenerali?.evento.stato?.idDizionario === StatoEventoEnum.CHIUSO) {
          this.unitaOperativa.disable();
        } else {
          this.unitaOperativa.enable();
        }
        this.datiGeneraliForm.patchValue({
          ...this.datiGeneraliForm.getRawValue(),
          provenienza: this.eventoRientroCensitoONon ? this.schedaRicService.schedaCreation$?.getValue()?.scheda?.provenienza : {},
        }, { emitEvent: false })
      }
      );
    }
  }

  compareWithIdCentro(opt1: CentriRicoveroModel, opt2: CentriRicoveroModel): boolean {
    return opt1 && opt2 ? opt1.idCentro === opt2.idCentro : opt1 === opt2;
  }

  // quando premo su info della modale
  onCronologiaDatiInfoClick = (event: any) => {
    this.schedaRicService.cronologiaOpened$.next(true);
    this.isTornaAllEditorClicked = false;
    this.showTornaAllEditor = true;
    this.savedDataBeforeCronologia = this.datiGeneraliForm.getRawValue();
    const dataRicovero = moment(event.schedaClinica.dataRicovero);
    const lesioneMielica = event.schedaClinica.lesioneMielica.idDizionario;
    this.datiGeneraliForm.patchValue({ ...event.schedaClinica, dataRicovero, lesioneMielica }, { emitEvent: false })
    this.showLesioneMielica = true;
    this.medicoCompilatore = event.medicoCompilatore
    this.ente = event.enteCompilatore
    this.presidio = event.presidioOspedaliero
    disableAllFieldsInForm(this.datiGeneraliForm)
    this.datiGeneraliForm.updateValueAndValidity()
  }

  onBackOnEditor($event?: MouseEvent) {
    if (this.showCronologiaBtnGenerali) {
      $event?.stopPropagation();
      $event?.preventDefault();
    }
    this.showLesioneMielica = false;
    this.medicoCompilatore = this.datiGenerali?.nomeCognomeMedico;
    this.ente = this.datiGenerali?.ente;
    this.presidio = this.datiGenerali?.presidioOspedaliero;
    this.medicoCompilatore = `${this.operatoreService.getOperatore().nomeOperatore} ${this.operatoreService.getOperatore().cognomeOperatore}`;
    this.ente = this.operatoreService.getOperatore().descLivello1Operatore || '';
    this.presidio = this.operatoreService.getOperatore().descLivello2Operatore || '';
    if (!this.isReadOnly(this.datiGenerali)) {
      this.datiGeneraliForm.get('idNosologico')?.enable({emitEvent: false});
      this.datiGeneraliForm.get('dataRicovero')?.enable({emitEvent: false});
      this.datiGeneraliForm.get('repartoRicovero')?.enable({emitEvent: false});
    }
    // @ts-ignore
    this.datiGeneraliForm.patchValue(this.savedDataBeforeCronologia)
    this.datiGeneraliForm.updateValueAndValidity()
    this.isTornaAllEditorClicked = true;
    this.showTornaAllEditor = false;
    this.schedaRicService.cronologiaOpened$.next(false)
  }

  compareByIdUnitaOperativa(option: any, value: any): boolean {
    if (option === null || value === null) {
      return false;
    }

    return option?.idUnitaOperativa === value?.idUnitaOperativa;
  }

  openPopupNote() {
    if (this.schedaRicService.idSchedaSelectedSubject.getValue()) { // se è update apro popup in modalita disabled
      let configNote = this.modalService.createNoteParam(this.lesioneNote.value, true);

      this.modalService.note(configNote)?.subscribe();
    } else {
      let configNote = this.modalService.createNoteParam(this.lesioneNote.value);

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != this.lesioneNote.value) {
          this.lesioneNote.setValue(res);
          this.lesioneNote.markAsDirty();
          this.lesioneNote.updateValueAndValidity();
        }
      });
    }
  }


  getErrorData() {
    return this.datiGeneraliForm.get('dataRicovero')?.hasError('required') ? 'Campo obbligatorio' :
      this.datiGeneraliForm.get('dataRicovero')?.hasError('dateBeforeDischarge') || this.datiGeneraliForm.get('dataRicovero')?.hasError('matDatepickerMin') ?
        (this.eventoRientroCensito ? ERROR_MESSAGE.DATE_BEFORE_PREV_DISCHARGE_RIENTRO :
          ERROR_MESSAGE.DATE_BEFORE_PREV_DISCHARGE) :
        this.datiGeneraliForm.get('dataRicovero')?.hasError('matDatepickerMax') ? ERROR_MESSAGE.MAX_DATE : 'Campo non valido'
  }

  ngOnDestroy() {
    // quando esco dal componente ripristino i dati compilati
    this.onBackOnEditor()
    this.datiGeneraliForm$?.unsubscribe()
    this.getCronologia$?.unsubscribe();
    this.schedaRicService.cronologiaOpened$.next(false);
  }

  onOpened() {
    this.showCronologiaBtnGenerali = true;
  }

  onClosed() {
    this.showCronologiaBtnGenerali = false;
  }

}
