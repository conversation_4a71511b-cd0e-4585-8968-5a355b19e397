import { CommonModule } from "@angular/common";
import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, ValidatorFn, Validators } from "@angular/forms";
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { MAT_DATE_FORMATS, MatNativeDateModule } from "@angular/material/core";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconRegistry } from "@angular/material/icon";
import { DomSanitizer } from "@angular/platform-browser";
import { first, Subject, takeUntil } from 'rxjs';
import { MaterialModule } from "../../../core/material.module";
import { DialogSuccessService } from "../../../shared/components/dialog-success/dialog-success.service";
import { IdOperatoreEnum, ERROR_MESSAGE, StatoEventoEnum } from "../../../shared/enums/enum";
import { DecoderService } from "../../../shared/services/decoder.service";
import { IT_FORMATS } from "../../../shared/utils/it-formats";
import { convertToDate } from "../../../shared/utils/utils";
import { dataNascitaValidator } from "../../../shared/validators/data-nascita.validator";
import { ElaborazioneParams } from "../../interfaces/elabora-estrazioni.interface";
import { EstrazioniService } from "../../services/estrazioni.service";
import { DizionarioModel } from "../../../shared/interfaces/scheda-ricovero.interface";
import { DatiCliniciService } from "../../services/dati-clinici.service";
import { OperatoreService } from "../../../core/services/operatore.service";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
    selector: 'app-elabora-estrazioni',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MaterialModule,
        MatDatepickerModule,
        MatNativeDateModule
    ],
    providers: [
        {
            provide: MAT_DATE_FORMATS,
            useValue: IT_FORMATS,
        },
        {
            provide: MomentDateAdapter,
            useClass: MomentDateAdapter,
            deps: [MAT_MOMENT_DATE_ADAPTER_OPTIONS]
        }
    ],
    templateUrl: './elabora-estrazioni.component.html',
    styleUrl: './elabora-estrazioni.component.scss'
})
export class ElaboraEstrazioniComponent implements OnInit, OnDestroy {

    reportInElaborazione: number = 0;

    filterForm!: FormGroup;
    private destroy$ = new Subject<void>();
    isResetButtonDisabled = false;
    today = new Date();
    ERROR_MESSAGE = ERROR_MESSAGE;
    optionsBoolean: Array<DizionarioModel>;
    isTavoloTecnico: boolean = false;

    constructor(
        private fb: FormBuilder,
        public decoderService: DecoderService,
        private estrazioniService: EstrazioniService,
        public datiCliniciService: DatiCliniciService,
        private dialogSuccessService: DialogSuccessService,
        private operatoreService: OperatoreService,
        iconRegistry: MatIconRegistry,
        sanitizer: DomSanitizer
    ) {
        iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));
        this.isTavoloTecnico = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM;
        this.createForm();
    }

    private createForm() {
        this.filterForm = this.fb.group({
            dataInizio: [null, [dataNascitaValidator(), Validators.required]],
            dataFine: [null, [dataNascitaValidator(), Validators.required]],
            statoEvento: [null],
            lesioneMielica: [null],
            lesioneTraumatica: [null],
            tipoEvento: [null]
        }, { validators: [this.dateRangeValidator()] });
        if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM) {
            this.filterForm.get('statoEvento')?.setValue(
                this.decoderService.listStatoEvento.find((opt) => opt.idDizionario === StatoEventoEnum.CHIUSO)
            );
            this.filterForm.get('statoEvento')?.disable();
        }
    }

    ngOnInit() {
        this.initializeDecoders();

        // // Quando cambia dataInizio, aggiorna validità
        // this.filterForm.get('dataInizio')?.valueChanges
        //     .pipe(takeUntil(this.destroy$))
        //     .subscribe((valInizio) => {
        //         const dataFine = this.filterForm.get('dataFine')?.value;
        //         if (valInizio && dataFine) {
        //             const start = convertToDate(valInizio);
        //             const end = convertToDate(dataFine);
        //             if (start && end && start > end) {
        //                 this.filterForm.get('dataFine')?.setValue(null, { emitEvent: false });
        //             }
        //         }
        //         this.filterForm.get('dataFine')?.updateValueAndValidity({ onlySelf: true });
        //     });

        // // Quando cambia dataFine, aggiorna validità
        // this.filterForm.get('dataFine')?.valueChanges
        //     .pipe(takeUntil(this.destroy$))
        //     .subscribe((valFine) => {
        //         const dataInizio = this.filterForm.get('dataInizio')?.value;
        //         if (valFine && dataInizio) {                    
        //             const start = convertToDate(dataInizio);
        //             const end = convertToDate(valFine);
        //             if (start && end && start > end) {
        //                 this.filterForm.get('dataInizio')?.setValue(null, { emitEvent: false });
        //             }
        //         }
        //         this.filterForm.get('dataInizio')?.updateValueAndValidity({ onlySelf: true });
        //     });

        this.decoderService.reportInElaborazione$
            .pipe(takeUntil(this.destroy$))
            .subscribe(count => {
                this.reportInElaborazione = count;
            });      
    }

    private initializeDecoders() {
        this.decoderService.getDcodBoolean().subscribe((value) => {
            this.optionsBoolean = value
        })
        if (!this.decoderService.listStatoLesioneEstrazioni?.length) {
            this.decoderService.getDcodStatoLesioneEstrazioni()
                .pipe(takeUntil(this.destroy$))
                .subscribe();
        }
        if (!this.decoderService.listTipoEvento?.length) {
            this.decoderService.getDcodTipoEvento()
                .pipe(takeUntil(this.destroy$))
                .subscribe();
        }
        if (!this.decoderService.listStatoEvento?.length) {
            this.decoderService.getDcodStatoEvento()
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: (value) => {                        
                        if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM) {
                            this.filterForm.get('statoEvento')?.setValue(value.find((opt) => opt.idDizionario === StatoEventoEnum.CHIUSO));
                            this.filterForm.get('statoEvento')?.disable();
                        }
                    }
                });
        }
        if (!this.decoderService.listStatoElaborazione?.length) {
            this.decoderService.getDcodStatoElaborazione()
                .pipe(takeUntil(this.destroy$))
                .subscribe();
        }
    }

    private buildElaborazioneParams(filterValues: any): ElaborazioneParams {
        const params: ElaborazioneParams = {};

        if (filterValues.dataInizio) {
            params.dataMin = filterValues.dataInizio;
        }

        if (filterValues.dataFine) {
            params.dataMax = filterValues.dataFine;
        }

        if (filterValues.lesioneMielica?.idDizionario) {
            params.idTipoLesione = filterValues.lesioneMielica.idDizionario;
        }

        if (filterValues.lesioneTraumatica !== null) {
            params.lesioneTraumatica = filterValues.lesioneTraumatica;
        }

        if (filterValues.tipoEvento?.idTipoEvento) {
            params.idTipoEvento = filterValues.tipoEvento.idTipoEvento;
        }

        if (filterValues.statoEvento?.idDizionario) {
            params.idStatoEvento = filterValues.statoEvento.idDizionario;
        }

        if (this.isTavoloTecnico) {
            params.idStatoEvento = StatoEventoEnum.CHIUSO;
        }

        return params;
    }

    private manageLesioneTraumaticaSave() : boolean | null {
        let lesioneTraumaticaValue = this.filterForm.get('lesioneTraumatica')?.value;
        let lesioneToSave : boolean | null = null;
        if (lesioneTraumaticaValue) {
            lesioneToSave = lesioneTraumaticaValue.idDizionario === 1 ? true : false;
        }
        return lesioneToSave;
    }

    onElabora() {
        if (this.filterForm.valid) {
            const lesioneToSave = this.manageLesioneTraumaticaSave();
            const formValue : any = {
                ...this.filterForm.value,
                lesioneTraumatica: lesioneToSave
            }
            const params = this.buildElaborazioneParams(formValue);

            this.estrazioniService.richiestaElaborazione(params)
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: () => {
                        this.estrazioniService.filtraEstrazioni({});
                        this.estrazioniService.isLoading$
                            .pipe(
                                takeUntil(this.destroy$),
                                first(isLoading => !isLoading)
                            )
                            .subscribe(() => {
                                this.dialogSuccessService.showSuccessDialog("Elaborazione richiesta con successo");
                                this.decoderService.aggiornaReportInElaborazione();
                                this.resetForm();
                            });
                    }
                });
        }
    }

    resetForm() {
        if (this.isResetButtonDisabled) {
            return;
        }
        this.isResetButtonDisabled = true;
        this.filterForm.reset();
        if (this.isTavoloTecnico) {
            const eventoChiuso = this.decoderService.listStatoEvento.find(el => el.idDizionario === StatoEventoEnum.CHIUSO);
            this.filterForm.get('statoEvento')?.setValue(eventoChiuso, { emitEvent: false });
        }
        this.isResetButtonDisabled = false;
    }

    compareFn(c1: any, c2: any): boolean {
        return c1 && c2 ? c1.codice === c2.codice : c1 === c2;
    }
    
    compareFnIdDizionario = (a: any, b: any): boolean => {
        return a && b ? a.idDizionario === b.idDizionario : a === b;
    };

    dateRangeValidator(): ValidatorFn {
        return (control: any): { [key: string]: any } | null => {
            if (!(control instanceof FormGroup)) {
                return null;
            }
            const start = control.get('dataInizio')?.value;
            const end = control.get('dataFine')?.value;

            if (!start || !end) {
                return null;
            }

            const startDate = convertToDate(start);
            const endDate = convertToDate(end);

            if (!startDate || !endDate) {
                return null;
            }

            return startDate <= endDate ? null : { rangeDateInvalid: true };
        };
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }
} 