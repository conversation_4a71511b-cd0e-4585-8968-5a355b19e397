<div [formGroup]="filterForm" class="filter-form my-3">
  <div class="form-row">
    <div class="form-field">
      <mat-label class="field-label">Data inizio*</mat-label>
      <mat-form-field class="input-width">
        <input matInput [matDatepicker]="pickerStart" formControlName="dataInizio" placeholder="GG/MM/AAAA" class="periodo-input" 
              [max]="filterForm.get('dataFine')?.value || today">
        <mat-datepicker-toggle matIconSuffix [for]="pickerStart">
          <mat-icon matDatepickerToggleIcon svgIcon="it-calendar" class="d-flex icon-calendar"></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #pickerStart></mat-datepicker>
        <mat-error *ngIf="filterForm.get('dataInizio')?.errors?.['futureDate']">
          {{ ERROR_MESSAGE.MAX_DATE }}
        </mat-error>
        <mat-error *ngIf="filterForm.get('dataInizio')?.errors?.['required']">
          {{ ERROR_MESSAGE.REQUIRED }}
        </mat-error>
        <mat-error *ngIf="filterForm.errors?.['rangeDateInvalid']" class="date-range-error">
          {{ ERROR_MESSAGE.DATE_RANGE_INVALID }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-label class="field-label">Data fine*</mat-label>
      <mat-form-field class="input-width">
        <input matInput [matDatepicker]="pickerEnd" formControlName="dataFine" placeholder="GG/MM/AAAA" class="periodo-input" 
              [min]="filterForm.get('dataInizio')?.value"
              [max]="today">
        <mat-datepicker-toggle matIconSuffix [for]="pickerEnd">
          <mat-icon matDatepickerToggleIcon svgIcon="it-calendar" class="d-flex icon-calendar"></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #pickerEnd></mat-datepicker>
        <mat-error *ngIf="filterForm.get('dataFine')?.errors?.['futureDate']">
          {{ ERROR_MESSAGE.MAX_DATE }}
        </mat-error>
        <mat-error *ngIf="filterForm.get('dataFine')?.errors?.['required']">
          {{ ERROR_MESSAGE.REQUIRED }}
        </mat-error>
        <mat-error *ngIf="filterForm.errors?.['rangeDateInvalid']" class="date-range-error">
          {{ ERROR_MESSAGE.DATE_RANGE_INVALID }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-label class="field-label">Stato evento</mat-label>
      <mat-form-field class="input-width">
        <mat-select formControlName="statoEvento" [compareWith]="compareFnIdDizionario" [placeholder]="'Seleziona'">
          <mat-option [value]="null">Seleziona</mat-option>
          <mat-option *ngIf="!decoderService.listStatoEvento?.length" [value]="''"
            disabled>Caricamento...</mat-option>
          <mat-option *ngFor="let option of decoderService.listStatoEvento" [value]="option">
            {{ option.descrizione }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-label class="field-label">Tipo evento</mat-label>
      <mat-form-field class="input-width" >
        <mat-select formControlName="tipoEvento" [compareWith]="compareFn" [placeholder]="'Seleziona'">
          <mat-option class="py-2" [value]="null">Seleziona</mat-option>
          <mat-option class="py-2" *ngFor="let option of decoderService.listTipoEvento" [value]="option">
            {{ option.descrizione }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-label class="field-label">Lesione mielica</mat-label>
      <mat-form-field class="input-width">
        <mat-select formControlName="lesioneMielica" [compareWith]="compareFn" [placeholder]="'Seleziona'">
          <mat-option [value]="null">Seleziona</mat-option>
          <mat-option *ngIf="decoderService.listStatoLesioneEstrazioni.length === 0" [value]="''"
            disabled>Caricamento...</mat-option>
          <mat-option *ngFor="let option of decoderService.listStatoLesioneEstrazioni" [value]="option">
            {{ option.descrizione }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="form-field">
      <mat-label class="field-label">Lesione traumatica</mat-label>
      <mat-radio-group formControlName="lesioneTraumatica" class="d-flex" style="gap: 2rem;">
        <mat-radio-button *ngFor="let option of optionsBoolean" [value]="option" color="primary"
          (mouseup)="datiCliniciService.toggleRadioSelection(filterForm.get('lesioneTraumatica'), option, $event)">
          {{ option.descrizione === 'SI' ? 'SÌ' : 'NO' }}
        </mat-radio-button>
      </mat-radio-group>
    </div>

    <div class="button-col">
      <div class="button-group align-items-end">
        <button mat-button type="button" (click)="resetForm()" color="primary" [disabled]="isResetButtonDisabled" style="text-decoration: underline !important;">
          Azzera
        </button>
        <button mat-raised-button (click)="onElabora()" [disabled]="!filterForm.valid || reportInElaborazione > 0" color="primary" class="no-wrap">
          Elabora
        </button>
      </div>
    </div>
  </div>
</div>