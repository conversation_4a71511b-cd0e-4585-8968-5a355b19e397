.filter-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}

.input-width {
  width: 250px !important;
}

.form-field {
  flex: 1 1 250px;
  min-width: 250px;
  max-width: 300px;
}

.radio-field {
  flex: 1 1 200px;
  min-width: 200px;
  max-width: 200px;
}

.button-col {
  flex: 0 0 auto;
  display: flex;
  justify-content: flex-end;
}

.button-group {
  display: flex;
  gap: 12px;
  align-items: flex-end;

  .button-underline {
    padding: 0 8px;
    min-width: 60px;
  }
}

.error-row {
  width: 100%;
}

.mar-l7 {
  margin-left: 7px;
}

.date-range-error {
  font-size: 12px;
}

.periodo-input {
  height: 1.5rem !important;
  min-height: 1.5rem !important;
  max-height: 1.5rem !important;
  line-height: 1.5rem !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  font-size: 1rem !important;
  box-sizing: border-box !important;
  font-weight: 500 !important;
}

.icon-calendar {
  color: #003354 !important;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-wrap {
  white-space: nowrap;
}

// New styles
::ng-deep .mat-form-field {
  width: 100%;
  margin-bottom: 8px;
}

::ng-deep .mat-form-field-wrapper {
  padding-bottom: 0;
}

::ng-deep .mat-form-field-infix {
  padding: 8px 0;
}

::ng-deep .mat-select-value {
  padding-right: 24px;
}

::ng-deep .mat-form-field-subscript-wrapper {
  margin-top: 0;
}

::ng-deep .mat-form-field-flex {
  padding: 0 8px;
}

@media (max-width: 767.98px) {
  .form-field {
    flex: 1 1 100%;
    max-width: 100%;
  }
  
  .button-group {
    width: 100%;
    justify-content: flex-end;
  }
  .button-group button {
    min-width: 120px;
  }
}