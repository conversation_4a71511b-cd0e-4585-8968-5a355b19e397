import { DizionarioModel } from "../../shared/interfaces/scheda-ricovero.interface";

export interface SavedFilters {
    [key: string]: any;
    dataInizio?: string | Date;
    dataFine?: string | Date;
}

export interface AdaptedFilters {
    periodo: {
        start: string | Date | null;
        end: string | Date | null;
    };
    statoEvento?: DizionarioModel;
    [key: string]: any;
} 

export interface ElaborazioneParams {
    dataMin?: string;
    dataMax?: string;
    idTipoLesione?: number;
    lesioneTraumatica?: boolean;
    idTipoEvento?: number;
    idStatoEvento?: number;
}

export interface PaginationConfig {
    totalItems: number;
    currentPage: number;
    itemsPerPage: number;
}

export interface TableConfig {
    displayedColumns: string[];
    defaultItemsPerPage: number;
}

export enum FilterKeys {
    PERIODO = 'periodo',
    ID_ELABORAZIONE = 'idElaborazione',
    STATO_ELABORAZIONE = 'statoElaborazione',
    LESIONE_MIELICA = 'lesioneMielica',
    LESIONE_TRAUMATICA = 'lesioneTraumatica',
    STATO_EVENTO = 'statoEvento',
    TIPO_EVENTO = 'tipoEvento'
}

export interface FilterValue {
    start?: Date | string;
    end?: Date | string;
    descrizione?: string;
    codice?: string;
    idDizionario?: number;
    idTipoEvento?: number;
}

export interface ActiveFilters {
    [FilterKeys.PERIODO]?: { start: Date | string; end: Date | string };
    [FilterKeys.ID_ELABORAZIONE]?: number;
    [FilterKeys.STATO_ELABORAZIONE]?: FilterValue;
    [FilterKeys.LESIONE_MIELICA]?: FilterValue;
    [FilterKeys.LESIONE_TRAUMATICA]?: boolean;
    [FilterKeys.STATO_EVENTO]?: FilterValue;
    [FilterKeys.TIPO_EVENTO]?: FilterValue;
} 