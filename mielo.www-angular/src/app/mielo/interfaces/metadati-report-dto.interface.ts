import { TipoEventoModel } from '../../shared/interfaces/scheda-ricovero.interface';
import { DizionarioDTO } from './dizionario-dto.interface';

export interface MetadatiReportDTO {
  idElaborazione: number;
  dataOraElaborazione: string;
  statoElaborazione: DizionarioDTO;
  dataMin: string;
  dataMax: string;
  tipoLesione: DizionarioDTO;
  statoEvento: DizionarioDTO;
  tipoEvento: TipoEventoModel;
} 