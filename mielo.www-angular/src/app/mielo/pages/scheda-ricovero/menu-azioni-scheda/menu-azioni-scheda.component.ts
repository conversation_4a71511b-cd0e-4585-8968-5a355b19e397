import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { Router, RouterModule } from '@angular/router';
import moment, {isMoment} from "moment";
import { combineLatest, Subject, Subscription, tap } from "rxjs";
import {filter, map, startWith} from "rxjs/operators";
import { MaterialModule } from '../../../../core/material.module';
import { IdOperatoreEnum, ECategoriaEvento, EDescrizioneEvento, StatoEventoEnum } from '../../../../shared/enums/enum';
import { TipoRicoveroEnum } from '../../../../shared/enums/tipo-ricovero.enum';
import { AggiungiSchedaRicoveroRequest, NuovoEventoRicoveroModel, OutputSchedaPazienteRicoveroModel, SchedaRicoveroDatiMancanti } from '../../../../shared/interfaces/scheda-ricovero.interface';
import { DialogService } from '../../../../shared/services/dialog.service';
import {
  TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO,
  TIPO_EVENTO_RIENTRO_CENSITO,
  TIPO_EVENTO_RIENTRO_NON_CENSITO
} from '../../../../shared/utils/const';
import { manageDataOraToSave } from "../../../../shared/utils/utils";
import { DatiCliniciService } from '../../../services/dati-clinici.service';
import { DatiDimissioneService } from '../../../services/dati-dimissione.service';
import { RicercaSchedaPazienteService } from "../../../services/ricerca-scheda-paziente.service";
import { SchedaRicoveroService } from '../../../services/scheda-ricovero.service';
import { EventiAssociatiService } from '../../../services/eventi-associati.service';
import { DatiDimissioneRientroService } from '../../../services/dati-dimissione-rientro.service';
import { OperatoreService } from '../../../../core/services/operatore.service';

@Component({
  selector: 'app-menu-azioni-scheda',
  standalone: true,
  imports: [CommonModule, MaterialModule, RouterModule],
  templateUrl: './menu-azioni-scheda.component.html',
  styleUrl: './menu-azioni-scheda.component.scss'
})
export class MenuAzioniSchedaComponent implements OnInit, OnChanges, OnDestroy {
  settRiabilitativo$: Subscription;
  combineDatiCliniciAndDimissioni: Subscription;
  private combineDatiCliniciValid$: Subscription;
  private datiAnagrafici$: Subscription;

  @Input() shouldShowActions!: boolean;
  @Input() set schedaRicoveroDatiMancanti(value: SchedaRicoveroDatiMancanti | null) {
    this._schedaRicoveroDatiMancanti = value;
    this.triggerValidation.next();
  }
  @Input() infoScheda : AggiungiSchedaRicoveroRequest | null;
  private _schedaRicoveroDatiMancanti: SchedaRicoveroDatiMancanti | null = null;
  private combineDatiCliniciAndDimissioniRientro: Subscription;

  datiAnagraficiValid$ = this.schedaService._areFormsCreateSchedaValidSubject.pipe(
    map(form => form.formDatiAnagrafici.valid)
  )
  datiSchedaGeneraliValid$ = this.schedaService._areFormsCreateSchedaValidSubject.pipe(
    map(form => form.formDatiGenerali.valid)
  )

  private triggerValidation = new Subject<void>();
  private destroy$ = new Subject<void>();

  @Output() onCreateNewEvent = new EventEmitter<void>();
  @Output() onGoBack = new EventEmitter<void>();
  @Output() onChiudiEvento = new EventEmitter<OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel>();
  @Output() onSalvaBozza = new EventEmitter<OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel>();

  isChiudiEventoButtonDisabled: boolean = false;
  isSaveButtonDisabled = true;
  checkCodFisc$: Subscription
  showCloseSaveEventBtn: boolean = false;
  showNewEventBtn: boolean = false;

  isCronologiaOpened: boolean = false;

  constructor(
    public schedaService: SchedaRicoveroService,
    private dialogService: DialogService,
    private datiCliniciService: DatiCliniciService,
    public ricercaSchedaService: RicercaSchedaPazienteService,
    private datiDimissioneService: DatiDimissioneService,
    private eventiService: EventiAssociatiService,
    private datiDimissioneRientro: DatiDimissioneRientroService,
    private operatoreService: OperatoreService,
    private router: Router
  ) {
    
    //se sono in creazione della scheda controllo la validità dei campi anagrafici e generali per abilitare il pulsante di salvataggio
    if (!this.schedaService.idSchedaSelectedSubject.getValue()) { 
      combineLatest([
        this.datiAnagraficiValid$,
        this.datiSchedaGeneraliValid$
      ]).pipe(
        takeUntilDestroyed(),
        startWith([false, false]),
        tap(([isAnagraficiValid, isGeneraliValid]) => {
          this.isSaveButtonDisabled = !isAnagraficiValid || !isGeneraliValid;
          this.showCloseSaveEventBtn = !!this.schedaService.statusSchedaSelectedSubject.getValue();
        })
      ).subscribe();
    } else {
      this.showCloseSaveEventBtn = !!this.schedaService.statusSchedaSelectedSubject.getValue();
    }


    combineLatest([
      this.schedaService.idSchedaSelectedSubject,
      this.schedaService.schedaDettagliata$
    ]).pipe(
        takeUntilDestroyed(),
        filter(([idScheda, scheda]) => !!idScheda && !!scheda),
        tap(([idScheda, scheda]) => {
          // todo verificare che se è un evento di riab deve controllare la validità dei form di dat gen,clinic,dimiss
          // todo se è semplicemente un evento di rientro deve controllare i 4 form di rientro e dat gen
          const isSocioEconomiciValid = !this.schedaService.getDatiMancantiSocioEconomici();

          if (scheda.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento ||
              scheda.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_RIENTRO_NON_CENSITO.idTipoEvento){
            const datiCliniciRientroValid$ = [
              this.datiCliniciService.getDatiEziologiaRientroValido$(),
              this.datiCliniciService.getDatiQuadroNeurologicoValido$()
            ]
            const datiDimissioniRientroValid$ = [
              this.datiDimissioneRientro.getInterventiEffettuatiRientroValid$(),
              this.datiDimissioneRientro.getGeneraleRientroValid$()
            ]

            this.combineDatiCliniciAndDimissioniRientro = combineLatest(
                [
                  ...datiCliniciRientroValid$,
                  ...datiDimissioniRientroValid$,
                  this.datiSchedaGeneraliValid$
                ]
            ).subscribe(valori => {
              return this.isChiudiEventoButtonDisabled = valori.some(v => !v) || !isSocioEconomiciValid
            });
          } else if (scheda.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento) {
            this.combineDatiCliniciAndDimissioniRientro?.unsubscribe()

            this.combineDatiCliniciAndDimissioni = combineLatest(
              [
                this.datiCliniciService.getDatiValutazioneValido$(),
                this.datiCliniciService.getDatiNecessitaAssistenzialiValido$(),
                this.datiCliniciService.getDatiQuadroNeurologicoValido$(),
                this.datiCliniciService.getDatiComplicanzeValido$(),
                this.datiSchedaGeneraliValid$,
                this.datiDimissioneService.getGeneraleValido$(),
                this.datiDimissioneService.getValutazioneValido$(),
                this.datiDimissioneService.getNecessitaValido$(),
                this.datiDimissioneService.getQuadroNeurologicoValido$(),
                this.datiDimissioneService.getComplicanzeValido$(),
                this.datiDimissioneService.getQuadroFunzionaleValido$(),
                this.datiDimissioneService.getAusiliValido$(),
                this.datiDimissioneService.getSocioAssistenzialiValid$()
              ]
            ).subscribe(valori => {
              return this.isChiudiEventoButtonDisabled = valori.some(v => !v) || !isSocioEconomiciValid
            });
          }

          this.showCloseSaveEventBtn = scheda.evento.stato?.idDizionario !== StatoEventoEnum.CHIUSO;

          this.showNewEventBtn =  scheda.evento.stato?.idDizionario === StatoEventoEnum.CHIUSO &&
                                  this.schedaService.getIsLastEvent() &&
                                  this.schedaService.getHasSameStrutturaAsOperatore() &&
                                  this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.OSP;
        })
    ).subscribe();
  }

  ngOnInit(): void {
    this.schedaService.cronologiaOpened$.subscribe({
      next: (value: boolean) => {
        this.isCronologiaOpened = value;
      }
    })
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['schedaRicoveroDatiMancanti']?.currentValue) { // dati mancanti provenienti dalla get campi obblig
      const isSocioEconomiciValid = !this.schedaService.getDatiMancantiSocioEconomici();
      this.combineDatiCliniciAndDimissioniRientro?.unsubscribe() // disabilito perchè quando chiamo get campi obblig verifico prima da questa i campi e poi dai form
      this.combineDatiCliniciAndDimissioni?.unsubscribe() // disabilito perchè quando chiamo get campi obblig verifico prima da questa i campi e poi dai form
      const datiMancanti = changes['schedaRicoveroDatiMancanti'].currentValue;

      // setto tutti i campi validi o meno in base ai dati mancanti recuperati dal be
      this.datiCliniciService.setDatiEziologiaValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaEziologica || false)
      this.datiCliniciService.setDatiLesioneTrattamentoValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaLesioneTrattamento || false)
      this.datiCliniciService.setDatiValutazioneValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaValutazioneIngresso || false)
      this.datiCliniciService.setDatiNecessitaAssistenzialiValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaNecessitaAssistenzialeIngresso || false)
      this.datiCliniciService.setDatiQuadroNeurologicoValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaQuadroNeurologico || false)
      if (datiMancanti.campiMancantiSchedeCliniche.schedaSettingRiabilitativo !== null) {
        this.datiCliniciService.setDatiSettingRiabilitativoValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaSettingRiabilitativo || false)
      }

      if (datiMancanti.campiMancantiSchedeCliniche.schedaComplicanze !== null) {
        this.datiCliniciService.setDatiComplicanzeValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaComplicanze || false)
      }

      if (datiMancanti.campiMancantiSchedeCliniche.schedaEziologicaRientro !== null) {
        this.datiCliniciService.setDatiEziologiaRientroValido(!datiMancanti?.campiMancantiSchedeCliniche.schedaEziologicaRientro || false)
      }

      this.datiDimissioneService.setGeneraleValido(!datiMancanti.campiMancantiSchedeDimissione.schedaGeneraleDimissioni || false)
      this.datiDimissioneService.setValutazioneValido(!datiMancanti.campiMancantiSchedeDimissione.schedaValutazioneDimissione || false)
      this.datiDimissioneService.setNecessitaValido(!datiMancanti.campiMancantiSchedeDimissione.schedaNecessitaAssistenzaDimissione || false)
      this.datiDimissioneService.setQuadroNeurologicoValido(!datiMancanti.campiMancantiSchedeDimissione.schedaQuadroNeurologicoDimissione || false)
      this.datiDimissioneService.setComplicanzeValido(!datiMancanti.campiMancantiSchedeDimissione.schedaComplicanzeDimissione || false)
      this.datiDimissioneService.setQuadroFunzionaleValido(!datiMancanti.campiMancantiSchedeDimissione.schedaQuadroFunzionaleDimissione || false)
      this.datiDimissioneService.setAusiliValido(!datiMancanti.campiMancantiSchedeDimissione.schedaAusiliDimissione || false)
      this.datiDimissioneService.setSocioAssistenzialiValid(!datiMancanti.campiMancantiSchedeDimissione.schedaAspettiSocioassistenzialiDimissione || false)

      if (datiMancanti.campiMancantiSchedeDimissione.schedaInterventiEffettuatiDimissioni !== null) {
        this.datiDimissioneRientro.setInterventiEffettuatiRientroValido(!datiMancanti?.campiMancantiSchedeDimissione.schedaInterventiEffettuatiDimissioni || false)
        this.datiDimissioneRientro.setGeneraleRientroValido(!datiMancanti?.campiMancantiSchedeDimissione.schedaGeneraleDimissioni || false);
      }

      // creo un observable che mi restituisce i campi validi di dati clinici
      let datiCliniciValid$ = [
        this.datiCliniciService.getDatiEziologiaValido$(),
        this.datiCliniciService.getDatiLesioneTrattamentoValido$(),
        this.datiCliniciService.getDatiValutazioneValido$(),
        this.datiCliniciService.getDatiNecessitaAssistenzialiValido$(),
        this.datiCliniciService.getDatiQuadroNeurologicoValido$()
      ];
      if (datiMancanti.campiMancantiSchedeCliniche.schedaSettingRiabilitativo !== null) {
        datiCliniciValid$.push(this.datiCliniciService.getDatiSettingRiabilitativoValido$());
      }
      if (datiMancanti.campiMancantiSchedeCliniche.schedaComplicanze !== null) {
        datiCliniciValid$.push(this.datiCliniciService.getDatiComplicanzeValido$());
      }
      if (datiMancanti.campiMancantiSchedeCliniche.schedaEziologicaRientro !== null) {
        datiCliniciValid$ = [];
        datiCliniciValid$.push(this.datiCliniciService.getDatiEziologiaRientroValido$());
        datiCliniciValid$.push(this.datiCliniciService.getDatiQuadroNeurologicoValido$());
      }

      // creo un observable che mi restituisce i campi validi di dati dimissioni
      let datiDimissioniValid$ = [
        this.datiDimissioneService.getGeneraleValido$(),
        this.datiDimissioneService.getValutazioneValido$(),
        this.datiDimissioneService.getNecessitaValido$(),
        this.datiDimissioneService.getQuadroNeurologicoValido$(),
        this.datiDimissioneService.getComplicanzeValido$(),
        this.datiDimissioneService.getQuadroFunzionaleValido$(),
        this.datiDimissioneService.getAusiliValido$(),
        this.datiDimissioneService.getSocioAssistenzialiValid$()
      ]
      // subscription che in base al valore di setting riaiabilitativo controlla gli accordion di dati clinici e dimissioni

      if (datiMancanti.campiMancantiSchedeDimissione.schedaInterventiEffettuatiDimissioni !== null) {
        datiDimissioniValid$ = [];
        datiDimissioniValid$.push(this.datiDimissioneRientro.getInterventiEffettuatiRientroValid$());
        datiDimissioniValid$.push(this.datiDimissioneRientro.getGeneraleRientroValid$());
      }

      // il risultato dei combineLatest mi restituisce un array di booleani che rappresentano la validità dei form in ordine di come sono inseriti negli array !!!
      if (this.schedaService.getSchedaCorrente().scheda.unitaOperativa.idUnitaOperativa === TipoRicoveroEnum.ACUTI) {
        this.settRiabilitativo$ = this.datiCliniciService.getDatiSettingRiabilitativo$().subscribe(
          trasferimentoSetting => {
            // se setting riabil è NO valuta sia i form di dati clinici che di dimissioni
            if (trasferimentoSetting?.trasferimentoSettingRiabilitativo === false) { // solo se seleziono NO
              this.combineDatiCliniciValid$?.unsubscribe()

              this.combineDatiCliniciAndDimissioni = combineLatest(
                [
                  ...datiCliniciValid$,
                  ...datiDimissioniValid$,
                  this.datiSchedaGeneraliValid$
                ]
              ).subscribe(valori => {
                return this.isChiudiEventoButtonDisabled = valori.some(v => !v) || !isSocioEconomiciValid
              });
              //   altrimenti valuto solo i form di dati clinici
            } else {
              this.combineDatiCliniciAndDimissioni?.unsubscribe()

              this.combineDatiCliniciValid$ = combineLatest([
                ...datiCliniciValid$,
                this.datiSchedaGeneraliValid$
              ])
                .pipe(
              ).subscribe(valori => {
                return this.isChiudiEventoButtonDisabled = valori.some(v => !v) || !isSocioEconomiciValid
              });

            }
          }
        )
      } else {
        this.combineDatiCliniciAndDimissioni = combineLatest(
          [
            ...datiCliniciValid$,
            ...datiDimissioniValid$,
            this.datiSchedaGeneraliValid$
          ]
        ).subscribe(valori => {
          return this.isChiudiEventoButtonDisabled = valori.some(v => !v) || !isSocioEconomiciValid
        });
      }
    }
  }


  goBack() {
    const doNotShowPopup = this.schedaService.getSchedaCorrente()?.evento?.stato?.idDizionario === StatoEventoEnum.CHIUSO ||
                           this.schedaService.getSchedaCorrente()?.codPresidioOspedaliero !== this.operatoreService.getOperatore().codLivello2Operatore ||
                          !this.schedaService.getSchedaCorrente()?.existAtLeastOneEventOnSamePresidio;
    if (doNotShowPopup) {
      this.onGoBack.emit();
      this.schedaService.idSchedaSelectedSubject.next(undefined);
      this.schedaService.schedaCreation$.next({} as AggiungiSchedaRicoveroRequest)
      return this.ricercaSchedaService.disabilitaNuovaSegn.next(true)
    }

    this.dialogService.confermaUscita().subscribe(result => {
      if (result === 'conferma') {
        this.onGoBack.emit();
        this.schedaService.idSchedaSelectedSubject.next(undefined);
        this.schedaService.schedaCreation$.next({} as AggiungiSchedaRicoveroRequest)
        this.ricercaSchedaService.disabilitaNuovaSegn.next(true)
      }
    });
  }

  save() {
    this.onCreateNewEvent.emit()
  }

  chiudiEvento() {
    const confirmDialog$ = this.dialogService.openConfirmDialog({
      titolo: 'Attenzione',
      messaggio: 'Se confermi l\'evento verrà chiuso e non sarà più possibile modificare i dati.\n\n Desideri chiudere l\'evento?',
      bottoni: [
        { testo: 'Continua le modifiche', azione: 'annulla' },
        { testo: 'Chiudi evento', azione: 'conferma' }
      ],
      tipo: 'warning'
    }).pipe(
      tap(result => {
        if (result === 'conferma') {
          const schedaRicovero: OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel = this.formatSchedaRicovero();
          schedaRicovero.scheda.bozza = false;
          this.onChiudiEvento.emit(schedaRicovero);
        }

        confirmDialog$.unsubscribe()
      })
    ).subscribe()
  }

  isCreateOperation(): boolean {
    return !this.schedaService.idSchedaSelectedSubject.getValue() && !this.schedaService.isNewEvent.getValue();
  }

  salvaBozza() {
    const bozza: OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel = this.formatSchedaRicovero();
    this.onSalvaBozza.emit(bozza);
  }

  formatSchedaRicovero(): OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel {
    const baseScheda = {
      scheda: { ...this.schedaService.getSchedaCorrente().scheda, ...this.schedaService.schedaCreation$.getValue()?.scheda },
      datiClinici: this.getDatiCliniciValues(),
      datiDimissioni: this.getDatiDimissioneValues()
    };
    baseScheda.scheda.dataRicovero = isMoment(baseScheda.scheda.dataRicovero) ? moment(baseScheda.scheda.dataRicovero).format('YYYY-MM-DD') : null

    if (this.schedaService.isNewEvent.getValue()) {
      const nuovoEvento: NuovoEventoRicoveroModel = {
        ...baseScheda,
        idPaziente: this.schedaService.getSchedaCorrente().idPaziente,
        evento: {
          ...this.schedaService.getSchedaCorrente().evento,
          stato: {
            idDizionario: StatoEventoEnum.IN_LAVORAZIONE,
            categoria: ECategoriaEvento.STATO_EVENTO,
            descrizione: EDescrizioneEvento["IN LAVORAZIONE"]
          },
        }
      };
      return nuovoEvento as NuovoEventoRicoveroModel;
    } else {
      return baseScheda as OutputSchedaPazienteRicoveroModel;
    }
  }

  getDatiCliniciValues() {
    return {
      schedaEziologica: this.datiCliniciService.getDatiEziologiaValue() ?
        (this.datiCliniciService.getDatiEziologiaValue()) :
        (this.schedaService.getSchedaCorrente().schedaEziologica),

      schedaEziologicaRientro: this.datiCliniciService.getDatiEziologiaRientroValue() ?
        (this.datiCliniciService.getDatiEziologiaRientroValue()) :
        (this.schedaService.getSchedaCorrente().schedaEziologicaRientro),

      schedaLesioneTrattamento: this.datiCliniciService.getDatiLesioneTrattamentoValue() ?
        (this.datiCliniciService.getDatiLesioneTrattamentoValue()) :
        (this.schedaService.getSchedaCorrente().schedaLesioneTrattamento),

      schedaValutazioneIngresso: this.datiCliniciService.getDatiValutazioneValue() ?
        (this.datiCliniciService.getDatiValutazioneValue()) :
        (this.schedaService.getSchedaCorrente().schedaValutazioneIngresso),

      schedaNecessitaAssistenzialeIngresso: this.datiCliniciService.getDatiNecessitaAssistenzialiValue() ?
        (this.datiCliniciService.getDatiNecessitaAssistenzialiValue()) :
        (this.schedaService.getSchedaCorrente().schedaNecessitaAssistenzialeIngresso),

      schedaQuadroNeurologico: this.datiCliniciService.getDatiQuadroNeurologicoValue() ?
        (this.datiCliniciService.getDatiQuadroNeurologicoValue()) :
        (this.schedaService.getSchedaCorrente().schedaQuadroNeurologico),

      schedaSettingRiabilitativo: this.datiCliniciService.getDatiSettingRiabilitativoValue() ?
        (this.datiCliniciService.getDatiSettingRiabilitativoValue()) :
        (this.schedaService.getSchedaCorrente().schedaSettingRiabilitativo),

      schedaComplicanze: this.datiCliniciService.getDatiComplicanzeValue() ?
        (this.datiCliniciService.getDatiComplicanzeValue()) :
        (this.schedaService.getSchedaCorrente().schedaComplicanze)
    };
  }

  getDatiDimissioneValues() {
    const schedaCorrente = this.schedaService.getSchedaCorrente().datiDimissioni;

    let generaleDimissioni = this.datiDimissioneService.getGeneraleValue() ?? schedaCorrente.schedaGeneraleDimissioni;
    //intervento fatto perchè nel file scheda-ricov-serv nel setDimissione c'è la condizione che se è un rientro setta i dati ge di dimiss nel subj generaleRientro e non nel generale
    if (this.schedaService.getEventoRientroCensito() || this.schedaService.getEventoRientroNonCensito()) {
      generaleDimissioni = this.datiDimissioneRientro.getGeneraleRientroValue()
    }

    if (this.schedaService.getEventoRientroCensito() || this.schedaService.getEventoRientroNonCensito()) {
      generaleDimissioni = this.datiDimissioneRientro.getGeneraleRientroValue() ?? schedaCorrente.schedaGeneraleDimissioni;
    }

    let dataOraDecesso = generaleDimissioni.dataOraDecesso;
    let dataOraDimissione = generaleDimissioni.dataOraDimissione;

    if (typeof dataOraDecesso === 'object') { // non arriva dal be
      // se prendo da form e c'è solo l ora non salvo la data
      if ((dataOraDecesso as any)?.oraDecesso && !(dataOraDecesso as any)?.dataDecesso) {
        dataOraDecesso = null
      } else if ((dataOraDecesso as any)?.dataDecesso && !(dataOraDecesso as any)?.oraDecesso) {
        dataOraDecesso = moment((dataOraDecesso as any)?.dataDecesso).set({
          second: 0 //workaround per non far comparire il campo oraDecesso compilato se non è stato inserito
        }).valueOf()
      } else if ((dataOraDecesso as any)?.dataDecesso && (dataOraDecesso as any)?.oraDecesso) {
        dataOraDecesso = manageDataOraToSave((dataOraDecesso as any)?.dataDecesso, (dataOraDecesso as any)?.oraDecesso);
      } else if (!dataOraDecesso?.dataDecesso && !dataOraDecesso?.oraDecesso) {
        dataOraDecesso = null; // se non sono compilati entrambi i campi, setto a null
      }
    }

    if (typeof dataOraDimissione === 'object') { // non arriva dal be
      // data dimissione
      if ((dataOraDimissione as any)?.oraDimissione && !(dataOraDimissione as any)?.dataDimissione) {
        dataOraDimissione = null
      } else if ((dataOraDimissione as any)?.dataDimissione && !(dataOraDimissione as any)?.oraDimissione) {
        dataOraDimissione = moment((dataOraDimissione as any)?.dataDimissione).set({
          second: 0 //workaround per non far comparire il campo oraDimissione compilato se non è stato inserito
        }).valueOf()
      } else if ((dataOraDimissione as any)?.dataDimissione && (dataOraDimissione as any)?.oraDimissione) {
        dataOraDimissione = manageDataOraToSave((dataOraDimissione as any)?.dataDimissione, (dataOraDimissione as any)?.oraDimissione);
      } else if (!dataOraDimissione?.dataDimissione && !dataOraDimissione?.oraDimissione) {
        dataOraDimissione = null; // se non sono compilati entrambi i campi, setto a null
      }
    }

    generaleDimissioni.dataOraDimissione = dataOraDimissione
    generaleDimissioni.dataOraDecesso = dataOraDecesso

    return {
      schedaGeneraleDimissioni: generaleDimissioni,
      schedaValutazioneDimissione: this.datiDimissioneService.getValutazioneValue() ?? schedaCorrente.schedaValutazioneDimissione,
      schedaNecessitaAssistenzaDimissione: this.datiDimissioneService.getNecessitaAssistenzaValue() ?? schedaCorrente.schedaNecessitaAssistenzaDimissione,
      schedaInterventiEffettuatiDimissioni: this.datiDimissioneRientro.getInterventiEffettuatiRientroValue() ?? schedaCorrente.schedaInterventiEffettuatiDimissioni,
      schedaQuadroNeurologicoDimissioni: this.datiDimissioneService.getQuadroNeurologicoValue() ?? schedaCorrente.schedaQuadroNeurologicoDimissione,
      schedaComplicanzeDimissione: this.datiDimissioneService.getComplicanzeValue() ?? schedaCorrente.schedaComplicanzeDimissione,
      schedaQuadroFunzionaleDimissione: this.datiDimissioneService.getQuadroFunzionaleValue() ?? schedaCorrente.schedaQuadroFunzionaleDimissione,
      schedaAusiliDimissione: this.datiDimissioneService.getAusiliValue() ?? schedaCorrente.schedaAusiliDimissione,
      schedaAspettiSocioassistenzialiDimissione: this.datiDimissioneService.getSocioassistenzialiValue() ?? schedaCorrente.schedaAspettiSocioassistenzialiDimissione
    };
  }


  isEditOperation(): boolean {
    return !!this.schedaService.idSchedaSelectedSubject.getValue() || this.schedaService.isNewEvent.getValue();
  }

  openNewEvento() {
    this.schedaService.statusSchedaSelectedSubject.next(true);
    this.schedaService.setRientroSchedaSelectedSubject(this.schedaService.getSchedaCorrente().isRientro)
    this.router.navigate(['/scheda-ricovero/nuovo-evento' + (this.eventiService.getIsInEventiAssociati() ? '/eventi-associati' : '')], {
      state: {
        from: 'nuovo-evento/menu-azioni-scheda',
        inputScheda: {
          idScheda: this.schedaService.getSchedaCorrente().scheda.id,
          idPaziente: this.schedaService.getSchedaCorrente().idPaziente,
          idUnitaOperativa: this.schedaService.getSchedaCorrente().isRientro ? 3 : null,
          idTipoEvento: this.schedaService.getSchedaCorrente().isRientro
            ? TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento
            : TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento
        }
      }
    });
  }

  ngOnDestroy() {
    this.combineDatiCliniciAndDimissioniRientro?.unsubscribe();
    this.datiAnagrafici$?.unsubscribe();
    this.combineDatiCliniciAndDimissioni?.unsubscribe()
    this.combineDatiCliniciValid$?.unsubscribe()
    this.settRiabilitativo$?.unsubscribe();
    this.checkCodFisc$?.unsubscribe()
    this.destroy$.next();
    this.destroy$.complete();
  }
}
