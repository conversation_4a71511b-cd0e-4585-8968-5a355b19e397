<div class="menu-container">
    <div class="d-flex justify-content-between align-items-center p-3">
        <button mat-stroked-button class="btn-secondary" (click)="goBack()">
            Torna alla ricerca
        </button>
        @if (isCreateOperation()) {
        <button mat-raised-button color="primary" class="btn-primary" [disabled]="isSaveButtonDisabled"
            (click)="save()">
            Inserisci
        </button>
        }
        @if (isEditOperation() && shouldShowActions) {
            @if (showCloseSaveEventBtn) {
                <div>
                    <button mat-stroked-button class="btn-secondary btn-close"
                        [disabled]="isChiudiEventoButtonDisabled || isCronologiaOpened" (click)="chiudiEvento()">
                        Chiudi evento
                    </button>

                    <button mat-raised-button color="primary" class="btn-primary btn-save" (click)="salvaBozza()"
                        [disabled]="isCronologiaOpened">
                        <PERSON>va bozza
                    </button>
                </div>
            } 
        }
        @if (isEditOperation() && showNewEventBtn) {
            <button class="btn btn-primary" (click)="openNewEvento()">Nuovo evento</button>
        }
    </div>
</div>