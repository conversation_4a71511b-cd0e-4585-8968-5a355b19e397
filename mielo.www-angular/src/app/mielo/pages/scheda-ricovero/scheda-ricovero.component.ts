import { CommonModule, Location } from "@angular/common";
import { Component, OnDestroy, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { MatSidenav } from "@angular/material/sidenav";
import { ActivatedRoute, Router } from '@angular/router';
import moment from "moment";
import { map, Observable, of, Subscription, switchMap, tap } from 'rxjs';
import { MaterialModule } from "../../../core/material.module";
import { DialogSuccessService } from "../../../shared/components/dialog-success/dialog-success.service";
import {
  HeaderPatientInfoComponent
} from "../../../shared/components/menu/header-patient-info/header-patient-info.component";
import { ECategoriaEvento, EDescrizioneEvento, IdOperatoreEnum, StatoEventoEnum } from "../../../shared/enums/enum";
import { SceltaSchedaEnum } from "../../../shared/enums/scelta-scheda.enum";
import { TipoRicoveroEnum } from "../../../shared/enums/tipo-ricovero.enum";
import { TDatiPaziente } from "../../../shared/interfaces/dati-paziente.interface";
import {
  AggiungiSchedaRicoveroRequest,
  DizionarioModel,
  EventoModel,
  InputTipoScheda,
  NuovoEventoRicoveroModel,
  OutputSchedaPazienteRicoveroModel,
  SchedaRicoveroDatiMancanti,
  SchedaRicoveroModel,
  SchedaRicoveroReqModel
} from "../../../shared/interfaces/scheda-ricovero.interface";
import { DialogService } from "../../../shared/services/dialog.service";
import {
  STATO_ESTERO,
  TIPO_EVENTO_PRIMO_RICOVERO,
  TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO,
  TIPO_EVENTO_RIENTRO_CENSITO,
  TIPO_EVENTO_RIENTRO_NON_CENSITO
} from "../../../shared/utils/const";
import { createDomicilioObj, createResidenzaObj } from "../../../shared/utils/residenza.utils";
import { encodeToBase64 } from "../../../shared/utils/utils";
import { DatiCliniciComponent } from "../../components/dati/dati-clinici/dati-clinici.component";
import { DatiGeneraliComponent } from "../../components/dati/dati-generali/dati-generali.component";
import { DatiPazienteComponent } from "../../components/dati/dati-paziente/dati-paziente.component";
import { DimissioniComponent } from "../../components/dimissioni/dimissioni.component";
import { EventiAssociatiService } from "../../services/eventi-associati.service";
import { RicercaSchedaPazienteService } from "../../services/ricerca-scheda-paziente.service";
import { SchedaRicoveroService } from "../../services/scheda-ricovero.service";
import { MenuAzioniSchedaComponent } from "./menu-azioni-scheda/menu-azioni-scheda.component";
import { DecoderService } from "../../../shared/services/decoder.service";
import { OperatoreService } from "../../../core/services/operatore.service";
import { InfoOperatoreDTO } from "../../../core/interfaces/info-operatore.interface";

type TFromComponent = 'ricerca-scheda-paziente' | 'eventi-associati' | undefined;

@Component({
  selector: 'app-scheda-ricovero',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    HeaderPatientInfoComponent,
    DatiGeneraliComponent,
    MenuAzioniSchedaComponent,
    DatiPazienteComponent,
    DatiCliniciComponent,
    DimissioniComponent
  ],
  templateUrl: './scheda-ricovero.component.html',
  styleUrl: './scheda-ricovero.component.scss'
})
export class SchedaRicoveroComponent implements OnDestroy {

  protected readonly SceltaSchedaEnum = SceltaSchedaEnum;
  protected readonly TipoRicoveroEnum = TipoRicoveroEnum;
  protected readonly TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO = TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO;

  @ViewChild(MatSidenav) sidenav!: MatSidenav;

  menuItemSelected: SceltaSchedaEnum = SceltaSchedaEnum.GENERALI;
  idScheda: number | undefined
  datiGeneraliScheda: SchedaRicoveroReqModel;
  datiGenerali: SchedaRicoveroModel;

  rientroCensito = false;
  rientroNonCensito = false;

  datiPaziente: TDatiPaziente = {} as TDatiPaziente;
  menuItems = [SceltaSchedaEnum.GENERALI]
  tipoRicovero: string;
  private saveScheda$: Subscription;
  private schedaDettagliata$: Subscription;
  private routeParams$: Subscription;

  schedaRicoveroDatiMancanti: SchedaRicoveroDatiMancanti;

  isRientro = false;
  lesioni: DizionarioModel[];
  lesioneMielicaRientro: DizionarioModel;
  fromComponent: TFromComponent;
  fromMenuAzioniScheda: boolean = false;

  operatore: InfoOperatoreDTO;

  constructor(
    public schedaService: SchedaRicoveroService,
    private router: Router,
    private dialogService: DialogService,
    private ricercaService: RicercaSchedaPazienteService,
    private route: ActivatedRoute,
    private eventiAssociatiService: EventiAssociatiService,
    private successDialogService: DialogSuccessService,
    private location: Location,
    private decoderService: DecoderService,
    private operatoreService: OperatoreService
  ) {
    this.operatore = this.operatoreService.getOperatore();

    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras.state as {
      from?: string;
      inputScheda?: InputTipoScheda;
    };

    const isNuovoEvento = state?.from?.includes('nuovo-evento');
    this.fromMenuAzioniScheda = !!state?.from?.includes('menu-azioni-scheda');
    const inputScheda = state?.inputScheda;

    //this.handleRientro();

    if (isNuovoEvento && inputScheda) {
      this.schedaService.isNewEvent.next(true);

      // Notify components of selected paziente
      this.schedaService.idEditPatientSubject.next(inputScheda.idPaziente);
      this.schedaService.idSchedaSelectedSubject.next(inputScheda.idScheda);
      this.schedaService.schedaCreation$.next(null);

      this.schedaService.getSchedeNuovoEvento(inputScheda).pipe(
        map((output: SchedaRicoveroModel): SchedaRicoveroModel => ({
          ...output,
        })),
        switchMap((temporarySchedaData: SchedaRicoveroModel) =>
          this.runEditPipeline(of(temporarySchedaData))
        ),
        takeUntilDestroyed()
      ).subscribe();

      return;
    }

    this.schedaService.isNewEvent.next(false);
    // EDIT or CREATE mode
    this.schedaService.idSchedaSelectedSubject$.pipe(
      takeUntilDestroyed(),
      switchMap(id => {
        if (id) {
          this.idScheda = id;
          return this.runEditPipeline(this.schedaService.getSchedaById(id));
        }

        // CREATE mode
        this.schedaService.idEditPatientSubject.next(null);

        this.rientroNonCensito = this.schedaService.getEventoRientroNonCensito();

        this.menuItems.push(SceltaSchedaEnum.PAZIENTE);
        return of(null);
      })
    ).subscribe();
  }


  ngOnInit() {
    this.route.params.subscribe(params => {
      if (params['componentFrom']) {
        this.fromComponent = params['componentFrom'];
      } else {
        this.route.firstChild?.params.subscribe(childParams => {
          this.fromComponent = childParams['componentFrom'];
        });
      }
    });

    if (this.schedaService.isRientroSchedaSelectedSubject.value)
      this.isRientro = this.schedaService.isRientroSchedaSelectedSubject.value

    this.schedaService.schedaCreation$.next({
      ...this.schedaService.schedaCreation$.getValue(),
      evento: {
        stato: {
          idDizionario: StatoEventoEnum.IN_LAVORAZIONE,
          categoria: ECategoriaEvento.STATO_EVENTO,
          descrizione: EDescrizioneEvento["IN LAVORAZIONE"]
        },
        tipoEvento: this.isRientro ? TIPO_EVENTO_RIENTRO_NON_CENSITO : TIPO_EVENTO_PRIMO_RICOVERO
      }
    } as AggiungiSchedaRicoveroRequest);

    this.decoderService.getDcodStatoLesione().subscribe({
      next: (value) => {
        this.lesioni = value;
        const lesioneMielicaAccertata = value.find(el => el.idDizionario === 33);
        if (lesioneMielicaAccertata)
          this.lesioneMielicaRientro = lesioneMielicaAccertata;
      }
    })

  }

  get titoloScheda(): string {
    const tipoEvento = this.datiGenerali?.evento?.tipoEvento?.idTipoEvento ??
      this.schedaService.schedaCreation$?.getValue()?.evento?.tipoEvento?.idTipoEvento;

    switch (tipoEvento) {
      case 21: // idTipoEvento === 21
        return 'SCHEDA RICOVERO RIABILITATIVO';
      case 2:
        return 'SCHEDA RICOVERO';
      case 3:
        return 'SCHEDA PRIMO ACCESSO<br>Lesione pregressa';
      case 4:
        return 'SCHEDA RIENTRO';
      default:
        return '';
    }
  }

  handleRientro() {
    this.isRientro = this.eventiAssociatiService.getIsRientro();
    if (this.isRientro) {
      this.successDialogService.showSuccessDialog(`Rientro: ${this.isRientro}`)
    }
  }

  goBack(mantieniFiltri: boolean) {
    if (this.schedaService.getTipoOperazione() === 'edit-patient') {
      this.schedaService.setTipoOperazione('edit');
      this.schedaService.updateSchedaSelezionata(SceltaSchedaEnum.GENERALI);
    } else {
      this.pulisciStatoEVaiRicerca(mantieniFiltri);
    }
  }

  saveScheda() {
    let eventoBodyRequest = this.schedaService.schedaCreation$.getValue()?.evento;

    if (eventoBodyRequest && this.isRientro) {
      eventoBodyRequest.tipoEvento = TIPO_EVENTO_RIENTRO_NON_CENSITO
    }

    const request = {
      evento: eventoBodyRequest,
      scheda: {
        ...this.schedaService.schedaCreation$.getValue()?.scheda,
        lesioneNote: encodeToBase64(this.schedaService.schedaCreation$.getValue()?.scheda.lesioneNote || '')
      },
      paziente: {
        ...this.schedaService.schedaCreation$.getValue()?.paziente,
        dataNascita: moment(this.schedaService.schedaCreation$.getValue()?.paziente.dataNascita).format('YYYY-MM-DD'),
        dataRilascioTeam: moment.isMoment(this.schedaService.schedaCreation$.getValue()?.paziente.dataRilascioTeam) ? moment(this.schedaService.schedaCreation$.getValue()?.paziente.dataRilascioTeam).format('YYYY-MM-DD') : undefined,
        residenza: createResidenzaObj(this.schedaService.schedaCreation$.getValue()?.paziente),
        domicilio: createDomicilioObj(this.schedaService.schedaCreation$.getValue()?.paziente),
        genere: { idDizionario: this.schedaService.schedaCreation$.getValue()?.paziente.genere } as any,
        scolarita: this.schedaService.schedaCreation$.getValue()?.paziente?.scolarita && (this.schedaService.schedaCreation$.getValue()?.paziente?.scolarita as any) >= 0 ? { idDizionario: this.schedaService.schedaCreation$.getValue()?.paziente.scolarita } as any : null,
        comuneNascita: (this.schedaService.schedaCreation$.getValue()?.paziente as any).provinciaNascita?.cdProvinciaISTAT !== STATO_ESTERO.cdProvinciaISTAT ? this.schedaService.schedaCreation$.getValue()?.paziente.comuneNascita : null
      }
    } as AggiungiSchedaRicoveroRequest

    // rendo l oggetto request uguale al dto
    delete (request.paziente as any).eta
    delete (request.paziente as any).provinciaResidenza
    delete (request.paziente as any).comuneResidenza
    delete (request.paziente as any).indirizzoResidenza
    delete (request.paziente as any).numeroCivico
    delete (request.paziente as any).provinciaDomicilio
    delete (request.paziente as any).comuneDomicilio
    delete (request.paziente as any).indirizzoDomicilio
    delete (request.paziente as any).numeroCivicoDomicilio
    delete (request.paziente as any).provinciaNascita

    this.saveScheda$ = this.schedaService.checkCodiceFiscale(request.paziente).pipe(
      tap(res => {
        if (res) {
          this.saveSchedaFN(request).subscribe()
        } else {
          const dialogCheckCodFisc$ = this.dialogService.openConfirmDialog({
            tipo: 'warning',
            titolo: 'Attenzione',
            messaggio: 'Il Codice Fiscale non è coerente con i dati anagrafici inseriti.\nDesideri salvare lo stesso?',
            bottoni: [
              { testo: 'Continua le modifiche', azione: 'annulla' },
              { testo: 'Salva Scheda Paziente', azione: 'conferma' }
            ]
          })
            .pipe(
              tap(res => {
                dialogCheckCodFisc$.unsubscribe()
                if (res === 'conferma') {
                  this.saveSchedaFN(request).subscribe()
                }
              })
            )
            .subscribe()
        }
      }
      ))
      .subscribe()
  }

  chiudiEvento(schedaRicovero: OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel) {
    if (this.isRientro) {
      schedaRicovero.scheda.lesioneMielica = this.lesioneMielicaRientro;
    }
    const aggiornaScheda$ = this.schedaService.aggiornaSchedaRicovero(schedaRicovero, false)
      .pipe(
        tap(() => {
          const confirmDialog$ = this.dialogService.openConfirmDialog({
            messaggio: 'L\'evento è stato chiuso con successo.',
            titolo: 'Evento chiuso',
            bottoni: [
              { testo: 'Torna a Ricerca Scheda Paziente', azione: 'conferma' }
            ],
            tipo: 'success'
          })
            .pipe(
              tap(res => {
                if (res === 'conferma') {
                  this.pulisciStatoEVaiRicerca()
                }
                confirmDialog$.unsubscribe()
              })
            )
            .subscribe()

          aggiornaScheda$.unsubscribe()
        })
      )
      .subscribe()
  }

  salvaBozza(bozza: OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel) {
    if (this.isRientro) {
      bozza.scheda.lesioneMielica = this.lesioneMielicaRientro;
    }
    this.schedaService.aggiornaSchedaRicovero(bozza, true).pipe(
      switchMap(() => this.dialogService.datiRicoveroAggiornati()), // show dialog after setting missing data
      tap(res => {
        this.idScheda = this.schedaService.idSchedaSelectedSubject.getValue();
        if (res === 'conferma') {
          this.pulisciStatoEVaiRicerca(true);
        }
      })
    ).subscribe();
  }

  private pulisciStatoEVaiRicerca(mantieniFiltri: boolean = false) {
    this.ricercaService.initFiltersfromBack = mantieniFiltri;
    if (!mantieniFiltri) {
      this.ricercaService.resetAllFilters();
      const filters = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM ?
        {
          statoEvento: {
            "idDizionario": StatoEventoEnum.CHIUSO,
            "descrizione": "CHIUSO"
          }
        } : {};
      this.ricercaService.filtraSchede(filters);
    }

    this.router.navigate(['/']);
  }

  updateMenuItems(items: SceltaSchedaEnum[]) {
    this.menuItems = items;
  }

  saveSchedaFN(request: AggiungiSchedaRicoveroRequest) {
    return this.schedaService.aggiungiSchedaRicovero(request).pipe(
      tap(res => {
        this.schedaService.schedaCreation$.next(null)
        const defaultValid = {
          valid: false,
          pristine: true
        }
        this.schedaService._areFormsCreateSchedaValidSubject$.next({
          formDatiAnagrafici: defaultValid,
          formDatiGenerali: defaultValid,
          formDatiSocioEconomici: defaultValid
        })

        const succDialog$ = this.dialogService.schedaSalvataConSuccesso(res).pipe(
          tap(res => {
            succDialog$.unsubscribe()
            if (res === 'conferma') {
              this.ricercaService.disabilitaNuovaSegn.next(true)
              this.pulisciStatoEVaiRicerca(false)
            }
          })
        ).subscribe()
      })
    )
  }

  onBackParent = () => {
    if (this.fromComponent === 'eventi-associati') {
      if (window.history.length > 2 && this.fromMenuAzioniScheda) {
        return window.history.go(-2);
      } else {
        return this.location.back()
      }
    }
    this.router.navigate(['/'])
  }

  get repartoEnum(): TipoRicoveroEnum | undefined {
    const id = this.datiGeneraliScheda?.unitaOperativa?.idUnitaOperativa;
    return Object.values(TipoRicoveroEnum).includes(id) ? id as TipoRicoveroEnum : undefined;
  }

  private runEditPipeline(scheda$: Observable<SchedaRicoveroModel>): Observable<null> {
    return scheda$.pipe(
      tap(schedaData => {
        this.schedaService.idEditPatientSubject.next(schedaData.idPaziente);
        this.schedaService.setSchedaDettagliata(schedaData);
        this.schedaService.setSchedaCorrente(schedaData);
        this.schedaService.setDatiCliniciPaziente(schedaData);
        this.rientroCensito = schedaData.evento.tipoEvento?.idTipoEvento === TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento;
        this.schedaService.setEventoRientroCensito(this.rientroCensito);
        this.rientroNonCensito = schedaData.evento.tipoEvento?.idTipoEvento === TIPO_EVENTO_RIENTRO_NON_CENSITO.idTipoEvento;
        this.schedaService.setEventoRientroNonCensito(this.rientroNonCensito);
        this.schedaService.setDatiDimissione(schedaData);

        this.menuItems.push(SceltaSchedaEnum.CLINICI);
        this.datiGeneraliScheda = schedaData.scheda;
        this.datiGenerali = schedaData;
        this.datiPaziente = {
          datiSocioEconomiciMancanti: schedaData.datiSocioEconomiciMancanti,
          codicePaziente: schedaData.codicePaziente,
          cognomePaziente: schedaData.cognomePaziente,
          dataNascitaPaziente: schedaData.dataNascitaPaziente,
          nomePaziente: schedaData.nomePaziente
        };

        this.schedaService.setDatiMancantiSocioEconomici(schedaData.datiSocioEconomiciMancanti);

        //reparto SPINALE & RIABILITATIVO schedaSettingRiabilitativo è null allora inserisce il Menu DIMISSIONI
        const settRiab = schedaData.datiClinici?.schedaSettingRiabilitativo?.trasferimentoSettingRiabilitativo
        const rientroNonCensito = this.schedaService.getEventoRientroNonCensito();
        const rientroCensito = this.schedaService.getEventoRientroCensito();
        if (settRiab === false || schedaData.scheda?.unitaOperativa?.idUnitaOperativa !== TipoRicoveroEnum.ACUTI || rientroCensito || rientroNonCensito) {
          this.menuItems.push(SceltaSchedaEnum.DIMISSIONI);
        }
      }),

      switchMap(schedaData => {
        if (this.schedaService.isNewEvent.getValue() || schedaData.evento.stato.idDizionario === StatoEventoEnum.CHIUSO) {
          // Skip getting missing fields in new event mode 'cause no form exists yet in db and when scheda is closed
          return of(null);
        }
        return this.schedaService.getCampiObbligatoriMancanti(schedaData.scheda, schedaData.evento).pipe(
          tap(schedaRicoveroDatiMancanti => {
            this.schedaService.datiMenuMancanti = schedaRicoveroDatiMancanti;
            this.schedaRicoveroDatiMancanti = schedaRicoveroDatiMancanti;
          }),
          map(() => null)
        );
      })
    );
  }

  shouldShowActions(): boolean {

    const struttOperatEvento = this.datiGenerali?.codPresidioOspedaliero;
    // se evento è in lavorazione e l operatore loggato non è del presidio con cui è stato aperto l evento
    if (this.datiGenerali?.evento?.stato?.idDizionario === StatoEventoEnum.IN_LAVORAZIONE &&
        this.operatore.codLivello2Operatore !== struttOperatEvento){
      return false;
    }
    const isNextEventRientro = this.schedaService.getSchedaCorrente()?.isRientro;
    // TODO: Valutare se rimuovere il controllo
    const isRientro = this.rientroCensito || this.rientroNonCensito;
    const isSameStruttura = this.operatore.codLivello2Operatore === struttOperatEvento || !struttOperatEvento
    
    return isNextEventRientro || isSameStruttura || isRientro;
  }

  ngOnDestroy() {
    this.routeParams$?.unsubscribe();
    this.schedaService.isCittadinoIdentificatoSubject.next(false)
    this.saveScheda$?.unsubscribe()
    this.schedaDettagliata$?.unsubscribe()
  }

}
