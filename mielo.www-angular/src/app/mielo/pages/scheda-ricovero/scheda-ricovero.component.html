<!-- Header menu modalità modifica -->
<div>
  <app-header-patient-info [schedaRicovero]="datiGeneraliScheda" [datiPaziente]="datiPaziente"
    [onBackParent]="onBackParent" *ngIf="idScheda || schedaService.isNewEvent.getValue()">
  </app-header-patient-info>
</div>

<div class="d-flex w-100">
  <div class="schede-sidenav">
    <div class="menu-box menu-sticky">
      <div
        [style.padding-top]="repartoEnum === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento ? null : '34px !important'"
        class="font-weight-medium f-20 d-flex margin-scheda">
        <svg class="icon mr-2 mb-1">
          <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-inbox"></use>
        </svg>
        <span [innerHTML]="titoloScheda"></span>
      </div>

      <div *ngFor="let item of menuItems" [ngClass]="item === menuItemSelected ?
            'selected menu-section font-weight-medium': ' menu-section font-weight-medium'"
        (click)="menuItemSelected =  item">
        <div class="d-flex justify-content-between">
          {{ item }}
          <svg class="icon mr-2 mb-1 light-grey">
            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-chevron-right"></use>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <div class="content-area flex-grow-1">
    <!-- Menu azioni -->
    <div class="azioni-sticky">
      <app-menu-azioni-scheda
        [shouldShowActions]="shouldShowActions()"
        (onCreateNewEvent)="saveScheda()"
        (onGoBack)="goBack(false)"
        [schedaRicoveroDatiMancanti]="schedaRicoveroDatiMancanti"
        (onChiudiEvento)="chiudiEvento($event)"
        (onSalvaBozza)="salvaBozza($event)">
      </app-menu-azioni-scheda>
    </div>

    <ng-container [ngSwitch]="menuItemSelected">
      <ng-container *ngSwitchDefault>
        <app-dati-generali [datiGenerali]="datiGenerali" [eventoRientroCensito]="rientroCensito" [eventoRientroNonCensito]="rientroNonCensito"
          [lesioni]="lesioni" />
      </ng-container>

      <ng-container *ngSwitchCase="SceltaSchedaEnum.PAZIENTE">
        <app-dati-paziente />
      </ng-container>

      <ng-container *ngSwitchCase="SceltaSchedaEnum.CLINICI">
        <app-dati-clinici (menuItemsChange)="updateMenuItems($event)" />
      </ng-container>

      <ng-container *ngSwitchCase="SceltaSchedaEnum.DIMISSIONI">
        <app-dimissioni [repartoType]="repartoEnum" />
      </ng-container>
    </ng-container>

  </div>
</div>