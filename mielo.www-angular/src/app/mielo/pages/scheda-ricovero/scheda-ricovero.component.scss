.schede-container {
  position: relative;
  height: calc(100vh - 64px);
  /* Sottrai l'altezza dell'header */
  margin-top: 64px;
  /* Aggiungi margine superiore pari all'altezza dell'header */
  left: 0;
  right: 0;
  height: 100vh !important;
}

.schede-sidenav {
  width: 320px !important;
  min-width: 320px !important;
  max-width: 320px !important;
  border-right: 1px solid #0000001f;
  flex-shrink: 0 !important;
}

:host-context(.edit-mode) .schede-sidenav {
  height: calc(100vh - 212px);
}

.content-area {
  background-color: white;
  padding: 0 16px;
}


.menu-box {
  height: max-content;
}

.margin-scheda {
  padding-top: 10px;
  padding-bottom: 20px;
  padding-left: 30px;
  border-bottom: 1px solid #0000001f;
}

.menu-section {
  padding-top: 15px;
  padding-bottom: 15px;
  padding-left: 40px;
  border-bottom: 1px solid #0000001f;
  color: #2A7A38;
  background-color: #FFFFFF;
  cursor: pointer;
}

.selected {
  background-color: #EEF3F6 !important;
  border-top: 5px solid #2A7A38 !important;
}

.light-grey {
  fill: #BFC2C8;
}

.d-flex {
  display: flex;
}

.flex-grow-1 {
  flex-grow: 1;
}

.azioni-sticky {
  position: sticky;
  top: 152px;
  z-index: 1000; // oppure più alto se ci sono altri elementi sopra
  background-color: white; // o colore coerente con il tema
  padding-top: .8rem;
}

.menu-sticky {
  position: sticky;
  top: 152px;;
  z-index: 1000; // oppure più alto se ci sono altri elementi sopra
  background-color: white; // o colore coerente con il tema
}