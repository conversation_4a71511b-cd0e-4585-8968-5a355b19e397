import { CommonModule } from "@angular/common";
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription, takeUntil, timer } from "rxjs";
import { MaterialModule } from "../../../core/material.module";
import { DecoderService } from "../../../shared/services/decoder.service";
import { ElaboraEstrazioniComponent } from "../../components/elabora-estrazioni/elabora-estrazioni.component";
import { FiltroElaborazioniComponent } from "../../components/filtro-elaborazioni/filtro-elaborazioni.component";
import { TabellaEstrazioniComponent } from "../../components/tabella-estrazioni/tabella-estrazioni.component";
import { MetadatiReportDTO } from "../../interfaces/metadati-report-dto.interface";
import { EstrazioniService } from "../../services/estrazioni.service";

@Component({
  selector: 'app-estrazioni',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    ElaboraEstrazioniComponent,
    FiltroElaborazioniComponent,
    TabellaEstrazioniComponent
  ],
  templateUrl: './estrazioni.component.html',
  styleUrl: './estrazioni.component.scss'
})
export class EstrazioniComponent implements OnInit, OnDestroy {
  dataSource: MetadatiReportDTO[] = [];
  reportInElaborazione: number = 0;
  configEstrazioni: string = '';

  private destroy$ = new Subject<void>();

  private refreshSubscription: Subscription | null = null;

  constructor(
    private decoderService: DecoderService,
    private estrazioniService: EstrazioniService,
    private router: Router
  ) { }

  ngOnInit() {
    //messo perchè altrimenti non si vede il filtro visto che visualizza da metà pagina nel caso da eliminare
    window.scrollTo({ top: 0, behavior: 'auto' });
    this.loadConfigEstrazioni();
    this.estrazioniService.resetAllFilters();

    this.estrazioniService.resultEstrazioni
      .pipe(takeUntil(this.destroy$))
      .subscribe(estrazioni => {
        this.dataSource = this.mapBackendData(estrazioni);
      });

    this.decoderService.reportInElaborazione$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.reportInElaborazione = count;
      });  
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }

  private mapBackendData(data: any[]): MetadatiReportDTO[] {
    return data.map(item => ({
      ...item,
      dataOraElaborazione: item.dataOraElaborazione ? new Date(item.dataOraElaborazione).toISOString() : null,
      dataMin: item.dataMin ? new Date(item.dataMin).toISOString() : null,
      dataMax: item.dataMax ? new Date(item.dataMax).toISOString() : null,
      tipoEvento: item.tipoEvento ?? null
    }));
  }

  private loadEstrazioni() {
    const requestBody = this.estrazioniService.buildRequestBody(this.estrazioniService.currentPage);
    this.estrazioniService.getEstrazioni(requestBody)
      .pipe(takeUntil(this.destroy$)).subscribe()
  }

  private loadReportInElaborazione() {
    this.decoderService.aggiornaReportInElaborazione();
  }

  //tempo in millisecondi per il refresh
  private loadConfigEstrazioni() {
    this.decoderService.getConfigEstrazioni()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (config) => {
          this.configEstrazioni = config;
          this.startRefresh();
        }
      });
  }

  private startRefresh() {
    const REFRESH_INTERVAL = Number(this.configEstrazioni);

    // Cancella un polling precedente, se esiste
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }

    this.refreshSubscription = timer(0, REFRESH_INTERVAL)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadEstrazioni();
        this.loadReportInElaborazione();
      });
  }

  navigateToRicerca() {
    this.router.navigate(['/']);
  }
} 