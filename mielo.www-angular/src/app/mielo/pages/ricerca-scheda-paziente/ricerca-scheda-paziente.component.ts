import { CommonModule } from "@angular/common";
import { Component, NgZone, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { Subject, takeUntil } from "rxjs";
import { MaterialModule } from "../../../core/material.module";
import { SchedaPaziente } from "../../../models/scheda-paziente.model";
import { ModaleNuovaSegnalazioneComponent } from "../../../shared/modals/modale-nuova-segnalazione/modale-nuova-segnalazione.component";
import { DecoderService } from "../../../shared/services/decoder.service";
import { FiltroRicercaSchedaComponent } from "../../components/filtro-ricerca-scheda/filtro-ricerca-scheda.component";
import { TabellaRicercaSchedaComponent } from "../../components/tabella-ricerca-scheda/tabella-ricerca-scheda.component";
import { RicercaSchedaPazienteService } from "../../services/ricerca-scheda-paziente.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { InfoOperatoreDTO } from "../../../core/interfaces/info-operatore.interface";
import { OperatoreService } from "../../../core/services/operatore.service";
import { IdOperatoreEnum, StatoEventoEnum } from "../../../shared/enums/enum";

@Component({
  selector: 'app-ricerca-scheda-paziente',
  standalone: true,
  imports: [FiltroRicercaSchedaComponent, MaterialModule, CommonModule, TabellaRicercaSchedaComponent, ModaleNuovaSegnalazioneComponent],
  templateUrl: './ricerca-scheda-paziente.component.html',
  styleUrl: './ricerca-scheda-paziente.component.scss'
})
export class RicercaSchedaPazienteComponent implements OnInit, OnDestroy {
  disabilitaNuovaSegn = true
  dataSource: SchedaPaziente[] = [];

  private destroy$ = new Subject<void>();

  operatore: InfoOperatoreDTO;
  catOperatore = IdOperatoreEnum;

  showEstrazioniBtn = false;

  constructor(
    private dialog: MatDialog,
    public ricercaSchedaService: RicercaSchedaPazienteService,
    private decoderService: DecoderService,
    private operatoreService: OperatoreService,
    private router: Router
  ) {
    this.operatore = this.operatoreService.getOperatore();
    this.showEstrazioniBtn = this.operatore.idRuolo !== IdOperatoreEnum.PS;

    this.ricercaSchedaService.disabilitaNuovaSegn$
      .pipe(
        takeUntilDestroyed(),
      )
      .subscribe((value) => {
        this.disabilitaNuovaSegn = value;
      });

    this.ricercaSchedaService.resultSchedePaziente
      .pipe(takeUntil(this.destroy$))
      .subscribe((schede) => {
        this.dataSource = schede;
      });
  }

  ngOnInit() {
    this.loadDecoders();
    const navigation = this.router.getCurrentNavigation();

    if (navigation?.extras?.state?.['resetFilters']) {
      this.resetAndLoadData();
    } else if (navigation?.extras?.state?.['skipInitialLoad']) {
      // Non facciamo nulla, evitando così la doppia chiamata
    } else {
      this.visualizzaSchede(0);
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadDecoders() {
    this.decoderService.getDcodStatoLesione().subscribe();
    this.decoderService.getDcodStatoEvento().subscribe();
  }

  visualizzaSchede(pageNumber: number) {
    const body = this.ricercaSchedaService.buildBodyVisualizzaSchede(pageNumber);
    if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM && !body.statoEvento) {
      body.statoEvento = {
        "idDizionario": StatoEventoEnum.CHIUSO,
        "descrizione": "CHIUSO"
      };
    }

    this.ricercaSchedaService.getVisualizzaSchedaPaziente(body)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        error: (err) => {
          console.error('Errore durante il caricamento dei dati:', err);
        }
      });
  }

  openNuovaSegnalazione() {
    const dialogRef = this.dialog.open(ModaleNuovaSegnalazioneComponent, {
      width: '400px',
      autoFocus: false,
      ariaLabel: 'Nuova segnalazione',
      role: 'dialog',
      hasBackdrop: true,
      panelClass: ['accessible-modal'],
      ariaDescribedBy: 'modal-description'
    });
  }

  resetAndLoadData() {
    this.ricercaSchedaService.resetAllFilters();
    this.visualizzaSchede(0);
  }
}
