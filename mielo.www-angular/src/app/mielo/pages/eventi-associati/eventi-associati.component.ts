import { CommonModule } from "@angular/common";
import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from "@angular/router";
import { Subscription } from "rxjs";
import { filter, map } from "rxjs/operators";
import { MaterialModule } from "../../../core/material.module";
import { IEventiAssociatiReq, IEventiResponse, IEventoAssociato } from "../../../models/eventi-associati.model";
import {
  GenericPaginatedTableComponent
} from "../../../shared/components/generic-paginated-table/generic-paginated-table.component";
import {
  HeaderPatientInfoComponent
} from "../../../shared/components/menu/header-patient-info/header-patient-info.component";
import { IdOperatoreEnum, StatoEventoEnum } from "../../../shared/enums/enum";
import { TDatiPaziente } from "../../../shared/interfaces/dati-paziente.interface";
import { TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO, TIPO_EVENTO_RIENTRO_CENSITO } from "../../../shared/utils/const";
import { formatDate } from "../../../shared/utils/utils";
import { EventiAssociatiService } from "../../services/eventi-associati.service";
import { SchedaRicoveroService } from "../../services/scheda-ricovero.service";
import { OperatoreService } from "../../../core/services/operatore.service";

@Component({
  selector: 'app-eventi-associati',
  standalone: true,
  imports: [MaterialModule, CommonModule, GenericPaginatedTableComponent, HeaderPatientInfoComponent, RouterOutlet],
  templateUrl: './eventi-associati.component.html',
  styleUrl: './eventi-associati.component.scss'
})
export class EventiAssociatiComponent implements OnInit, OnDestroy {
  itemsPerPage = 6
  idPaziente: number;
  idScheda?: number;
  eventiAssociati: IEventoAssociato[] = []
  columns = [
    {
      key: 'tipoEvento.descrizione',
      label: 'Tipologia Evento'
    },
    {
      key: 'idNosologico',
      label: 'ID nosologico'
    },
    {
      key: 'dataRicovero',
      label: 'Data ricovero'
    },
    {
      key: 'nome',
      label: 'Nome'
    },
    {
      key: 'cognome',
      label: 'Cognome'
    },
    {
      key: 'codiceIdentificativo',
      label: 'Codice identificativo'
    },
    {
      key: 'presidioMedicoCompilatore',
      label: 'Struttura'
    },
    {
      key: 'medicoCompilatore',
      label: 'Medico compilatore'
    },
    {
      key: 'statoEvento.descrizione',
      label: 'Stato'
    },
    {
      key: 'actions',
      label: 'Azioni'
    },
  ]
  eventiAssociatiRes: IEventiResponse;
  currentPage = 0;
  isChildRouteActive = false;
  router$: Subscription;
  datiPaziente: TDatiPaziente & { idPaziente: number };
  tableActions = [(idScheda: number) => {
    // nuovo ev abilitato se: se mi trovo su un evento chiuso, se non ci sono altri ev in lavoraz, e se è l ultimo ev
    const isEventoSelezionatoChiuso = this.eventiAssociati.find((el) => el.idScheda === idScheda)?.statoEvento.idDizionario === StatoEventoEnum.CHIUSO
    const areEventsInLavorazione = this.eventiAssociati.filter((el) => el.statoEvento.idDizionario === StatoEventoEnum.IN_LAVORAZIONE).length
    const isLastEventoSelezionato = this.eventiAssociati.sort((a,b)   => b.idScheda - a.idScheda)[0].idScheda === idScheda

    this.schedaService.setIsLastEvent(isEventoSelezionatoChiuso && !areEventsInLavorazione && isLastEventoSelezionato);

    this.schedaService.idSchedaSelectedSubject.next(idScheda)
    this.router.navigate(['/scheda-ricovero/details/eventi-associati'])
  }]

  latestEvent: IEventoAssociato | null = null;

  newEventVisible: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private eventiService: EventiAssociatiService,
    public router: Router,
    private schedaService: SchedaRicoveroService,
    private operatoreService: OperatoreService
  ) {
    const navigation = router.getCurrentNavigation();
    const state = navigation?.extras.state;
    this.datiPaziente = state?.["datiPaziente"];

    this.router$ = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.isChildRouteActive = this.route.firstChild?.snapshot.url[0]?.path === 'new';
      });

    this.newEventVisible = this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.OSP;
  }


  ngOnInit() {
    this.schedaService.schedaCreation$.next(null)
    this.idPaziente = this.schedaService.getSchedaCorrente().idPaziente
    this.schedaService.idEditPatientSubject.next(this.idPaziente);
    this.idScheda = this.schedaService.idSchedaSelectedSubject.getValue();
    this.getEventiAssociati({ idScheda: this.idScheda!, pageNumber: this.currentPage, pageSize: this.itemsPerPage });
    this.eventiService.setIsInEventiAssociati(true);
  }

  onPageChanged = (pageNumber: number) => {
    this.currentPage = pageNumber;
    this.getEventiAssociati({ idScheda: this.idScheda!, pageNumber, pageSize: this.itemsPerPage })
  }

  getEventiAssociati(req: IEventiAssociatiReq) {
    this.eventiService.getEventiAssociatiService(req).pipe(
      map(eventiAssociatiResp => {
        this.eventiAssociati = eventiAssociatiResp.content.map(evento => ({
          ...evento,
          dataRicovero: formatDate(evento.dataRicovero)
        }
        )) as unknown as IEventoAssociato[]
        this.eventiAssociatiRes = eventiAssociatiResp
        this.eventiService.changeIsRientro(this.eventiAssociati[0].isRientro)
        this.latestEvent = this.eventiAssociati
          .filter(e => !!e.dataRicovero)
          .sort((a, b) => new Date(b.dataRicovero).getTime() - new Date(a.dataRicovero).getTime())[0];
      })
    )
      .subscribe()
  }

  openNewEvento() {
    this.schedaService.statusSchedaSelectedSubject.next(true);
    this.router.navigate(['/scheda-ricovero/nuovo-evento/eventi-associati'], {
      state: {
        from: 'nuovo-evento',
        inputScheda: {
          idPaziente: this.idPaziente,
          idScheda: this.idScheda,
          // if event is rientro I set a default unita operativa 
          // because it does not matter for the BE as long as it is not null
          idUnitaOperativa: this.latestEvent?.isRientro ? 3 : null,
          idTipoEvento: this.latestEvent?.isRientro
            ? TIPO_EVENTO_RIENTRO_CENSITO.idTipoEvento
            : TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento
        }
      }
    });
  }

  isNuovoEventoButtonDisabled(): boolean {
    return this.eventiAssociati.some(e => e.statoEvento?.idDizionario !== StatoEventoEnum.CHIUSO);
  }

  ngOnDestroy() {
    this.router$.unsubscribe();
  }

}
