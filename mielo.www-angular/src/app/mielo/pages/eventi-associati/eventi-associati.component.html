<div>
    <app-header-patient-info
            [datiPaziente]="datiPaziente"
    />
    <router-outlet></router-outlet>

    <div class="m-5" *ngIf="!isChildRouteActive">
        <div class="row justify-content-between mt-title align-items-center">
            <h6 class="ml-2">EVENTI ASSOCIATI</h6>
            <div class="d-flex" *ngIf="newEventVisible">
                <button class="btn btn-primary"
                        (click)="openNewEvento()"
                        [disabled]="isNuovoEventoButtonDisabled()">Nuovo evento
                </button>
            </div>
        </div>

        <div class="mt-3">
            <app-generic-table
                    [actions]="tableActions"
                    [onPageChanged]="onPageChanged"
                    [dataSource]="eventiAssociati"
                    [totalItems]="eventiAssociatiRes ? eventiAssociatiRes.totalElements : 0"
                    [currentPage]="currentPage"
                    [itemsPerPage]="itemsPerPage"
                    [columns]="columns"
            ></app-generic-table>
        </div>

    </div>
</div>
