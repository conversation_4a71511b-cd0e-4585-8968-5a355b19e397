import {Component, OnInit, Type} from '@angular/core';
import {CommonModule} from "@angular/common";
import {Menu, MenuListInterface} from "../../../classes/menu.class";
import {MenuAzioniSchedaComponent} from "../../scheda-ricovero/menu-azioni-scheda/menu-azioni-scheda.component";
import {SceltaSchedaEnum} from "../../../../shared/enums/scelta-scheda.enum";
import {SchedaRicoveroService} from "../../../services/scheda-ricovero.service";
import {MaterialModule} from "../../../../core/material.module";
import {DimissioniComponent} from "../../../components/dimissioni/dimissioni.component";
import {EventiAssociatiService} from "../../../services/eventi-associati.service";
import {DialogSuccessService} from "../../../../shared/components/dialog-success/dialog-success.service";
import {ActivatedRoute, Router} from "@angular/router";

@Component({
  selector: 'app-new-evento',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    MenuAzioniSchedaComponent
  ],
  templateUrl: './new-evento.component.html',
  styleUrls: ['./new-evento.component.scss', './../../scheda-ricovero/scheda-ricovero.component.scss']
})
export class NewEventoComponent implements OnInit {
  menuElements: Array<MenuListInterface>;
  selectedComponent: Type<any> | null = null;
  isRientro: boolean;

  constructor(
    public schedaService: SchedaRicoveroService,
    private eventiAssociatiService: EventiAssociatiService,
    private succDialServ: DialogSuccessService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.schedaService.setTipoOperazione('edit');
  }

  ngOnInit() {
    this.menuElements = Menu.buildMenuEdit();
    this.menuElements.push(
        {
          title: 'Dimissione',
          principal: false,
          tipoScheda: SceltaSchedaEnum.DIMISSIONI,
          hidden: false,
          componente: DimissioniComponent,
        },
    )

    this.isRientro = this.eventiAssociatiService.getIsRientro();
    if (this.isRientro) {
      this.succDialServ.showSuccessDialog(`Rientro: ${this.isRientro}`)
    }
    this.selectScheda(SceltaSchedaEnum.GENERALI);
  }

  selectScheda(tipo: SceltaSchedaEnum) {
    const selectedMenu = this.menuElements.find(item => item.tipoScheda === tipo);
    this.schedaService.updateSchedaSelezionata(tipo);
    this.selectedComponent = selectedMenu ? selectedMenu.componente : null;
  }

  onGoBack() {
    this.router.navigate(['../'], { relativeTo: this.route });
  }
}
