<div class="d-flex w-100">
    <div class="d-flex w-100">
        <div class="schede-sidenav">
            <div class="menu-box mt-4">
                <div class="font-weight-medium f-20 d-flex margin-scheda" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1215686275)">
                    <svg class="icon mr-2 mb-1">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-inbox"></use>
                    </svg>
                    SCHEDA {{isRientro ? 'RIENTRO' : 'RICOVERO'}}
                </div>
                <div *ngFor="let item of menuElements"
                     [ngClass]="item.tipoScheda == schedaService.schedaSelezionata ? 'selected menu-section font-weight-medium': ' menu-section font-weight-medium'"
                     (click)="selectScheda(item.tipoScheda)">
                    <div class="d-flex justify-content-between">
                        {{ item.title }}
                        <svg class="icon mr-2 mb-1 light-grey">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-chevron-right"></use>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-area flex-grow-1">
            <!-- Menu azioni -->
            <app-menu-azioni-scheda
                    (onCreateNewEvent)="null"
                    (onGoBack)="onGoBack()"
                    (onSalvaBozza)="null">
            </app-menu-azioni-scheda>

            <ng-container *ngIf="selectedComponent">
                <ng-container *ngComponentOutlet="selectedComponent"></ng-container>
            </ng-container>
        </div>
    </div>
</div>
