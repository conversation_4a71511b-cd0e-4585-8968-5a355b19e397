import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DatiAnagrafici, DatiSocioEconomici } from '../../shared/interfaces/dati-paziente.interface';
import { TipoOperazione } from './scheda-ricovero.service';

@Injectable({
  providedIn: 'root'
})
export class DatiStorageService {
  // Form data BehaviorSubjects
  private datiAnagrafici$ = new BehaviorSubject<DatiAnagrafici | null>(null);
  private datiSocioEconomici$ = new BehaviorSubject<DatiSocioEconomici | null>(null);
  private datiEziologia$ = new BehaviorSubject<any | null>(null);
  private datiLesioneTrattamento$ = new BehaviorSubject<any | null>(null);
  private datiNecessitaAssistenziali$ = new BehaviorSubject<any | null>(null);
  private datiQuadroNeurologico$ = new BehaviorSubject<any | null>(null);
  private datiSettingRiabilitativo$ = new BehaviorSubject<any | null>(null);

  // Scheda BehaviorSubjects
  private schedaDettagliata$ = new BehaviorSubject<any>(null);
  private ultimoIdScheda$ = new BehaviorSubject<number | null>(null);

  // Tipo operazione
  private tipoOperazione$ = new BehaviorSubject<TipoOperazione | null>(null);

  // Stato del cittadino
  private cittadinoIdentificato$ = new BehaviorSubject<boolean>(false);

  // Aggiungi un soggetto per i campi disabilitati
  private campiDisabilitati$ = new BehaviorSubject<string[]>([]);

  // Stato validità form
  private datiGeneraliValido$ = new BehaviorSubject<boolean>(false);
  private datiAnagraficiValido$ = new BehaviorSubject<boolean>(false);
  private datiSocioEconomiciValido$ = new BehaviorSubject<boolean>(false);

  constructor() { }


  // Validità form dati generali
  getDatiGeneraliValido$(): Observable<boolean> {
    return this.datiGeneraliValido$.asObservable();
  }

  setDatiGeneraliValidoSubject(isValid: boolean): void {
    this.datiGeneraliValido$.next(isValid);
  }

  // Dati Anagrafici
  getDatiAnagrafici$(): Observable<DatiAnagrafici | null> {
    return this.datiAnagrafici$.asObservable();
  }

  getDatiAnagraficiValue(): DatiAnagrafici | null {
    return this.datiAnagrafici$.getValue();
  }

  saveDatiAnagrafici(dati: DatiAnagrafici | null): void {
    this.datiAnagrafici$.next(dati);
  }

  // Validità form dati anagrafici
  getDatiAnagraficiValido$(): Observable<boolean> {
    return this.datiAnagraficiValido$.asObservable();
  }

  setDatiAnagraficiValidoSubject(isValid: boolean): void {
    this.datiAnagraficiValido$.next(isValid);
  }

  // Dati Socio-Economici
  getDatiSocioEconomici$(): Observable<DatiSocioEconomici | null> {
    return this.datiSocioEconomici$.asObservable();
  }

  getDatiSocioEconomiciValue(): DatiSocioEconomici | null {
    return this.datiSocioEconomici$.getValue();
  }

  saveDatiSocioEconomici(dati: DatiSocioEconomici | null): void {
    this.datiSocioEconomici$.next(dati);
  }

  // Validità form dati socio-economici
  getDatiSocioEconomiciValido$(): Observable<boolean> {
    return this.datiSocioEconomiciValido$.asObservable();
  }

  setDatiSocioEconomiciValidoSubject(isValid: boolean): void {
    this.datiSocioEconomiciValido$.next(isValid);
  }

  // Tipo Operazione
  getTipoOperazione$(): Observable<TipoOperazione | null> {
    return this.tipoOperazione$.asObservable();
  }

  getTipoOperazioneValue(): TipoOperazione | null {
    return this.tipoOperazione$.getValue();
  }

  setTipoOperazione(tipo: TipoOperazione | null): void {
    this.tipoOperazione$.next(tipo);
  }

  clearTipoOperazione(): void {
    this.tipoOperazione$.next(null);
  }

  // Dati Eziologia
  getDatiEziologia$(): Observable<any | null> {
    return this.datiEziologia$.asObservable();
  }

  getDatiEziologiaValue(): any | null {
    return this.datiEziologia$.getValue();
  }

  saveDatiEziologia(dati: any | null): void {
    this.datiEziologia$.next(dati);
  }

  // Dati Lesione Trattamento
  getDatiLesioneTrattamento$(): Observable<any | null> {
    return this.datiLesioneTrattamento$.asObservable();
  }

  getDatiLesioneTrattamentoValue(): any | null {
    return this.datiLesioneTrattamento$.getValue();
  }

  saveDatiLesioneTrattamento(dati: any | null): void {
    this.datiLesioneTrattamento$.next(dati);
  }

  // Dati Necessità Assistenziali
  getDatiNecessitaAssistenziali$(): Observable<any | null> {
    return this.datiNecessitaAssistenziali$.asObservable();
  }

  getDatiNecessitaAssistenzialiValue(): any | null {
    return this.datiNecessitaAssistenziali$.getValue();
  }

  saveDatiNecessitaAssistenziali(dati: any | null): void {
    this.datiNecessitaAssistenziali$.next(dati);
  }

  // Dati Quadro Neurologico
  getDatiQuadroNeurologico$(): Observable<any | null> {
    return this.datiQuadroNeurologico$.asObservable();
  }

  getDatiQuadroNeurologicoValue(): any | null {
    return this.datiQuadroNeurologico$.getValue();
  }

  saveDatiQuadroNeurologico(dati: any | null): void {
    this.datiQuadroNeurologico$.next(dati);
  }

  // Dati Setting Riabilitativo
  getDatiSettingRiabilitativo$(): Observable<any | null> {
    return this.datiSettingRiabilitativo$.asObservable();
  }

  getDatiSettingRiabilitativoValue(): any | null {
    return this.datiSettingRiabilitativo$.getValue();
  }

  saveDatiSettingRiabilitativo(dati: any | null): void {
    this.datiSettingRiabilitativo$.next(dati);
  }

  // Scheda dettagliata
  getSchedaDettagliata$(): Observable<any> {
    return this.schedaDettagliata$.asObservable();
  }

  getSchedaDettagliataValue(): any {
    return this.schedaDettagliata$.getValue();
  }

  saveSchedaDettagliata(scheda: any): void {
    this.schedaDettagliata$.next(scheda);
  }

  // Ultimo ID Scheda
  getUltimoIdScheda$(): Observable<number | null> {
    return this.ultimoIdScheda$.asObservable();
  }

  getUltimoIdSchedaValue(): number | null {
    return this.ultimoIdScheda$.getValue();
  }

  saveUltimoIdScheda(id: number | null): void {
    this.ultimoIdScheda$.next(id);
  }

  // Cittadino identificato
  getCittadinoIdentificato$(): Observable<boolean> {
    return this.cittadinoIdentificato$.asObservable();
  }

  getCittadinoIdentificatoValue(): boolean {
    return this.cittadinoIdentificato$.getValue();
  }

  setCittadinoIdentificato(value: boolean): void {
    this.cittadinoIdentificato$.next(value);
  }

  // Campi disabilitati
  getCampiDisabilitati$(): Observable<string[]> {
    return this.campiDisabilitati$.asObservable();
  }

  getCampiDisabilitatiValue(): string[] {
    return this.campiDisabilitati$.getValue();
  }

  setCampiDisabilitati(campi: string[]): void {
    this.campiDisabilitati$.next(campi);
  }

  // Reset dei dati
  resetAllData(): void {
    this.datiAnagrafici$.next(null);
    this.datiSocioEconomici$.next(null);
    this.datiEziologia$.next(null);
    this.datiLesioneTrattamento$.next(null);
    this.datiNecessitaAssistenziali$.next(null);
    this.datiQuadroNeurologico$.next(null);
    this.datiSettingRiabilitativo$.next(null);
    this.schedaDettagliata$.next(null);
    this.ultimoIdScheda$.next(null);
    this.tipoOperazione$.next(null);
    this.cittadinoIdentificato$.next(false);
    this.campiDisabilitati$.next([]);
    this.datiGeneraliValido$.next(false);
    this.datiAnagraficiValido$.next(false);
    this.datiSocioEconomiciValido$.next(false);
  }

  // Reset di singoli gruppi di dati
  resetDatiGenerali(): void {
    this.datiGeneraliValido$.next(false);
  }

  resetDatiAnagrafici(): void {
    this.datiAnagrafici$.next(null);
    this.datiAnagraficiValido$.next(false);
  }

  resetDatiSocioEconomici(): void {
    this.datiSocioEconomici$.next(null);
    this.datiSocioEconomiciValido$.next(false);
  }

  resetDatiEziologia(): void {
    this.datiEziologia$.next(null);
  }

  resetDatiLesioneTrattamento(): void {
    this.datiLesioneTrattamento$.next(null);
  }

  resetDatiNecessitaAssistenziali(): void {
    this.datiNecessitaAssistenziali$.next(null);
  }

  resetDatiQuadroNeurologico(): void {
    this.datiQuadroNeurologico$.next(null);
  }

  resetDatiSettingRiabilitativo(): void {
    this.datiSettingRiabilitativo$.next(null);
  }

  resetSchedaData(): void {
    this.schedaDettagliata$.next(null);
    this.ultimoIdScheda$.next(null);
  }
}
