import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AbstractControl } from '@angular/forms';
import { SchedaGeneraleDimissioniModel, SchedaInterventiEffettuatiDimissioniModel } from '../../shared/interfaces/dati-dimissione.interface';

@Injectable({
  providedIn: 'root'
})
export class DatiDimissioneRientroService {
  private interventiEffettuatiRientroDataSubject = new BehaviorSubject<SchedaInterventiEffettuatiDimissioniModel | null>(null);
  interventiEffettuatiRientroData$ = this.interventiEffettuatiRientroDataSubject.asObservable();

  private generaleRientroDataSubject = new BehaviorSubject<SchedaGeneraleDimissioniModel | null>(null);
  generaleRientroData$ = this.generaleRientroDataSubject.asObservable();

  private interventiEffettuatiRientroValidoSubject = new BehaviorSubject<boolean>(false);
  interventiEffettuatiRientroValido$ = this.interventiEffettuatiRientroValidoSubject.asObservable();

  private generaleRientroValidoSubject = new BehaviorSubject<boolean>(false);
  generaleRientroValido$ = this.generaleRientroValidoSubject.asObservable();

  constructor() {
  }

  setInterventiEffettuatiRientro(data: SchedaInterventiEffettuatiDimissioniModel): void {
    this.interventiEffettuatiRientroDataSubject.next(data);
  }

  getInterventiEffettuatiRientroValue(): SchedaInterventiEffettuatiDimissioniModel | null {
    const value = this.interventiEffettuatiRientroDataSubject.value;
    return value;
  }

  setInterventiEffettuatiRientroValido(valido: boolean): void {
    this.interventiEffettuatiRientroValidoSubject.next(valido);
  }

  getInterventiEffettuatiRientroValidoValue(): boolean {
    return this.interventiEffettuatiRientroValidoSubject.value;
  }

  getInterventiEffettuatiRientroValid$() {
    return this.interventiEffettuatiRientroValidoSubject.asObservable()
  }

  setGeneraleRientro(data: SchedaGeneraleDimissioniModel): void {
    this.generaleRientroDataSubject.next(data);
  }

  getGeneraleRientroValue(): SchedaGeneraleDimissioniModel | null {
    return this.generaleRientroDataSubject.value;
  }

  setGeneraleRientroValido(valido: boolean): void {
    this.generaleRientroValidoSubject.next(valido);
  }

  getGeneraleRientroValid$() {
    return this.generaleRientroValidoSubject.asObservable()
  }

  getGeneraleRientroValidoValue(): boolean {
    return this.generaleRientroValidoSubject.value;
  }

  resetDatiRientro(): void {
    this.interventiEffettuatiRientroDataSubject.next(null);
    this.generaleRientroDataSubject.next(null);
    this.interventiEffettuatiRientroValidoSubject.next(false);
    this.generaleRientroValidoSubject.next(false);
  }

  /**
    * Verifica se un controllo ha il validatore required
    */
  isRequired(control: AbstractControl | null): boolean {
    if (!control || !control.validator) return false;
    const validator = control.validator({} as any);
    return validator && validator['required'];
  }

} 