import { HttpClient } from "@angular/common/http";
import { EventEmitter, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, finalize } from 'rxjs';
import { map } from "rxjs/operators";
import { BASE_URL } from "../../core/config";
import { SchedaPaziente } from "../../shared/interfaces/scheda-paziente.interface";
import {OperatoreService} from "../../core/services/operatore.service";
import {IdOperatoreEnum} from "../../shared/enums/enum";

@Injectable({
  providedIn: 'root'
})
export class RicercaSchedaPazienteService {
  resultSchedePaziente: BehaviorSubject<SchedaPaziente[]> = new BehaviorSubject<SchedaPaziente[]>([]);
  resultPaginationSchede: any;
  private paginationStatus = new BehaviorSubject<any>(null);
  paginationStatus$ = this.paginationStatus.asObservable();
  itemsPerPage = 6;
  currentPage = 0;
  private savedFilters = new BehaviorSubject<any>({});
  savedFilters$ = this.savedFilters.asObservable();
  initFiltersfromBack: boolean = false;

  disabilitaNuovaSegn = new BehaviorSubject<boolean>(true);
  disabilitaNuovaSegn$ = this.disabilitaNuovaSegn.asObservable();

  currentFilters: any = {};
  paginazioneResettata = new EventEmitter<void>();
  private isLoading = false;

  constructor(private httpClient: HttpClient,
              private operatoreService: OperatoreService
              ) { }

  // Metodo per ottenere i filtri salvati
  getSavedFilters(): any {
    return this.savedFilters.value;
  }

  getVisualizzaSchedaPaziente(body: any): Observable<any> {
    const apiUrl = `${BASE_URL}/scheda/find`;

    return this.httpClient.post<any>(apiUrl,
      body,
      { observe: 'response' }
    ).pipe(
      map(res => {
        if (res && res.status == 200) {
          let result = res.body;
          this.resultSchedePaziente.next(result.content);
          this.resultPaginationSchede = result;
          this.paginationStatus.next({
            totalElements: result.totalElements,
            totalPages: result.totalPages,
            size: result.size,
            number: result.number
          });

          if (result.size) {
            this.itemsPerPage = result.size;
          }
          return { value: this.resultSchedePaziente.value };
        } else {
          throw new Error('Errore nella risposta');
        }
      }),
      finalize(() => {
        this.isLoading = false;
      })
    );
  }

  buildBodyVisualizzaSchede(page: number): any {
    const requestBody: any = {
      pageSize: this.itemsPerPage,
      pageNumber: page
    };

    Object.keys(this.currentFilters).forEach(key => {
      const value = this.currentFilters[key];
      if (value !== null && value !== undefined && value !== '') {
        if (key === 'codiceIdentificativo') {
          requestBody['cdFiscale'] = value;
        }
        else if (key === 'lesioneMielica' && typeof value === 'object' && value.idDizionario) {
          requestBody[key] = value;
        }
        else {
          requestBody[key] = value;
        }
      }
    });

    return requestBody;
  }

  filtraSchede(filterValues?: any): void {
    if (!filterValues || Object.keys(filterValues).length === 0 ||
      Object.values(filterValues).every(val => val === null || val === undefined || val === '')) {
      this.currentFilters = {};
    } else {
      this.currentFilters = { ...filterValues };
      this.savedFilters.next({ ...filterValues });
    }

    this.currentPage = 0;
    this.paginazioneResettata.emit();

    // Garantisce che ogni chiamata venga eseguita, anche in caso di click rapidi
    const requestBody = this.buildBodyVisualizzaSchede(this.currentPage);
    this.isLoading = true;

    this.getVisualizzaSchedaPaziente(requestBody).subscribe({
      error: (err) => {
        console.error('Errore durante l\'applicazione dei filtri:', err);
        this.isLoading = false;
      }
    });
  }

  resetAllFilters(): void {
    if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM) {//tav tecnicod deve sempre filtrare per stato evento CHIUSO
      this.currentFilters = {statoEvento: this.currentFilters.statoEvento}
      this.savedFilters.next({statoEvento: this.currentFilters.statoEvento});
    } else {
    this.savedFilters.next({});
      this.currentFilters = {};
    }
  }
}
