import { HttpClient } from "@angular/common/http";
import { Injectable } from '@angular/core';
import { BehaviorSubject, catchError, Observable } from "rxjs";
import { map } from "rxjs/operators";
import { BASE_URL } from "../../core/config";
import { IEventiAssociatiReq, IEventiResponse } from "../../models/eventi-associati.model";


@Injectable({
  providedIn: 'root'
})
export class EventiAssociatiService {
  private readonly EVENTI_ASSOCIATI_KEY = 'mielo_in_eventi_associati';
  private inEventiAssociati$ = new BehaviorSubject<boolean>(false);
  private isRientro$ = new BehaviorSubject<boolean>(false);

  constructor(private httpClient: HttpClient) { }

  getEventiAssociatiService(req: IEventiAssociatiReq) {
    const apiUrl = `${BASE_URL}/scheda/find/associate`;
    req.idScheda = +req.idScheda

    return this.httpClient.post<IEventiResponse>(apiUrl, req).pipe(
        map(res => res),
        catchError(err => {
          throw err;
        })
    );
  }

  isInEventiAssociati$(): Observable<boolean> {
    return this.inEventiAssociati$.asObservable();
  }

  setIsInEventiAssociati(value: boolean): void {
    this.inEventiAssociati$.next(value);
  }

  getIsInEventiAssociati(): boolean {
    return this.inEventiAssociati$.getValue();
  }

  clearIsInEventiAssociati(): void {
    this.inEventiAssociati$.next(false);
  }

  changeIsRientro(value: boolean): void {
    this.isRientro$.next(value);
  }

  getIsRientro(): boolean {
    return this.isRientro$.getValue();
  }

  getIsRientro$(): Observable<boolean> {
    return this.isRientro$.asObservable();
  }
}
