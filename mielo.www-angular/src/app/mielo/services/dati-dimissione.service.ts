import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  SchedaAspettiSocioassistenzialiDimissioneModel,
  SchedaAusiliDimissioneModel,
  SchedaComplicanzeDimissioneModel,
  SchedaGeneraleDimissioniModel,
  SchedaInterventiEffettuatiDimissioniModel,
  SchedaNecessitaAssistenzaDimissioneModel,
  SchedaQuadroFunzionaleDimissioneModel,
  SchedaQuadroNeurologicoDimissioneModel,
  SchedaValutazioneDimissioneModel
} from '../../shared/interfaces/dati-dimissione.interface';

@Injectable({
  providedIn: 'root'
})
export class DatiDimissioneService {

  private generale$ = new BehaviorSubject<SchedaGeneraleDimissioniModel | null>(null);
  private generaleValido$ = new BehaviorSubject<boolean | null>(null);
  private valutazione$ = new BehaviorSubject<SchedaValutazioneDimissioneModel | null>(null);
  private valutazioneValido$ = new BehaviorSubject<boolean | null>(null);
  private necessitaAssistenza$ = new BehaviorSubject<SchedaNecessitaAssistenzaDimissioneModel | null>(null);
  private necessitaAssistenzaValido$ = new BehaviorSubject<boolean | null>(null);
  private quadroNeurologico$ = new BehaviorSubject<SchedaQuadroNeurologicoDimissioneModel | null>(null);
  private quadroNeurologicoValido$ = new BehaviorSubject<boolean | null>(null);
  private complicanze$ = new BehaviorSubject<SchedaComplicanzeDimissioneModel | null>(null);
  private complicanzeValido$ = new BehaviorSubject<boolean | null>(null);
  private quadroFunzionale$ = new BehaviorSubject<SchedaQuadroFunzionaleDimissioneModel | null>(null);
  private quadroFunzionaleValido$ = new BehaviorSubject<boolean | null>(null);
  private ausili$ = new BehaviorSubject<SchedaAusiliDimissioneModel | null>(null);
  private ausiliValido$ = new BehaviorSubject<boolean | null>(null);
  private socioassistenziali$ = new BehaviorSubject<SchedaAspettiSocioassistenzialiDimissioneModel | null>(null);
  private generaleRientro$ = new BehaviorSubject<SchedaGeneraleDimissioniModel | null>(null);
  private generaleRientroValido$ = new BehaviorSubject<boolean | null>(null);
  private interventiEffettuatiRientro$ = new BehaviorSubject<SchedaInterventiEffettuatiDimissioniModel | null>(null);
  private interventiEffettuatiRientroValido$ = new BehaviorSubject<boolean | null>(null);
  private socioassistenzialiValid$ = new BehaviorSubject<boolean | null>(null);

  /**
   * Adatta l'altezza del dialog
   */
  adjustDialogHeight(): void {
    setTimeout(() => {
      const dialogSurfaces = document.querySelectorAll('.mdc-dialog__surface');
      (Array.from(dialogSurfaces) as HTMLElement[]).forEach(surface => {
        surface.style.setProperty('height', 'auto', 'important');
        surface.style.setProperty('max-height', '80vh', 'important');
      });
    }, 100);
  }

  /**
    * Gestisce la deselezionabilità dei radio button
    */
  toggleRadioSelection(control: AbstractControl | null, option: any, event: MouseEvent): void {
    if (!control) return;

    const currentValue = control.value;
    const isDizionarioMatch =
      currentValue?.idDizionario && option?.idDizionario &&
      currentValue.idDizionario === option.idDizionario;

    const isPrimitiveMatch = currentValue === option;

    if (isDizionarioMatch || isPrimitiveMatch) {
      event.preventDefault();
      setTimeout(() => {
        control.setValue(null);
        control.markAsPristine();
        control.markAsUntouched();
        control.updateValueAndValidity({ emitEvent: true });
        const radioButton = event.target as HTMLElement;
        const input = radioButton.closest('mat-radio-button')?.querySelector('input[type="radio"]') as HTMLInputElement;
        input.blur();
      }, 0);
    }
  }

  /**
   * Verifica se un controllo ha il validatore required
   */
  isRequired(control: AbstractControl | null): boolean {
    if (!control || !control.validator) return false;
    const validator = control.validator({} as any);
    return validator && validator['required'];
  }

  /**
   * Limita l'input a massimo 2 cifre numeriche
   */
  restrictToTwoDigits(event: any): boolean {
    const controlKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Home', 'End'];
    if (controlKeys.includes(event.key)) {
      return true;
    }

    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
      return false;
    }

    const input = event.target.value;
    const selectionStart = event.target.selectionStart;
    const selectionEnd = event.target.selectionEnd;

    if (selectionStart !== selectionEnd) {
      const newValue = input.substring(0, selectionStart) + event.key + input.substring(selectionEnd);
      if (newValue.length > 2) {
        event.preventDefault();
        return false;
      }
      return true;
    }

    if (input.length >= 2) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  // Getter, setter e reset per ogni sezione:

  // =================== GENERALE ===================

  getGenerale$(): Observable<SchedaGeneraleDimissioniModel | null> {
    return this.generale$.asObservable();
  }

  getGeneraleValue(): SchedaGeneraleDimissioniModel | null {
    return this.generale$.getValue();
  }

    setGenerale(data: SchedaGeneraleDimissioniModel | null): void {
    this.generale$.next(data);
  }

  setGeneraleValido(valido: boolean | null): void {
    this.generaleValido$.next(valido);
  }

  getGeneraleValido$(){
    return this.generaleValido$.asObservable();
  }

  resetGenerale(): void {
    this.generale$.next(null);
  }

  // =================== VALUTAZIONE ===================

  getValutazione$(): Observable<SchedaValutazioneDimissioneModel | null> {
    return this.valutazione$.asObservable();
  }

  getValutazioneValue(): SchedaValutazioneDimissioneModel | null {
    return this.valutazione$.getValue();
  }

  setValutazione(data: SchedaValutazioneDimissioneModel | null): void {
    this.valutazione$.next(data);
  }

  resetValutazione(): void {
    this.valutazione$.next(null);
  }

  // =================== NECESSITÀ ASSISTENZA ===================

  getNecessitaAssistenza$(): Observable<SchedaNecessitaAssistenzaDimissioneModel | null> {
    return this.necessitaAssistenza$.asObservable();
  }

  getNecessitaAssistenzaValue(): SchedaNecessitaAssistenzaDimissioneModel | null {
    return this.necessitaAssistenza$.getValue();
  }

  setNecessitaAssistenza(data: SchedaNecessitaAssistenzaDimissioneModel | null): void {
    this.necessitaAssistenza$.next(data);
  }

  resetNecessitaAssistenza(): void {
    this.necessitaAssistenza$.next(null);
  }

  // =================== QUADRO NEUROLOGICO ===================

  getQuadroNeurologico$(): Observable<SchedaQuadroNeurologicoDimissioneModel | null> {
    return this.quadroNeurologico$.asObservable();
  }

  getQuadroNeurologicoValue(): SchedaQuadroNeurologicoDimissioneModel | null {
    return this.quadroNeurologico$.getValue();
  }

  setQuadroNeurologico(data: SchedaQuadroNeurologicoDimissioneModel | null): void {
    this.quadroNeurologico$.next(data);
  }

  setQuadroNeurologicoValido(val: boolean): void {
    this.quadroNeurologicoValido$.next(val);
  }

  getQuadroNeurologicoValido$() {
    return  this.quadroNeurologicoValido$.asObservable();
  }

  resetQuadroNeurologico(): void {
    this.quadroNeurologico$.next(null);
  }

  // =================== COMPLICANZE ===================

  getComplicanze$(): Observable<SchedaComplicanzeDimissioneModel | null> {
    return this.complicanze$.asObservable();
  }

  getComplicanzeValue(): SchedaComplicanzeDimissioneModel | null {
    return this.complicanze$.getValue();
  }

  setComplicanze(data: SchedaComplicanzeDimissioneModel | null): void {
    this.complicanze$.next(data);
  }

 setComplicanzeValido(valido: boolean | null): void {
    this.complicanzeValido$.next(valido);
  }

  getComplicanzeValido$() {
    return this.complicanzeValido$.asObservable();
  }

  resetComplicanze(): void {
    this.complicanze$.next(null);
  }

  // =================== QUADRO FUNZIONALE ===================

  getQuadroFunzionale$(): Observable<SchedaQuadroFunzionaleDimissioneModel | null> {
    return this.quadroFunzionale$.asObservable();
  }

  getQuadroFunzionaleValue(): SchedaQuadroFunzionaleDimissioneModel | null {
    return this.quadroFunzionale$.getValue();
  }

  getQuadroFunzionaleValido$() {
    return this.quadroFunzionaleValido$.asObservable();
  }

  setQuadroFunzionale(data: SchedaQuadroFunzionaleDimissioneModel | null): void {
    this.quadroFunzionale$.next(data);
  }

  setQuadroFunzionaleValido(valido: boolean | null): void {
    this.quadroFunzionaleValido$.next(valido);
  }

  resetQuadroFunzionale(): void {
    this.quadroFunzionale$.next(null);
  }

  // =================== AUSILI ===================

  getAusili$(): Observable<SchedaAusiliDimissioneModel | null> {
    return this.ausili$.asObservable();
  }

  getAusiliValue(): SchedaAusiliDimissioneModel | null {
    return this.ausili$.getValue();
  }

  setAusili(data: SchedaAusiliDimissioneModel | null): void {
    this.ausili$.next(data);
  }

  resetAusili(): void {
    this.ausili$.next(null);
  }

  // =================== ASPETTI SOCIO ASSISTENZIALI ===================

  getSocioassistenziali$(): Observable<SchedaAspettiSocioassistenzialiDimissioneModel | null> {
    return this.socioassistenziali$.asObservable();
  }

  getSocioassistenzialiValue(): SchedaAspettiSocioassistenzialiDimissioneModel | null {
    return this.socioassistenziali$.getValue();
  }

  setSocioassistenziali(data: SchedaAspettiSocioassistenzialiDimissioneModel | null): void {
    this.socioassistenziali$.next(data);
  }

  setSocioAssistenzialiValid(valido: boolean | null): void {
    this.socioassistenzialiValid$.next(valido);
  }

  getSocioAssistenzialiValid$() {
    return this.socioassistenzialiValid$.asObservable()
  }

  resetSocioassistenziali(): void {
    this.socioassistenziali$.next(null);
  }

  // =================== AUSILI - VALIDO ===================

  getAusiliValido$(): Observable<boolean | null> {
    return this.ausiliValido$.asObservable();
  }

  setAusiliValido(valido: boolean | null): void {
    this.ausiliValido$.next(valido);
  }

  resetAusiliValido(): void {
    this.ausiliValido$.next(null);
  }

  // =================== VALUTAZIONE - VALIDO ===================

  getValutazioneValido$(): Observable<boolean | null> {
    return this.valutazioneValido$.asObservable();
  }

  getValutazioneValidoValue(): boolean | null {
    return this.valutazioneValido$.getValue();
  }

  setValutazioneValido(valido: boolean | null): void {
    this.valutazioneValido$.next(valido);
  }

  resetValutazioneValido(): void {
    this.valutazioneValido$.next(null);
  }

  // =================== NECESSITA' - VALIDO ===================

  getNecessitaValido$(): Observable<boolean | null> {
    return this.valutazioneValido$.asObservable();
  }

  getNecessitaValidoValue(): boolean | null {
    return this.valutazioneValido$.getValue();
  }

  setNecessitaValido(valido: boolean | null): void {
    this.valutazioneValido$.next(valido);
  }

  resetNecessitaValido(): void {
    this.valutazioneValido$.next(null);
  }
  
  // Reset globale opzionale
  resetAllDatiDimissione(): void {
    this.resetGenerale();
    this.resetValutazione();
    this.resetNecessitaAssistenza();
    this.resetQuadroNeurologico();
    this.resetComplicanze();
    this.resetQuadroFunzionale();
    this.resetAusili();
    this.resetSocioassistenziali();
  }
}
