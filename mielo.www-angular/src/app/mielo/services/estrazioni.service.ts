import { HttpClient, HttpParams } from "@angular/common/http";
import { EventEmitter, Injectable } from '@angular/core';
import { BehaviorSubject, finalize, Observable } from 'rxjs';
import { map } from "rxjs/operators";
import { BASE_URL } from "../../core/config";
import { MetadatiReportDTO } from "../interfaces/metadati-report-dto.interface";
import { DecoderService } from "../../shared/services/decoder.service";
import { OperatoreService } from "../../core/services/operatore.service";
import { IdOperatoreEnum, StatoEventoEnum } from "../../shared/enums/enum";

export interface EstrazioneReportResponse {
  content: MetadatiReportDTO[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
}

export interface EstrazioneElaboraResponse {
  idElaborazione: number;
  stato: string;
  message?: string;
}

interface FilterPeriod {
  start: Date | string | null;
  end: Date | string | null;
}

interface FilterValue {
  idDizionario?: number;
  idTipoEvento?: number;
  descrizione?: string;
  codice?: string;
}

interface FilterState {
  periodo?: FilterPeriod;
  lesioneMielica?: FilterValue;
  lesioneTraumatica?: boolean;
  tipoEvento?: FilterValue;
  statoEvento?: FilterValue;
  idElaborazione?: number;
  statoElaborazione?: FilterValue;
}

@Injectable({
  providedIn: 'root'
})
export class EstrazioniService {
  resultEstrazioni: BehaviorSubject<MetadatiReportDTO[]> = new BehaviorSubject<MetadatiReportDTO[]>([]);
  resultPaginationEstrazioni: EstrazioneReportResponse | null = null;
  private paginationStatus = new BehaviorSubject<EstrazioneReportResponse | null>(null);
  paginationStatus$ = this.paginationStatus.asObservable();
  itemsPerPage = 6;
  currentPage = 0;
  
  private currentFiltersSubject = new BehaviorSubject<FilterState>({});
  currentFilters = this.currentFiltersSubject.asObservable();
  
  paginazioneResettata = new EventEmitter<void>();
  private isLoading = false;
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  isLoading$ = this.isLoadingSubject.asObservable();

  constructor(
    private httpClient: HttpClient,
    private decoderService: DecoderService,
    private operatoreService: OperatoreService
  ) { }

  getCurrentFilters(): FilterState {
    return this.currentFiltersSubject.value;
  }

  /**
   * GET lista report richiesti (ListaReport)
   */
  getEstrazioni(params: {
    idElaborazione?: number;
    dataMin?: string;
    dataMax?: string;
    idTipoLesione?: number;
    lesioneTraumatica?: boolean;
    idTipoEvento?: number;
    idStatoEvento?: number;
    pageSize: number;
    pageNumber: number;
  }): Observable<EstrazioneReportResponse> {
    const apiUrl = `${BASE_URL}/estrazione/listaReport`;
    let httpParams = new HttpParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });
    return this.httpClient.get<EstrazioneReportResponse>(apiUrl, { params: httpParams }).pipe(
      map(res => {
        this.resultEstrazioni.next(res.content);
        this.resultPaginationEstrazioni = res;
        this.paginationStatus.next(res);
        if (res.size) {
          this.itemsPerPage = res.size;
        }
        return res;
      }),
      finalize(() => {
        this.isLoading = false;
        this.isLoadingSubject.next(false);
      })
    );
  }

  /**
   * PUT richiesta elaborazione (elabora)
   */
  richiestaElaborazione(params: {
    dataMin?: string;
    dataMax?: string;
    idTipoLesione?: number;
    lesioneTraumatica?: boolean;
    idTipoEvento?: number;
    idStatoEvento?: number;
  }): Observable<EstrazioneElaboraResponse> {
    const apiUrl = `${BASE_URL}/estrazione/elabora`;
    let httpParams = new HttpParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });
    return this.httpClient.put<EstrazioneElaboraResponse>(apiUrl, {}, { params: httpParams });
  }

  /**
   * GET download file
   */
  downloadFile(idElaborazione: number): Observable<Blob> {
    const apiUrl = `${BASE_URL}/estrazione/fileReport`;
    const params = new HttpParams().set('idElaborazione', idElaborazione.toString());
    return this.httpClient.get(apiUrl, { params, responseType: 'blob' });
  }

  downloadFileCsv(idElaborazione: number) {
    const apiUrl = `${BASE_URL}/estrazione/fileReport`;
    const params = new HttpParams().set('idElaborazione', idElaborazione.toString());
    return this.httpClient.get(apiUrl, {
      params,
      observe: 'response',
      responseType: 'blob'
    });
  }

  buildRequestParams(page: number): {
    idElaborazione?: number;
    dataMin?: string;
    dataMax?: string;
    idTipoLesione?: number;
    idTipoEvento?: number;
    idStatoEvento?: number;
    idStatoElaborazione?: number;
    pageSize: number;
    pageNumber: number;
  } {
    const params: any = {
      pageSize: this.itemsPerPage,
      pageNumber: page
    };
    
    const currentFilters = this.currentFiltersSubject.value;
    
    if (currentFilters.periodo) {
      if (currentFilters.periodo.start) {
        const startDate = currentFilters.periodo.start instanceof Date
          ? currentFilters.periodo.start
          : new Date(currentFilters.periodo.start);
        params['dataMin'] = startDate;
      }
      if (currentFilters.periodo.end) {
        const endDate = currentFilters.periodo.end instanceof Date
          ? currentFilters.periodo.end
          : new Date(currentFilters.periodo.end);
        params['dataMax'] = endDate;
      }
    }

    if (currentFilters.lesioneMielica?.idDizionario) {
      params['idTipoLesione'] = currentFilters.lesioneMielica.idDizionario;
    }

    if (currentFilters.lesioneTraumatica !== null) {
      params['lesioneTraumatica'] = currentFilters.lesioneTraumatica;
    }

    if (currentFilters.tipoEvento?.idTipoEvento) {
      params['idTipoEvento'] = currentFilters.tipoEvento.idTipoEvento;
    }

    if (currentFilters.statoEvento?.idDizionario) {
      params['idStatoEvento'] = currentFilters.statoEvento.idDizionario;
    }

    if (currentFilters.idElaborazione) {
      params['idElaborazione'] = currentFilters.idElaborazione;
    }

    if (currentFilters.statoElaborazione?.idDizionario) {
      params['idStatoElaborazione'] = currentFilters.statoElaborazione.idDizionario;
    }

    if (this.operatoreService.getOperatore().idRuolo === IdOperatoreEnum.COM) {
      params['idStatoEvento'] = StatoEventoEnum.CHIUSO;
    }

    return params;
  }

  filtraEstrazioni(filterValues?: FilterState): void {
    if (!filterValues || Object.keys(filterValues).length === 0) {
      this.currentFiltersSubject.next({});
    } else {
      this.currentFiltersSubject.next({ ...filterValues });
    }
    
    this.currentPage = 0;
    this.paginazioneResettata.emit();
    const params = this.buildRequestParams(this.currentPage);
    this.isLoading = true;
    this.isLoadingSubject.next(true);
    this.getEstrazioni(params).subscribe({
      error: (err) => {
        console.error('Errore durante l\'applicazione dei filtri:', err);
        this.isLoading = false;
        this.isLoadingSubject.next(false);
      }
    });
  }

  resetAllFilters(): void {
    this.currentFiltersSubject.next({});
    this.currentPage = 0;
    this.paginazioneResettata.emit();

    const params = this.buildRequestParams(0); // <-- assicurati di usare un metodo che non dipende da subject
    this.getEstrazioni(params).subscribe();
  }


  buildRequestBody(page: number): {
    idElaborazione?: number;
    dataMin?: string;
    dataMax?: string;
    idTipoLesione?: number;
    idStatoEvento?: number;
    pageSize: number;
    pageNumber: number;
  } {
    return this.buildRequestParams(page);
  }
} 