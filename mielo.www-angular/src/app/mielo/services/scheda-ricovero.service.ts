import { HttpClient } from "@angular/common/http";
import { Injectable } from '@angular/core';
import { Router } from "@angular/router";
import { BehaviorSubject, catchError, map, mapTo, Observable, of, switchMap, tap, throwError } from "rxjs";
import { BASE_URL } from '../../core/config';
import {EGenereSessoID, ETipoScheda, TipoCodiceIdentEnum} from "../../shared/enums/enum";
import { SceltaSchedaEnum } from "../../shared/enums/scelta-scheda.enum";
import {
  DatiEziologia,
  DatiLesioneTrattamento,
  DatiNecessitaAssistenziali,
  DatiQuadroNeurologico,
  DatiSettingRiabilitativo
} from "../../shared/interfaces/dati-clinici.interface";
import {
  DatiAnagrafici, DatiGenerali,
  DatiSocioEconomici,
  PazienteInfo
} from "../../shared/interfaces/dati-paziente.interface";
import { ComuneModel, NazioneModel } from "../../shared/interfaces/decoder.interface";
import {
  AggiungiSchedaRicoveroRequest, EventoModel, InputTipoScheda, NuovoEventoRicoveroModel, OutputCreazioneScheda, OutputSchedaPazienteRicoveroModel,
  PazienteModel,
  SchedaRicoveroDatiMancanti,
  SchedaRicoveroModel,
  SchedaRicoveroReqModel,
  SISSModel
} from '../../shared/interfaces/scheda-ricovero.interface';
import { DialogService } from "../../shared/services/dialog.service";
import { convertMomentInRequest, decodeNoteFields, deepClone, encodeNoteFields } from "../../shared/utils/utils";
import { DatiCliniciService } from "./dati-clinici.service";
import { DatiDimissioneService } from "./dati-dimissione.service";
import { DatiStorageService } from './dati-storage.service';
import {OutputHistoryDTO} from "../../shared/interfaces/shared/shared.interface";
import { DatiDimissioneRientroService } from "./dati-dimissione-rientro.service";

export type TipoOperazione = 'create' | 'edit' | 'view' | 'edit-patient';

interface ValidSubject {
  valid: boolean;
  pristine: boolean;
}

@Injectable({
  providedIn: 'root'
})

export class SchedaRicoveroService {
  // subj che contiene tutti dati di quando si compila una nuova scheda o quando si modifica una scheda
  schedaCreation$ = new BehaviorSubject<AggiungiSchedaRicoveroRequest | null>(null);
  cronologiaOpened$ = new BehaviorSubject<boolean>(false);
  schedaCreationObj = this.schedaCreation$.asObservable();

  // subject per controllare validità del form per disabilitare bottone salva
  defaultValid = {
    valid: false,
    pristine: true
  }
  _areFormsCreateSchedaValidSubject$ = new BehaviorSubject<{
    formDatiGenerali: ValidSubject,
    formDatiAnagrafici: ValidSubject,
    formDatiClinici?: ValidSubject,
    formDatiSocioEconomici: ValidSubject,
    formDatiDimissioni?: ValidSubject
  }>({
    formDatiAnagrafici: this.defaultValid,
    formDatiGenerali: this.defaultValid,
    formDatiSocioEconomici: this.defaultValid
  });
  _areFormsCreateSchedaValidSubject = this._areFormsCreateSchedaValidSubject$.asObservable();

  // utillizzato quando si apre una scheda per recuperare le info e per capire se si è in edit o create
  idSchedaSelectedSubject = new BehaviorSubject<number | undefined>(undefined);
  idSchedaSelectedSubject$ = this.idSchedaSelectedSubject.asObservable();

  statusSchedaSelectedSubject = new BehaviorSubject<boolean | undefined>(undefined);
  statusSchedaSelectedSubject$ = this.statusSchedaSelectedSubject.asObservable();

  isRientroSchedaSelectedSubject = new BehaviorSubject<boolean | undefined>(false);
  isRientroSchedaSelectedSubject$ = this.isRientroSchedaSelectedSubject.asObservable();

  idEditPatientSubject = new BehaviorSubject<number | null>(null);
  idEditPatientSubject$ = this.idEditPatientSubject.asObservable();

  isCittadinoIdentificatoSubject = new BehaviorSubject<boolean>(false);
  isCittadinoIdentificatoSubject$ = this.isCittadinoIdentificatoSubject.asObservable();

  pazienteSISSIdentificatoSubject = new BehaviorSubject<SISSModel | null>(null);
  pazienteSISSIdentificatoSubject$ = this.pazienteSISSIdentificatoSubject.asObservable();

  schedaSelezionata: SceltaSchedaEnum;

  // Subjects per il tipo di operazione
  private _tipoOperazioneSubject = new BehaviorSubject<TipoOperazione | null>(null);
  tipoOperazione$ = this._tipoOperazioneSubject.asObservable();

  // Subjects per i dati della scheda dettagliata
  schedaDettagliataSubject = new BehaviorSubject<any | null>(null);
  schedaDettagliata$ = this.schedaDettagliataSubject.asObservable();

  // Subjects per il componente selezionato
  private _schedaSelezionata = new BehaviorSubject<SceltaSchedaEnum | null>(null);
  schedaSelezionata$ = this._schedaSelezionata.asObservable();

  // Subjects per indicare se si tratta di nuovo evento
  isNewEvent = new BehaviorSubject<boolean>(false);
  isNewEvent$ = this.isNewEvent.asObservable();
  
  // Subjects per indicare se si tratta di ultimo evento
  isLastEvent = new BehaviorSubject<boolean>(false);

  hasSameStrutturaAsOperatore = new BehaviorSubject<boolean>(false);

  getHasSameStrutturaAsOperatore() : boolean {
    return this.hasSameStrutturaAsOperatore.value;
  }

  setHasSameStrutturaAsOperatore(value: boolean) {
    this.hasSameStrutturaAsOperatore.next(value);
  }

  getIsLastEvent() : boolean {
    return this.isLastEvent.value;
  }

  setIsLastEvent(value: boolean) {
    this.isLastEvent.next(value)
  }

  // Subject per i dati anagrafici
  private datiAnagraficiSubject = new BehaviorSubject<DatiAnagrafici>({
    nazioneNascita: {} as NazioneModel,
    tipologiaCodice: '',
    codiceFiscale: '',
    dataRilascioTeam: null,
    nome: '',
    cognome: '',
    dataNascita: null,
    provinciaNascita: '',
    comuneNascita: null,
    cittadinanza: '',
    eta: 0,
    genere: null,
    provinciaResidenza: '',
    comuneResidenza: '',
    indirizzoResidenza: '',
    numeroCivico: '',
    domicilioCoincideConResidenza: false,
    provinciaDomicilio: '',
    comuneDomicilio: '',
    indirizzoDomicilio: '',
    numeroCivicoDomicilio: ''
  });
  private datiAnagraficiValidoSubject = new BehaviorSubject<boolean>(false);
  datiAnagrafici$ = this.datiAnagraficiSubject.asObservable();
  datiAnagraficiValido$ = this.datiAnagraficiValidoSubject.asObservable();

  // Subject per i dati generali
  private datiGeneraliValidoSubject = new BehaviorSubject<boolean>(false);
  datiGeneraliValido$ = this.datiGeneraliValidoSubject.asObservable();
  private datiGeneraliSubject = new BehaviorSubject<DatiGenerali | null>(null);
  datiGenerali$ = this.datiGeneraliSubject.asObservable();

  // Subject per i dati socio-economici
  private datiSocioEconomiciSubject = new BehaviorSubject<DatiSocioEconomici | null>(null);
  private datiSocioEconomiciValidoSubject = new BehaviorSubject<boolean>(false);
  datiSocioEconomici$ = this.datiSocioEconomiciSubject.asObservable();
  datiSocioEconomiciValido$ = this.datiSocioEconomiciValidoSubject.asObservable();

  // Dati eziologia
  private datiEziologiaSubject = new BehaviorSubject<DatiEziologia | null>(null);
  private datiEziologiaValidoSubject = new BehaviorSubject<boolean>(false);
  datiEziologia$ = this.datiEziologiaSubject.asObservable();
  datiEziologiaValido$ = this.datiEziologiaValidoSubject.asObservable();

  // Dati lesione e trattamento
  private datiLesioneTrattamentoSubject = new BehaviorSubject<DatiLesioneTrattamento | null>(null);
  private datiLesioneTrattamentoValidoSubject = new BehaviorSubject<boolean>(false);
  datiLesioneTrattamento$ = this.datiLesioneTrattamentoSubject.asObservable();
  datiLesioneTrattamentoValido$ = this.datiLesioneTrattamentoValidoSubject.asObservable();

  // Dati necessità assistenziali
  private datiNecessitaAssistenzialiSubject = new BehaviorSubject<DatiNecessitaAssistenziali | null>(null);
  private datiNecessitaAssistenzialiValidoSubject = new BehaviorSubject<boolean>(false);
  datiNecessitaAssistenziali$ = this.datiNecessitaAssistenzialiSubject.asObservable();
  datiNecessitaAssistenzialiValido$ = this.datiNecessitaAssistenzialiValidoSubject.asObservable();

  // Dati quadro neurologico
  private datiQuadroNeurologicoSubject = new BehaviorSubject<DatiQuadroNeurologico | null>(null);
  private datiQuadroNeurologicoValidoSubject = new BehaviorSubject<boolean>(false);
  datiQuadroNeurologico$ = this.datiQuadroNeurologicoSubject.asObservable();
  datiQuadroNeurologicoValido$ = this.datiQuadroNeurologicoValidoSubject.asObservable();

  // Dati setting riabilitativo
  private datiSettingRiabilitativoSubject = new BehaviorSubject<DatiSettingRiabilitativo | null>(null);
  private datiSettingRiabilitativoValidoSubject = new BehaviorSubject<boolean>(false);
  datiSettingRiabilitativo$ = this.datiSettingRiabilitativoSubject.asObservable();
  datiSettingRiabilitativoValido$ = this.datiSettingRiabilitativoValidoSubject.asObservable();

  // Valutazione ingresso
  private valutazioneIngressoValidoSubject = new BehaviorSubject<boolean>(false);
  valutazioneIngressoValido$ = this.valutazioneIngressoValidoSubject.asObservable();

  private pazienteInfo: PazienteInfo | null = null;

  private initialAnagraficiData: any;
  private initialSocioEconomiciData: any;

  datiMancantiSocioEconomiciSubject = new BehaviorSubject<boolean>(false);
  datiMancantiSocioEconomici$ = this.datiMancantiSocioEconomiciSubject.asObservable();

  datiMenuMancanti: SchedaRicoveroDatiMancanti | null = null;

  private _schedaSalvata: any = null;
  private _tipoOperazione: string | null = null;
  private _pazienteSelezionato: any = null;
  private _datiGenerali: any = null;

  private ultimaSchedaIdSubject = new BehaviorSubject<number | null>(null);
  private schedaCorrenteSubject = new BehaviorSubject<any>(null);

  private readonly defaultDatiAnagrafici: DatiAnagrafici = {
    nazioneNascita: {} as NazioneModel,
    tipologiaCodice: '',
    codiceFiscale: '',
    dataRilascioTeam: null,
    nome: '',
    cognome: '',
    dataNascita: null,
    provinciaNascita: '',
    comuneNascita: null,
    cittadinanza: '',
    eta: 0,
    genere: null,
    provinciaResidenza: '',
    comuneResidenza: '',
    indirizzoResidenza: '',
    numeroCivico: '',
    domicilioCoincideConResidenza: false,
    provinciaDomicilio: '',
    comuneDomicilio: '',
    indirizzoDomicilio: '',
    numeroCivicoDomicilio: ''
  };

  private _eventoRientroNonCensito: boolean = false;
  private _eventoRientroCensito: boolean = false;

  constructor(
    private httpClient: HttpClient,
    private datiStorageService: DatiStorageService,
    private datiCliniciService: DatiCliniciService,
    private datiDimissioneService: DatiDimissioneService,
    private datiDimissioneRientroService: DatiDimissioneRientroService,
    private dialogService: DialogService,
    private router: Router
  ) {
  }

  updateSchedaSelezionata(scheda: SceltaSchedaEnum) {
    this._schedaSelezionata.next(scheda);
    this.schedaSelezionata = scheda;
  }

  setRientroSchedaSelectedSubject(value: boolean) {
    this.isRientroSchedaSelectedSubject.next(value);
  }

  // DATI GENERALI
  salvaDatiGenerali(dati: DatiGenerali): void {
    this.datiGeneraliSubject.next(dati);
  }

  setDatiGeneraliValido(valido: boolean): void {
    this.datiGeneraliValidoSubject.next(valido);
  }

  isDatiGeneraliValido(): boolean {
    return this.datiGeneraliValidoSubject.value;
  }

  // DATI ANAGRAFICI
  salvaDatiAnagrafici(dati: DatiAnagrafici): void {
    this.datiAnagraficiSubject.next(dati);
    this.datiStorageService.saveDatiAnagrafici(dati);
  }

  getDatiAnagrafici(): DatiAnagrafici {
    const dati = this.datiAnagraficiSubject.value;
    if (!dati || (dati && !dati.codiceFiscale)) {
      const datiStorage = this.datiStorageService.getDatiAnagraficiValue();
      if (datiStorage && datiStorage.codiceFiscale) {
        this.datiAnagraficiSubject.next(datiStorage);
        return datiStorage;
      }
    }
    return dati;
  }

  setDatiAnagraficiValido(valido: boolean): void {
    this.datiAnagraficiValidoSubject.next(valido);
  }

  isDatiAnagraficiValido(): boolean {
    return this.datiAnagraficiValidoSubject.value;
  }

  // DATI SOCIO ECONOMICI
  salvaDatiSocioEconomici(dati: DatiSocioEconomici): void {
    this.datiSocioEconomiciSubject.next(dati);
  }

  getDatiSocioEconomici(): DatiSocioEconomici | null {
    return this.datiSocioEconomiciSubject.value;
  }

  setDatiSocioEconomiciValido(valido: boolean): void {
    this.datiSocioEconomiciValidoSubject.next(valido);
  }

  isDatiSocioEconomiciValido(): boolean {
    return this.datiSocioEconomiciValidoSubject.value;
  }

  // DATI EZIOLOGIA
  salvaDatiEziologia(dati: DatiEziologia): void {
    this.datiEziologiaSubject.next(dati);
    // Usa il DatiStorageService
    this.datiStorageService.saveDatiEziologia(dati);
  }

  getDatiEziologia(): DatiEziologia | null {
    if (!this.datiEziologiaSubject.getValue()) {
      // Usa il DatiStorageService
      const savedData = this.datiStorageService.getDatiEziologiaValue();
      if (savedData) {
        this.datiEziologiaSubject.next(savedData);
      }
    }
    return this.datiEziologiaSubject.getValue();
  }

  setDatiEziologiaValido(valido: boolean): void {
    this.datiEziologiaValidoSubject.next(valido);
  }

  // DATI LESIONE E TRATTAMENTO
  salvaDatiLesioneTrattamento(dati: DatiLesioneTrattamento): void {
    this.datiLesioneTrattamentoSubject.next(dati);
    // Usa il DatiStorageService
    this.datiStorageService.saveDatiLesioneTrattamento(dati);
  }

  getDatiLesioneTrattamento(): DatiLesioneTrattamento | null {
    if (!this.datiLesioneTrattamentoSubject.getValue()) {
      // Usa il DatiStorageService
      const savedData = this.datiStorageService.getDatiLesioneTrattamentoValue();
      if (savedData) {
        this.datiLesioneTrattamentoSubject.next(savedData);
      }
    }
    return this.datiLesioneTrattamentoSubject.getValue();
  }

  setDatiLesioneTrattamentoValido(valido: boolean): void {
    this.datiLesioneTrattamentoValidoSubject.next(valido);
  }

  // DATI NECESSITÀ ASSISTENZIALI
  salvaDatiNecessitaAssistenziali(dati: DatiNecessitaAssistenziali): void {
    this.datiNecessitaAssistenzialiSubject.next(dati);
    // Usa il DatiStorageService
    this.datiStorageService.saveDatiNecessitaAssistenziali(dati);
  }

  getDatiNecessitaAssistenziali(): DatiNecessitaAssistenziali | null {
    if (!this.datiNecessitaAssistenzialiSubject.getValue()) {
      // Usa il DatiStorageService
      const savedData = this.datiStorageService.getDatiNecessitaAssistenzialiValue();
      if (savedData) {
        this.datiNecessitaAssistenzialiSubject.next(savedData);
      }
    }
    return this.datiNecessitaAssistenzialiSubject.getValue();
  }

  setDatiNecessitaAssistenzialiValido(valido: boolean): void {
    this.datiNecessitaAssistenzialiValidoSubject.next(valido);
  }

  isDatiNecessitaAssistenzialiValido(): boolean {
    return this.datiNecessitaAssistenzialiValidoSubject.getValue();
  }

  // DATI QUADRO NEUROLOGICO
  salvaDatiQuadroNeurologico(dati: DatiQuadroNeurologico): void {
    this.datiQuadroNeurologicoSubject.next(dati);
    // Usa il DatiStorageService
    this.datiStorageService.saveDatiQuadroNeurologico(dati);
  }

  getDatiQuadroNeurologico(): DatiQuadroNeurologico | null {
    if (!this.datiQuadroNeurologicoSubject.getValue()) {
      // Usa il DatiStorageService
      const savedData = this.datiStorageService.getDatiQuadroNeurologicoValue();
      if (savedData) {
        this.datiQuadroNeurologicoSubject.next(savedData);
      }
    }
    return this.datiQuadroNeurologicoSubject.getValue();
  }

  setDatiQuadroNeurologicoValido(valido: boolean): void {
    this.datiQuadroNeurologicoValidoSubject.next(valido);
  }

  isDatiQuadroNeurologicoValido(): boolean {
    return this.datiQuadroNeurologicoValidoSubject.getValue();
  }

  // DATI SETTING RIBABILITATIVO
  salvaDatiSettingRiabilitativo(dati: DatiSettingRiabilitativo): void {
    this.datiSettingRiabilitativoSubject.next(dati);
    // Usa il DatiStorageService
    this.datiStorageService.saveDatiSettingRiabilitativo(dati);
  }

  getDatiSettingRiabilitativo(): DatiSettingRiabilitativo | null {
    if (!this.datiSettingRiabilitativoSubject.getValue()) {
      // Usa il DatiStorageService
      const savedData = this.datiStorageService.getDatiSettingRiabilitativoValue();
      if (savedData) {
        this.datiSettingRiabilitativoSubject.next(savedData);
      }
    }
    return this.datiSettingRiabilitativoSubject.getValue();
  }

  setDatiSettingRiabilitativoValido(valido: boolean): void {
    this.datiSettingRiabilitativoValidoSubject.next(valido);
  }

  isDatiSettingRiabilitativoValido(): boolean {
    return this.datiSettingRiabilitativoValidoSubject.getValue();
  }

  // TIPO OPERAZIONE
  getTipoOperazione(): TipoOperazione | null {
    return this._tipoOperazioneSubject.value;
  }

  setTipoOperazione(tipo: TipoOperazione): void {
    this._tipoOperazioneSubject.next(tipo);
  }

  clearTipoOperazione(): void {
    this._tipoOperazioneSubject.next(null);
  }

  // SCHEDA DETTAGLIATA
  setSchedaDettagliata(scheda: any): void {
    this.schedaDettagliataSubject.next(scheda);
  }

  getSchedaSalvata(): any {
    return this.schedaDettagliataSubject.value;
  }

  setDatiCliniciPaziente(scheda: SchedaRicoveroModel) {
    this.datiCliniciService.setDatiEziologia((scheda?.datiClinici?.schedaEziologica) || null);
    this.datiCliniciService.setDatiEziologiaRientro((scheda?.datiClinici?.schedaEziologicaRientro) || null);
    this.datiCliniciService.setDatiLesioneTrattamento((scheda?.datiClinici?.schedaLesioneTrattamento) || null);
    this.datiCliniciService.setDatiNecessitaAssistenziali((scheda?.datiClinici?.schedaNecessitaAssistenzialeIngresso) || null);
    this.datiCliniciService.setDatiQuadroNeurologico((scheda?.datiClinici?.schedaQuadroNeurologico) || null);
    this.datiCliniciService.setDatiValutazione((scheda?.datiClinici?.schedaValutazioneIngresso) || null);
    this.datiCliniciService.setDatiSettingRiabilitativo((scheda?.datiClinici?.schedaSettingRiabilitativo) || null);
    this.datiCliniciService.setDatiComplicanze((scheda?.datiClinici?.schedaComplicanze) || null);
  }

  getSchedeNuovoEvento(scheda: InputTipoScheda) {
    return this.httpClient.post<SchedaRicoveroModel>(`${BASE_URL}/scheda/getSchedeNuovoEvento`, scheda);
  }

  setDatiDimissione(scheda: SchedaRicoveroModel) {
    if (this._eventoRientroCensito || this._eventoRientroNonCensito) {
      this.datiDimissioneRientroService.setGeneraleRientro((scheda?.datiDimissioni?.schedaGeneraleDimissioni) || null);
      this.datiDimissioneRientroService.setInterventiEffettuatiRientro(scheda?.datiDimissioni?.schedaInterventiEffettuatiDimissioni || null);
    } else {
      this.datiDimissioneService.setGenerale((scheda?.datiDimissioni?.schedaGeneraleDimissioni) || null);
    }
    this.datiDimissioneService.setValutazione((scheda?.datiDimissioni?.schedaValutazioneDimissione) || null);
    this.datiDimissioneService.setNecessitaAssistenza((scheda?.datiDimissioni?.schedaNecessitaAssistenzaDimissione) || null);
    this.datiDimissioneService.setQuadroNeurologico((scheda?.datiDimissioni?.schedaQuadroNeurologicoDimissioni) || null);
    this.datiDimissioneService.setComplicanze((scheda?.datiDimissioni?.schedaComplicanzeDimissione) || null);
    this.datiDimissioneService.setQuadroFunzionale((scheda?.datiDimissioni?.schedaQuadroFunzionaleDimissione) || null);
    this.datiDimissioneService.setAusili((scheda?.datiDimissioni?.schedaAusiliDimissione) || null);
    this.datiDimissioneService.setSocioassistenziali((scheda?.datiDimissioni?.schedaAspettiSocioassistenzialiDimissione) || null);
  }

  getSchedaById(idScheda: string | number) {
    return this.httpClient.get<SchedaRicoveroModel>(`${BASE_URL}/scheda/find/${idScheda}`).pipe(
      tap(schedaDettagliata => {
        const schedaDettagliataDecodedBase64 = { ...decodeNoteFields(schedaDettagliata) };
        this.setDatiCliniciPaziente(schedaDettagliataDecodedBase64);
        this.setDatiDimissione(schedaDettagliataDecodedBase64);
        this.setSchedaCorrente(schedaDettagliataDecodedBase64);
      })
    )
  }

  updateSchedaPaziente(scheda: SchedaRicoveroModel): void {
    // Chiamata API per aggiornare la scheda
    this.httpClient.put<SchedaRicoveroModel>(`${BASE_URL}/scheda/update`, scheda)
      .subscribe(response => {
        // Aggiorna il subject solo dopo la risposta positiva dal server
        this.schedaDettagliataSubject.next(scheda);

        this.dialogService.annullaSchedaPaziente().subscribe(result => {
          if (result === 'conferma') {
            this.router.navigate(['/ricerca']);
          }
        });
      });
  }

  // salva bozza
  aggiornaSchedaRicovero(
    scheda: OutputSchedaPazienteRicoveroModel | NuovoEventoRicoveroModel,
    draft: boolean
  ): Observable<null> {
    scheda.datiClinici = encodeNoteFields(deepClone(scheda.datiClinici));
    scheda.datiDimissioni = encodeNoteFields(deepClone(scheda.datiDimissioni));
  
    const endpoint = this.isNewEvent.getValue()
      ? `${BASE_URL}/scheda/creaSchedaRientroORiabilitazione`
      : `${BASE_URL}/scheda/update`;
  
    return this.httpClient.put<number | null>(endpoint, scheda).pipe(
      catchError(error => {
        console.error("Errore durante l'aggiornamento della scheda:", error);
        return throwError(() => error);
      }),
      switchMap(idScheda => {
        if (draft && !this.isNewEvent.getValue()) {
          return this.getSchedaById(scheda.scheda.id!).pipe(
            tap(schedaData => {
              this.setSchedaCorrente(schedaData);
            }),
            map(() => null)
          );
        }
      
        if (this.isNewEvent.getValue() && idScheda) {
          this.isNewEvent.next(false);
          this.idSchedaSelectedSubject.next(idScheda);
      
          const schedaWithUpdatedId = this.schedaCreation$.value;
          schedaWithUpdatedId!.scheda.id = idScheda;
          this.schedaCreation$.next(schedaWithUpdatedId);
      
          return this.getSchedaById(idScheda).pipe(
            tap(schedaData => {
              this.setSchedaCorrente(schedaData);
              this.setSchedaDettagliata(schedaData);
            }),
            map(() => null)
          );
        }
      
        return of(null);
      })      
    );
  }
  

  private salvaDatiPazienteInStorage(idPaziente: number, pazienteInfo: PazienteInfo): void {
    // Verifica che idPaziente sia definito prima di passarlo a saveUltimoIdScheda
    this.datiStorageService.saveUltimoIdScheda(pazienteInfo.idPaziente || null);
  }

  private estraiPazienteDaScheda(scheda: any): any {
    if (!scheda) {
      return null;
    }

    if (scheda.paziente) {
      return scheda.paziente;
    } else if (scheda.scheda && scheda.scheda.paziente) {
      return scheda.scheda.paziente;
    } else if (scheda.evento && scheda.evento.scheda && scheda.evento.scheda.paziente) {
      return scheda.evento.scheda.paziente;
    }
    return null;
  }


  setSchedaSalvata(scheda: any): void {
    this._schedaSalvata = scheda;
    this.datiStorageService.saveSchedaDettagliata(scheda);
  }


  aggiungiSchedaRicovero(request: AggiungiSchedaRicoveroRequest) {
    request = convertMomentInRequest(request)

    return this.httpClient.post<OutputCreazioneScheda>(`${BASE_URL}/scheda/aggiungiSchedaRicovero`, request);
  }

  getPazienteById(idPaziente: number) {
    return this.httpClient.get<PazienteModel>(`${BASE_URL}/paziente/find/${idPaziente}`)
  }

  updatePaziente(patientInfo: PazienteModel) {
    //restituisce true se ci sono dati mancanti nei socio sanitari false altrimenti
    return this.httpClient.post<boolean>(`${BASE_URL}/paziente/update`, patientInfo).pipe(
      tap(response => {
        this.datiMancantiSocioEconomiciSubject.next(response);
      })
    );
  }


  identificaCittadino(codiceFiscale: string) {
    // MOCK NICCE

    // return of(
    //     {
    //       "nome": "CINQUE",
    //       "cognome": "TESTGV",
    //       "sesso": "M",
    //       "dataNascita": "1968-04-04",
    //       "comuneNascita": {
    //         "comKeyId": 1850,
    //         "cdComuneISTAT": "015146",
    //         "dsComune": "MILANO",
    //         "cdCAP": "20100",
    //         "provincia": {"provKeyId": 15, "cdProvinciaISTAT": "015", "dsProvincia": "MILANO", "cdSigla": "MI"}
    //       },
    //       "nazioneNascita": null,
    //       "residenza": {
    //         "comune": {
    //           "comKeyId": 1850,
    //           "cdComuneISTAT": "015146",
    //           "dsComune": "MILANO",
    //           "cdCAP": "20100",
    //           "provincia": {"provKeyId": 15, "cdProvinciaISTAT": "015", "dsProvincia": "MILANO", "cdSigla": "MI"}
    //         },
    //         "nazione": null, "indirizzo": "GALL. MILANO", "numeroCivico": "5"
    //       },
    //       "domicilio": {
    //         "indirizzo": "GAsLL. MILANO", "numeroCivico": "5",
    //         "comune": {
    //           "comKeyId": 1850,
    //           "cdComuneISTAT": "015146",
    //           "dsComune": "MILANO",
    //           "cdCAP": "20100",
    //           "provincia": {"provKeyId": 15, "cdProvinciaISTAT": "015", "dsProvincia": "MILANO", "cdSigla": "MI"}
    //         },
    //       }
    //     }
    // ) as unknown as Observable<SISSModel>

    return this.httpClient.get<SISSModel>(`${BASE_URL}/paziente/identifica/${codiceFiscale}`);
  }

  setInitialAnagraficiData(data: DatiAnagrafici): void {
    if (!data) return;

    try {
      this.initialAnagraficiData = JSON.parse(JSON.stringify(data));


      if (this.getTipoOperazione() === 'edit-patient' &&
        (!this.datiAnagraficiSubject.getValue() ||
          Object.keys(this.datiAnagraficiSubject.getValue()).length === 0)) {
        this.datiAnagraficiSubject.next(JSON.parse(JSON.stringify(data)));
      }
    } catch (e) {
      console.error('Errore nella clonazione dei dati iniziali:', e);
    }
  }

  setInitialSocioEconomiciData(data: any): void {
    this.initialSocioEconomiciData = data || null;
  }

  getInitialAnagraficiData(): any {
    return this.initialAnagraficiData;
  }

  getInitialSocioEconomiciData(): any {
    return this.initialSocioEconomiciData;
  }

  getDatiMancantiSocioEconomici(): boolean {
    return this.datiMancantiSocioEconomiciSubject.getValue();
  }

  setDatiMancantiSocioEconomici(value: boolean): void {
    this.datiMancantiSocioEconomiciSubject.next(value);
  }

  setIsCittadinoIdentificato(value: boolean): void {
    this.datiStorageService.setCittadinoIdentificato(value);
  }

  getIsCittadinoIdentificato(): boolean {
    return this.datiStorageService.getCittadinoIdentificatoValue();
  }

  clearDatiAnagrafici(): void {
    this.datiStorageService.resetDatiAnagrafici();
  }

  clearDatiGenerali(): void {
    this.datiStorageService.resetDatiGenerali();
  }

  clearDatiSocioEconomici(): void {
    this.datiStorageService.resetDatiSocioEconomici();
  }

  // VALUTAZIONE INGRESSO VALIDO
  setValutazioneIngressoValido(valido: boolean): void {
    this.valutazioneIngressoValidoSubject.next(valido);
  }


  getCittadinoIdentificato$(): Observable<boolean> {
    return this.datiStorageService.getCittadinoIdentificato$();
  }

  setCampiDisabilitati(campi: string[]): void {
    this.datiStorageService.setCampiDisabilitati(campi);
  }

  getCampiDisabilitati(): string[] {
    return this.datiStorageService.getCampiDisabilitatiValue();
  }

  getCampiDisabilitati$(): Observable<string[]> {
    return this.datiStorageService.getCampiDisabilitati$();
  }

  setUltimaSchedaId(id: number): void {
    this.ultimaSchedaIdSubject.next(id);
  }

  getUltimaSchedaId(): number | null {
    return this.ultimaSchedaIdSubject.value;
  }

  setSchedaCorrente(scheda: any): void {
    this.schedaCorrenteSubject.next(scheda);
  }

  getSchedaCorrente(): any {
    return this.schedaCorrenteSubject.value;
  }

  getSchedaCorrente$(): Observable<any> {
    return this.schedaCorrenteSubject.asObservable();
  }

  checkCodiceFiscale(paziente: PazienteModel) {
    if (paziente.tipoCodiceIdentificativo.idDizionario === TipoCodiceIdentEnum.CF
      && (paziente.comuneNascita as ComuneModel)?.cdComuneISTAT
      && paziente.genere.idDizionario !== EGenereSessoID.N) {
      return this.httpClient.post<boolean>(`${BASE_URL}/paziente/verifica/codicefiscale`, paziente);
    }
    return of(true)
  }


  getCampiObbligatoriMancanti(scheda: SchedaRicoveroReqModel, evento: EventoModel) {
    return this.httpClient.post<SchedaRicoveroDatiMancanti>(`${BASE_URL}/scheda/campiObbligatoriMancanti`, { scheda, evento });
  }

  areAllCampiValid(campi: any): boolean {
    const flattenValues = (obj: any): boolean[] =>
      Object.values(obj).flatMap(value => {
        if (value !== null && typeof value === 'object') {
          return flattenValues(value);
        }
        return typeof value === 'boolean' ? [value] : [];
      });

    return !flattenValues(campi).some(val => val === true);
  }

  getCronologia(obj: {idScheda: number, idTipoScheda: ETipoScheda | null}) { // null è scheda dati generali
    return this.httpClient.post<OutputHistoryDTO>(`${BASE_URL}/scheda/history`, obj);
  }
  
  setEventoRientroNonCensito(value: boolean): void {
    this._eventoRientroNonCensito = value;
  }

  getEventoRientroNonCensito(): boolean {
    return this._eventoRientroNonCensito;
  }

  setEventoRientroCensito(value: boolean): void {
    this._eventoRientroCensito = value;
  }

  getEventoRientroCensito(): boolean {
    return this._eventoRientroCensito;
  }

}
