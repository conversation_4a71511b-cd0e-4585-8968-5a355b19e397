import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class PazienteStorageService {
    private pazienteId$ = new BehaviorSubject<number | null>(null);
    private pazienteNome$ = new BehaviorSubject<string>('');
    private pazienteCognome$ = new BehaviorSubject<string>('');
    private pazienteNomeCompleto$ = new BehaviorSubject<string>('');
    private pazienteCodice$ = new BehaviorSubject<string>('');
    private pazienteDataNascita$ = new BehaviorSubject<string>('');
    
    private isUpdatingFromNomeCompleto = false;
    private isUpdatingNomeCompleto = false;

    constructor() {}

    getPazienteId$(): Observable<number | null> {
        return this.pazienteId$.asObservable();
    }

    getPazienteNome$(): Observable<string> {
        return this.pazienteNome$.asObservable();
    }

    getPazienteCognome$(): Observable<string> {
        return this.pazienteCognome$.asObservable();
    }

    getPazienteNomeCompleto$(): Observable<string> {
        return this.pazienteNomeCompleto$.asObservable();
    }

    getPazienteCodice$(): Observable<string> {
        return this.pazienteCodice$.asObservable();
    }

    getPazienteDataNascita$(): Observable<string> {
        return this.pazienteDataNascita$.asObservable();
    }

    getPazienteIdValue(): number | null {
        return this.pazienteId$.getValue();
    }

    getPazienteNomeValue(): string {
        return this.pazienteNome$.getValue();
    }

    getPazienteCognomeValue(): string {
        return this.pazienteCognome$.getValue();
    }

    getPazienteNomeCompletoValue(): string {
        return this.pazienteNomeCompleto$.getValue();
    }

    getPazienteCodiceValue(): string {
        return this.pazienteCodice$.getValue();
    }

    getPazienteDataNascitaValue(): string {
        return this.pazienteDataNascita$.getValue();
    }

    setPazienteId(id: number | null): void {
        this.pazienteId$.next(id);
    }

    setPazienteNome(nome: string): void {
        this.pazienteNome$.next(nome);
        if (!this.isUpdatingFromNomeCompleto) {
            this.aggiornaNomeCompleto();
        }
    }

    setPazienteCognome(cognome: string): void {
        this.pazienteCognome$.next(cognome);
        if (!this.isUpdatingFromNomeCompleto) {
            this.aggiornaNomeCompleto();
        }
    }

    setPazienteNomeCompleto(nomeCompleto: string): void {
        this.pazienteNomeCompleto$.next(nomeCompleto);
        
        if (!this.isUpdatingNomeCompleto && nomeCompleto) {
            this.isUpdatingFromNomeCompleto = true;
            
            const parti = nomeCompleto.trim().split(' ');
            if (parti.length > 1) {
                this.pazienteNome$.next(parti[0]);
                this.pazienteCognome$.next(parti.slice(1).join(' '));
            } else {
                this.pazienteNome$.next(nomeCompleto);
                this.pazienteCognome$.next('');
            }
            
            this.isUpdatingFromNomeCompleto = false;
        }
    }

    setPazienteCodice(codice: string): void {
        this.pazienteCodice$.next(codice);
    }

    setPazienteDataNascita(dataNascita: string): void {
        this.pazienteDataNascita$.next(dataNascita);
    }

    private aggiornaNomeCompleto(): void {
        if (this.isUpdatingFromNomeCompleto) {
            return;
        }
        
        this.isUpdatingNomeCompleto = true;
        
        const nome = this.pazienteNome$.getValue();
        const cognome = this.pazienteCognome$.getValue();
        
        if (nome && cognome) {
            this.pazienteNomeCompleto$.next(`${nome} ${cognome}`);
        } else if (nome) {
            this.pazienteNomeCompleto$.next(nome);
        } else if (cognome) {
            this.pazienteNomeCompleto$.next(cognome);
        } else {
            this.pazienteNomeCompleto$.next('');
        }
        
        this.isUpdatingNomeCompleto = false;
    }

    resetPazienteData(): void {
        this.pazienteId$.next(null);
        this.pazienteNome$.next('');
        this.pazienteCognome$.next('');
        this.pazienteNomeCompleto$.next('');
        this.pazienteCodice$.next('');
        this.pazienteDataNascita$.next('');
    }
} 