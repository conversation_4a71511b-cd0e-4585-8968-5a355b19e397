import { Injectable } from '@angular/core';
import { AbstractControl, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  SchedaComplicanzeModel,
  SchedaEziologiaRientroModel,
  SchedaEziologicaModel,
  SchedaLesioneTrattamentoModel,
  SchedaNecessitaAssistenzialeIngressoModel,
  SchedaQuadroNeurologicoModel,
  SchedaSettingRiabilitativoModel,
  SchedaValutazioneIngressoModel
} from '../../shared/interfaces/dati-clinici.interface';
import { DizionarioModel } from '../../shared/interfaces/scheda-ricovero.interface';
import { dateNotFutureValidator, getDateTimeErrorMessage, timeValidator } from '../../shared/validators/date-time.validator';

@Injectable({
  providedIn: 'root'
})
export class DatiCliniciService {

  private datiEziologia$ = new BehaviorSubject<SchedaEziologicaModel | null>(null);
  private datiEziologiaValido$ = new BehaviorSubject<boolean | null>(null);
  private datiEziologiaRientro$ = new BehaviorSubject<SchedaEziologiaRientroModel | null>(null);
  private datiEziologiaRientroValido$ = new BehaviorSubject<boolean | null>(null);
  private datiLesioneTrattamento$ = new BehaviorSubject<SchedaLesioneTrattamentoModel | null>(null);
  private datiLesioneTrattamentoValido$ = new BehaviorSubject<boolean | null>(null);
  private datiValutazione$ = new BehaviorSubject<SchedaValutazioneIngressoModel | null>(null);
  private datiValutazioneValido$ = new BehaviorSubject<boolean | null>(null);
  private datiNecessitaAssistenziali$ = new BehaviorSubject<SchedaNecessitaAssistenzialeIngressoModel | null>(null);
  private datiNecessitaAssistenzialiValido$ = new BehaviorSubject<boolean | null>(null);
  private datiQuadroNeurologico$ = new BehaviorSubject<SchedaQuadroNeurologicoModel | null>(null);
  private datiQuadroNeurologicoValido$ = new BehaviorSubject<boolean | null>(null);
  private datiSettingRiabilitativo$ = new BehaviorSubject<SchedaSettingRiabilitativoModel | null>(null);
  private datiSettingRiabilitativoValido$ = new BehaviorSubject<boolean | null>(null);
  private datiComplicanze$ = new BehaviorSubject<SchedaComplicanzeModel | null>(null);
  private datiComplicanzeValido$ = new BehaviorSubject<boolean | null>(null);

  // Getter, setter e reset per ogni sezione:

  // =================== EZIOLOGIA ===================

  getDatiEziologia$(): Observable<SchedaEziologicaModel | null> {
    return this.datiEziologia$.asObservable();
  }

  getDatiEziologiaValue(): SchedaEziologicaModel | null {
    return this.datiEziologia$.getValue();
  }

  setDatiEziologia(dati: SchedaEziologicaModel | null): void {
    this.datiEziologia$.next(dati);
  }

  resetDatiEziologia(): void {
    this.datiEziologia$.next(null);
  }

  // =================== EZIOLOGIA RIENTRO ===================

  getDatiEziologiaRientro$(): Observable<SchedaEziologiaRientroModel | null> {
    return this.datiEziologiaRientro$.asObservable();
  }

  getDatiEziologiaRientroValue(): SchedaEziologiaRientroModel | null {
    return this.datiEziologiaRientro$.getValue();
  }

  setDatiEziologiaRientro(dati: SchedaEziologiaRientroModel | null): void {
    this.datiEziologiaRientro$.next(dati);
  }

  resetDatiEziologiaRientro(): void {
    this.datiEziologiaRientro$.next(null);
  }

  // =================== LESIONE TRATTAMENTO ===================

  getDatiLesioneTrattamento$(): Observable<SchedaLesioneTrattamentoModel | null> {
    return this.datiLesioneTrattamento$.asObservable();
  }

  getDatiLesioneTrattamentoValue(): SchedaLesioneTrattamentoModel | null {
    return this.datiLesioneTrattamento$.getValue();
  }

  setDatiLesioneTrattamento(dati: SchedaLesioneTrattamentoModel | null): void {
    this.datiLesioneTrattamento$.next(dati);
  }

  resetDatiLesioneTrattamento(): void {
    this.datiLesioneTrattamento$.next(null);
  }

  // =================== VALUTAZIONE IN INGRESSO ===================

  getDatidatiValutazione$(): Observable<SchedaValutazioneIngressoModel | null> {
    return this.datiValutazione$.asObservable();
  }

  getDatiValutazioneValue(): SchedaValutazioneIngressoModel | null {
    return this.datiValutazione$.getValue();
  }

  setDatiValutazione(dati: SchedaValutazioneIngressoModel | null): void {
    this.datiValutazione$.next(dati);
  }

  resetDatidatiValutazione(): void {
    this.datiLesioneTrattamento$.next(null);
  }

  // =================== NECESSITÀ ASSISTENZIALI IN INGRESSO ===================

  getDatiNecessitaAssistenziali$(): Observable<SchedaNecessitaAssistenzialeIngressoModel | null> {
    return this.datiNecessitaAssistenziali$.asObservable();
  }

  getDatiNecessitaAssistenzialiValue(): SchedaNecessitaAssistenzialeIngressoModel | null {
    return this.datiNecessitaAssistenziali$.getValue();
  }

  setDatiNecessitaAssistenziali(dati: SchedaNecessitaAssistenzialeIngressoModel | null): void {
    this.datiNecessitaAssistenziali$.next(dati);
  }

  resetDatiNecessitaAssistenziali(): void {
    this.datiNecessitaAssistenziali$.next(null);
  }

  // =================== QUADRO NEUROLOGICO ===================

  getDatiQuadroNeurologico$(): Observable<SchedaQuadroNeurologicoModel | null> {
    return this.datiQuadroNeurologico$.asObservable();
  }

  getDatiQuadroNeurologicoValue(): SchedaQuadroNeurologicoModel | null {
    return this.datiQuadroNeurologico$.getValue();
  }

  setDatiQuadroNeurologico(dati: SchedaQuadroNeurologicoModel | null): void {
    this.datiQuadroNeurologico$.next(dati);
  }

  resetDatiQuadroNeurologico(): void {
    this.datiQuadroNeurologico$.next(null);
  }

  // =================== SETTING RIABILITATIVO ===================

  getDatiSettingRiabilitativo$(): Observable<SchedaSettingRiabilitativoModel | null> {
    return this.datiSettingRiabilitativo$.asObservable();
  }

  getDatiSettingRiabilitativoValue(): SchedaSettingRiabilitativoModel | null {
    return this.datiSettingRiabilitativo$.getValue();
  }

  setDatiSettingRiabilitativo(dati: SchedaSettingRiabilitativoModel | null): void {
    this.datiSettingRiabilitativo$.next(dati);
  }

  resetDatiSettingRiabilitativo(): void {
    this.datiSettingRiabilitativo$.next(null);
  }

  // =================== COMPLICANZE ===================

  getDatiComplicanze$(): Observable<SchedaComplicanzeModel | null> {
    return this.datiComplicanze$.asObservable();
  }

  getDatiComplicanzeValue(): SchedaComplicanzeModel | null {
    return this.datiComplicanze$.getValue();
  }

  setDatiComplicanze(dati: SchedaComplicanzeModel | null): void {
    this.datiComplicanze$.next(dati);
  }

  resetDatiComplicanze(): void {
    this.datiComplicanze$.next(null);
  }

  // =================== EZIOLOGIA - VALIDO ===================

  getDatiEziologiaValido$(): Observable<boolean | null> {
    return this.datiEziologiaValido$.asObservable();
  }

  getDatiEziologiaValidoValue(): boolean | null {
    return this.datiEziologiaValido$.getValue();
  }

  setDatiEziologiaValido(valido: boolean | null): void {
    this.datiEziologiaValido$.next(valido);
  }

  resetDatiEziologiaValido(): void {
    this.datiEziologiaValido$.next(null);
  }

  // =================== EZIOLOGIA RIENTRO - VALIDO ===================

  getDatiEziologiaRientroValido$(): Observable<boolean | null> {
    return this.datiEziologiaRientroValido$.asObservable();
  }

  getDatiEziologiaRientroValidoValue(): boolean | null {
    return this.datiEziologiaRientroValido$.getValue();
  }

  setDatiEziologiaRientroValido(valido: boolean | null): void {
    this.datiEziologiaRientroValido$.next(valido);
  }

  resetDatiEziologiaRientroValido(): void {
    this.datiEziologiaRientroValido$.next(null);
  }

  // =================== LESIONE TRATTAMENTO - VALIDO ===================

  getDatiLesioneTrattamentoValido$(): Observable<boolean | null> {
    return this.datiLesioneTrattamentoValido$.asObservable();
  }

  getDatiLesioneTrattamentoValidoValue(): boolean | null {
    return this.datiLesioneTrattamentoValido$.getValue();
  }

  setDatiLesioneTrattamentoValido(valido: boolean | null): void {
    this.datiLesioneTrattamentoValido$.next(valido);
  }

  resetDatiLesioneTrattamentoValido(): void {
    this.datiLesioneTrattamentoValido$.next(null);
  }

  // =================== VALUTAZIONE - VALIDO ===================

  getDatiValutazioneValido$(): Observable<boolean | null> {
    return this.datiValutazioneValido$.asObservable();
  }

  getDatiValutazioneValidoValue(): boolean | null {
    return this.datiValutazioneValido$.getValue();
  }

  setDatiValutazioneValido(valido: boolean | null): void {
    this.datiValutazioneValido$.next(valido);
  }

  resetDatiValutazioneValido(): void {
    this.datiValutazioneValido$.next(null);
  }

  // =================== NECESSITÀ ASSISTENZIALI - VALIDO ===================

  getDatiNecessitaAssistenzialiValido$(): Observable<boolean | null> {
    return this.datiNecessitaAssistenzialiValido$.asObservable();
  }

  getDatiNecessitaAssistenzialiValidoValue(): boolean | null {
    return this.datiNecessitaAssistenzialiValido$.getValue();
  }

  setDatiNecessitaAssistenzialiValido(valido: boolean | null): void {
    this.datiNecessitaAssistenzialiValido$.next(valido);
  }

  resetDatiNecessitaAssistenzialiValido(): void {
    this.datiNecessitaAssistenzialiValido$.next(null);
  }

  // =================== QUADRO NEUROLOGICO - VALIDO ===================

  getDatiQuadroNeurologicoValido$(): Observable<boolean | null> {
    return this.datiQuadroNeurologicoValido$.asObservable();
  }

  getDatiQuadroNeurologicoValidoValue(): boolean | null {
    return this.datiQuadroNeurologicoValido$.getValue();
  }

  setDatiQuadroNeurologicoValido(valido: boolean | null): void {
    this.datiQuadroNeurologicoValido$.next(valido);
  }

  resetDatiQuadroNeurologicoValido(): void {
    this.datiQuadroNeurologicoValido$.next(null);
  }

  // =================== SETTING RIABILITATIVO - VALIDO ===================

  getDatiSettingRiabilitativoValido$(): Observable<boolean | null> {
    return this.datiSettingRiabilitativoValido$.asObservable();
  }

  getDatiSettingRiabilitativoValidoValue(): boolean | null {
    return this.datiSettingRiabilitativoValido$.getValue();
  }

  setDatiSettingRiabilitativoValido(valido: boolean | null): void {
    this.datiSettingRiabilitativoValido$.next(valido);
  }

  resetDatiSettingRiabilitativoValido(): void {
    this.datiSettingRiabilitativoValido$.next(null);
  }

  // =================== COMPLICANZE - VALIDO ===================

  getDatiComplicanzeValido$(): Observable<boolean | null> {
    return this.datiComplicanzeValido$.asObservable();
  }

  getDatiComplicanzeValidoValue(): boolean | null {
    return this.datiComplicanzeValido$.getValue();
  }

  setDatiComplicanzeValido(valido: boolean | null): void {
    this.datiComplicanzeValido$.next(valido);
  }

  resetDatiComplicanzeValido(): void {
    this.datiComplicanzeValido$.next(null);
  }

  // Reset totale opzionale
  resetAllDatiClinici(): void {
    this.resetDatiEziologia();
    this.resetDatiEziologiaValido();
    this.resetDatiLesioneTrattamento();
    this.resetDatiLesioneTrattamentoValido();
    this.resetDatiNecessitaAssistenziali();
    this.resetDatiNecessitaAssistenzialiValido();
    this.resetDatiQuadroNeurologico();
    this.resetDatiQuadroNeurologicoValido();
    this.resetDatiSettingRiabilitativo();
    this.resetDatiSettingRiabilitativoValido();
    this.resetDatiComplicanze();
    this.resetDatiComplicanzeValido();
  }

  // Controlla la validità di tutte le sezioni
  isDatiCliniciValido(): boolean {
    return this.getDatiEziologiaValidoValue() || false &&
      this.getDatiEziologiaRientroValidoValue() || false &&
      this.getDatiLesioneTrattamentoValidoValue() || false &&
      this.getDatiValutazioneValidoValue() || false &&
      this.getDatiNecessitaAssistenzialiValidoValue() || false &&
      this.getDatiQuadroNeurologicoValidoValue() || false &&
      this.getDatiSettingRiabilitativoValidoValue() || false &&
      (
        this.getDatiComplicanzeValue() ? this.getDatiComplicanzeValidoValue() : true
      )

  }

  // =================== METODI COMUNI ===================

  /**
   * Converte un valore DizionarioModel in boolean
   */
  convertBooleanValue(val: DizionarioModel | null): boolean | null {
    if (!val || !val.descrizione) return null;
    if (val.descrizione.toUpperCase() === 'SI') return true;
    if (val.descrizione.toUpperCase() === 'NO') return false;
    return null;
  }

  /**
   * Imposta un controllo con valore boolean
   */
  populateBooleanControl(control: AbstractControl | null, booleanValue: boolean | null | undefined, optionsBoolean: DizionarioModel[]): void {
    if (booleanValue === null || booleanValue === undefined || !control) return;

    const valoreBool = booleanValue ?
      optionsBoolean.find(opt => opt.descrizione === 'SI') :
      optionsBoolean.find(opt => opt.descrizione === 'NO');

    if (valoreBool) {
      control.setValue(valoreBool, { emitEvent: false });
    }
  }

  /**
   * Gestisce la deselezionabilità dei radio button
   */
  toggleRadioSelection(control: AbstractControl | null, option: any, event: MouseEvent): void {
    if (!control || control.disabled) return;

    const currentValue = control.value;
    const isDizionarioMatch =
      currentValue?.idDizionario && option?.idDizionario &&
      currentValue.idDizionario === option.idDizionario;

    const isPrimitiveMatch = currentValue === option;

    if (isDizionarioMatch || isPrimitiveMatch) {
      event.preventDefault();
      setTimeout(() => {
        control.setValue(null);
        control.markAsPristine();
        control.markAsUntouched();
        control.updateValueAndValidity({ emitEvent: true });
        const radioButton = event.target as HTMLElement;
        const input = radioButton.closest('mat-radio-button')?.querySelector('input[type="radio"]') as HTMLInputElement;
        input.blur();
      }, 0);
    }
  }

  /**
   * Verifica se un controllo ha il validatore required
   */
  isRequired(control: AbstractControl | null): boolean {
    if (!control || !control.validator) return false;
    const validator = control.validator({} as any);
    return validator && validator['required'];
  }

  /**
   * Adatta l'altezza del dialog
   */
  adjustDialogHeight(): void {
    setTimeout(() => {
      const dialogSurfaces = document.querySelectorAll('.mdc-dialog__surface');
      (Array.from(dialogSurfaces) as HTMLElement[]).forEach(surface => {
        surface.style.setProperty('height', 'auto', 'important');
        surface.style.setProperty('max-height', '80vh', 'important');
      });
    }, 100);
  }

  /**
   * Limita l'input a massimo 2 cifre numeriche
   */
  restrictToTwoDigits(event: KeyboardEvent): boolean {
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'];
    if (allowedKeys.includes(event.key)) {
      return true;
    }
    const isDigit = /^\d$/.test(event.key);
    if (!isDigit) {
      return false;
    }
    const input = event.target as HTMLInputElement;
    const selectionStart = input.selectionStart ?? 0;
    const selectionEnd = input.selectionEnd ?? 0;
    const hasSelection = selectionStart !== selectionEnd;
    let newValue: string;
    if (hasSelection) {
      newValue = input.value.substring(0, selectionStart) + 
                event.key + 
                input.value.substring(selectionEnd);
    } else {
      newValue = input.value.substring(0, selectionStart) + 
                event.key + 
                input.value.substring(selectionStart);
    }
    return newValue.length <= 2;
  }

  validateDate(control: AbstractControl | null, value: any): ValidationErrors | null {
    if (!control) return null;
    return dateNotFutureValidator()(control);
  }

  validateTime(control: AbstractControl | null, value: string | null, dataTrauma?: Date): boolean {
    if (!control) return false;
    
    const getRelatedDate = dataTrauma ? () => dataTrauma : undefined;
    const errors = timeValidator(getRelatedDate)(control);
    
    control.setErrors(errors);
    return !errors;
  }

  getDateTimeErrorMessage(control: AbstractControl | null): string {
    return getDateTimeErrorMessage(control);
  }

  resetNoteFields(form: FormGroup, noteFields: string[]): void {
    noteFields.forEach(field => {
      const control = form.get(field);
      if (control) {
        control.setValue(null);
        control.disable({ emitEvent: false });
      }
    });
  }

}
