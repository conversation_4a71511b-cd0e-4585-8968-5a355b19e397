import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import {Component, ElementRef, LOCALE_ID, OnInit, Renderer2, ViewEncapsulation} from '@angular/core';
import { RouterLink, RouterLinkActive, RouterModule, RouterOutlet } from '@angular/router';
import { InfoOperatoreDTO } from "./core/interfaces/info-operatore.interface";
import { LoaderComponent } from "./core/loader/loader.component";
import { OperatoreService } from "./core/services/operatore.service";
import {MAT_DATE_FORMATS, MAT_DATE_LOCALE} from "@angular/material/core";
import {IT_FORMATS} from "./shared/utils/it-formats";
import {MAT_MOMENT_DATE_ADAPTER_OPTIONS, provideMomentDateAdapter} from "@angular/material-moment-adapter";
import moment from "moment/moment";
import 'moment/locale/it';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, HttpClientModule, RouterOutlet, LoaderComponent, RouterModule, RouterLink, RouterLinkActive],
  templateUrl: './app.component.html',
  providers: [
    { provide: MAT_DATE_LOCALE, useValue: 'it-IT' },
    provideMomentDateAdapter(),
    { provide: MAT_DATE_FORMATS, useValue: IT_FORMATS },
    { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { strict: true, useUtc: false } }
  ],
  styleUrl: './app.component.scss',
  encapsulation: ViewEncapsulation.None,
  host: {
    '[attr.aria-hidden]': 'null'
  }
})
export class AppComponent implements OnInit{
  titolo = 'Registro delle mielolesioni';
  versione = '1.00';

  operatore:  InfoOperatoreDTO = {};

  constructor(private service: OperatoreService, private el: ElementRef, private renderer: Renderer2) {
    moment.updateLocale('it', {
      weekdaysShort: ['Dom', 'Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab'],
      weekdaysMin: ['Dom', 'Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab'],
    });
    this.service.getInfoOperatore().subscribe((res)=>{
      this.operatore = res;
    })
    this.service.setOperatore(this.operatore);
  }

  ngAfterViewInit() {
    this.preventShrink();
  }

   ngOnInit() {
    const hostElement = this.el.nativeElement;
    this.renderer.removeAttribute(hostElement, 'aria-hidden');

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'aria-hidden') {
          this.renderer.removeAttribute(hostElement, 'aria-hidden');
        }
      });
    });

    observer.observe(hostElement, { attributes: true });
  }


  /**Importaante: serve ad impedire all'header di rimpicciolirsi durante lo scroll */
  preventShrink() {
    const targetElements = [
      this.el.nativeElement.querySelector('.it25-top-bar'),
      this.el.nativeElement.querySelector('.it25-barra-ist'),
      this.el.nativeElement.querySelector('.it25-menu-principale')
    ];

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          targetElements.forEach((el) => {
            if (el?.classList.contains('shrink')) {
              this.renderer.removeClass(el, 'shrink');
            }
            if (el?.classList.contains('header-shrinked')) {
              this.renderer.removeClass(el, 'header-shrinked');
            }
          });
        }
      });
    });

    // Osserva i cambiamenti sulle classi dei target
    targetElements.forEach((el) => {
      if (el) {
        observer.observe(el, { attributes: true });
      }
    });
  }


}
