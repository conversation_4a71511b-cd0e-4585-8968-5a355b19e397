import { CentriRicoveroModel, DizionarioModel } from "./scheda-ricovero.interface";
import { CheckBoxDictionaryModel, CheckBoxNumeroModel, CheckBoxSempliceModel, UnitaOperativaModel } from "./shared/shared.interface";

// 1. DATI GENERALI DI DIMISSIONI
// Su java: SchedaGeneraleDimissioniDTO

export interface SchedaGeneraleDimissioniModel {
    altroCondizioneDimissione: string;
    altroRepartoDestinazione: string;
    causa: string;
    condizioneDimissione: DizionarioModel;
    dataCreazione: Date;
    dataOraDecesso: Date;
    dataOraDimissione: Date;
    dataUltimoAggiornamento: Date;
    destinazione: DizionarioModel;
    idScheda: number;
    idSchedaDimissioni: number;
    indicazioniRiabilitativeDimissione: string;
    modalitaDimissioneSdo: DizionarioModel;
    nomeScheda: string;
    noteCondizioneDimissione: string;
    noteDestinazione: string;
    noteIndicazioniRiabilitativeDimissione: string;
    noteTipologiaRiabilitativa: string;
    noteGeneraliDimissione: string
    noteProblematicheDimProgrammata: string
    problematicheLista: CheckBoxSempliceModel[];
    repartoDestinazione: CentriRicoveroModel;
    ricoveroIn: UnitaOperativaModel;
    strutturaDestinazione: DizionarioModel;
    strutturaDestinazioneAlternativo: string;
    tipologiaRiabilitativa: DizionarioModel;
    altroProblematicheDimProgrammata: string;
}

// 2. VALUTAZIONE ALLA DIMISSIONI
// Su java: SchedaValutazioneDimissioneDTO

export interface SchedaValutazioneDimissioneModel {
    collaborazione: DizionarioModel;
    deficitCognitivo: DizionarioModel;
    idScheda: number;
    nomeScheda: string;
    noteCollaborazione: string;
    noteDeficitCognitivo: string;
    noteOrientamentoSpaziale: string;
    noteOrientamentoTemporale: string;
    noteVigilanza: string;
    orientamentoSpaziale: DizionarioModel;
    orientamentoTemporale: DizionarioModel;
    vigilanza: DizionarioModel;
}

// 3. NECESSITÀ ASSISTENZIALI IN DIMISSIONI
// Su java: SchedaNecessitaAssistenzaDimissioneDTO

export interface SchedaNecessitaAssistenzaDimissioneModel {
    accessoVenoso: boolean;
    alimentazione: DizionarioModel;
    altroTrattamentiEffettuati: string;
    alvoAutonomia: boolean;
    alvoContinente: boolean;
    gestioneAlvoDimissioneLista: CheckBoxSempliceModel[];
    gestioneVescicale: DizionarioModel;
    idScheda: number;
    nomeScheda: string;
    noteAccessoVenoso: string;
    noteAlimentazione: string;
    noteAlvoAutonomia: string;
    noteAlvoContinente: string;
    noteGestioneAlvo: string;
    noteGestioneVescicale: string;
    noteRespirazione: string;
    noteTipoMaterasso: string;
    noteTracheostomia: string;
    noteTrattamentiEffettuati: string;
    respirazioneDimissioneLista: CheckBoxNumeroModel[];
    tipoMaterasso: DizionarioModel;
    tracheostomia: boolean;
    trattamentiEffettuatiLista: CheckBoxSempliceModel[];
}

// 4. QUADRO NEUROLOGICO
// Su java: SchedaQuadroNeurologicoDimissioneDTO

export interface SchedaQuadroNeurologicoDimissioneModel {
    ais: string;
    asia: DizionarioModel;
    dataValutazioneAsia: string;
    dataValutazioneNeurologica: string;
    idScheda: number;
    livelloMotorioDx: DizionarioModel;
    livelloMotorioSx: DizionarioModel;
    livelloNeurologico: DizionarioModel;
    livelloSensitivoDx: DizionarioModel;
    livelloSensitivoSx: DizionarioModel;
    nomeScheda: string;
    noteAis: string;
    noteAsia: string;
    notePunteggi: string;
    punteggioLivelloMotorioDx: string;
    punteggioLivelloMotorioSx: string;
    punteggioLivelloNeurologico: string;
    punteggioLivelloSensitivoDx: string;
    punteggioLivelloSensitivoSx: string;
}

// 5. COMPLICANZE PRESENTI ALLE DIMISSIONI
// Su java: SchedaComplicanzeDimissioneDTO

export interface SchedaComplicanzeDimissioneModel {
    altroComplicanzeRespiratorie: string;
    altroComplicanzeUrologiche: string;
    altroRachide: string;
    altroSedeOssificazione: string;
    altroSedePressione: string;
    complicanzeRespiratorieDimissioneLista: CheckBoxSempliceModel[];
    complicanzeUrologicheDimissioneLista: CheckBoxSempliceModel[];
    dataCreazione?: Date;
    dolore: boolean;
    doloreDimissioneLista: CheckBoxSempliceModel[];
    emboliaPolmonare: boolean;
    idScheda: number;
    infezioneContaminazioneMultiresistente: boolean;
    lesioniPressione: boolean;
    lesioniPressioneLista: CheckBoxNumeroModel[];
    nomeScheda?: string;
    noteComplicanzeRespiratorie: string;
    noteComplicanzeUrologiche: string;
    noteDolore: string;
    noteEmbolia: string;
    noteMultiresistente: string;
    noteOssificazione: string;
    noteOsteomielite: string;
    notePressione: string;
    noteRachide: string;
    noteSepsi: string;
    noteTrombosi: string;
    ossificazioneDimissioneLista: CheckBoxDictionaryModel[];
    ossificazioneEterotopiche: boolean;
    osteomielite: boolean;
    rachideDimissioneLista: CheckBoxSempliceModel[];
    sepsi: boolean;
    trombosiDimissioneLista: CheckBoxSempliceModel[];
    trombosiVenosaProfonda: boolean;
}

// 6. QUADRO FUNZIONALE ALLA DIMISSIONI E TRATTAMENTI SPECIALISTICI
// Su java: SchedaQuadroFunzionaleDimissioneDTO

export interface SchedaQuadroFunzionaleDimissioneModel {
    ais: string;
    altroTrattamentiSpasticita: string;
    consulenzaDisfunzionaleSessuale: boolean;
    idScheda: number;
    nomeScheda: string;
    noteConsulenzaDisfunzionaleSessuale: string;
    notePunteggioScalaAshworth: string;
    noteSpasticita: string;
    noteTrattamentiSpasticita: string;
    punteggioScalaAshworth: number;
    scim: boolean;
    scimCuraSe: number;
    scimMobilita: number;
    scimRespirazioneSfinteri: number;
    spasticita: boolean;
    trattamentiQuadroFunzionaleLista: CheckBoxSempliceModel[];
    wisci: string;
}

// 7. AUSILI – ORTESI PRESCRITTI ALLA DIMISSIONI
// Su java: SchedaAusiliDimissioneDTO

export interface SchedaAusiliDimissioneModel {
    altroAusiliFunzioniVitali: string;
    altroAusiliMobilita: string;
    altroAusiliUrinari: string;
    ausiliFunzioniVitaliLista: CheckBoxSempliceModel[];
    ausiliMobilitaLista: CheckBoxSempliceModel[];
    ausiliUrinariLista: CheckBoxSempliceModel[];
    idScheda: number;
    nomeScheda: string;
    noteAusiliFunzioniVitali: string;
    noteAusiliMobilita: string;
    noteAusiliUrinari: string;
    notePresidiPosturali: string;
    presidiPosturaliLista: CheckBoxSempliceModel[];
}

// 8. ASPETTI SOCIOASSISTENZIALI E DI CONTINUITÀ
// Su java: SchedaAspettiSocioassistenzialiDimissioneDTO

export interface SchedaAspettiSocioassistenzialiDimissioneModel {
    addestramentoCaregiver: boolean;
    altroCaregiver: string;
    attivazioneCureDomiciliari: boolean;
    caregiverLista: CheckBoxSempliceModel[];
    idScheda: number;
    nomeScheda: string;
    noteAddestramentoCaregiver: string;
    noteCaregiver: string;
    noteCureDomiciliari: string;
    notePrevistoControlloUnitaSpinale: string;
    noteProgettoIndividualeConTerritorio: string;
    previstoControlloUnitaSpinale: boolean;
    progettoIndividualeConTerritorio: boolean;
}

// Interventi Effettuati Durante il Ricovero
// Su java: SchedaInterventiEffettuatiDimissioniDTO

export interface SchedaInterventiEffettuatiDimissioniModel {
    nomeScheda: string;
    idScheda: number;
    interventiChirurgiciDimissioniLista: CheckBoxSempliceModel[];
    altroInterventiChirurgici: string;
    noteInterventiChirurgici: string;
    interventiRespiratoriLista: CheckBoxSempliceModel[];
    altroInterventiRespiratori: string;
    noteInterventiRespiratori: string;
}

export interface SchedaDimissioneModel {
    schedaGeneraleDimissioni: SchedaGeneraleDimissioniModel;
    schedaValutazioneDimissione: SchedaValutazioneDimissioneModel;
    schedaNecessitaAssistenzaDimissione: SchedaNecessitaAssistenzaDimissioneModel;
    schedaInterventiEffettuatiDimissioni: SchedaInterventiEffettuatiDimissioniModel;
    schedaQuadroNeurologicoDimissioni: SchedaQuadroNeurologicoDimissioneModel;
    schedaComplicanzeDimissione: SchedaComplicanzeDimissioneModel;
    schedaQuadroFunzionaleDimissione: SchedaQuadroFunzionaleDimissioneModel;
    schedaAusiliDimissione: SchedaAusiliDimissioneModel;
    schedaAspettiSocioassistenzialiDimissione: SchedaAspettiSocioassistenzialiDimissioneModel;
}
