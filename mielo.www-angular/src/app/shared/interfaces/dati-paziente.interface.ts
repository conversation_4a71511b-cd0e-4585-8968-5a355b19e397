import { ComuneModel, NazioneModel, ProvinciaModel } from './decoder.interface';
import { DizionarioModel, SchedaRicoveroModel, UnitaOperativaModel } from "./scheda-ricovero.interface";

export interface DatiAnagrafici {
    tipologiaCodice?: number | string;
    codiceFiscale: string;
    dataRilascioTeam?: Date | null;
    nome: string;
    cognome: string;
    dataNascita: Date | null;
    provinciaNascita?: string | ProvinciaModel;
    comuneNascita?: null | ComuneModel;
    cittadinanza?: string | NazioneModel;
    eta: number;
    genere: 'M' | 'F' | 'N' | null;
    provinciaResidenza: string | ProvinciaModel;
    comuneResidenza: string | ComuneModel;
    indirizzoResidenza?: string;
    numeroCivico?: string;
    domicilioCoincideConResidenza?: boolean;
    provinciaDomicilio?: string | ProvinciaModel;
    comuneDomicilio?: string | ComuneModel;
    indirizzoDomicilio?: string;
    numeroCivicoDomicilio?: string;
    nazioneNascita: NazioneModel;
    residenza?: {
        comune: ComuneModel;
        nazione: NazioneModel | null;
        indirizzo: string;
        numeroCivico: string;
    };
}

export interface DatiSocioEconomici {
    scolarita: 'ELEMENTARE' | 'MEDIA' | 'SUPERIORE' | 'LAUREA' | 'POST_LAUREA' | '';
    occupazione: any;
    altro: string;
    statoCivile: any;
}

export interface DatiGenerali {
    idNosologico: string | null;
    dataRicovero: Date | null;
    unitaOperativa: UnitaOperativaModel;
    repartoDiRicovero: UnitaOperativaModel | string | null;
    lesioneMielica: DizionarioModel;
    note: string;
}

export interface PazienteInfo {
    nome: string;
    cognome?: string;
    nomeCompleto?: string;
    codiceFiscale: string;
    dataNascita: string;
    idPaziente?: number;
}

export type TDatiPaziente = Pick<SchedaRicoveroModel, 'codicePaziente' | 'cognomePaziente' | 'nomePaziente' | 'dataNascitaPaziente' | 'datiSocioEconomiciMancanti'>
