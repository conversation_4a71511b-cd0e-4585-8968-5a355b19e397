import { DizionarioModel } from "./scheda-ricovero.interface";
import { CheckBoxDataModel, CheckBoxDictionaryModel, CheckBoxNumeroModel, CheckBoxSempliceModel, InterventoChirurgicoModel, StruttureRiabilitativeModel, UnitaOperativaModel } from "./shared/shared.interface";

export interface DatiEziologia {
    lesioneMidollareConfermata: boolean | null;
    traumatica: boolean | null;
    nonTraumatica: string;
    altroNonTraumatica: string;
    dataTrauma: Date | null;
    oraTrauma: string;
    tipologiaTrauma: string;
    altroTipologiaTrauma: string;
    noteAltroTipologiaTrauma: string;
    sport: string;
    altroSport: string;
    noteAltroSport: string;
    caduta: string;
    altroCaduta: string;
    noteAltroCaduta: string;
    infortunioSulLavoro: boolean | null;
    infortunioDomestico: boolean | null;
    tentatoSuicidioAutolesione: boolean | null;
    note: string;
}

export interface DatiLesioneTrattamento {
    rx: boolean;
    tc: boolean;
    rmn: boolean;
    dataEsameRx: Date | null;
    dataEsameTc: Date | null;
    dataEsameRmn: Date | null;
    lesioneVertebra: string;
    tipoLesioneVertebrale: DizionarioModel | null;
    noteTipoLesioneVertebrale: string;
    interventoChirurgico: boolean | null;
    dataIntervento: Date | null;
    oraIntervento: string;
    tipologiaIntervento: string | number;
    noteTipologiaIntervento: string;
    nessunoTraumi: boolean;
    cranioTraumi: boolean;
    scheletroTraumi: boolean;
    organiInterniTraumi: boolean;
    altroTraumi: boolean;
    altroTraumiDescrizione: string;
    noteTraumiAssociati: string;
    trattamentoMedicoAcuto: string;
    altroTrattamentoMedicoAcuto: string;
    noteTrattamentoMedicoAcuto: string;
    presaInCaricoRiabilitativa: boolean | null;
    interventiAggiuntivi: Array<{
        dataIntervento: Date | null;
        oraIntervento: string;
        tipologiaIntervento: string;
        tipologiaInterventoId?: number;
        note: string;
    }>;
}

export interface DatiValutazioneIngresso {
    dataValutazione: Date | null;
    medicoDiRiferimento: string;
    anamnesi: string;
    valutazioneClinica: string;
    note: string;
}

export interface DatiNecessitaAssistenziali {
    respirazione: boolean;
    mobilita: boolean;
    eliminazioneVescicale: boolean;
    eliminazioneIntestinale: boolean;
    cutenea: boolean;
    altro: string;
    note: string;
    tracheostomia: string;
    noteTrachestomia: string;
    accessoVenoso: string;
    noteAccessoVenoso: string;
    ventilazioneInvasiva: boolean;
    ventilazioneInvasivaHDie: string;
    ventilazioneNonInvasiva: boolean;
    ventilazioneNonInvasivaHDie: string;
    stimolatoreDiaframmatico: boolean;
    stimolatoreDiaframmaticoDie: string;
    respiroSpontaneoAria: boolean;
    respiroSpontaneoO2: boolean;
    noteRespirazione: string;
    gestioneVescicale: string;
    noteGestioneVescicale: string;
    alimentazione: string;
    noteAlimentazione: string;
    spontanea: boolean;
    indottaFarmaciLocali: boolean;
    incontinente: boolean;
    indottaFarmaciOS: boolean;
    indottaManovraRiflessa: boolean;
    irrigazioneTransanale: boolean;
    noteGestioneAlvo: string;
    tipoMaterasso: DizionarioModel;
    noteTipoMaterasso: string;
    posizionamentoCarrozzina: string;
}

export interface DatiQuadroNeurologico {
    statoCoscienza: string;
    statoMentale: string;
    linguaggio: string;
    movimentiTesta: string;
    sensibilita: string;
    funzioniNeurovegetative: string;
    note: string;
    dataValutazione: Date | null;
    asia: boolean | null;
    dataValutazioneAsia: Date | null;
    ais: string;
    livelloNeurologico: string;
    livelloMotorioDx: string;
    livelloMotorioSx: string;
    livelloSensitivoDx: string;
    livelloSensitivoSx: string;
    noteAsia: string;
    noteAis: string;
    noteLivelloSensitivo: string;
    scim: boolean | null;
    scimCuraDiSe: string;
    scimRespirazioneGestioneSfinteri: string;
    scimMobilita: string;
}

export interface DatiSettingRiabilitativo {
    trasferimentoSettingRiabilitativo: boolean | null;
    settingRiabilitativo: string;
    note: string;
    dataTransferimento: string | null;
    strutturaDiDestinazione: string;
}

/**************************************************************************************/

// SchedaEziologicaDTO
export interface SchedaEziologicaModel {
    altroSport: string;
    altroTipoCaduta: string;
    altroTipoLesione: string;
    altroTipoTrauma: string;
    dataOraTrauma: Date;
    idScheda: number;
    infortunioDomestico: boolean;
    infortunioLavoro: boolean;
    lesioneMielicaConfermata: boolean;
    lesioneTraumatica: boolean;
    nomeScheda: string;
    notaSport: string;
    notaTipoTrauma: string;
    noteTipoCaduta: string;
    sport: DizionarioModel;
    tipoLesione: DizionarioModel;
    suicidioAutolesione: boolean;
    tipoCaduta: DizionarioModel;
    tipoTrauma: DizionarioModel;
    copiaEventoPrecedente: boolean;
}

// SchedaEziologicaRientroDTO
export interface SchedaEziologiaRientroModel {
    altroCausa: string;
    altroNonTraumatica: string;
    causaRientroLista: CheckBoxSempliceModel[];
    dataEsordioComplicanza: Date;
    dataTrauma: Date;
    idScheda: number;
    nomeScheda: string;
    tipoNonTraumatica: DizionarioModel;
    traumatica: boolean;
    annoEsordioLesione: number;
}


// SchedaLesioneTrattamentoDTO
export interface SchedaLesioneTrattamentoModel {
    altroTraumiAssociati: string;
    altroTrattamentoMedicoAcuto: string;
    diagnostica: CheckBoxDataModel[];
    idScheda: number | null;
    interventoChirurgico: InterventoChirurgicoModel[];
    interventiChirurgici: boolean | null;
    lesioneVertebra: CheckBoxSempliceModel[];
    nomeScheda: string;
    noteTraumiAssociati: string;
    noteTrattamentoMedicoAcuto: string;
    noteTipoLesioneVertebrale: string;
    presaCaricoRiabilitativaFaseAcuta: boolean | null;
    traumiAssociati: CheckBoxSempliceModel[];
    trattamentoMedicoAcuto: DizionarioModel | null;
    tipoLesioneVertebrale: DizionarioModel | null;
}

// SchedaValutazioneIngressoDTO
export interface SchedaValutazioneIngressoModel {
    collaborazione: DizionarioModel;
    dataCompilazioneValutazioneIngresso: Date;
    deficitCognitivo: DizionarioModel;
    idScheda: number;
    nomeScheda: string;
    noteCollaborazione: string;
    noteDeficitCognitivo: string;
    noteOrientamentoSpaziale: string;
    noteOrientamentoTemporale: string;
    noteVigilanza: string;
    orientamentoSpaziale: DizionarioModel;
    orientamentoTemporale: DizionarioModel;
    vigilanza: DizionarioModel;
}

// SchedaNecessitaAssistenzialeIngressoDTO
export interface SchedaNecessitaAssistenzialeIngressoModel {
    accessoVenoso: boolean;
    alimentazione: DizionarioModel;
    gestioneAlvo: CheckBoxSempliceModel[];
    gestioneVescicale: DizionarioModel;
    idScheda: number;
    nomeScheda: string;
    noteAccessoVenoso: string;
    noteAlimentazione: string;
    noteGestioneAlvo: string;
    noteGestioneVescicale: string;
    noteRespirazione: string;
    noteTipoMaterasso: string;
    noteTracheostomia: string;
    posizionamentoCarrozzina: boolean;
    respirazioni: CheckBoxNumeroModel[];
    tipoMaterasso: DizionarioModel;
    tracheostomia: boolean;
}

// SchedaQuadroNeurologicoDTO
export interface SchedaQuadroNeurologicoModel {
    ais: string;
    asia: DizionarioModel;
    dataValutazioneAsia?: string;
    dataValutazioneNeurologica?: string;
    idScheda: number | null;
    livelloMotorioDx: DizionarioModel;
    livelloMotorioSx: DizionarioModel;
    livelloNeurologico: DizionarioModel;
    livelloSensitivoDx: DizionarioModel;
    livelloSensitivoSx: DizionarioModel;
    nomeScheda: string;
    noteAis: string;
    noteAsia: string;
    notePunteggi: string;
    punteggioLivelloMotorioDx: string;
    punteggioLivelloMotorioSx: string;
    punteggioLivelloNeurologico: string;
    punteggioLivelloSensitivoDx: string;
    punteggioLivelloSensitivoSx: string;
    scim: boolean;
    scimCuraSe: number;
    scimMobilita: number;
    scimRespirazione: number;
}

// SchedaSettingRiabilitativoDTO
export interface SchedaSettingRiabilitativoModel {
    dataTrasferimento?: string;
    idScheda: number | null;
    nomeScheda: string;
    noteSettingRiabilitativo: string;
    settingRiabilitativo: UnitaOperativaModel | null;
    strutturaDestinazione: StruttureRiabilitativeModel | null;
    trasferimentoSettingRiabilitativo: boolean | null;
}

// SchedaComplicanzeDTO
export interface SchedaComplicanzeModel {
    nomeScheda?: string;
    idScheda?: number;

    ossificazioneEterotopiche?: DizionarioModel;
    ossificazioneLista: CheckBoxDictionaryModel[];
    altroSedeOssificazione?: string;
    noteOssificazione?: string;

    lesioniPressione?: DizionarioModel;
    lesioniPressioneLista: CheckBoxNumeroModel[];
    altroSedePressione?: string;
    notePressione?: string;

    osteomielite?: DizionarioModel;
    noteOsteomielite?: string;

    trombosiVenosaProfonda?: DizionarioModel;
    trombosiLista: CheckBoxSempliceModel[];
    noteTrombosi?: string;

    emboliaPolmonare?: DizionarioModel;
    noteEmbolia?: string;

    complicanzeRespiratorie: CheckBoxDictionaryModel[];
    altroComplicanzeRespiratorie?: string;
    noteComplicanzeRespiratorie?: string;

    complicanzeUrologiche: CheckBoxDictionaryModel[];
    altroComplicanzeUrologiche?: string;
    noteComplicanzeUrologiche?: string;

    rachideLista: CheckBoxDictionaryModel[];
    altroRachide?: string;
    noteRachide?: string;

    spasticita?: DizionarioModel;
    punteggioScalaAshworth?: number;
    notePunteggioScalaAshworth?: string;
    noteSpasticita?: string;

    doloreLista: CheckBoxDictionaryModel[];
    doloreBoolean?: boolean;
    noteDolore?: string;

    infezioneContaminazioneMultiresistente?: DizionarioModel;
    noteMultiresistente?: string;

    sepsi?: DizionarioModel;
    noteSepsi?: string;

}

export interface SchedaDatiCliniciModel {
    schedaEziologica: SchedaEziologicaModel;
    schedaEziologicaRientro: SchedaEziologiaRientroModel;
    schedaLesioneTrattamento: SchedaLesioneTrattamentoModel;
    schedaValutazioneIngresso: SchedaValutazioneIngressoModel;
    schedaNecessitaAssistenzialeIngresso: SchedaNecessitaAssistenzialeIngressoModel;
    schedaQuadroNeurologico: SchedaQuadroNeurologicoModel;
    schedaSettingRiabilitativo: SchedaSettingRiabilitativoModel;
    schedaComplicanze?: SchedaComplicanzeModel;
}