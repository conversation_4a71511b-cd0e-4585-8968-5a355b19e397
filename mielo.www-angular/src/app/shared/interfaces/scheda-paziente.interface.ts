import { PazienteModel } from "./scheda-ricovero.interface";

export interface SchedaPaziente {
    idPaziente: number;
    idScheda?: number;
    nome: string;
    cognome: string;
    idNosologico: string;
    descLesione: string;
    medicoCompilatore: string;
    codiceIdentificativo: string;
    dataNascita: string;
    descStato: string;
    numeroEventi: number;
    ultimoEvento?: boolean;
    fkStatoUltimoEvento?: number;
    fkStato: number;
    isRientro: boolean;
}

export interface FindSchedeResponseModel {
    content: SchedaPaziente[];
    pageable: {
        pageNumber: number;
        pageSize: number;
        sort: {
            empty: boolean;
            sorted: boolean;
            unsorted: boolean;
        }
    };
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: {
        empty: boolean;
        sorted: boolean;
        unsorted: boolean;
    };
    numberOfElements: number;
    first: boolean;
    empty: boolean;
}

export interface FindSchedeResponse {
    status: number;
    body: FindSchedeResponseModel;
}
