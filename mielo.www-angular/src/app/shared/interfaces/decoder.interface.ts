// import { DecodeModel } from "../models/decode.model";
//
// export interface DecoderInterface {
//     gruppo: string;
//     lista: DecodeModel[];
//     empty: boolean;
// }

export interface DecodeModel {
    id: number;
    codice: string;
    descrizione: string;
    flgElaborato?: string;
}

export interface StatoLesioneModel extends DecodeModel {
    idDizionario?: number;
    categoria?: string;
}

export interface ComuneModel {
    codice: string;
    descrizione: string;
    dsComune: string;
    cdComuneISTAT: string;
    provincia: ProvinciaModel;
    cdCAP?: string;
    comKeyId?: number;
    provKeyId: null
}

export interface ProvinciaModel {
    descrizione: string;
    dsProvincia: string;
    cdProvinciaISTAT: string;
    cdSigla?: string;
    provKeyId?: number;
}

export interface NazioneModel {
    codice: string;
    descrizione: string;
    dsNazione: string;
    cdNazioneISTAT: string;
    cdSigla?: string;
    nazKeyId?: number;
}

export interface IPageable {
  pageNumber: number;
  pageSize: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  offset: number;
}

export interface IPageableResponse {
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  pageable: IPageable;
  size: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  totalElements: number;
  totalPages: number;
}
