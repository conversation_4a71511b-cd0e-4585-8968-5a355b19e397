import { Subject } from "rxjs";

export interface ModalParamInterface {
    text?: string;
    readOnly?: boolean;
}

export interface DialogNoteParam {
    message?: string;
    textTitle?: string;
    textButtonConfirm?: string;
    disabled?: boolean;
}

export interface DialogNormativaParam {
    textTitle?: string;
    textButtonConfirm?: string;
    textButtonCancel?: string;
}

export interface DialogFirmaParam {
    textTitle?: string;
    textButtonConfirm?: string;
    textButtonCancel?: string;
    disableButtonConfirm?: boolean;
    showPreviewBso?: Subject<any>;
    fileName?: string;
    generatedFiles?: any[];
}

export interface DialogSelectionVertebraParam {
    selectedVertebrae?: string[];
    textTitle?: string;
    textButtonConfirm?: string;
    textButtonCancel?: string;
}