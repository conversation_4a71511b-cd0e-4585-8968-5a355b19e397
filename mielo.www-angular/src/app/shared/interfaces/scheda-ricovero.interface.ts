
import { SchedaDatiCliniciModel } from "./dati-clinici.interface";
import { SchedaDimissioneModel } from "./dati-dimissione.interface";

export interface DizionarioModel {
    idDizionario: number;
    categoria: string;
    descrizione: string;
}

export interface TipoEventoModel {
    idTipoEvento: number;
    descrizione: string;
}

export interface EventoModel {
    tipoEvento: TipoEventoModel;
    stato: DizionarioModel;
}

export interface ProvinciaModel {
    provKeyId: number | null;
    cdProvinciaISTAT: string;
    dsProvincia: string;
    cdSigla: string;
}

export interface ComuneModel {
    comKeyId: number;
    cdComuneISTAT: string;
    dsComune: string;
    cdCAP: string;
    provincia: ProvinciaModel;
}

export interface NazioneModel {
    nazKeyId: number;
    cdNazioneISTAT: string;
    dsNazione: string;
    cdSigla: string;
    provincia?: null;
}

export interface UnitaOperativaModel {
    idUnitaOperativa: number;
    nome: string;
    codice: string;
}

export interface SchedaRicoveroReqModel {
    id?: number;
    idNosologico?: string;
    dataRicovero?: string;
    lesioneMielica: DizionarioModel;
    lesioneNote?: string;
    bozza?: boolean;
    unitaOperativa: UnitaOperativaModel;
    provenienza?: DizionarioModel;
    schedaAttiva?: boolean;
    repartoRicovero?: CentriRicoveroModel;
    evento?: EventoModel;
    codPresidioOspedaliero?: string;
}

export interface CentriRicoveroModel {
    idCentro: number;
    codLivello1: string;
    codLivello2: string;
    codLivello3: string;
    descrizioneLivello1: string;
    descrizioneLivello2: string;
    descrizioneLivello3: string;
}

export interface OutputSchedaPazienteRicoveroModel {
    scheda: SchedaRicoveroReqModel;
    datiClinici: SchedaDatiCliniciModel;
    datiDimissioni: SchedaDimissioneModel;
}

export interface NuovoEventoRicoveroModel {
    idPaziente: number;
    evento: EventoModel;
    scheda: SchedaRicoveroReqModel;
    datiClinici: SchedaDatiCliniciModel;
    datiDimissioni: SchedaDimissioneModel;
}

export interface PazienteModel {
    idPaziente?: number;
    nome: string;
    cognome: string;
    genere: DizionarioModel;
    codiceIdentificativo: string;
    tipoCodiceIdentificativo: DizionarioModel;
    dataNascita: string;
    dataRilascioTeam?: string;
    comuneNascita: ComuneModel | null;
    titoloStudio: DizionarioModel;
    condizioneProfessionale: DizionarioModel;
    statoCivile: DizionarioModel;
    domicilioEqResidenza: boolean;
    nazioneNascita: NazioneModel | null;
    cittadinanza: NazioneModel;
    eta: number;
    residenza: ResidenzaModel;
    domicilio: ResidenzaModel;
    scolarita: DizionarioModel;
    altroProfessione?: string;
    altro: string;
}

export interface AggiungiSchedaRicoveroRequest {
    evento: EventoModel;
    scheda: SchedaRicoveroReqModel;
    paziente: PazienteModel;
}

export interface OutputCreazioneScheda {
    idScheda: number;
    idPaziente: number;
    nomePaziente: string;
    cognomePaziente: string;
}


export interface SchedaRicoveroModel {
    codicePaziente: string;
    cognomePaziente: string;
    idPaziente: number;
    dataNascitaPaziente: number | string;
    datiClinici: SchedaDatiCliniciModel;
    datiDimissioni: SchedaDimissioneModel;
    datiSocioEconomiciMancanti: boolean;
    evento: EventoModel;
    nomePaziente: string;
    scheda: SchedaRicoveroReqModel;
    tipoCodiceIdentificativo: DizionarioModel;
    nomeCognomeMedico: string;
    ente: string;
    presidioOspedaliero: string;
    dataDimissioneTrasferimentoPrecedente: Date;
    codPresidioOspedaliero?: string;
    existAtLeastOneEventOnSamePresidio: boolean;
}

export interface ResidenzaModel {
    comune: ComuneModel | null | NazioneModel;
    nazione: NazioneModel | null;
    indirizzo: string;
    numeroCivico: string;
}

export interface SISSModel {
    nome: string | null;
    cognome: string | null;
    sesso: string | null | number;
    dataNascita: string | null;
    comuneNascita: ComuneModel | null;
    nazioneNascita: NazioneModel | null;
    residenza: ResidenzaModel | null;
    domicilio: ResidenzaModel | null;
}

export interface SchedaRicoveroDatiMancanti {
    campiMancantiSchedaGenerale: boolean;
    campiMancantiSchedeCliniche: {
        schedaEziologica: boolean;
        schedaLesioneTrattamento: boolean;
        schedaValutazioneIngresso: boolean;
        schedaNecessitaAssistenzialeIngresso: boolean;
        schedaQuadroNeurologico: boolean;
        schedaSettingRiabilitativo: boolean;
        schedaComplicanze: boolean;
        schedaEziologicaRientro?: boolean;
    };
    campiMancantiSchedeDimissione: {
        schedaGeneraleDimissioni: boolean;
        schedaValutazioneDimissione: boolean;
        schedaNecessitaAssistenzaDimissione: boolean;
        schedaQuadroNeurologicoDimissioni: boolean;
        schedaComplicanzeDimissione: boolean;
        schedaQuadroFunzionaleDimissione: boolean;
        schedaAusiliDimissione: boolean;
        schedaAspettiSocioassistenzialiDimissione: boolean;
        schedaInterventiEffettuatiDimissioni: boolean;
    };
}

export interface InputTipoScheda {
    idPaziente: number;
    idUnitaOperativa?: number;
    idTipoEvento?: number;
    idScheda?: number;
}