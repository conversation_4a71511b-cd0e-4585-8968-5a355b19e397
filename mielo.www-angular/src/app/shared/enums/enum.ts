export enum TipoCodiceIdentEnum {
  CF = 6,
  TEAM = 441,
  STP = 442,
  CFP = 443,
  ENI = 444
}

export enum StatoEventoEnum {
    IN_LAVORAZIONE = 35,
    CHIUSO = 36,
}

export enum ELesioneMielica {
  ACCERTATA = 33,
  SOSPETTA = 34
}

export enum EGenereSesso {
  M = 'M',
  F = 'F',
  N = 'N'
}
export enum EGenereSessoID {
  M = 30,
  F = 31,
  N = 32
}

export enum ENazioni {
  ITALIA = '100',
}

export enum EOccupazione {
  DIPENDENTE = 16,
  LAVORATORE_AUTONOMO = 17,
  STUDENTE = 18,
  CASALINGA = 19,
  DISOCCUPATO = 20,
  PENSIONATO = 21,
  LAVORO_A_TERMINE = 22,
  ALTRO = 23
}

export enum ECategoriaEvento {
  STATO_EVENTO = 'STATO_EVENTO'
}

export enum EDescrizioneEvento {
  'IN LAVORAZIONE' = 'IN LAVORAZIONE'
}

export enum EComplicanzeAltroNessuna {
  ALTRO_OSS = 227,
  ALTRO_LES_PRES = 242,
  ALTRO_COMPL_RESP = 260,
  NESSUNO_COMPL_RESP = 261,
  ALTRO_URO = 274,
  NESSUNA_URO = 275,
  ALTRO_RACHIDE = 280,
  NESSUNA_RACHIDE = 281
}

export enum ERROR_MESSAGE {
  MAX_DATE = 'La data non può essere futura',
  DATE_BEFORE_PREV_DISCHARGE_RIENTRO = 'Data inferiore a quella di dimissione del ricovero precedente',
  DATE_BEFORE_PREV_DISCHARGE = 'Data inferiore a quella di trasferimento del ricovero precedente',
  DATE_BEFORE_ARRIVAL = 'La data non può essere inferiore a quella di ricovero',
  START_DATE_FUTURE = 'La data di inizio non può essere futura',
  END_DATE_FUTURE = 'La data di fine non può essere futura',
  DATE_RANGE_INVALID = 'Data di fine precedente alla data di inizio',
  INVALID_DATE_FORMAT = 'Formato data non valido. Usa GG/MM/AAAA, MM/AAAA o AAAA',
  MAX_TIME = "L'ora non può essere futura",
  INVALID_DATE = 'La data inserita non è valida',
  INVALID_TIME = "L'ora inserita non è valida",
  REQUIRED = 'Campo obbligatorio',
  INVALID_YEAR = 'Anno non valido'
}

export enum MISSING_FIELDS_DATI_GENERALI {
  'Unita Operativa' = 'Ricovero in'
}

export enum ETipoScheda {
  EZIOLOGIA = 1,
  LESIONE_E_TRATTAMENTO = 2,
  VALUTAZIONE_IN_INGRESSO = 3,
  NECESSITA_ASSISTENZIALI_IN_INGRESSO = 4,
  QUADRO_NEUROLOGICO = 5,
  SETTING_RIABILITATIVO = 6,
  COMPLICANZE = 7,
  DATI_GENERALI_DI_DIMISSIONE = 8,
  VALUTAZIONE_IN_DIMISSIONE = 9,
  NECESSITA_ASSISTENZIALI_IN_DIMISSIONE = 10,
  QUADRO_NEUROLOGICO_DIMISSIONE = 11,
  COMPLICANZE_PRESENTI_ALLA_DIMISSIONE = 12,
  QUADRO_FUNZIONALE_ALLA_DIMISSIONE = 13,
  AUSILI_ORTESI_PRESCRITTI_ALLA_DIMISSIONE = 14,
  ASPETTI_SOCIOASSISTENZIALI_E_DI_CONTINUITA = 15,
  INTERVENTI_EFFETTUATI = 16,
  EZIOLOGIA_IN_RIENTRO = 21
}

export enum IdOperatoreEnum {
  OSP = '2',
  PS = '1',
  COM = '3' //tav tect
}