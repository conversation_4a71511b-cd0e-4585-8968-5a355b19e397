import { DizionarioModel } from "../interfaces/scheda-ricovero.interface";

export enum DiagnosticaType {
  RX = 'RX',
  TC = 'TC',
  RMN = 'RMN'
}

export enum TraumaType {
  CRANIO = 'CRANIO',
  NESSUNO = 'NESSUNO',
  SCHELETRO = 'SCHELETRO',
  ORGANI_INTERNI = 'ORGANI INTERNI'
}

export enum TrattamentoType {
  ALTRO = 'ALTRO'
}

export class DizionarioHelper {

  static findInDizionario(options: Array<DizionarioModel>, enumValue: string): DizionarioModel | undefined {
    return options.find(item => item.descrizione === enumValue);
  }

  static someInDizionario(items: any[] | undefined, options: Array<DizionarioModel>, enumValue: string): boolean {
    if (!items || items.length === 0) return false;
    return items.some(item => 
      options.find(opt => opt.idDizionario === item.idDizionario)?.descrizione === enumValue
    );
  }
} 