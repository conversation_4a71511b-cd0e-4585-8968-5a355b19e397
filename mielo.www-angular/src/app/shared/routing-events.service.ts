import { Injectable } from "@angular/core";
import {
    NavigationCancel,
    NavigationEnd,
    NavigationError,
    NavigationStart,
    Router,
    RouterEvent,
} from "@angular/router";
import { BootStrapLombardiaUtils } from "./bootstrap-lombardia.service";

declare var $: any;

@Injectable()
export class RoutingService {
    constructor(
        private router: Router,
        private bootStrapLombardiaUtils: BootStrapLombardiaUtils) { }

    initializeGlobalRoutingEvents(): void {
        this.router.events.subscribe((routingEvent: RouterEvent) => {
            if (routingEvent instanceof NavigationStart)
                this.bootStrapLombardiaUtils.setLoadingModal(true);
            if (
                routingEvent instanceof NavigationEnd ||
                routingEvent instanceof NavigationError ||
                routingEvent instanceof NavigationCancel
            )
                this.bootStrapLombardiaUtils.setLoadingModal(false);
            if (routingEvent instanceof NavigationCancel) {
                this.bootStrapLombardiaUtils.setLoadingModal(false);
                this.bootStrapLombardiaUtils.setLoadingModal(false);
            }
        });
    }
}
