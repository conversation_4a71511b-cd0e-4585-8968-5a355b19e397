import { FormGroup } from "@angular/forms";
import moment from "moment/moment";
import { DizionarioModel, NazioneModel, SISSModel } from "../interfaces/scheda-ricovero.interface";

//Formatta una data dal timestamp del database al formato italiano
export const formatDate = (dateFromDB: number | Date | moment.Moment | string | null | undefined): string => {
  if (!dateFromDB) return '';
  return moment(dateFromDB).format("DD/MM/YYYY");
}

//Converte qualsiasi tipo di data in un oggetto Date standard
export const convertToDate = (dateValue: any): Date | null => {
  if (!dateValue) return null;

  // Se è già un Date, restituiscilo direttamente
  if (dateValue instanceof Date) return dateValue;

  // Se è un oggetto Moment
  if (typeof dateValue === 'object' && dateValue._isAMomentObject) {
    try {
      return new Date(dateValue.valueOf());
    } catch (e) {
      console.error('Errore nella conversione della data:', e);
      return null;
    }
  }

  // Se è una stringa ISO o timestamp
  if (typeof dateValue === 'string' || typeof dateValue === 'number') {
    try {
      return new Date(dateValue);
    } catch (e) {
      console.error('Errore nella conversione della data:', e);
      return null;
    }
  }

  return null;
}

//Controlla se una data è valida
export const isValidDate = (date: any): boolean => {
  if (!date) return false;
  const dateObj = convertToDate(date);
  return dateObj !== null && !isNaN(dateObj.getTime());
}

export const mapSISStoDatiAnagrafici = (sissObj: SISSModel) => {
  return {
    nome: sissObj.nome,
    cognome: sissObj.cognome,
    dataNascita: convertToDate(sissObj.dataNascita),
    genere: sissObj.sesso,
    comuneNascita: sissObj.comuneNascita,
    provinciaNascita: sissObj.comuneNascita?.provincia,
    provinciaResidenza: {
      cdProvinciaISTAT: sissObj.residenza?.comune?.provincia?.cdProvinciaISTAT,
      cdSigla: sissObj.residenza?.comune?.provincia?.cdSigla,
      dsProvincia: sissObj.residenza?.comune?.provincia?.dsProvincia,
      provKeyId: sissObj.residenza?.comune?.provincia?.provKeyId
    },
    comuneResidenza: sissObj.residenza?.comune,
    indirizzoResidenza:sissObj.residenza?.indirizzo,
    numeroCivico: sissObj.residenza?.numeroCivico,

    provinciaDomicilio:{
      cdProvinciaISTAT: sissObj.domicilio?.comune?.provincia?.cdProvinciaISTAT,
      cdSigla: sissObj.domicilio?.comune?.provincia?.cdSigla,
      dsProvincia: sissObj.domicilio?.comune?.provincia?.dsProvincia,
      provKeyId: sissObj.domicilio?.comune?.provincia?.provKeyId
    },
    comuneDomicilio: sissObj.domicilio?.comune,
    indirizzoDomicilio: sissObj.domicilio?.indirizzo,
    numeroCivicoDomicilio: sissObj.domicilio?.numeroCivico,
  };
};

// converte le date moment di un oggetto in date compatibili per il be
export function convertMomentInRequest(obj: any): any {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (value && typeof value === 'object') {
        if (value._isAMomentObject) {
          obj[key] = moment(value).format('YYYY-MM-DD');
        } else {
          convertMomentInRequest(value);
        }
      }
    }
  }
  return obj;
}

export function encodeToBase64(input: string): string {

  if (!input || input.trim() === '') return '';

  // Codifica la stringa in UTF-8 per supportare caratteri speciali
  const utf8Encoded = new TextEncoder().encode(input);

  // Converte l'array di byte in Base64
  const base64String = btoa(String.fromCharCode(...utf8Encoded));

  return base64String;
}
export function encodeNoteFields(obj: any): any {
  if (!obj) return obj;

  Object.keys(obj).forEach(key => {
    // Se è un campo nota e una stringa, codificalo
    if ((key.toLowerCase().includes('nota') || key.toLowerCase().includes('note')) && typeof obj[key] === 'string') {
      // Controlla se la stringa è già codificata in Base64
      if (!isBase64Encoded(obj[key])) {
        obj[key] = encodeToBase64(obj[key] || '');
      }
    }
    // Se è un array, processa ogni elemento
    else if (Array.isArray(obj[key])) {
      obj[key] = obj[key].map((item: any) => {
        // Se l'elemento è un oggetto, applica ricorsivamente encodeNoteFields
        if (item && typeof item === 'object') {
          return encodeNoteFields(item);
        }
        return item;
      });
    }
    // Se è un oggetto, applica ricorsivamente encodeNoteFields
    else if (obj[key] && typeof obj[key] === 'object') {
      obj[key] = encodeNoteFields(obj[key]);
    }
  });

  return obj;
}

// Funzione helper per verificare se una stringa è già codificata in Base64
function isBase64Encoded(str: string): boolean {
  if (!str || typeof str !== 'string') return false;

  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  if (str.length % 4 !== 0 || !base64Regex.test(str)) return false;

  try {
    const decoded = atob(str);
    const reencoded = btoa(decoded);

    // Controllo aggiuntivo: almeno un carattere non stampabile → probabilmente non testo codificato
    if (/[^ -~]/.test(decoded)) return false;

    return reencoded === str;
  } catch {
    return false;
  }
}
export function decodeFromBase64(input: string): string {

  if (!input || input.trim() === '') return '';
  if (!isBase64(input)) {
    return input;
  }
  // Decodifica la stringa Base64 in un array di byte
  const decodedBytes = atob(input).split('').map(char => char.charCodeAt(0));

  // Converte l'array di byte in una stringa UTF-8
  return new TextDecoder().decode(new Uint8Array(decodedBytes));
}

export function decodeNoteFields(obj: any): any {
  if (!obj) return obj;

  Object.keys(obj).forEach(key => {
    // Se è un campo nota e una stringa, decodificalo
    if ((key.toLowerCase().includes('nota') || key.toLowerCase().includes('note')) && typeof obj[key] === 'string') {
      obj[key] = decodeFromBase64(obj[key] || '');
    }
    // Se è un array, processa ogni elemento
    else if (Array.isArray(obj[key])) {
      obj[key] = obj[key].map((item: any) => {
        // Se l'elemento è un oggetto, applica ricorsivamente decodeNoteFields
        if (item && typeof item === 'object') {
          return decodeNoteFields(item);
        }
        return item;
      });
    }
    // Se è un oggetto, applica ricorsivamente decodeNoteFields
    else if (obj[key] && typeof obj[key] === 'object') {
      obj[key] = decodeNoteFields(obj[key]);
    }
  });

  return obj;
}

function isBase64(str: string): boolean {
  try {
    return btoa(atob(str)) === str;
  } catch (err) {
    return false;
  }
}

export function compareByIdDizionario(option: DizionarioModel, value: DizionarioModel): boolean {
  if (option === null || value === null) {
    return false;
  }

  return option?.idDizionario === value?.idDizionario;
}

export function compareByCdNazioneIstat(option: NazioneModel, value: NazioneModel): boolean {
  if (option === null || value === null) {
    return false;
  }

  return option?.cdNazioneISTAT === value?.cdNazioneISTAT;
}


export function compareBycodLivello2(option: any, value: any): boolean {
  if (option === null || value === null) {
    return false;
  }

  return option?.codLivello2 === value?.codLivello2;
}

export function compareByUnitaOperativa(option: any, value: any): boolean {
  if (option === null || value === null) {
    return false;
  }

  return option?.idUnitaOperativa === value?.idUnitaOperativa;
}

export function compareByIdCentro(option: any, value: any): boolean {
  if (option === null || value === null) {
    return false;
  }

  return option?.idCentro === value?.idCentro;
}

export function toggleRadioSelection(formControl: FormGroup, controlName: string, value: any): void {
  const control = formControl.get(controlName);
  if (control?.disabled) return;

  // Se il valore corrente è uguale al valore cliccato, deseleziona (imposta a null)
  if (typeof control?.value === 'object' && typeof value === 'object') {
    if (control?.value?.idDizionario === value?.idDizionario) {
      control?.setValue(null);
      return;
    }
  }
  if (typeof value === 'object' && typeof control?.value === 'number') {
    if (control?.value === value?.idDizionario) {
      control?.setValue(null);
      return;
    }
  }

  if (control?.value === value) {
    control?.setValue(null);
  } else {
    // Altrimenti, seleziona il nuovo valore
    control?.setValue(value);
  }
}

// Formatta una data e ora in formato italiano (gg/mm/aaaa hh:mm)
export const formatDateTimeIT = (dateInput: number | string | Date | null | undefined): string => {
  if (!dateInput) return '/';
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '/';
  // Ottieni data e ora separatamente
  const datePart = date.toLocaleDateString('it-IT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
  const timePart = date.toLocaleTimeString('it-IT', {
    hour: '2-digit',
    minute: '2-digit'
  });
  return `${datePart} - ${timePart}`;
}

export function manageDataOraToSave(data: string, ora: string) {
  if (!data) return null;

  const dataMoment = moment(data)
  const oraMoment = moment(ora || '00:00', 'HH:mm')

  return dataMoment.set({hour: oraMoment.hour(), minute: oraMoment.minute(), second: 1}).valueOf(); //workaround per  FAR comparire il campo oraDecesso compilato
}

export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj; // Valori primitivi o null vengono restituiti così come sono
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any; // Clona oggetti Date
  }

  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as any; // Clona ogni elemento dell'array ricorsivamente
  }

  // Clona oggetti generici
  const copy = {} as { [K in keyof T]: T[K] };
  Object.keys(obj).forEach(key => {
    copy[key as keyof T] = deepClone((obj as any)[key]);
  });

  return copy;
}

export function disableAllFieldsInForm(form: FormGroup) {
  Object.keys(form.controls).forEach(controlName => {
    if (!controlName.toLowerCase().includes('nota')) {
      form.get(controlName)?.disable({ emitEvent: false });
    }
  });
}

export function enableAllFieldsInForm(form: FormGroup) {
  Object.keys(form.controls).forEach(controlName => {
    form.get(controlName)?.enable({ emitEvent: false });
  });
}