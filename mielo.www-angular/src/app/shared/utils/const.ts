export const cdProvinciaISTAT_ESTERO = '000'

export const STATO_ESTERO = {
  "provKeyId": null,
  "cdProvinciaISTAT": "000",
  "dsProvincia": "Stato estero",
  "cdSigla": "EE",
  "descrizione": ''
}

export const STATO_ITALIANO = {
  "nazKeyId": 50013,
  "cdNazioneISTAT": "100",
  "dsNazione": "ITALIA",
  "cdSigla": "IT"
}

export const EMPTY_COMUNE = {
  "comKeyId": null,
  "cdComuneISTAT": "",
  "dsComune": "",
  "cdCAP": "",
  "provincia": null
}

export const TIPO_EVENTO_PRIMO_RICOVERO = {
  idTipoEvento: 2,
  descrizione: "PRIMO RICOVERO (ESORDIO MIELOLESIONE)"
}
export const TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO = {
  idTipoEvento: 21,
  descrizione: "EVENTO DI RICOVERO RIABILITATIVO"
}
export const TIPO_EVENTO_RIENTRO_NON_CENSITO = {
  idTipoEvento: 3,
  descrizione: "PRIMO ACCESSO (LESIONE PREGRESSA)"
}
export const TIPO_EVENTO_RIENTRO_CENSITO = {
  idTipoEvento: 4,
  descrizione: "EVENTO DI RIENTRO"
}

export const MODALITA_DIMISSIONE_ORDINARIA = {
  "idDizionario": 284,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "ORDINARIA A DOMICILIO"
}

export const MODALITA_DIMISSIONE_PROTETTA_ADI = {
  "idDizionario": 285,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "PROTETTA A DOMICILIO CON ATTIVAZIONE ADI"
}

export const MODALITA_DIMISSIONE_STRUTTURE_RESIDENZIALI = {
  "idDizionario": 286,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "PROTETTA PRESSO STRUTTURE RESIDENZIALI"
}

export const MODALITA_DIMISSIONE_VOLONTARIA = {
  "idDizionario": 287,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "VOLONTARIA"
}

export const MODALITA_DIMISSIONE_ALTRO_ISTITUTO_ACUTI = {
  "idDizionario": 288,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "VERSO ALTRO ISTITUTO PER ACUTI"
}

export const MODALITA_DIMISSIONE_ALTRO_ISTITUTO_RIABILITAZIONE = {
  "idDizionario": 289,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "VERSO ALTRO ISTITUTO DI RIABILITAZIONE"
}

export const MODALITA_DIMISSIONE_DECESSO = {
  "idDizionario": 290,
  "categoria": "MODALITA_DIMISSIONE_SDO",
  "descrizione": "DECEDUTO"
}

export const CONDIZIONE_DIMISSIONE_PROGRAMMATA = {
  "idDizionario": 291,
  "categoria": "CONDIZIONE_DIMISSIONE",
  "descrizione": "PROGRAMMATA"
}

export const PROBLEMATICHE_DIMISSIONE_PROGRAMMATA_ALTRO = {
  "idDizionario": 301,
  "categoria": "PROBLEMATICHE_DIMISSIONE_PROGRAMMATA",
  "descrizione": "ALTRO"
}

export const TRATTAMENTI_EFFETTUATI_ALTRO = {
  "idDizionario": 372,
  "categoria": "TRATTAMENTI_EFFETTUATI",
  "descrizione": "ALTRO"
}


export const BOOLEAN_SI = {
  "idDizionario": 1,
  "categoria": "BOOLEAN",
  "descrizione": "SI"
}

export const BOOLEAN_NO = {
  "idDizionario": 2,
  "categoria": "BOOLEAN",
  "descrizione": "NO"
}
