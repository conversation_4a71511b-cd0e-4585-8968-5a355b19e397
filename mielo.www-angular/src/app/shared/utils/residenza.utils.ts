import { ResidenzaModel } from '../interfaces/scheda-ricovero.interface';
import { STATO_ESTERO, STATO_ITALIANO } from './const';

/**
 * Crea un oggetto ResidenzaModel a partire dai dati del paziente
 * @param paziente Oggetto paziente contenente i dati di residenza
 * @returns Oggetto ResidenzaModel
 */
export function createResidenzaObj(paziente: any): ResidenzaModel {
  let nazione = null;
  if (paziente?.provinciaResidenza?.cdProvinciaISTAT) {
    nazione = paziente.provinciaResidenza.cdProvinciaISTAT === STATO_ESTERO.cdProvinciaISTAT
      ? paziente.comuneResidenza
      : STATO_ITALIANO;
  }

  return {
    comune: paziente.provinciaResidenza?.cdProvinciaISTAT !== STATO_ESTERO.cdProvinciaISTAT
      ? paziente.comuneResidenza
      : null,
    nazione,
    indirizzo: paziente.indirizzoResidenza,
    numeroCivico: paziente.numeroCivico
  };
}

/**
 * Crea un oggetto ResidenzaModel per il domicilio a partire dai dati del paziente
 * @param paziente Oggetto paziente contenente i dati di domicilio
 * @returns Oggetto ResidenzaModel per il domicilio
 */
export function createDomicilioObj(paziente: any): ResidenzaModel | null{
  // Se il domicilio è uguale alla residenza, ritorna l'oggetto residenza
  if (paziente.domicilioEqResidenza) {
    return createResidenzaObj(paziente);
  }

  let nazione = null;
  if (paziente.provinciaDomicilio?.cdProvinciaISTAT) {
    nazione = paziente.provinciaDomicilio.cdProvinciaISTAT === STATO_ESTERO.cdProvinciaISTAT
      ? paziente.comuneDomicilio
      : STATO_ITALIANO;
  }

  if (!paziente.comuneDomicilio && !nazione && !paziente.indirizzoDomicilio && !paziente.numeroCivicoDomicilio) {
    return null;
  }

  // è obblig per il be selezionar il comune
  if (!paziente.comuneDomicilio) return null

  return {
    comune: paziente.comuneDomicilio,
    nazione: paziente.comuneDomicilio ? nazione : null,
    indirizzo: paziente.indirizzoDomicilio || null,
    numeroCivico: paziente.numeroCivicoDomicilio || null
  };
}
