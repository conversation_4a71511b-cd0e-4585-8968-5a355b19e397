import { CommonModule } from "@angular/common";
import { Component } from '@angular/core';
import { MatDialogRef } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { MaterialModule } from "../../../core/material.module";
import { DatiStorageService } from "../../../mielo/services/dati-storage.service";
import { SchedaRicoveroService } from "../../../mielo/services/scheda-ricovero.service";

@Component({
  selector: 'app-modale-nuova-segnalazione',
  standalone: true,
  imports: [CommonModule, MaterialModule],
  templateUrl: './modale-nuova-segnalazione.component.html',
  styleUrl: './modale-nuova-segnalazione.component.scss'
})
export class ModaleNuovaSegnalazioneComponent {

  constructor(private dialogRef: MatDialogRef<ModaleNuovaSegnalazioneComponent>,
              private router: Router,
              public schedaService: SchedaRicoveroService,
              private datiStorageService: DatiStorageService) {}

  goToSchedaRicovero(option: string) {
    this.schedaService.setSchedaCorrente(null)
    this.datiStorageService.resetDatiAnagrafici();
    this.datiStorageService.resetDatiGenerali();
    this.datiStorageService.resetDatiSocioEconomici();
    this.datiStorageService.setCittadinoIdentificato(false);
    
    if (option && option == 'rientro-non-censito') {
      this.schedaService.setEventoRientroNonCensito(true);
      this.schedaService.setRientroSchedaSelectedSubject(true)
    } else {
      this.schedaService.setEventoRientroNonCensito(false);
      this.schedaService.setRientroSchedaSelectedSubject(false)
    }
    this.schedaService.setTipoOperazione('create');
    this.router.navigate(['/scheda-ricovero']);
    this.dialogRef.close();
  }
}
