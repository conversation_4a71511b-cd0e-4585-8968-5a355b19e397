import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MaterialModule } from '../../../core/material.module';

@Component({
    selector: 'app-modale-conferma',
    standalone: true,
    imports: [CommonModule, MaterialModule],
    templateUrl: './modale-conferma.component.html',
    styleUrls: ['./modale-conferma.component.scss']
})
export class ModaleConfermaComponent {
    constructor(
        public dialogRef: MatDialogRef<ModaleConfermaComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {}

    onActionClick(azione: string): void {
        this.dialogRef.close(azione);
    }
} 