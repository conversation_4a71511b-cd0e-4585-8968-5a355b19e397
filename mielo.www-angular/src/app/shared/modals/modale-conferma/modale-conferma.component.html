<div class="modale-container">
    <div class="icon-container">
        <svg class="icon" [ngClass]="data.tipo">
            <use [attr.xlink:href]="'./assets/bootstrap-lombardia/svg/sprite.svg#it-' + 
                (data.tipo === 'success' ? 'check-circle' : 'warning-circle')">
            </use>
        </svg>
    </div>
    <h2 mat-dialog-title>{{data.titolo}}</h2>
    <div mat-dialog-content *ngIf="data.messaggio">
        {{data.messaggio}}
    </div>
    <div mat-dialog-actions>
        <ng-container *ngFor="let bottone of data.bottoni">
            <button mat-flat-button
                    [ngClass]="{'btn-primary': bottone.azione === 'conferma', 
                            'btn-secondary': bottone.azione === 'annulla'}"
                    (click)="onActionClick(bottone.azione)">
                {{bottone.testo}}
            </button>
        </ng-container>
    </div>
</div> 