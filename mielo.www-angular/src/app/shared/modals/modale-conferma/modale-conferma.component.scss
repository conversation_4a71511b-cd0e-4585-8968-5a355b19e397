.modale-container {
    text-align: center;
    padding: 32px;
    max-width: 400px;
    margin: 0 auto;
}

.icon-container {
    margin-bottom: 24px;
    
    .icon {
        width: 64px;
        height: 64px;
        
        &.success {
            fill: #2A7A38;
        }
        
        &.warning {
            fill: #FFA726;
        }
    }
}

h2[mat-dialog-title] {
    color: #1B496D;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 16px;
}

[mat-dialog-content] {
    color: #1B496D;
    font-size: 16px;
    margin-bottom: 24px;
    line-height: 1.5;
    font-weight: 600;
    white-space: pre-line;
}

[mat-dialog-actions] {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0;
    margin: 0;

    button {
        width: 100%;
        padding: 12px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 16px;
        text-transform: none;
        margin: 0;

        ::ng-deep .mat-ripple-element {
            display: none !important;
        }

        &:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        &.btn-primary {
            background-color: #2A7A38;
            color: white;
            border: none;

            &:hover, &:focus, &:active {
                background-color: #2A7A38 !important;
                box-shadow: none !important;
            }
        }

        &.btn-secondary {
            background-color: white;
            color: #2A7A38;
            border: 2px solid #2A7A38;
            line-height: 1;

            &:hover, &:focus, &:active {
                background-color: white !important;
                border: 2px solid #2A7A38 !important;
                box-shadow: none !important;
            }
        }
    }
}

::ng-deep {
    .mat-mdc-button-touch-target {
        display: none !important;
    }

    .mdc-button__ripple {
        display: none !important;
    }

    .mat-mdc-button-persistent-ripple {
        display: none !important;
    }

    .mat-mdc-button-focus-overlay {
        display: none !important;
    }
}

::ng-deep .mat-mdc-dialog-container {
    padding: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

:host ::ng-deep .success-dialog {
    .btn-secondary {
        border-color: #2A7A38;
        color: #2A7A38;
    }

    .btn-primary {
        background-color: #2A7A38;
        color: white;
    }
} 