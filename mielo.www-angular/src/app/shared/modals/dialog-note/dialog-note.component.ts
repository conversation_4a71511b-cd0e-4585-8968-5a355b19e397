import { Component, Inject, OnInit } from '@angular/core';
import {MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatDialogActions} from "@angular/material/dialog";
import { DialogNoteParam } from '../../interfaces/modal-param.interface';
import {MaterialModule} from "../../../core/material.module";
import {CommonModule} from "@angular/common";
import {FormsModule} from "@angular/forms";

@Component({
  standalone: true,
  imports: [MatDialogActions, MaterialModule, CommonModule, FormsModule],
  selector: 'app-dialog-note',
  templateUrl: './dialog-note.component.html',
  styleUrls: ['./dialog-note.component.scss']
})
export class DialogNoteComponent implements OnInit {

  /**Contenuto testuale del pop up Nota */
  nota: string | undefined;

  constructor(
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<DialogNoteComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogNoteParam
  ) {
    dialogRef.disableClose = true;
  }

  ngOnInit() {
    this.nota = this.data.message;

  }

  onSalva() {
    if (this.nota != null) {
      this.dialogRef.close(this.nota.trim());
    }
  }

  onClose() {
    this.dialogRef.close();
  }
}
