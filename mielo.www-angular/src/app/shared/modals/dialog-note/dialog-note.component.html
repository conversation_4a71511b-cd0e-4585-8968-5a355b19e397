<!-- ---------------- MODALITA NOTE --------------------- -->
<!-- d-flex dialog-title - classi per il titolo centrato -->
<div class="mt-3 mb-2">
    <div class="ml-2">
        <h2 mat-dialog-title><span>{{data.textTitle}}</span></h2>
    </div>
    <mat-dialog-content>
        <div class="msg-content" spellcheck="false">
            <textarea matInput matTextareaAutosize
              [(ngModel)]="nota"
              [disabled]="data.disabled? data.disabled: false"
                      placeholder="Inserisci"
            ></textarea>
        </div>
    </mat-dialog-content>
  <mat-dialog-actions class="row justify-content-end mr-24">
    <button type="button" mat-stroked-button class="mat-stroked-button" (click)="onClose()">Annulla</button>

    <button type="button" mat-raised-button color="primary" (click)="onSalva()">{{data.disabled? 'Chiudi': data.textButtonConfirm}}</button>
  </mat-dialog-actions>
</div>
