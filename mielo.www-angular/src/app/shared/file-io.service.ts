import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable } from "rxjs";

@Injectable()
export class FileIOService {
    constructor(
        private httpClient: HttpClient,
        @Inject('uploadFileOctetApiUrl') private uploadFileOctetApiUrl: string,
        @Inject('uploadFileMultipartApiUrl') private uploadFileMultipartApiUrl: string,
        @Inject('deleteUploadedFileApiUrl') private deleteUploadedFileApiUrl: string) { }

    uploadFileAllegatoOctet(file: File, httpOptions: any): Observable<any> {
        return this.httpClient.post(`${this.uploadFileOctetApiUrl}`, file, httpOptions);
    }

    uploadFileAllegatoMultipart(formData: FormData, httpOptions: any): Observable<any> {
        return this.httpClient.post(`${this.uploadFileMultipartApiUrl}`, formData, httpOptions);
    }

    deleteAllegato(httpOptions: any): Observable<any> {
        return this.httpClient.delete(`${this.deleteUploadedFileApiUrl}`, httpOptions);
    }

}