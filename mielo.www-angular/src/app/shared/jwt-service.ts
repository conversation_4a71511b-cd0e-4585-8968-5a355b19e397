import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable } from "rxjs";

@Injectable({ providedIn: 'root' })
export class JwtService {

    constructor(private httpClient: HttpClient, @Inject('jwtUrl') private jwtUrl: string) { }

    public getJwt(): Observable<string> {
        return this.httpClient.get(this.jwtUrl, {responseType: 'text'});
    }

}