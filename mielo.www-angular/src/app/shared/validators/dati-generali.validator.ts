import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export class NosologicoValidator {
    /**
     * Validatore per l'ID nosologico nel formato NN/NNNNNN
     * dove N deve essere un numero
     * Lunghezza totale: 9 caratteri (incluso il /)
     */
    static format(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const value = control.value.toString().trim();

            // Verifica la lunghezza totale (9 caratteri)
            if (value.length !== 9) {
                return { lunghezzaInvalida: true };
            }

            // Verifica il formato NN/NNNNNN (solo numeri)
            const regex = /^\d{2}\/\d{6}$/;

            if (!regex.test(value)) {
                return { formatoInvalido: true };
            }

            return null;
        };
    }
} 