import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import moment from 'moment';
import { ERROR_MESSAGE } from '../enums/enum';


export function dateNotFutureValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;
        let dateMoment: moment.Moment | null = null;

        if (value && value._isAMomentObject) {
            dateMoment = moment(value);
        } else if (value instanceof Date) {
            dateMoment = moment(value);
        } else if (typeof value === 'string') {
            dateMoment = moment(value, ['DD/MM/YYYY', 'YYYY-MM-DD', moment.ISO_8601], true);
        }

        const today = moment().startOf('day');

        if (!dateMoment || !dateMoment.isValid()) {
            return { invalidDate: true };
        }

        if (dateMoment.startOf('day').isAfter(today)) {
            return { maxDate: true };
        }

        return null;
    };
}

export function timeValidator(getRelatedDate?: () => Date | null): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;

        if (!value) return null;

        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(value)) {
            return { invalidTime: true };
        }

        const relatedDate = getRelatedDate ? getRelatedDate() : null;
        if (relatedDate) {
            const [hours, minutes] = value.split(':').map(Number);
            
            const dateToCheck = moment(relatedDate).startOf('day');
            const today = moment().startOf('day');

            if (dateToCheck.isSame(today)) {
                const timeToCheck = moment().hours(hours).minutes(minutes).seconds(0);
                const now = moment();

                if (timeToCheck.isAfter(now)) {
                    return { maxTime: true };
                }
            }
        }

        return null;
    };
}

export function dateTimeNotFutureValidator(dateField: string, timeField: string): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
        const dateControl = group.get(dateField);
        const timeControl = group.get(timeField);

        if (!dateControl || !timeControl) return null;

        const dateValue = dateControl.value;
        const timeValue = timeControl.value;

        if (!dateValue || !timeValue) return null;

        const dateErrors = dateNotFutureValidator()(dateControl);
        if (dateErrors && dateErrors['invalidDate']) {
            return { invalidDate: true };
        }

        const getRelatedDate = () => {
            if (dateValue && dateValue._isAMomentObject) {
                return dateValue.toDate();
            }
            return new Date(dateValue);
        };
        const timeErrors = timeValidator(getRelatedDate)(timeControl);

        if (dateErrors || timeErrors) {
            if (timeErrors && timeErrors['maxTime']) {
                timeControl.setErrors({ ...(timeControl.errors || {}), futureTime: true });
            }
            return { futureDatetime: true };
        }

        // Verifica combinata data + ora
        const [hours, minutes] = timeValue.split(':').map(Number);
        
        let dateTime = moment(dateValue).hours(hours).minutes(minutes).seconds(0);
        
        const now = moment();

        if (dateTime.isAfter(now)) {
            // Aggiorna gli errori sui controlli
            const today = moment().startOf('day');
            const inputDate = moment(dateValue).startOf('day');

            if (inputDate.isSame(today)) {
                timeControl.setErrors({ ...(timeControl.errors || {}), futureTime: true });
            } else {
                dateControl.setErrors({ ...(dateControl.errors || {}), futureDate: true });
            }

            return { futureDatetime: true };
        } else {
            if (dateControl.errors) {
                const errors = { ...dateControl.errors };
                delete errors['futureDate'];
                dateControl.setErrors(Object.keys(errors).length ? errors : null);
            }

            if (timeControl.errors) {
                const errors = { ...timeControl.errors };
                delete errors['futureTime'];
                timeControl.setErrors(Object.keys(errors).length ? errors : null);
            }
        }

        return null;
    };
}


export function getDateTimeErrorMessage(control: AbstractControl | null): string {
    if (!control) return '';

    if (control.errors?.['required']) {
      return ERROR_MESSAGE.REQUIRED;
    }

    if (control.errors?.['futureDate'] || control.errors?.['matDatepickerMax'] || control.errors?.['maxDate']) {
        return ERROR_MESSAGE.MAX_DATE;
    }
    if (control.errors?.['futureTime'] || control.errors?.['maxTime']) {
        return ERROR_MESSAGE.MAX_TIME;
    }
    if (control.errors?.['futureDatetime']) {
        return ERROR_MESSAGE.MAX_TIME;
    }
    if (control.errors?.['invalidDate'] || control.errors?.['matDatepickerParse']) {
        return ERROR_MESSAGE.INVALID_DATE;
    }
    if (control.errors?.['invalidTime']) {
        return ERROR_MESSAGE.INVALID_TIME;
    }

    return '';
} 