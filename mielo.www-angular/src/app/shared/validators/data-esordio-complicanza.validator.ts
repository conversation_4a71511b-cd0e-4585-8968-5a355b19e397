import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function dataEsordioComplicanzaValidator(currentDate: Date = new Date()): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        if (!control.value) {
            return null;
        }

        const value = control.value.trim();

        // Pattern regex per i diversi formati data
        const patterns = {
            fullDate: /^(\d{2})\/(\d{2})\/(\d{4})$/,
            monthYear: /^(\d{2})\/(\d{4})$/,
            year: /^(\d{4})$/
        };

        let isValid = false;
        let date: Date | null = null;

        // Controllo formato data completa (gg/mm/aaaa)
        if (patterns.fullDate.test(value)) {
            const [, day, month, year] = value.match(patterns.fullDate) || [];
            date = new Date(+year, +month - 1, +day);
            isValid = date.getDate() === +day &&
                date.getMonth() === +month - 1 &&
                date.getFullYear() === +year;
        }
        // Controllo formato mese/anno (mm/aaaa)
        else if (patterns.monthYear.test(value)) {
            const [, month, year] = value.match(patterns.monthYear) || [];
            date = new Date(+year, +month - 1, 1);
            isValid = date.getMonth() === +month - 1 &&
                date.getFullYear() === +year;
        }
        // Controllo formato solo anno (aaaa)
        else if (patterns.year.test(value)) {
            const year = +value;
            date = new Date(year, 0, 1);
            isValid = date.getFullYear() === year;
        }

        if (!isValid) {
            return { invalidFormat: true };
        }

        // Controllo che la data non sia futura
        if (date && date > currentDate) {
            return { futureDate: true };
        }

        return null;
    };
} 