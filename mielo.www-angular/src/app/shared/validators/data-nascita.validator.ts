import {ValidatorFn} from "@angular/forms";
import moment from "moment";

export function dataNascitaValidator(): ValidatorFn {
  return (control) => {
    const value = control.value;
    if (!value) return null;

    // Verifica il formato DD/MM/YYYY
    const isValidFormat = moment(value, 'DD/MM/YYYY', true).isValid();
    if (!isValidFormat) {
      return { invalidFormat: true };
    }

    // Verifica che la data non sia futura
    const isFutureDate = moment(value, 'DD/MM/YYYY').isAfter(moment());
    if (isFutureDate) {
      return { futureDate: true };
    }

    return null;
  };
}
