import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import {RESTRICT} from "../../core/constants/restrict";
import {TipoCodiceIdentEnum} from "../enums/enum";

export class CodiceIdentificativoValidators {
    /**
     * Validatore per il codice fiscale che supporta anche i casi di omocodia
     */
    static codiceFiscale(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const cf = control.value.toUpperCase();
            const cfRegex = RESTRICT.CF_REGEX ;

            if (!cfRegex.test(cf)) {
                return { codiceFiscaleInvalido: true };
            }

            // // Verifica che le sostituzioni per omocodia siano valide
            // // Le cifre possono essere sostituite da lettere solo da destra verso sinistra
            // const numeriPosizioni = [6, 7, 9, 10, 12, 13, 14];
            // let ultimaPosizioneLettere = 15;
            //
            // for (const pos of numeriPosizioni.slice().reverse()) {
            //     const char = cf.charAt(pos);
            //     if ('LMNPQRSTUV'.includes(char)) {
            //         // Se troviamo una lettera, tutte le posizioni successive devono essere lettere
            //         for (let i = pos + 1; i < ultimaPosizioneLettere; i++) {
            //             if (!'LMNPQRSTUV'.includes(cf.charAt(i)) && '0123456789'.includes(cf.charAt(i))) {
            //                 return { omocodiaInvalida: true };
            //             }
            //         }
            //     } else if ('0123456789'.includes(char)) {
            //         ultimaPosizioneLettere = pos;
            //     }
            // }

            return null;
        };
    }

    /**
     * Validatore per il codice fiscale provvisorio (11 cifre)
     */
    static codiceFiscaleProvvisorio(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const regex = /^\d{11}$/;
            return regex.test(control.value) ? null : { codiceFiscaleProvvisorioInvalido: true };
        };
    }

    /**
     * Validatore per il codice STP (STP + 13 cifre)
     */
    static codiceSTP(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const regex = /^STP\d{13}$/i;
            return regex.test(control.value.toUpperCase()) ? null : { codiceSTPInvalido: true };
        };
    }

    /**
     * Validatore per il codice TEAM (20 caratteri alfanumerici)
     */
    static codiceTEAM(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const regex = /^[A-Z0-9]{20}$/i;
            return regex.test(control.value.toUpperCase()) ? null : { codiceTEAMInvalido: true };
        };
    }

    /**
     * Validatore per il codice ENI (ENI + 13 cifre)
     */
    static codiceENI(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const regex = /^ENI\d{13}$/i;
            return regex.test(control.value.toUpperCase()) ? null : { codiceENIInvalido: true };
        };
    }

    /**
     * Validatore condizionale che applica il validatore appropriato in base alla tipologia selezionata
     */
    static validatoreDinamico(tipologiaControl: AbstractControl): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            if (!control.value) return null;

            const tipologia = tipologiaControl.value;
            switch (tipologia) {
                case TipoCodiceIdentEnum.CF:
                    return CodiceIdentificativoValidators.codiceFiscale()(control);
                case TipoCodiceIdentEnum.CFP:
                    return CodiceIdentificativoValidators.codiceFiscaleProvvisorio()(control);
                case TipoCodiceIdentEnum.STP:
                    return CodiceIdentificativoValidators.codiceSTP()(control);
                case TipoCodiceIdentEnum.TEAM:
                    return CodiceIdentificativoValidators.codiceTEAM()(control);
                case TipoCodiceIdentEnum.ENI:
                    return CodiceIdentificativoValidators.codiceENI()(control);
                default:
                    return null;
            }
        };
    }


    /**
     * Validatore che accetta tutti i diversi tipi di formato codice
     */
    static codiceIdentificativoUniversale(): ValidatorFn {
      return (control: AbstractControl): ValidationErrors | null => {
        if (!control.value) return null;

        // Verifica se almeno uno dei validatori passa
        const errori = [
          CodiceIdentificativoValidators.codiceFiscaleLight()(control),
          CodiceIdentificativoValidators.codiceFiscaleProvvisorio()(control),
          CodiceIdentificativoValidators.codiceSTP()(control),
          CodiceIdentificativoValidators.codiceTEAM()(control),
          CodiceIdentificativoValidators.codiceENI()(control)
        ];

        // Se almeno uno restituisce null, significa che è valido
        if (errori.some(error => error === null)) {
          return null;
        }

        // Se tutti hanno dato errore, restituisci un errore generico
        return { codiceInvalido: true };
      };
    }


  /**
   * Validatore per il codice fiscale meno strngente da usare per il filtro nella ricerca
   */
  static codiceFiscaleLight(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;

      const cf = control.value.toUpperCase();
      const cfRegex = RESTRICT.regExp_CF_16 ;

      if (!cfRegex.test(cf)) {
        return { codiceFiscaleInvalido: true };
      }

      return null;
    };
  }
}
