import { HttpErrorResponse } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { DialogErrorComponent } from "./dialog-error.component";
import { MISSING_FIELDS_DATI_GENERALI } from "../../enums/enum";

@Injectable({
  providedIn: 'root'
})
export class ErrorService {
  errorMessageGeneric = "L'operazione non è andata a buon fine! Ti preghiamo di riprovare.";
  dialog: MatDialog = inject(MatDialog);

  manageError(error: HttpErrorResponse) : void {
    let errorMessage = '';
    let isWarning = false;

    if (error.error) {
      // Gestione errori basati sul codice restituito dall'API
      const serverError = error.error;
      
      if (serverError.code === -400) {
        errorMessage = this.errorMessageGeneric;
      } else if (serverError.codice !== undefined) {
        switch (serverError.codice) {
          case 0: // ERR_GENERICO
            errorMessage = this.errorMessageGeneric;
            break;
          case 3: // Valore mancante
            const missingField = MISSING_FIELDS_DATI_GENERALI[serverError.campo as keyof typeof MISSING_FIELDS_DATI_GENERALI] || serverError.campo;
            const section = MISSING_FIELDS_DATI_GENERALI[serverError.campo as keyof typeof MISSING_FIELDS_DATI_GENERALI] ? 'della sezione “Dati Generali”' : '';
            errorMessage = `Specificare il campo “${missingField}” ${section}.`;
            isWarning = true;
            break;
          case 7: // USER_ALREADY_EXIST
            errorMessage = 'Codice Identificativo già presente nel database.';
            isWarning = true;
            break;
          case 13: // ERR_NICCE_USER_NOT_FOUND
            errorMessage = "Nell'anagrafe regionale non è stato trovato nessun Codice Identificativo corrispondente a quello digitato.";
            isWarning = true;
            break;
          case 14: // SCHEDA_ALREADY_EXIST
            errorMessage = 'ID nosologico già presente nel database.';
            isWarning = true;
            break;
          case 15: // EVENTO_IN_LAVORAZIONE
            errorMessage = this.errorMessageGeneric;
            break;
          default:
            // Se c'è un codice di errore sconosciuto, usa la descrizione o il messaggio predefinito
            errorMessage = this.errorMessageGeneric;
        }
      } else {
        // Altrimenti, mostra il title o messaggio
        errorMessage = serverError.title || serverError.messaggio || errorMessage;
      }
    } else {
      // Gestione basata sul codice HTTP
      switch (error.status) {
        case 400: // Bad Request
          errorMessage = 'Richiesta non valida. Controlla i dati inseriti.';
          break;
        case 401: // Unauthorized
          errorMessage = 'Autorizzazione negata. Si prega di effettuare di nuovo l\'accesso.';
          break;
        case 403: // Forbidden
          errorMessage = 'Accesso negato. Si prega di effettuare di nuovo l\'accesso.';
          break;
        case 404: // Not Found
          errorMessage = 'Ops, risorsa non trovata. Verifica l\'URL o la risorsa richiesta.';
          break;
        case 500: // Internal Server Error
          errorMessage = this.errorMessageGeneric;
          break;
        case 503: // Service Unavailable
          errorMessage = this.errorMessageGeneric;
          break;
        default:
          // Qualsiasi altro stato HTTP
          errorMessage = this.errorMessageGeneric;
          // errorMessage = `Errore inaspettato. Stato: ${error.status}.<br>Messaggio: ${error.statusText}`;
          break;
      }
    }

    if (isWarning) {
      this.showWarning(errorMessage);
    } else {
      this.showError(errorMessage);
    }
  }

  showError(error: string): void {
    this.dialog.open(DialogErrorComponent, {
      width: '25%',
      disableClose: true,
      data: {
        subtitle: error,
        iconType: 'error'
      }
    });
  }

  showWarning(message: string): void {
    this.dialog.open(DialogErrorComponent, {
      width: '25%',
      disableClose: true,
      data: {
        subtitle: message,
        iconType: 'warning-circle'
      }
    });
  }
}
