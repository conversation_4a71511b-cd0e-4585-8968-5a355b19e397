import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-dialog-error',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dialog-error.component.html',
  styleUrl: './dialog-error.component.scss'
})
export class DialogErrorComponent {

  title: string;
  subtitle: string;
  text: string;
  btnText: string;
  iconType: string;
  iconClass: string;
  dialogClass: string;

  constructor (
    public dialogRef: MatDialogRef<DialogErrorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.iconType = data.iconType || 'error';
    this.iconClass = this.iconType === 'warning-circle' ? 'icon-warning' : 'icon-danger';
    this.dialogClass = this.iconType === 'warning-circle' ? 'warning-dialog' : '';
    
    // Imposta il titolo in base al tipo di icona
    if (this.iconType === 'warning-circle' && !data.title) {
      this.title = 'Attenzione';
    } else {
      this.title = data.title || 'Errore';
    }
    
    this.subtitle = data.subtitle || "Si è verificato un errore. Si prega di riprovare";
    this.text = data.text || '';
    this.btnText = data.btnText || 'OK';
    
    // Applica la classe al dialogo
    this.dialogRef.addPanelClass(this.dialogClass);
  }

  closeDialog() {
    this.dialogRef.close()
  }

}
