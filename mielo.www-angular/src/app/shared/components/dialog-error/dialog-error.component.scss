.container-dialog {
  padding: 2rem;
}

.dialog-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  align-self: stretch;
  text-align: center;
}

.dialog-footer {
  display: flex;
  width: 100%;
  justify-content: space-between;
  flex-direction: column;
  gap: 1rem;
}

.dialog-title {
  color: #003354 !important;
  font-size: 1.5rem !important;
  font-style: normal !important;
  font-weight: 700 !important;
  line-height: normal !important;
}

.dialog-subtitle {
  color: #003354 !important;
  font-size: 1rem !important;
  font-style: normal !important;
  font-weight: 600 !important;
  line-height: normal !important;
}

svg {
  width: 4rem;
  height: 4rem;
}

.icon-danger {
  fill: #d9364f;
}

.icon-warning {
  fill: #e8b012 !important;
}

p.step-title {
  color: #d9364f !important;
}

.btn-primary {
  background-color: white !important;
  border: 2px, solid, #2A7A38 !important;
  color: #2A7A38 !important;
}

