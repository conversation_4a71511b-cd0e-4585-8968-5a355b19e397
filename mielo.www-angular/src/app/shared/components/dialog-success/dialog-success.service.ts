import {inject, Injectable} from "@angular/core";
import {MatDialog} from "@angular/material/dialog";
import {DialogSuccessComponent} from "./dialog-success.component";

@Injectable({
  providedIn: 'root'
})
export class DialogSuccessService {

  dialog: MatDialog = inject(MatDialog);

  showSuccessDialog(message: string): void {

    this.dialog.open(DialogSuccessComponent, {
      width: '25%',
      disableClose: true,
      data: {
        subtitle: message
      }
    })

  }

}
