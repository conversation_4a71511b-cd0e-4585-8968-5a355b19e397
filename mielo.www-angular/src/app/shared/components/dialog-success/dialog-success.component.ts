import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-dialog-success',
  standalone: true,
  imports: [],
  templateUrl: './dialog-success.component.html',
  styleUrl: './dialog-success.component.scss'
})
export class DialogSuccessComponent {

  title: string;
  subtitle: string;
  text: string;
  btnText: string;

  constructor (
    public dialogRef: MatDialogRef<DialogSuccessComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.title = data.title;
    this.subtitle = data.subtitle ? data.subtitle : '';
    this.text = data.text ? data.text : '';
    this.btnText = data.btnText ? data.btnText : 'OK';
  }

  closeDialog() {
    this.dialogRef.close()
  }

}
