<form [formGroup]="complicanzeForm" class="box-dati mt-4">

    <!-- Ossificazioni Eterotopiche -->
    <div class="row mb-2 align-items-center">
        <div class="d-flex pl-0 align-items-center mb-2 col-12">
            <div class="pl-0 col-auto">
                <label class="field-label">
                    Ossificazioni Eterotopiche<span *ngIf="ossificazioneEterotopicheRequired">*</span>
                </label>
            </div>
            <div class="pl-0 col-12">
                @if (isDimissione) {
                    <mat-radio-group formControlName="ossificazioneEterotopiche" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopiche',true)"
                                          [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopiche',false)"
                                          [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                    <mat-radio-group formControlName="ossificazioneEterotopicheSelected">
                        <!-- SI-->
                        <mat-radio-button
                                [checked]="(complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario
                                    && complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario !== 248)
                                    || complicanzeForm.get('ossificazioneEterotopicheSelected')?.value?.idDizionario === 411"
                                (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopicheSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                        <!-- NO-->
                        <mat-radio-button
                                [checked]="(complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario
                                    && complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario === 248)
                                    || complicanzeForm.get('ossificazioneEterotopicheSelected')?.value?.idDizionario === 248"
                                (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopicheSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <!-- presenti intercorse     -->
                    <mat-radio-group formControlName="ossificazioneEterotopiche" class="mx-5">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                          [id]="'ossificazione-presenti-' + option.idDizionario"
                                          [checked]="complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario === option.idDizionario"
                                          [disabled]="complicanzeForm.get('ossificazioneEterotopicheSelected')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopiche', option)"
                                          [value]="option" color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                }
            </div>
        </div>
    </div>

    <!-- Sede Ossificazioni -->
    <div class="row mb-3 pb-3">
        <div class="col-12">
            <div class="pl-0 col-6 row align-items-center">
                <label class="field-label col-auto pl-0">Sede<span
                        *ngIf="isDimissione ? complicanzeForm.get('ossificazioneEterotopiche')?.value === true : complicanzeForm.get( 'ossificazioneEterotopicheSelected')?.value?.idDizionario === 411">*</span></label>
                <div class="col-auto ml-3 d-flex align-items-center">
                    <button mat-button type="button" class="p-0 note-button"
                            (mousedown)="openPopupNote('noteOssificazione')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
            <div class="row" [formArrayName]="isDimissione ? 'ossificazioneDimissioneLista' : 'ossificazioneLista'">
                <div class="col-6 mb-2 pl-0" *ngFor="let control of ossificazioneArray.controls; let i = index">
                    <div class="d-flex align-items-center" [formGroupName]="i">

                        <mat-checkbox (change)="toggleRelativeRadioLimitante(i, $event.checked, control)"
                                      class="pl-0 col-5"
                                      [id]="optionsSedeOssificazione[i].idDizionario.toString()"
                                      formControlName="selected" color="primary">
                            {{ optionsSedeOssificazione[i].descrizione | capitalizeFirst }}
                        </mat-checkbox>

                        <!--altro-->
                        <div class="col-12 mr-2" [formGroup]="complicanzeForm"
                             *ngIf="optionsSedeOssificazione[i].idDizionario === EComplicanzeAltroNessuna.ALTRO_OSS">
                            <mat-form-field appearance="outline" class="col-12 mr-2">
                                <input matInput formControlName="altroSedeOssificazione"
                                       [matTooltip]="complicanzeForm.get('altroSedeOssificazione')?.value"
                                       [placeholder]="ossificazioneArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                       maxlength="250" />
                                <mat-error *ngIf="complicanzeForm.get('altroSedeOssificazione')?.hasError('required')">
                                    {{ ERROR_MESSAGE.REQUIRED }}
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <!--radio limitante-->
                        <mat-radio-group class="pl-0 col-auto" formControlName="idDizionario2">
                            <mat-radio-button *ngFor="let limitante of optionsLimitante"
                                              (click)="toggleRadioInArray(ossificazioneArray, i, 'idDizionario2', limitante.idDizionario)"
                                              [id]="'ossificazione-limitante-' + i + '-' + limitante.idDizionario"
                                              [value]="limitante.idDizionario" color="primary">
                                {{ limitante.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Lesioni da Pressione -->
    <div class="row mb-2 align-items-center">
        <div class="pl-0 d-flex align-items-center col-6 col-md-6 mb-2">
            <div class="pl-0 col-auto">
                <label class="field-label">
                    Lesioni da Pressione<span *ngIf="lesioniPressioneRequired">*</span>
                </label>
            </div>
            <div class="pl-0 col-12">
                @if (isDimissione) {
                    <mat-radio-group formControlName="lesioniPressione" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressione',true)"
                                          [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressione',false)"
                                          [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                    <mat-radio-group formControlName="lesioniPressioneSelected">
                        <!-- SI-->
                        <mat-radio-button [checked]="(complicanzeForm.get('lesioniPressione')?.value?.idDizionario &&
                            complicanzeForm.get('lesioniPressione')?.value?.idDizionario !== 248) ||
                            complicanzeForm.get('lesioniPressioneSelected')?.value?.idDizionario === 411"
                                          (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressioneSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                          [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                        <!-- NO-->
                        <mat-radio-button
                                [checked]="complicanzeForm.get('lesioniPressioneSelected')?.value?.idDizionario === 248"
                                (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressioneSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <!-- PRESENTI INTERCORSE -->
                    <mat-radio-group formControlName="lesioniPressione" class="mx-5">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                          [id]="'lesioni-presenti-' + option.idDizionario"
                                          [checked]="complicanzeForm.get('lesioniPressione')?.value?.idDizionario === option.idDizionario"
                                          [disabled]="complicanzeForm.get('lesioniPressioneSelected')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressione', option)" [value]="option"
                                          color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                }
            </div>

        </div>
    </div>

    <!-- Sede Lesioni da Pressione -->
    <div class="row mb-3 pb-3">
        <div class="col-12">
            <div class="pl-0 col-6 row align-items-center">
                <label class="field-label col-auto pl-0 mb-1">Sede<span
                        *ngIf="isDimissione ? complicanzeForm.get('lesioniPressione')?.value === true : complicanzeForm.get( 'lesioniPressioneSelected')?.value?.idDizionario === 411">*</span></label>
                <div class="col-auto ml-3 d-flex align-items-center">
                    <button mat-button type="button" class="p-0 note-button"
                            (mousedown)="openPopupNote('notePressione')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
            <div class="row" formArrayName="lesioniPressioneLista">
                <div class="col-6 my-2 pl-0" *ngFor="let controlLesioni of lesioniPressioneArray.controls; let i = index">
                    <div class="d-flex align-items-center" [formGroupName]="i">

                        <mat-checkbox (change)="toggleLesionePressione(i, $event.checked)" class="col-5 pl-0"
                                      [id]="'lesioni-checkbox-' + optionsSedeLesionePressione[i].idDizionario"
                                      formControlName="selected" color="primary">
                            {{ optionsSedeLesionePressione[i].descrizione | capitalizeFirst }}
                        </mat-checkbox>

                        <!-- ALTRO-->
                        <div class="col-12 mr-2 mb-2 pl-0" [formGroup]="complicanzeForm"
                             *ngIf="controlLesioni.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_LES_PRES">
                            <mat-form-field class="col-12 p-0" appearance="outline">
                                <input matInput formControlName="altroSedePressione"
                                       [matTooltip]="complicanzeForm.get('altroSedePressione')?.value"
                                       [placeholder]="lesioniPressioneArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                       maxlength="250" />
                                <mat-error *ngIf="complicanzeForm.get('altroSedePressione')?.hasError('required')">
                                    {{ ERROR_MESSAGE.REQUIRED }}
                                </mat-error>
                            </mat-form-field>
                        </div>

                        <!-- RADIO 1 2 3 4 -->
                        <mat-radio-group class="col-5 d-flex justify-content-between align-items-center"
                                         formControlName="numero">
                            <label class="field-label">{{ lesioniPressioneArray.controls[i].get('selected')?.value ? 'Stadio*' : 'Stadio' }}</label>
                            <mat-radio-button (click)="toggleRadioInArray(lesioniPressioneArray, i, 'numero', stadio)"
                                              *ngFor="let stadio of optionsStadio"
                                              [id]="'lesioni-stadio-' + i + '-' + stadio"
                                              [value]="stadio" color="primary">
                                {{ stadio }}
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Osteomielite -->
    <div class="row mb-3">
        <div class="col-12 d-flex align-items-center pe-md-2 mb-3 mb-md-0">
            <div class="col-auto">
                <label class="align-self-center field-label">
                    Osteomielite<span *ngIf="osteomieliteRequired">*</span>
                </label>
            </div>
            <div class="col-8 d-flex">
                @if (isDimissione) {
                    <mat-radio-group formControlName="osteomielite" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'osteomielite',true)"
                                          [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'osteomielite',false)"
                                          [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                    <mat-radio-group formControlName="osteomieliteSelected">
                        <!--                            SI-->
                        <mat-radio-button
                                [checked]="(complicanzeForm.get('osteomielite')?.value?.idDizionario && complicanzeForm.get('osteomielite')?.value?.idDizionario !== 248)
                                                || complicanzeForm.get('osteomieliteSelected')?.value?.idDizionario === 411"
                                (click)="toggleRadioSelection(complicanzeForm, 'osteomieliteSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                        <!--                        NO-->
                        <mat-radio-button [checked]="complicanzeForm.get('osteomielite')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'osteomieliteSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                          [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <!-- presenti intercorse -->
                    <mat-radio-group formControlName="osteomielite" class="mx-5">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                          [id]="'osteomielite-presenti-' + option.idDizionario"
                                          [checked]="complicanzeForm.get('osteomielite')?.value?.idDizionario === option.idDizionario"
                                          [disabled]="complicanzeForm.get('osteomieliteSelected')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'osteomielite', option)" [value]="option"
                                          color="primary">
                            {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                }

                <button mat-button type="button" class="note-button ml-3" (click)="openPopupNote('noteOsteomielite')">
                    <svg class="icon mr-2 mb-1 icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span class="pl-1 icon-primary">Note</span>
                </button>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Trombosi venosa profonda -->
    <div class="row pb-3 ">
        <div class="col-12 d-flex align-items-center pe-md-2 mb-md-0">
            <div class="col-auto">
                <label class="align-self-center field-label">
                    Trombosi venosa profonda<span *ngIf="trombosiVenosaProfondaRequired">*</span>
                </label>
            </div>
            <div class="col-8 d-flex">
                @if (isDimissione) {
                    <mat-radio-group formControlName="trombosiVenosaProfonda" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfonda',true)"
                                          [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfonda',false)"
                                          [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                    <mat-radio-group formControlName="trombosiVenosaProfondaSelected">
                        <!--                            SI-->
                        <mat-radio-button
                                [checked]="(complicanzeForm.get('trombosiVenosaProfonda')?.value?.idDizionario
                                    && complicanzeForm.get('trombosiVenosaProfonda')?.value?.idDizionario !== 248)
                                    || complicanzeForm.get('trombosiVenosaProfondaSelected')?.value?.idDizionario === 411"
                                (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfondaSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                        <!--                        NO-->
                        <mat-radio-button
                                [checked]="complicanzeForm.get('trombosiVenosaProfonda')?.value?.idDizionario === 248"
                                (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfondaSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <!--                        presenti intercorse     -->
                    <mat-radio-group formControlName="trombosiVenosaProfonda" class="mx-5">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                          [id]="'trombosi-presenti-' + option.idDizionario"
                                          [checked]="complicanzeForm.get('trombosiVenosaProfonda')?.value?.idDizionario === option.idDizionario"
                                          [disabled]="complicanzeForm.get('trombosiVenosaProfondaSelected')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfonda', option)"
                                          [value]="option" color="primary">
                            {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                }

            </div>
        </div>
    </div>

    <!-- Sede Trombosi -->
    <div class="row mb-3">
        <div class="col-12">

            <div class="col-6 d-flex pl-0 align-items-baseline">
                <label class="field-label col-auto mb-3">Sede<span
                        *ngIf="isDimissione ? complicanzeForm.get('trombosiVenosaProfonda')?.value === true : complicanzeForm.get( 'trombosiVenosaProfondaSelected')?.value?.idDizionario === 411">*</span>
                </label>
                <div class="col-auto ml-3 d-flex align-items-center">
                    <button mat-button type="button" class="p-0 note-button"
                            (mousedown)="openPopupNote('noteTrombosi')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>

            <!-- CHECKBOX-->
            <div class="row d-flex flex-wrap"
                 [formArrayName]="isDimissione ? 'trombosiDimissioneLista' : 'trombosiLista'">
                <div class="pl-0 col-4 mb-2" *ngFor="let control of trombosiArray.controls; let i = index">
                    <div class="d-flex align-items-center" [formGroupName]="i">
                        <mat-checkbox (change)="toggleTrombosi(i, $event.checked)" class="pl-0"
                                      [id]="'trombosi-checkbox-' + optionsSedeTrombosi[i].idDizionario"
                                      formControlName="selected" color="primary">
                            {{ optionsSedeTrombosi[i].descrizione | capitalizeFirst }}
                        </mat-checkbox>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Embolia Polmonare -->
    <div class="row mb-3">
        <div class="col-12 d-flex align-items-center pe-md-2 mb-3 mb-md-0">
            <div class="col-auto">
                <label class="align-self-center field-label">
                    Embolia Polmonare<span *ngIf="emboliaPolmonareRequired">*</span>
                </label>
            </div>
            <div class="col-8 d-flex">
                @if (isDimissione) {
                    <mat-radio-group formControlName="emboliaPolmonare" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonare',true)"
                                          [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonare',false)"
                                          [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                    <mat-radio-group formControlName="emboliaPolmonareSelected">
                        <!--                            SI-->
                        <mat-radio-button
                                [checked]="(complicanzeForm.get('emboliaPolmonare')?.value?.idDizionario
                                    && complicanzeForm.get('emboliaPolmonare')?.value?.idDizionario !== 248)
                                    || complicanzeForm.get('emboliaPolmonareSelected')?.value?.idDizionario === 411"
                                (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonareSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                        <!--                        NO-->
                        <mat-radio-button
                                [checked]="complicanzeForm.get('emboliaPolmonareSelected')?.value?.idDizionario === 248"
                                (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonareSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <!--                        presenti intercorse     -->
                    <mat-radio-group formControlName="emboliaPolmonare" class="mx-5">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                          [id]="'embolia-presenti-' + option.idDizionario"
                                          [checked]="complicanzeForm.get('emboliaPolmonare')?.value?.idDizionario === option.idDizionario"
                                          [disabled]="complicanzeForm.get('emboliaPolmonareSelected')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonare', option)" [value]="option"
                                          color="primary">
                            {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                }

                <button mat-button type="button" class="ml-3 note-button p-0" (click)="openPopupNote('noteEmbolia')">
                    <svg class="icon mr-2 mb-1 icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span class="pl-1 icon-primary">Note</span>
                </button>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Complicanze Respiratorie -->
    <div class="row mb-3 pb-3">
        <div class="col-12">
            <div>
                <div class="col-12 mb-3 row align-items-center">
                    <label class="field-label col-auto pl-0">
                        Complicanze Respiratorie<span *ngIf="complicanzeRespiratorieRequired">*</span>
                    </label>
                    <div class="col-auto ml-3 d-flex align-items-center">
                        <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteComplicanzeRespiratorie')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
                <div class="row"
                     [formArrayName]="isDimissione ? 'complicanzeRespiratorieDimissioneLista' : 'complicanzeRespiratorie'">
                    <div class="col-6 mb-2 pl-0"
                         *ngFor="let control of complicanzeRespiratorieArray.controls; let i = index">
                        <div class="d-flex align-items-center" [formGroupName]="i">

                            <!-- Checkbox -->
                            <mat-checkbox (change)="onComplicanzaRespiratoriaChange(i, $event.checked)" class="pl-0"
                                          [id]="'complicanze-resp-checkbox-' + optionsComplicanzeRespiratorie[i].idDizionario"
                                          [ngClass]="isDimissione ? 'col-8' : 'col-6'" formControlName="selected" color="primary">
                                {{ optionsComplicanzeRespiratorie[i].descrizione | capitalizeFirst }}
                            </mat-checkbox>

                            <!-- Altro input -->
                            <div class="col-12 mb-2 pl-0" [formGroup]="complicanzeForm"
                                 *ngIf="control.get(isDimissione ? 'idDizionario' : 'idDizionario1')?.value === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP">
                                <mat-form-field appearance="outline" class="pl-0 col-12">
                                    <input matInput formControlName="altroComplicanzeRespiratorie"
                                           [matTooltip]="complicanzeForm.get('altroComplicanzeRespiratorie')?.value"
                                           [placeholder]="complicanzeRespiratorieArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                           maxlength="250" />
                                    <mat-error
                                            *ngIf="complicanzeForm.get('altroComplicanzeRespiratorie')?.hasError('required')">
                                        {{ ERROR_MESSAGE.REQUIRED }}
                                    </mat-error>
                                </mat-form-field>
                            </div>

                            <!-- Radio buttons -->
                            <mat-radio-group
                                    *ngIf="complicanzeForm.controls['complicanzeRespiratorie'] && optionsComplicanzeRespiratorie[i]?.descrizione !== 'NESSUNA'"
                                    class="pl-0 col-6" formControlName="idDizionario2">
                                <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                                  [id]="'complicanze-resp-radio-' + i + '-' + option.idDizionario"
                                                  (click)="toggleRadioInArray(complicanzeRespiratorieArray, i, 'idDizionario2', option)"
                                                  [value]="option.idDizionario" color="primary">
                                    {{ option.descrizione | capitalizeFirst }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Complicanze Urologiche -->
    <div class="row mb-3 pb-3">
        <div class="col-12">
            <div>
                <div class="col-12 mb-3 row align-items-center">
                    <label class="field-label col-auto pl-0">
                        Complicanze Urologiche<span *ngIf="complicanzeUrologicheRequired">*</span>
                    </label>
                    <div class="col-auto ml-3 d-flex align-items-center">
                        <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteComplicanzeUrologiche')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
                <div class="row"
                     [formArrayName]="isDimissione ? 'complicanzeUrologicheDimissioneLista' : 'complicanzeUrologiche'">
                    <div class="col-12">
                        <div class="row">
                            <ng-container *ngFor="let control of complicanzeUrologicheArray.controls; let i = index">
                                <div class="col-6 my-2 d-flex align-items-center" [formGroupName]="i"
                                     *ngIf="control.get(isDimissione ? 'idDizionario' : 'idDizionario1')?.value !== EComplicanzeAltroNessuna.ALTRO_URO">

                                    <!-- Checkbox -->
                                    <mat-checkbox (change)="onComplicanzaUrologicaChange(i, $event.checked)"
                                                  [id]="'complicanze-uro-checkbox-' + optionsComplicanzeUrologiche[i].idDizionario"
                                                  class="pl-0" [ngClass]="isDimissione ? 'col-8' : 'col-6'"
                                                  formControlName="selected" color="primary">
                                        {{ optionsComplicanzeUrologiche[i].descrizione | capitalizeFirst }}
                                    </mat-checkbox>

                                    <!-- Radio buttons -->
                                    <mat-radio-group
                                            *ngIf="complicanzeForm.controls['complicanzeUrologiche'] && optionsComplicanzeUrologiche[i]?.descrizione !== 'NESSUNA'"
                                            class="pl-0 col-6" formControlName="idDizionario2">
                                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                                          [id]="'complicanze-uro-radio-' + i + '-' + option.idDizionario"
                                                          (click)="toggleRadioInArray(complicanzeUrologicheArray, i, 'idDizionario2', option)"
                                                          [value]="option.idDizionario" color="primary">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                                <div class="col-12 d-flex align-items-center" [formGroupName]="i"
                                     *ngIf="control.get(isDimissione ? 'idDizionario' : 'idDizionario1')?.value === EComplicanzeAltroNessuna.ALTRO_URO">

                                    <!-- Checkbox -->
                                    <mat-checkbox (change)="onComplicanzaUrologicaChange(i, $event.checked)"
                                                  class="pl-0 col-3" formControlName="selected" color="primary">
                                        {{ optionsComplicanzeUrologiche[i].descrizione | capitalizeFirst }}
                                    </mat-checkbox>

                                    <!-- Altro input -->
                                    <div class="mb-2 pl-0" [ngClass]="isDimissione ? 'col-8' : 'col-6'"
                                         [formGroup]="complicanzeForm"
                                         *ngIf="control.get(isDimissione ? 'idDizionario' : 'idDizionario1')?.value === EComplicanzeAltroNessuna.ALTRO_URO">
                                        <mat-form-field appearance="outline" class="pl-0"
                                                        [ngClass]="isDimissione ? 'col-9' : 'col-12'">
                                            <input matInput formControlName="altroComplicanzeUrologiche"
                                                   [matTooltip]="complicanzeForm.get('altroComplicanzeUrologiche')?.value"
                                                   [placeholder]="complicanzeUrologicheArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                                   maxlength="250" />
                                            <mat-error
                                                    *ngIf="complicanzeForm.get('altroComplicanzeUrologiche')?.hasError('required')">
                                                {{ ERROR_MESSAGE.REQUIRED }}
                                            </mat-error>
                                        </mat-form-field>
                                    </div>

                                    <!-- Radio buttons -->
                                    <mat-radio-group
                                            *ngIf="complicanzeForm.controls['complicanzeUrologiche'] && optionsComplicanzeUrologiche[i]?.descrizione !== 'NESSUNA'"
                                            class="pl-0 col-6" formControlName="idDizionario2">
                                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                                          [id]="'complicanze-uro-altro-radio-' + i + '-' + option.idDizionario"
                                                          (click)="toggleRadioInArray(complicanzeUrologicheArray, i, 'idDizionario2', option)"
                                                          [value]="option.idDizionario" color="primary">
                                            {{ option.descrizione | capitalizeFirst }}
                                        </mat-radio-button>
                                    </mat-radio-group>
                                </div>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- Rachide -->
    <div class="row mb-3 pb-3">
        <div class="col-12">
            <div class="col-12 mb-3 row align-items-center">
                <label class="field-label col-auto pl-0">
                    Rachide<span *ngIf="rachideRequired">*</span>
                </label>
                <div class="col-auto ml-3 d-flex align-items-center">
                    <button mat-button type="button" class="p-0 note-button" (mousedown)="openPopupNote('noteRachide')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
            <div class="row" [formArrayName]="isDimissione ? 'rachideDimissioneLista' : 'rachideLista'">
                <div [ngClass]="control.get('idDizionario1')?.value === EComplicanzeAltroNessuna.ALTRO_RACHIDE ? 'col-12 pl-0 my-2' : 'col-6 mb-2 pl-0'"
                     *ngFor="let control of rachideArray.controls; let i = index" [formGroupName]="i">
                    <div class="d-flex align-items-center">
                        <mat-checkbox (change)="onComplicanzaRachideChange(i, $event.checked)"
                                      [id]="'rachide-checkbox-' + optionsRachide[i].idDizionario"
                                      [ngClass]="control.get('idDizionario1')?.value === EComplicanzeAltroNessuna.ALTRO_RACHIDE ? 'pl-0 col-2' : 'pl-0 col-4'"
                                      formControlName="selected" color="primary">
                            {{ optionsRachide[i].descrizione | capitalizeFirst }}
                        </mat-checkbox>
                        <!--                        altro-->
                        <div class="col-6" [formGroup]="complicanzeForm"
                             *ngIf="control.get(isDimissione ? 'idDizionario' : 'idDizionario1')?.value === EComplicanzeAltroNessuna.ALTRO_RACHIDE">
                            <mat-form-field class="col-12 p-0" appearance="outline">
                                <input matInput formControlName="altroRachide"
                                       [matTooltip]="complicanzeForm.get('altroRachide')?.value"
                                       [placeholder]="rachideArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                       maxlength="250" />
                                <mat-error *ngIf="complicanzeForm.get('altroRachide')?.hasError('required')">
                                    {{ ERROR_MESSAGE.REQUIRED }}
                                </mat-error>
                            </mat-form-field>
                        </div>
                        <mat-radio-group *ngIf="complicanzeForm.controls['rachideLista']" class="col-4 d-flex"
                                         formControlName="idDizionario2">
                            <ng-container *ngFor="let option of optionsPresentiIntercorse">
                                <mat-radio-button *ngIf="optionsRachide[i].descrizione !== 'NESSUNA'"
                                                  [id]="'rachide-radio-' + i + '-' + option.idDizionario"
                                                  (click)="toggleRadioInArray(rachideArray, i, 'idDizionario2', option)"
                                                  [value]="option.idDizionario" color="primary">
                                    {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                                </mat-radio-button>
                            </ng-container>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- SPASTICITÀ – SCALA ASHWORTH -->
    <div *ngIf="!isDimissione && complicanzeForm.controls['spasticitaSelected']">
        <div class="row pb-3">
            <div class="col-12 d-flex align-items-center pe-md-2 mb-md-0">
                <div class="col-3">
                    <label class="field-label">
                        Spasticità – Scala Ashworth
                        <span *ngIf="spasticitaRequired">*</span>
                    </label>
                </div>

                <div class="col-8 d-flex align-items-center">
                    @if (optionsPresentiIntercorseSiNo.siNo.length) {
                        <mat-radio-group formControlName="spasticitaSelected">
                            <!--                            SI-->
                            <mat-radio-button
                                    [checked]="(complicanzeForm.get('spasticita')?.value?.idDizionario && complicanzeForm.get('spasticita')?.value?.idDizionario !== 248)
                                                    || complicanzeForm.get('spasticitaSelected')?.value?.idDizionario === 411"
                                    (click)="toggleRadioSelection(complicanzeForm, 'spasticitaSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                    [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                                {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                            </mat-radio-button>
                            <!--                        NO-->
                            <mat-radio-button
                                    [checked]="complicanzeForm.get('spasticitaSelected')?.value?.idDizionario === 248"
                                    (click)="toggleRadioSelection(complicanzeForm, 'spasticitaSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                    [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                                {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <!--                        presenti intercorse     -->
                        <mat-radio-group formControlName="spasticita" class="ml-5">
                            <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                              [id]="'spasticita-presenti-' + option.idDizionario"
                                              [checked]="complicanzeForm.get('spasticita')?.value?.idDizionario === option.idDizionario"
                                              [disabled]="complicanzeForm.get('spasticitaSelected')?.value?.idDizionario === 248"
                                              (click)="toggleRadioSelection(complicanzeForm, 'spasticita', option)" [value]="option"
                                              color="primary">
                                {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                    }
                    <div class="ml-5 mt-2">
                        <button mat-button type="button" class="p-0 note-button"
                                (click)="openPopupNote('noteSpasticita')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>

            </div>
        </div>

        <!-- PUNTEGGIO SCALA ASHWORTH -->
        <div class="row mb-3 pt-3">
            <div class="col-12 d-flex align-items-center pe-md-2 mb-md-0">
                <div class="col-3">
                    <label class="field-label">
                        Punteggio Scala Ashworth<span
                            *ngIf="complicanzeForm.get('spasticitaSelected')?.value?.idDizionario === 411">*</span>
                    </label>
                </div>
                <div class="col-9 d-flex">
                    <mat-radio-group formControlName="punteggioScalaAshworth"
                                     class="d-flex justify-content-between align-items-center">
                        <mat-radio-button *ngFor="let stadio of optionsSpasticita"
                                          [id]="'spasticita-stadio-' + stadio"
                                          [value]="stadio" color="primary"
                                          (click)="toggleRadioSelection(complicanzeForm, 'punteggioScalaAshworth', stadio)">
                            {{ stadio }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <div class="ml-5 mt-2">
                        <button mat-button type="button" class="p-0 note-button"
                                (click)="openPopupNote('notePunteggioScalaAshworth')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr *ngIf="!isDimissione" class="my-4">

    <!-- DOLORE -->
    <div class="row mb-3">
        <div class="col-12 d-flex align-items-center">
            <label class="col-auto field-label">
                Dolore<span *ngIf="doloreRequired">*</span>
            </label>
            <mat-radio-group [formControlName]="isDimissione ? 'dolore' : 'doloreBoolean'">
                <mat-radio-button
                        (click)="toggleRadioSelection(complicanzeForm, isDimissione ? 'dolore' : 'doloreBoolean', true)"
                        [value]="true" color="primary">Si
                </mat-radio-button>
                <mat-radio-button
                        (click)="toggleRadioSelection(complicanzeForm, isDimissione ? 'dolore' : 'doloreBoolean', false)"
                        [value]="false" class="ms-4" color="primary">No
                </mat-radio-button>
            </mat-radio-group>
        </div>
    </div>

    <!-- TIPOLOGIA DOLORE -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="col-6 row d-flex align-items-center">
                <label class="field-label col-auto mb-1">Tipologia dolore<span
                        *ngIf="complicanzeForm.get(isDimissione ? 'dolore' : 'doloreBoolean')?.value">*</span></label>
                <div class="col-auto ml-3 d-flex align-items-center">
                    <button mat-button type="button" class="p-0 note-button"
                            [disabled]="complicanzeForm.get(isDimissione ? 'dolore' : 'doloreBoolean')?.disabled"
                            (mousedown)="openPopupNote('noteDolore')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
            <div class="row" [formArrayName]="isDimissione ? 'doloreDimissioneLista' : 'doloreLista'">
                <div class="mb-2 pl-0" *ngFor="let control of doloreArray.controls; let i = index"
                     [ngClass]="isDimissione ? 'col-4' : 'col-6'">
                    <div class="d-flex align-items-center" [formGroupName]="i">
                        <mat-checkbox (change)="toggleDolore(i, $event.checked)" class="pl-0 col-auto"
                                      [id]="'dolore-checkbox-' + optionsDoloreComplicanze[i].idDizionario"
                                      formControlName="selected" color="primary">
                            {{ optionsDoloreComplicanze[i].descrizione | capitalizeFirst }}
                        </mat-checkbox>
                        <mat-radio-group *ngIf="complicanzeForm.controls['doloreLista']" class="pl-0 col-6"
                                         formControlName="idDizionario2">
                            <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                              [id]="'dolore-radio-' + i + '-' + option.idDizionario"
                                              (click)="toggleRadioInArray(doloreArray, i, 'idDizionario2', option)"
                                              [value]="option.idDizionario" color="primary">
                                {{ formatIntercorse(option.descrizione, 'm') | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- INFEZIONE CONTAMINAZIONE MULTIREISTENTE -->
    <div class="row mb-3">
        <div class="pl-0 d-flex align-items-center col-12 mb-2">
            <div class="pl-0 col-auto">
                <label class="field-label">
                    Infezione-contaminazione multiresistente<span
                        *ngIf="infezioneContaminazioneMultiresistenteRequired">*</span>
                </label>
            </div>
            <div class="pl-0 col-auto">
                @if (isDimissione) {
                    <mat-radio-group formControlName="infezioneContaminazioneMultiresistente">
                        <mat-radio-button
                                (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistente',true)"
                                [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button
                                (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistente',false)"
                                [value]="false" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                    <mat-radio-group formControlName="infezioneContaminazioneMultiresistenteSelected">
                        <!-- SI-->
                        <mat-radio-button
                                [checked]="(complicanzeForm.get('infezioneContaminazioneMultiresistente')?.value?.idDizionario
                                    && complicanzeForm.get('infezioneContaminazioneMultiresistente')?.value?.idDizionario !== 248)
                                    || complicanzeForm.get('infezioneContaminazioneMultiresistenteSelected')?.value?.idDizionario === 411"
                                (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistenteSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                        <!-- NO-->
                        <mat-radio-button
                                [checked]="complicanzeForm.get('infezioneContaminazioneMultiresistenteSelected')?.value?.idDizionario === 248"
                                (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistenteSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                            {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    <!-- PRESENTI INTERCORSE -->
                    <mat-radio-group formControlName="infezioneContaminazioneMultiresistente" class="mx-5">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                          [checked]="complicanzeForm.get('infezioneContaminazioneMultiresistente')?.value?.idDizionario === option.idDizionario"
                                          [disabled]="complicanzeForm.get('infezioneContaminazioneMultiresistenteSelected')?.value?.idDizionario === 248"
                                          (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistente', option)"
                                          [value]="option" color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                }
            </div>

            <div class="pl-0 col-auto ml-3">
                <button mat-button type="button" class="p-0 note-button"
                        (mousedown)="openPopupNote('noteMultiresistente')">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span class="ms-1">Note</span>
                </button>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <!-- SEPSI -->
    <div class="pl-0 d-flex align-items-center col-12 mb-2">
        <div class="pl-0 col-auto">
            <label class="field-label">
                Sepsi<span *ngIf="sepsiRequired">*</span>
            </label>
        </div>

        <div class="pl-0 col-auto">
            @if (isDimissione) {
                <mat-radio-group formControlName="sepsi">
                    <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'sepsi',true)" [value]="true"
                                      color="primary">Si
                    </mat-radio-button>
                    <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'sepsi',false)" [value]="false"
                                      color="primary">No
                    </mat-radio-button>
                </mat-radio-group>
            } @else if (optionsPresentiIntercorseSiNo.siNo.length) {
                <mat-radio-group formControlName="sepsiSelected">
                    <!--                            SI-->
                    <mat-radio-button [checked]="(complicanzeForm.get('sepsi')?.value?.idDizionario && complicanzeForm.get('sepsi')?.value?.idDizionario !== 248)
                                            || complicanzeForm.get('sepsiSelected')?.value?.idDizionario === 411"
                                      (click)="toggleRadioSelection(complicanzeForm, 'sepsiSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                      [value]="optionsPresentiIntercorseSiNo.siNo[0]" color="primary">
                        {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                    </mat-radio-button>
                    <!--                        NO-->
                    <mat-radio-button [checked]="complicanzeForm.get('sepsiSelected')?.value?.idDizionario === 248"
                                      (click)="toggleRadioSelection(complicanzeForm, 'sepsiSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                      [value]="optionsPresentiIntercorseSiNo.siNo[1]" color="primary">
                        {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
                <!--                        presenti intercorse     -->
                <mat-radio-group formControlName="sepsi" class="mx-5">
                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                      [checked]="complicanzeForm.get('sepsi')?.value?.idDizionario === option.idDizionario"
                                      [disabled]="complicanzeForm.get('sepsiSelected')?.value?.idDizionario === 248"
                                      (click)="toggleRadioSelection(complicanzeForm, 'sepsi', option)" [value]="option" color="primary">
                        {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
            }
        </div>

        <div class="pl-0 col-auto ml-3">
            <button mat-button type="button" class="p-0 note-button" (mousedown)="openPopupNote('noteSepsi')">
                <svg class="icon icon-primary">
                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                </svg>
                <span class="ms-1">Note</span>
            </button>
        </div>
    </div>
</form>