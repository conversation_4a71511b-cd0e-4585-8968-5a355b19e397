import { CommonModule } from "@angular/common"
import { Component, Input, OnDestroy, OnInit } from '@angular/core'
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms'
import { MatButtonModule } from "@angular/material/button"
import { MatCheckboxModule } from "@angular/material/checkbox"
import { MatExpansionModule } from "@angular/material/expansion"
import { MatFormFieldModule } from "@angular/material/form-field"
import { MatInputModule } from "@angular/material/input"
import { MatRadioModule } from "@angular/material/radio"
import { MatSelectModule } from "@angular/material/select"
import { MatTooltip } from "@angular/material/tooltip"
import { debounceTime, distinctUntilChanged, forkJoin, Observable, Subscription, tap } from "rxjs"
import { DatiCliniciService } from "../../../mielo/services/dati-clinici.service"
import { DatiDimissioneService } from "../../../mielo/services/dati-dimissione.service"
import { EComplicanzeAltroNessuna, ERROR_MESSAGE } from "../../enums/enum"
import { TipoRicoveroEnum } from "../../enums/tipo-ricovero.enum"
import { SchedaComplicanzeModel } from "../../interfaces/dati-clinici.interface"
import { SchedaComplicanzeDimissioneModel } from "../../interfaces/dati-dimissione.interface"
import { DizionarioModel } from "../../interfaces/scheda-ricovero.interface"
import {
  CheckBoxDictionaryModel,
  CheckBoxNumeroModel
} from "../../interfaces/shared/shared.interface"
import { CapitalizePipe } from "../../pipes/capitalize.pipe"
import { DecoderService } from "../../services/decoder.service"
import { ModalService } from "../../services/modal.service"
import { decodeFromBase64, toggleRadioSelection } from "../../utils/utils"

@Component({
  selector: 'app-complicanze',
  templateUrl: './complicanze.component.html',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatRadioModule,
    MatCheckboxModule,
    MatSelectModule,
    MatTooltip,
    CapitalizePipe,
  ],
  standalone: true,
  styleUrls: ['./complicanze.component.scss']
})
export class ComplicanzeComponent implements OnInit, OnDestroy {

  @Input() repartoType?: TipoRicoveroEnum
  @Input() isDimissione: boolean = false;
  @Input() readOnly: boolean = false;

  complicanzeForm: FormGroup
  optionsSedeOssificazione: DizionarioModel[] = [];
  optionsSedeLesionePressione: DizionarioModel[] = [];
  optionsSedeTrombosi: DizionarioModel[] = [];
  optionsComplicanzeRespiratorie: DizionarioModel[] = [];
  optionsComplicanzeUrologiche: DizionarioModel[] = [];
  optionsRachide: DizionarioModel[] = [];
  optionsDoloreComplicanze: DizionarioModel[] = [];
  optionsLimitante: DizionarioModel[] = [];
  optionsPresentiIntercorse: DizionarioModel[] = [];
  optionsPresentiIntercorseSiNo: { presenti: DizionarioModel[], siNo: DizionarioModel[] } = { presenti: [], siNo: [] };
  optionsStadio: number[] = [];
  optionsSpasticita: number[] = [];
  loadDecoders$: Subscription;
  subscriptions: Subscription[];

  keysPresentiIntercorseSiNokeys = [
    'ossificazioneEterotopiche',
    'lesioniPressione',
    'osteomielite',
    'trombosiVenosaProfonda',
    'emboliaPolmonare',
    'spasticita',
    'dolore',
    'infezioneContaminazioneMultiresistente',
    'sepsi',
  ];

  payloadPresentIntercorse: { [key: string]: DizionarioModel } = {};

  complicanzeRespiratorieRequired: boolean = false;
  complicanzeUrologicheRequired: boolean = false;
  rachideRequired: boolean = false;
  emboliaPolmonareRequired: boolean = false;
  doloreRequired: boolean = false;
  sepsiRequired: boolean = false;
  infezioneContaminazioneMultiresistenteRequired: boolean = false;
  ossificazioneEterotopicheRequired: boolean = false;
  lesioniPressioneRequired: boolean = false;
  trombosiVenosaProfondaRequired: boolean = false;
  osteomieliteRequired: boolean = false;
  spasticitaRequired: boolean = false;

  protected readonly EComplicanzeAltroNessuna = EComplicanzeAltroNessuna;
  protected readonly toggleRadioSelection = toggleRadioSelection;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;


  constructor(private fb: FormBuilder,
    private modalService: ModalService,
    private decoderService: DecoderService,
    public datiCliniciService: DatiCliniciService,
    public datiDimissioneService: DatiDimissioneService,
  ) {
  }

  ngOnInit(): void {
    this.complicanzeForm = this.isDimissione
      ? this.createComplicanzeDimissioneForm()
      : this.createComplicanzeDatiCliniForm();

    if (this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI) {
      this.complicanzeRespiratorieRequired = true;
      this.complicanzeUrologicheRequired = true;
      this.rachideRequired = true;
      this.emboliaPolmonareRequired = true;
      this.doloreRequired = true;
      this.sepsiRequired = true;
      this.infezioneContaminazioneMultiresistenteRequired = true;
      this.ossificazioneEterotopicheRequired = true;
      this.lesioniPressioneRequired = true;
      this.trombosiVenosaProfondaRequired = true;
      this.osteomieliteRequired = true;
      this.spasticitaRequired = true;

      this.keysPresentiIntercorseSiNokeys.forEach(field => {
        const controlName = this.isDimissione ? field : `${field}Selected`;
        const control = this.complicanzeForm.get(controlName);
        control?.setValidators(Validators.required);
        control?.updateValueAndValidity();
      });
    }


    this.initFormOptions();
  }

  createComplicanzeDimissioneForm(): FormGroup {
    return this.fb.group({
      altroComplicanzeRespiratorie: [{ value: undefined, disabled: true }],
      altroComplicanzeUrologiche: [{ value: undefined, disabled: true }],
      altroRachide: [{ value: undefined, disabled: true }],
      altroSedeOssificazione: [{ value: undefined, disabled: true }],
      altroSedePressione: [{ value: undefined, disabled: true }],
      complicanzeRespiratorieDimissioneLista: this.fb.array([], this.atLeastOneSelectedInArray('atLeastOneComplicanzaRespiratoriaSelected')),
      complicanzeUrologicheDimissioneLista: this.fb.array([], this.atLeastOneSelectedInArray('atLeastOneComplicanzaUrologicaSelected')),
      dolore: undefined,
      doloreDimissioneLista: this.fb.array([], this.atLeastOneSelectedInArray('atLeastOneDoloreSelected', 'dolore')),
      emboliaPolmonare: undefined,
      idScheda: [{ value: undefined, disabled: true }],
      nomeScheda: [''],
      infezioneContaminazioneMultiresistente: undefined,
      lesioniPressione: undefined,
      lesioniPressioneLista: this.fb.array([], this.createArrayWithRadioValidator('lesioniPressione', {
        atLeastOneSelected: 'atLeastOneLesioneSelected',
        radioMissing: 'radioMissingLesione'
          },
          'numero'
      )),
      noteComplicanzeRespiratorie: undefined,
      noteComplicanzeUrologiche: undefined,
      noteDolore: undefined,
      noteEmbolia: undefined,
      noteMultiresistente: undefined,
      noteOssificazione: undefined,
      noteOsteomielite: undefined,
      notePressione: undefined,
      noteRachide: undefined,
      noteSepsi: undefined,
      noteTrombosi: undefined,
      ossificazioneDimissioneLista: this.fb.array([], this.createArrayWithRadioValidator('ossificazioneEterotopiche',
          {
            atLeastOneSelected: 'atLeastOneOssificazioneSelected',
            radioMissing: 'radioMissingOssificazione'
          }
          )),
      ossificazioneEterotopiche: undefined,
      osteomielite: undefined,
      rachideDimissioneLista: this.fb.array([], this.atLeastOneSelectedInArray('atLeastOneRachideSelected')),
      sepsi: undefined,
      trombosiDimissioneLista: this.fb.array([], this.atLeastOneSelectedInArray('atLeastOneTrombosiSelected', 'trombosiVenosaProfonda')),
      trombosiVenosaProfonda: undefined,
    });
  }

  createComplicanzeDatiCliniForm(): FormGroup {
    const form = this.fb.group({
      nomeScheda: [''],
      idScheda: [{ value: null, disabled: true }],

      ossificazioneEterotopicheSelected: [undefined, Validators.required],
      ossificazioneEterotopiche: undefined,
      ossificazioneLista: this.fb.array([], this.createArrayWithRadioValidator('ossificazioneEterotopicheSelected', {
        atLeastOneSelected: 'atLeastOneOssificazioneSelected',
        radioMissing: 'radioMissingOssificazione'
      })),
      altroSedeOssificazione: [{ value: undefined, disabled: true }, Validators.required],
      noteOssificazione:  undefined,

      lesioniPressioneSelected: [undefined,Validators.required],
      lesioniPressione: undefined,
      lesioniPressioneLista: this.fb.array([], this.createArrayWithRadioValidator('lesioniPressioneSelected', {
        atLeastOneSelected: 'atLeastOneLesioneSelected',
        radioMissing: 'radioMissingLesione'
      },
          'numero'
      )),
      altroSedePressione: [{ value: undefined, disabled: true }],
      notePressione: undefined,

      osteomieliteSelected: [undefined, Validators.required],
      osteomielite: undefined,
      noteOsteomielite: undefined,

      trombosiVenosaProfondaSelected: [undefined, Validators.required],
      trombosiVenosaProfonda: undefined,
      trombosiLista: this.fb.array([], this.atLeastOneSelectedInArray('atLeastOneTrombosiSelected', 'trombosiVenosaProfondaSelected')),
      noteTrombosi: undefined,

      emboliaPolmonareSelected: [undefined, Validators.required],
      emboliaPolmonare: undefined,
      noteEmbolia: undefined,

      complicanzeRespiratorie: this.fb.array([], this.createArrayWithRadioValidator('complicanzeRespiratorie', {
        atLeastOneSelected: 'atLeastOneComplicanzaRespiratoriaSelected',
        radioMissing: 'radioMissingComplicanzaRespiratoria'
      })),
      altroComplicanzeRespiratorie: [{ value: undefined, disabled: true }],
      noteComplicanzeRespiratorie: undefined,

      complicanzeUrologiche: this.fb.array([], this.createArrayWithRadioValidator('complicanzeUrologiche', {
        atLeastOneSelected: 'atLeastOneComplicanzaUrologicaSelected',
        radioMissing: 'radioMissingComplicanzaUrologica'
      })),
      altroComplicanzeUrologiche: [{ value: undefined, disabled: true }, Validators.required],
      noteComplicanzeUrologiche: undefined,

      rachideLista: this.fb.array([], this.createArrayWithRadioValidator('rachideLista', {
        atLeastOneSelected: 'atLeastOneRachideSelected',
        radioMissing: 'radioMissingRachide'
      })),
      altroRachide: [{ value: undefined, disabled: true }],
      noteRachide: undefined,

      spasticitaSelected: [undefined, Validators.required],
      spasticita: undefined,
      punteggioScalaAshworth: [{ value: undefined, disabled: true }],
      noteSpasticita: undefined,
      notePunteggioScalaAshworth: undefined,

      doloreBoolean: [undefined, Validators.required],
      doloreLista: this.fb.array([], this.createArrayWithRadioValidator('doloreBoolean', {
        atLeastOneSelected: 'atLeastOneDoloreSelected',
        radioMissing: 'radioMissingDolore'
      })),
      noteDolore: undefined,

      infezioneContaminazioneMultiresistenteSelected: [undefined, Validators.required],
      infezioneContaminazioneMultiresistente: [{ value: undefined, disabled: true }],
      noteMultiresistente: undefined,

      sepsiSelected: [undefined, Validators.required],
      sepsi: [{ value: undefined, disabled: true }],
      noteSepsi: undefined,
    });
    form.get('noteDolore')?.disable();
    return form;
  }

  initFormOptions() {
    // Creo array per gli stadi delle lesioni da pressione (1-4)
    this.optionsStadio = [1, 2, 3, 4];
    this.optionsSpasticita = [0, 1, 2, 3, 4];

    if (!this.isDimissione) {
      this.defaultDisablePresenteENon();
    }

    if (this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI) {
      this.complicanzeForm.get('complicanzeRespiratorie')?.addValidators(Validators.required);
      this.complicanzeForm.get('complicanzeUrologiche')?.addValidators(Validators.required);
      this.complicanzeForm.get('rachide')?.setValidators(Validators.required);
      this.complicanzeForm.get(this.isDimissione ? 'dolore' : 'doloreBoolean')?.setValidators(Validators.required);
      this.complicanzeForm.get('sepsi')?.setValidators(Validators.required);
      this.complicanzeForm.get('infezioneContaminazioneMultiresistente')?.setValidators(Validators.required);
      this.complicanzeForm.get('ossificazioneEterotopiche')?.setValidators(Validators.required);
      this.complicanzeForm.get('lesioniPressione')?.setValidators(Validators.required);
      this.complicanzeForm.get('trombosiVenosaProfonda')?.setValidators(Validators.required);
      this.complicanzeForm.get('osteomielite')?.setValidators(Validators.required);
    }

    const decoders: Record<string, Observable<any>> = {
      sedeOssificazione: this.decoderService.getDcodSedeOssificazione(),
      sedeLesionePressione: this.decoderService.getDcodSedeLesionePressione(),
      sedeTrombosi: this.decoderService.getDcodSedeTrombosi(),
      complicanzeRespiratorie: this.decoderService.getDcodComplicanzeRespiratorie(),
      complicanzeUrologiche: this.decoderService.getDcodComplicanzeUrologiche(),
      rachide: this.decoderService.getDcodRachide(),
      doloreComplicanze: this.decoderService.getDcodDoloreComplicanze(),
      limitante: this.decoderService.getDcodLimitanteENon()
    };

    if (!this.isDimissione) {
      decoders['presenti'] = this.decoderService.getDcodPresentiENon();
      decoders['presentiSiNo'] = this.decoderService.getDcodPresentiENonConSiNo();
    }

    this.loadDecoders$ = forkJoin(decoders).pipe(
      tap(options => {
        this.optionsSedeOssificazione = options['sedeOssificazione']
        this.optionsSedeLesionePressione = options['sedeLesionePressione'];
        this.optionsSedeTrombosi = options['sedeTrombosi'];
        this.optionsComplicanzeRespiratorie = options['complicanzeRespiratorie'];
        this.optionsComplicanzeUrologiche = options['complicanzeUrologiche'];
        this.optionsRachide = options['rachide'];
        this.optionsDoloreComplicanze = options['doloreComplicanze'];
        this.optionsLimitante = options['limitante'];
        if (!this.isDimissione) {
          this.optionsPresentiIntercorse = options['presenti'];
          this.optionsPresentiIntercorseSiNo = {
            presenti: options['presentiSiNo'].filter((o: DizionarioModel) => o.idDizionario === 249 || o.idDizionario === 250),
            siNo: options['presentiSiNo'].filter((o: DizionarioModel) => o.idDizionario === 248 || o.idDizionario === 411)
          }
        }

        this.createOssificazFormArray()

        this.createLesioniPressioneFormArray()

        this.createTrombosiFormArray()

        this.createComplicanzeRespiratorieFormArray()

        this.createComplicanzeUrologicheFormArray()

        this.createRachideFormArray()

        this.createDoloreFormArray()

        let dati;
        if (!this.isDimissione) {
          dati = this.datiCliniciService.getDatiComplicanzeValue();
        } else {
          dati = this.datiDimissioneService.getComplicanzeValue();
        }
        if (dati) {
          this.populateForm(dati)
        }

        if (this.readOnly) {
          Object.keys(this.complicanzeForm.controls).forEach(controlName => {
            if (!controlName.toLowerCase().includes('note') || !controlName.toLowerCase().includes('nota')) {
              this.complicanzeForm.get(controlName)?.disable({ emitEvent: false });
            }
          });
        } else {
          this.subscribeToControlsChanges();

          // Forza la validazione iniziale di tutti gli array
          setTimeout(() => {
            this.forceArrayValidation();
            this.checkPresInterOnInit()
          }, 0);

          const formChanges$ = this.complicanzeForm.valueChanges.pipe(
            distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
            debounceTime(1)
          ).subscribe(() => {
            const formValue = this.complicanzeForm.getRawValue();

            let payload: SchedaComplicanzeModel | SchedaComplicanzeDimissioneModel;

            if (!this.isDimissione) {
              payload = {
                ...this.formatFormValues(formValue),
                // ...this.payloadPresentIntercorse,
                ossificazioneLista: this.getSelectedItems(this.ossificazioneArray),
                lesioniPressioneLista: this.getSelectedItems(this.lesioniPressioneArray),
                trombosiLista: this.getSelectedItems(this.trombosiArray),
                complicanzeRespiratorie: this.getSelectedItems(this.complicanzeRespiratorieArray),
                complicanzeUrologiche: this.getSelectedItems(this.complicanzeUrologicheArray),
                rachideLista: this.getSelectedItems(this.rachideArray),
                doloreLista: this.getSelectedItems(this.doloreArray)
              };

              this.datiCliniciService.setDatiComplicanze(payload as SchedaComplicanzeModel);
              this.datiCliniciService.setDatiComplicanzeValido(this.complicanzeForm.valid);
            } else {
              payload = {
                ...formValue,
                ossificazioneDimissioneLista: this.getSelectedItems(this.ossificazioneArray),
                lesioniPressioneLista: this.getSelectedItems(this.lesioniPressioneArray),
                trombosiDimissioneLista: this.getSelectedItems(this.trombosiArray),
                complicanzeRespiratorieDimissioneLista: this.getSelectedItems(this.complicanzeRespiratorieArray),
                complicanzeUrologicheDimissioneLista: this.getSelectedItems(this.complicanzeUrologicheArray),
                rachideDimissioneLista: this.getSelectedItems(this.rachideArray),
                doloreDimissioneLista: this.getSelectedItems(this.doloreArray)
              };

              this.datiDimissioneService.setComplicanze(payload as SchedaComplicanzeDimissioneModel);
              this.datiDimissioneService.setComplicanzeValido(this.complicanzeForm.valid);
            }
          })

          this.subscriptions?.push(formChanges$);
        }
      })
    ).subscribe()
  }

  formatFormValues(formValue: any): any {
    const formattedValues = { ...formValue };

    this.keysPresentiIntercorseSiNokeys.forEach((key) => {
      const selectedKey = `${key}Selected`;
      const selectedValue = formValue[selectedKey];
      if (!formattedValues[key]) {
        formattedValues[key] = selectedValue
      }
    });

    return formattedValues;
  }

  populateForm(dati: any) {
    this.patchIdsArray(dati.trombosiDimissioneLista || dati.trombosiLista, this.trombosiArray, 'idDizionario', dati.trombosiVenosaProfonda);

    if (this.isDimissione) {
      this.patchIdsArray(dati.doloreDimissioneLista, this.doloreArray, 'idDizionario', dati.dolore);
      this.populateArrayIdDizionario(dati.rachideDimissioneLista.map((item: any) => (item.idDizionario)), this.rachideArray);
      this.patchIdsArray(dati.complicanzeRespiratorieDimissioneLista || dati.complicanzeRespiratorie, this.complicanzeRespiratorieArray, 'idDizionario',);
      this.patchIdsArray(dati.complicanzeUrologicheDimissioneLista || dati.complicanzeUrologiche, this.complicanzeUrologicheArray, 'idDizionario',);
      this.populateArrayIdDizionario(dati.complicanzeRespiratorieDimissioneLista, this.complicanzeRespiratorieArray);
      this.populateArrayIdDizionario(dati.complicanzeUrologicheDimissioneLista, this.complicanzeUrologicheArray);
    } else {
      this.populateDoloreArray(dati.doloreLista, dati.doloreBoolean);
      this.populateRachideArray(dati.rachideLista);
      this.populateArrayIdDizionario(dati.rachideLista, this.rachideArray);
      this.populateComplicanzeUrologicheArray(dati.complicanzeUrologiche);
      this.populateArrayIdDizionario(dati.complicanzeUrologiche, this.complicanzeUrologicheArray);
      this.populateComplicanzeRespiratorieArray(dati.complicanzeRespiratorie);
      this.populateArrayIdDizionario(dati.complicanzeRespiratorie, this.complicanzeRespiratorieArray);
      this.populateArrayIdDizionario(dati.trombosiLista, this.trombosiArray);
    }

    this.populateOssificazioneArray(this.isDimissione ? dati.ossificazioneDimissioneLista : dati.ossificazioneLista, dati.ossificazioneEterotopiche);
    this.populateLesioniPress(dati.lesioniPressioneLista, dati.lesioniPressione);

    const commonConfig: Array<{ key: string; enableOnValue?: boolean }> = [
      { key: 'ossificazioneEterotopiche', enableOnValue: true },
      { key: 'lesioniPressione', enableOnValue: true },
      { key: 'osteomielite', enableOnValue: true },
      { key: 'trombosiVenosaProfonda', enableOnValue: true },
      { key: 'emboliaPolmonare', enableOnValue: true },
      { key: 'infezioneContaminazioneMultiresistente', enableOnValue: true },
      { key: 'sepsi', enableOnValue: true },
      { key: 'dolore', enableOnValue: true },

      { key: 'noteOssificazione' },
      { key: 'notePressione' },
      { key: 'noteOsteomielite' },
      { key: 'noteTrombosi' },
      { key: 'noteEmbolia' },
      { key: 'noteMultiresistente' },
      { key: 'noteSepsi' },
      { key: 'noteDolore' },
      { key: 'noteComplicanzeUrologiche' },
      { key: 'noteComplicanzeRespiratorie' },
      { key: 'noteRachide' },

      { key: 'altroSedeOssificazione' },
      { key: 'altroSedePressione' },
      { key: 'altroComplicanzeRespiratorie' },
      { key: 'altroComplicanzeUrologiche' },
      { key: 'altroRachide' },
      { key: 'idScheda' },
      { key: 'nomeScheda' },
    ];

    const datiCliniciOnly: Array<{ key: string; enableOnValue?: boolean }> = [
      { key: 'rachide', enableOnValue: true },
      { key: 'spasticita', enableOnValue: true },
      { key: 'spasticitaSelected', enableOnValue: true },
      { key: 'punteggioScalaAshworth', enableOnValue: true },
      { key: 'noteSpasticita' },
      { key: 'notePunteggioScalaAshworth' },
      {key: 'doloreBoolean', enableOnValue: true},
      {key: 'ossificazioneEterotopicheSelected', enableOnValue: true},
      {key: 'lesioniPressioneSelected', enableOnValue: true},
      {key: 'trombosiVenosaProfondaSelected', enableOnValue: true},
      {key: 'emboliaPolmonareSelected', enableOnValue: true},
      // {key: 'osteomieliteSelected', enableOnValue: true},
      {key: 'infezioneContaminazioneMultiresistenteSelected', enableOnValue: true},
      {key: 'sepsiSelected', enableOnValue: true},
    ];

    const finalConfig = this.isDimissione
      ? commonConfig
      : [...commonConfig, ...datiCliniciOnly];

    finalConfig.forEach(cfg => {
      const value = (dati as any)[cfg.key];
      if (cfg.enableOnValue) {
        this.patchControl(cfg.key, value, { enableOnValue: true });
        if (this.repartoType && this.repartoType !== TipoRicoveroEnum.ACUTI) { // complic in dimiss acuto sono opzionali
          this.complicanzeForm.get(cfg.key)?.setValidators(Validators.required);
        }
      } else {
        // if (cfg.key.startsWith('note')) {
        //   return this.patchControl(cfg.key, decodeFromBase64(value));
        // }
        this.patchControl(cfg.key, value);
      }
    });

    if (!this.isDimissione) {
      this.keysPresentiIntercorseSiNokeys.forEach(key => {
        const value: DizionarioModel = (dati as any)[key];
        const selectedControl = this.complicanzeForm.get(`${key}Selected`);
        const mainControl = this.complicanzeForm.get(key);

        if (value?.idDizionario === 248 || value?.idDizionario === 411) {
          selectedControl?.setValue(
              this.optionsPresentiIntercorseSiNo?.siNo?.find(o => o.idDizionario === value.idDizionario) ?? null
            );
          if (value.idDizionario === 411) mainControl?.setValidators(Validators.required);
            this.toggleArrayStatus(key, value);
        } else if (value) {
          mainControl?.setValue(value);
          selectedControl?.setValue(
            this.optionsPresentiIntercorseSiNo?.siNo?.find(o => o.idDizionario === 411) ?? null
          );
        }

      });
    }

    // Forza la validazione di tutti gli array dopo il popolamento
    this.complicanzeForm.updateValueAndValidity();
    this.forceArrayValidation();
  }

  defaultDisablePresenteENon() {
    Object.keys(this.complicanzeForm.controls).forEach(key => {
      if (key.endsWith('Selected')) {
        const disableControlKey = key.replace('Selected', '');
        this.complicanzeForm.get(disableControlKey)?.disable();
      }
    });
  }

  /** Set a scalar control, optionally enabling it if `value != null`. */
  private patchControl(
    path: string,
    value: any,
    opts: { enableOnValue?: boolean } = {}
  ) {
    const ctrl = this.complicanzeForm.get(path);
    if (!ctrl) return;
    if (path === 'punteggioScalaAshworth') {
      // se spasticità è SI abilito punteggio
      if ((this.complicanzeForm.get('spasticita')?.value?.idDizionario && (this.complicanzeForm.get('spasticita')?.value?.idDizionario === 249 || this.complicanzeForm.get('spasticita')?.value?.idDizionario === 411))
          || this.complicanzeForm.get('spasticitaSelected')?.value?.idDizionario === 411) {
        ctrl.enable({ emitEvent: false });
      }
    }
    if (path === 'dolore' || path === 'doloreBoolean') {
      if (this.complicanzeForm.get(this.isDimissione ? 'doloreDimissioneLista' : 'doloreLista')?.value) {
        ctrl.enable({ emitEvent: false });
      }
    }
    if (opts.enableOnValue && value != null) {
      ctrl.enable({ emitEvent: false });
      if (path === 'ossificazioneEterotopicheSelected' || path === 'lesioniPressioneSelected' || path === 'trombosiVenosaProfondaSelected' || path === 'emboliaPolmonareSelected' ||
          path === 'osteomieliteSelected' || path === 'infezioneContaminazioneMultiresistenteSelected' || path === 'sepsiSelected') {
        this.complicanzeForm.get(path.replace('Selected', ''))?.enable({emitEvent: false});
      }
    }
    ctrl.setValue(value, { emitEvent: false });
  }

  /** Populate a FormArray of simple booleans from an array of ids */
  private patchIdsArray(
    source: number[] | { idDizionario: number }[],
    array: FormArray,
    keyField: 'idDizionario' | 'key' = 'idDizionario',
    isParentSelected?: boolean //serve a capire se è selezionato si/no
  ) {
    // if your source is DTO objects, map to array of ids:
    const ids = (source as any[]).map(item =>
      typeof item === 'number' ? item : item[keyField]
    );
    this.populateArrayIdDizionario(ids, array, isParentSelected);
  }

  populateArrayIdDizionario(data: number[], formArray: FormArray, isParentSelected?: boolean) { //isParentSelected serve a capire se è selezionata il radio si/no
    formArray.controls?.forEach((item, index) => {
      const selected = data.find(d => d === item.get('idDizionario')?.value);

      if (selected) {
        item.patchValue({
          selected: true
        });
        item.get('selected')?.enable()

        // abilito altro
        if (selected === EComplicanzeAltroNessuna.ALTRO_RACHIDE ||
          selected === EComplicanzeAltroNessuna.ALTRO_RACHIDE) {
          this.complicanzeForm.get('altroRachide')?.enable()
        }
        if (selected === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP ||
          selected === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP) {
          this.complicanzeForm.get('altroComplicanzeRespiratorie')?.enable()
        }
        if (selected === EComplicanzeAltroNessuna.ALTRO_URO ||
          selected === EComplicanzeAltroNessuna.ALTRO_URO) {
          this.complicanzeForm.get('altroComplicanzeUrologiche')?.enable()
        }
      }
      if (isParentSelected) {
        item.get('selected')?.enable()
      }
    });
  }

  populateOssificazioneArray(ossificazList: CheckBoxDictionaryModel[], isSelectedSi: boolean) {
    this.ossificazioneArray.controls.forEach((item, index) => {
      const ossificazione = ossificazList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (ossificazione) {
        item.patchValue({
          idDizionario2: ossificazione.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()

        if (ossificazione.idDizionario1 === EComplicanzeAltroNessuna.ALTRO_OSS)
          this.complicanzeForm.get('altroSedeOssificazione')?.enable()
      }
      if (isSelectedSi) {
        item.get('idDizionario1')?.enable()
        item.get('selected')?.enable()
      }
    });
  }

  populateDoloreArray(doloreList: CheckBoxDictionaryModel[], doloreBoolean: boolean) {
    this.doloreArray.controls.forEach((item, index) => {
      const sDolore = doloreList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (doloreBoolean) {
        item.get('selected')?.enable()
      }
      if (sDolore) {
        item.get('idDizionario2')?.enable()
        item.patchValue({
          idDizionario2: sDolore.idDizionario2,
          selected: true
        });
      }
    });
  }

  populateRachideArray(rachideList: CheckBoxDictionaryModel[]) {
    this.rachideArray.controls.forEach((item, index) => {
      const rachide = rachideList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (rachide) {
        item.patchValue({
          idDizionario2: rachide.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()

        if (rachide.idDizionario1 === EComplicanzeAltroNessuna.ALTRO_RACHIDE) {
          this.complicanzeForm.get('altroRachide')?.enable()
        }
      }
    });
  }

  populateComplicanzeUrologicheArray(complicanzeList: CheckBoxDictionaryModel[]) {
    this.complicanzeUrologicheArray.controls.forEach((item, index) => {
      const complicanze = complicanzeList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (complicanze) {
        item.patchValue({
          idDizionario2: complicanze.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()

        if (complicanze.idDizionario1 === EComplicanzeAltroNessuna.ALTRO_URO) {
          this.complicanzeForm.get('altroComplicanzeUrologiche')?.enable()
        }
      }
    });
  }

  populateComplicanzeRespiratorieArray(complicanzeList: CheckBoxDictionaryModel[]) {
    this.complicanzeRespiratorieArray.controls.forEach((item, index) => {
      const complicanze = complicanzeList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (complicanze) {
        item.patchValue({
          idDizionario2: complicanze.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()
        if (complicanze.idDizionario1 === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP) {
          this.complicanzeForm.get('altroComplicanzeRespiratorie')?.enable()
        }
      }
    });
  }

  populateLesioniPress(lesioni: CheckBoxNumeroModel[], isLesioneTrue: boolean) {
    this.lesioniPressioneArray.controls.forEach((item, index) => {
      const lesione = lesioni.find(l => l.idDizionario === item.get('idDizionario')?.value);
      if (lesione) {
        item.patchValue({
          selected: true,
          numero: lesione.numero
        });
        item.get('selected')?.enable()
        item.get('numero')?.enable()

        if (lesione.idDizionario === EComplicanzeAltroNessuna.ALTRO_LES_PRES) {
          this.complicanzeForm.get('altroSedePressione')?.enable()
        }
      }
      if (isLesioneTrue) {
        item.get('selected')?.enable()
      }
    });
  }

  configureConnectedControl(enabled: boolean, controlName: string, value?: any): void {
    if (enabled) {
      this.complicanzeForm.get(controlName)?.enable();
      this.complicanzeForm.get(controlName)?.reset();
      this.complicanzeForm.get(controlName)?.setValidators(Validators.required);
      this.complicanzeForm.get(controlName)?.updateValueAndValidity();
    } else {
      this.complicanzeForm.get(controlName)?.disable();
      this.complicanzeForm.get(controlName)?.reset();
      this.complicanzeForm.get(controlName)?.clearValidators();
      this.complicanzeForm.get(controlName)?.updateValueAndValidity();
    }
    if (this.keysPresentiIntercorseSiNokeys.find(key => key === controlName)) this.payloadPresentIntercorse[controlName] = value;

    // Forza la validazione degli array correlati dopo il cambio di stato
    setTimeout(() => {
      this.forceArrayValidation();
    }, 0);
  }

  subscribeToControlsChanges(): void {
    const ossificazioneEterotopiche = this.isDimissione ? 'ossificazioneEterotopiche' : 'ossificazioneEterotopicheSelected';
    const lesioniPressione = this.isDimissione ? 'lesioniPressione' : 'lesioniPressioneSelected';
    const osteomielite = this.isDimissione ? 'osteomielite' : 'osteomieliteSelected';
    const infezioneContaminazioneMultiresistente = this.isDimissione ? 'infezioneContaminazioneMultiresistente' : 'infezioneContaminazioneMultiresistenteSelected';
    const trombosiVenosaProfonda = this.isDimissione ? 'trombosiVenosaProfonda' : 'trombosiVenosaProfondaSelected';
    const emboliaPolmonare = this.isDimissione ? 'emboliaPolmonare' : 'emboliaPolmonareSelected';
    const spasticita = this.isDimissione ? 'spasticita' : 'spasticitaSelected';
    const dolore = this.isDimissione ? 'dolore' : 'doloreBoolean';
    const sepsi = this.isDimissione ? 'sepsi' : 'sepsiSelected';

    const osteomielite$ = this.complicanzeForm.get(osteomielite)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['osteomieliteSelected']) {
            this.configureConnectedControl(true, 'osteomielite', value);
          }
          this.complicanzeForm.get('noteOsteomielite')?.enable();
        } else {
          if (this.complicanzeForm.controls['osteomieliteSelected']) {
            this.configureConnectedControl(false, 'osteomielite', value);
          }
          this.complicanzeForm.get('noteOsteomielite')?.disable();
          this.complicanzeForm.get('noteOsteomielite')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const ossificazioneEterotopiche$ = this.complicanzeForm.get(ossificazioneEterotopiche)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['ossificazioneEterotopicheSelected']) {
            this.configureConnectedControl(true, 'ossificazioneEterotopiche', value);
          }
          this.complicanzeForm.get('noteOssificazione')?.enable();
          this.ossificazioneArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            const idDizionario2 = c.get('idDizionario2');
            if (selected instanceof FormControl) {
              selected.enable();
              // If the checkbox is already selected, also enable the radio button
              if (selected.value === true && idDizionario2 instanceof FormControl) {
                idDizionario2.enable();
              }
            } else {
              console.warn(`Unexpected control at ossificazioneArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['ossificazioneEterotopicheSelected']) {
            this.configureConnectedControl(false, 'ossificazioneEterotopiche', value);
          }
          this.complicanzeForm.get('noteOssificazione')?.disable();
          this.complicanzeForm.get('noteOssificazione')?.reset();
          this.complicanzeForm.get('altroSedeOssificazione')?.reset();
          this.complicanzeForm.get('altroSedeOssificazione')?.disable();
          this.createOssificazFormArray();
        }

        // Forza la riesecuzione della validazione dell'array
        this.ossificazioneArray.updateValueAndValidity();
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const lesioniDaPressione$ = this.complicanzeForm.get(lesioniPressione)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['lesioniPressioneSelected']) {
            this.configureConnectedControl(true, 'lesioniPressione', value);
          }
          this.complicanzeForm.get('notePressione')?.enable();
          this.lesioniPressioneArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at lesioniPressioneArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['lesioniPressioneSelected']) {
            this.configureConnectedControl(false, 'lesioniPressione', value);
          }
          this.complicanzeForm.get('notePressione')?.disable();
          this.complicanzeForm.get('notePressione')?.reset();
          this.complicanzeForm.get('altroSedePressione')?.reset();
          this.complicanzeForm.get('altroSedePressione')?.disable();
          this.createLesioniPressioneFormArray();
        }

        // Forza la riesecuzione della validazione dell'array
        this.lesioniPressioneArray.updateValueAndValidity();
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const emboliaPolmonare$ = this.complicanzeForm.get(emboliaPolmonare)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['emboliaPolmonareSelected']) {
            this.configureConnectedControl(true, 'emboliaPolmonare', value);
          }
          this.complicanzeForm.get('noteEmbolia')?.enable();
        } else {
          if (this.complicanzeForm.controls['emboliaPolmonareSelected']) {
            this.configureConnectedControl(false, 'emboliaPolmonare', value);
          }
          this.complicanzeForm.get('noteEmbolia')?.disable();
          this.complicanzeForm.get('noteEmbolia')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const spasticita$ = this.complicanzeForm.get(spasticita)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['spasticitaSelected']) {
            this.configureConnectedControl(true, 'spasticita', value);
          }
          this.complicanzeForm.get('punteggioScalaAshworth')?.enable();
          this.complicanzeForm.get('punteggioScalaAshworth')?.setValue(null, Validators.required);
          this.complicanzeForm.get('noteSpasticita')?.enable();
        } else {
          if (this.complicanzeForm.controls['spasticitaSelected']) {
            this.configureConnectedControl(false, 'spasticita', value);
          }
          this.complicanzeForm.get('punteggioScalaAshworth')?.disable();
          this.complicanzeForm.get('punteggioScalaAshworth')?.reset();
          this.complicanzeForm.get('noteSpasticita')?.disable();
          this.complicanzeForm.get('noteSpasticita')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const trombosiVenosaProfonda$ = this.complicanzeForm.get(trombosiVenosaProfonda)?.valueChanges
      .subscribe(value => {
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['trombosiVenosaProfondaSelected']) {
            this.configureConnectedControl(true, 'trombosiVenosaProfonda', value);
          }
          this.complicanzeForm.get('noteTrombosi')?.enable();
          this.trombosiArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at trombosiArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['trombosiVenosaProfondaSelected']) {
            this.configureConnectedControl(false, 'trombosiVenosaProfonda', value);
          }
          this.complicanzeForm.get('noteTrombosi')?.disable();
          this.complicanzeForm.get('noteTrombosi')?.reset();
          this.createTrombosiFormArray();
        }

        // Forza la riesecuzione della validazione dell'array
        this.trombosiArray.updateValueAndValidity();
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const dolore$ = this.complicanzeForm.get(dolore)?.valueChanges
      .subscribe(value => {
        if (value) {
          this.complicanzeForm.get('noteDolore')?.enable();
          this.doloreArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at doloreArray[${index}]`, selected);
            }
          });
        } else {
          this.complicanzeForm.get('noteDolore')?.disable();
          this.complicanzeForm.get('noteDolore')?.reset();
          this.createDoloreFormArray();
        }

        // Forza la riesecuzione della validazione dell'array
        this.doloreArray.updateValueAndValidity();
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const infezioneContaminazioneMultiresistente$ = this.complicanzeForm.get(infezioneContaminazioneMultiresistente)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['infezioneContaminazioneMultiresistenteSelected']) {
            this.configureConnectedControl(true, 'infezioneContaminazioneMultiresistente', value);
          }
          this.complicanzeForm.get('noteMultiresistente')?.enable();
        } else {
          if (this.complicanzeForm.controls['infezioneContaminazioneMultiresistenteSelected']) {
            this.configureConnectedControl(false, 'infezioneContaminazioneMultiresistente', value);
          }
          this.complicanzeForm.get('noteMultiresistente')?.disable();
          this.complicanzeForm.get('noteMultiresistente')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const sepsi$ = this.complicanzeForm.get(sepsi)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['sepsiSelected']) {
            this.configureConnectedControl(true, 'sepsi', value);
          }
          this.complicanzeForm.get('noteSepsi')?.enable();
        } else {
          if (this.complicanzeForm.controls['sepsiSelected']) {
            this.configureConnectedControl(false, 'sepsi', value);
          }
          this.complicanzeForm.get('noteSepsi')?.disable();
          this.complicanzeForm.get('noteSepsi')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    this.subscriptions = [
      osteomielite$,
      ossificazioneEterotopiche$,
      lesioniDaPressione$,
      spasticita$,
      trombosiVenosaProfonda$,
      emboliaPolmonare$,
      dolore$,
      infezioneContaminazioneMultiresistente$,
      sepsi$
    ];
  }

  handleComplicanzaChange(
    formArray: FormArray,
    index: number,
    checked: boolean,
    altroFieldName: string,
    altroEnumValue: number,
    nessunaEnumValue?: number
  ): void {
    const item = formArray.at(index) as FormGroup;
    const idDizionario = item.get(this.isDimissione ? 'idDizionario' : 'idDizionario1')?.value; //gestisco i due casi dimissione e daticlinici(radiobtns)
    const idDizionario2 = item.get('idDizionario2');

    item.get('selected')?.setValue(checked);

    if (nessunaEnumValue && idDizionario === nessunaEnumValue && checked) { // se seleziono nessuna
      formArray.controls.forEach((control, i) => {
        if (i !== index) {
          control.get('selected')?.setValue(false);
          control.get('idDizionario2')?.reset();
          control.get('idDizionario2')?.clearValidators();
          control.get('idDizionario2')?.disable();

          // if (idDizionario?.value === altroEnumValue) {
          this.complicanzeForm.get(altroFieldName)?.reset();
          this.complicanzeForm.get(altroFieldName)?.disable();
          // }
        }
      });

      idDizionario2?.reset();
      idDizionario2?.clearValidators();
      idDizionario2?.disable();
    }
    else if (checked) { // se seleziono le altre chckbx
      // deseleziono nessuna
      formArray.controls.find(contrlol => contrlol.get(this.isDimissione ? 'idDizionario' : 'idDizionario1')?.value === nessunaEnumValue)?.get('selected')?.setValue(false);
      idDizionario2?.setValidators(Validators.required);
      idDizionario2?.enable();

      if (idDizionario === altroEnumValue) {
        this.complicanzeForm.get(altroFieldName)?.setValidators(Validators.required);
        this.complicanzeForm.get(altroFieldName)?.enable();
      }
    } else {
      idDizionario2?.reset();
      idDizionario2?.clearValidators();
      idDizionario2?.disable();

      if (idDizionario === altroEnumValue) {
        this.complicanzeForm.get(altroFieldName)?.setValue(undefined);
        this.complicanzeForm.get(altroFieldName)?.disable();
      }
    }

    idDizionario2?.updateValueAndValidity();
  }

  // Gestisce il cambio delle complicanze respiratorie
  onComplicanzaRespiratoriaChange(index: number, checked: boolean): void {
    this.handleComplicanzaChange(
      this.complicanzeRespiratorieArray,
      index,
      checked,
      'altroComplicanzeRespiratorie',
      this.EComplicanzeAltroNessuna.ALTRO_COMPL_RESP,
      this.EComplicanzeAltroNessuna.NESSUNO_COMPL_RESP
    );
  }

  // Gestisce il cambio delle complicanze urologiche
  onComplicanzaUrologicaChange(index: number, checked: boolean): void {
    this.handleComplicanzaChange(
      this.complicanzeUrologicheArray,
      index,
      checked,
      'altroComplicanzeUrologiche',
      this.EComplicanzeAltroNessuna.ALTRO_URO,
      this.EComplicanzeAltroNessuna.NESSUNA_URO
    );
  }

  // Gestisce il cambio delle complicanze del rachide
  onComplicanzaRachideChange(index: number, checked: boolean): void {
    this.handleComplicanzaChange(
      this.rachideArray,
      index,
      checked,
      'altroRachide',
      this.EComplicanzeAltroNessuna.ALTRO_RACHIDE,
      this.EComplicanzeAltroNessuna.NESSUNA_RACHIDE
    );
  }

  // Apre il popup per le note
  openPopupNote(controlName: string): void {
    const noteControl = this.complicanzeForm.get(controlName)


    if (noteControl) {
      let configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly)

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != noteControl.value) {
          noteControl.setValue(res)
          noteControl.markAsDirty()
          noteControl.updateValueAndValidity()
        }
      })
    }
  }


  // Crea un nuovo FormGroup per ogni sede di ossificazione
  createOssificazioneItem(sede: DizionarioModel): FormGroup {
    return this.fb.group({
      idDizionario1: [sede.idDizionario], // ID della sede
      idDizionario2: [{ value: null, disabled: true }],              // ID limitante (null inizialmente)
      selected: [{ value: false, disabled: true }]                   // Checkbox selezionato o no
    });
  }

  // Aggiungi un elemento al FormArray
  addOssificazioneItem(sede: DizionarioModel): void {
    this.ossificazioneArray.push(this.createOssificazioneItem(sede));
  }

  createOssificazFormArray() {
    this.ossificazioneArray.clear()
    this.optionsSedeOssificazione.forEach(sede => {
      this.addOssificazioneItem(sede);
    });

  }

  toggleArrayStatus(key: string, selectedOption: DizionarioModel): void {
    const array = this.getArrayByKey(key);
    if (!array) return;

    array.controls.forEach(group => {
      const fg = group as FormGroup;
      if (selectedOption?.idDizionario === 411) {
        fg.get('selected')?.enable();
        // fg.get('idDizionario2')?.disable();
      } else {
        fg.get('selected')?.disable();
        fg.get('idDizionario2')?.reset();
        fg.get('idDizionario2')?.disable();
      }
    });
  }

  toggleRelativeRadioLimitante(index: number, checked: boolean, control?: AbstractControl): void {
    if (!control) return;

    const altroCtrl = this.complicanzeForm.get('altroSedeOssificazione');
    const idDizionario2Ctrl = this.ossificazioneArray.at(index).get('idDizionario2');
    const isAltroCheckbox = this.optionsSedeOssificazione[index].idDizionario === this.EComplicanzeAltroNessuna.ALTRO_OSS;

    if (checked) {
      if (isAltroCheckbox && altroCtrl) {
        altroCtrl.setValidators(Validators.required);
        altroCtrl.enable({ emitEvent: false });
      }
      if (idDizionario2Ctrl) {
        idDizionario2Ctrl.setValidators(Validators.required);
        idDizionario2Ctrl.enable({ emitEvent: false });
      }
    } else {
      if (isAltroCheckbox && altroCtrl) {
        altroCtrl.reset();
        altroCtrl.clearValidators();
        altroCtrl.disable({ emitEvent: false });
      }
      if (idDizionario2Ctrl) {
        idDizionario2Ctrl.reset();
        idDizionario2Ctrl.clearValidators();
        idDizionario2Ctrl.disable({ emitEvent: false });
      }
    }

    altroCtrl?.updateValueAndValidity({ emitEvent: false });
    idDizionario2Ctrl?.updateValueAndValidity({ emitEvent: false });
  }


  /**
   * Forza la riesecuzione della validazione di tutti gli array del form.
   * Questo è necessario perché i validatori personalizzati dipendono da altri controlli
   * e Angular non sa automaticamente quando rieseguire la validazione.
   */
  forceArrayValidation(): void {
    // Array che hanno validatori dipendenti da altri controlli
    const arraysToValidate = [
      this.ossificazioneArray,
      this.lesioniPressioneArray,
      this.trombosiArray,
      this.doloreArray,
      this.complicanzeRespiratorieArray,
      this.complicanzeUrologicheArray,
      this.rachideArray
    ];

    arraysToValidate.forEach(array => {
      if (array) {
        array.updateValueAndValidity();
      }
    });
  }

  ngOnDestroy() {
    this.loadDecoders$?.unsubscribe()
    this.subscriptions?.forEach(sub => sub?.unsubscribe())
  }

  // Crea un nuovo FormGroup per ogni sede di lesione da pressione
  createLesionePressioneItem(sede: DizionarioModel): FormGroup {
    return this.fb.group({
      selected: [{ value: false, disabled: true }],
      idDizionario: [sede.idDizionario],
      numero: [{ value: null, disabled: true }]
    })
  }

  // Aggiungi un elemento al FormArray delle lesioni da pressione
  addLesionePressioneItem(sede: DizionarioModel): void {
    this.lesioniPressioneArray.push(this.createLesionePressioneItem(sede));
  }

  // Gestisce la selezione/deselezione di una sede di lesione
  toggleLesionePressione(index: number, checked: boolean): void {
    const item = this.lesioniPressioneArray.at(index) as FormGroup;
    const idDizionario = item.get('idDizionario')?.value;

    if (!checked) {
      // Se deselezionato, resetta anche il valore dello stadio
      item.get('numero')?.setValue(null);
      item.get('numero')?.disable();

      // Se è l'opzione "Altro", resetta e disabilita il campo descrizione
      if (idDizionario === this.EComplicanzeAltroNessuna.ALTRO_LES_PRES) {
        this.complicanzeForm.get('altroSedePressione')?.reset();
        this.complicanzeForm.get('altroSedePressione')?.disable();
      }
    } else {
      item.get('numero')?.enable();
      item.get('numero')?.setValidators([Validators.required]);

      // Se è l'opzione "Altro", abilita il campo input
      if (idDizionario === this.EComplicanzeAltroNessuna.ALTRO_LES_PRES) {
        this.complicanzeForm.get('altroSedePressione')?.setValidators(Validators.required);
        this.complicanzeForm.get('altroSedePressione')?.enable();
      }
    }

    // Aggiorna lo stato di selezione
    item.get('selected')?.setValue(checked);
    item.get('selected')?.updateValueAndValidity();
    item.get('numero')?.updateValueAndValidity();
  }

  // Crea il FormArray per le lesioni da pressione
  createLesioniPressioneFormArray() {
    this.lesioniPressioneArray.clear()
    this.optionsSedeLesionePressione.forEach(sede => {
      this.addLesionePressioneItem(sede);
    });
  }


  // Aggiungi un elemento al FormArray delle trombosi
  addTrombosiItem(sede: DizionarioModel): void {
    const createTrombosiItem = (sede: DizionarioModel) => {
      return this.fb.group({
        idDizionario: [sede.idDizionario],
        selected: [{ value: false, disabled: true }]
      })
    }

    this.trombosiArray.push(createTrombosiItem(sede));
  }


  // Gestisce la selezione/deselezione di una sede di trombosi
  toggleTrombosi(index: number, checked: boolean): void {
    const item = this.trombosiArray.at(index) as FormGroup;
    item.get('selected')?.setValue(checked);
  }

  // Crea il FormArray per le trombosi
  createTrombosiFormArray() {
    this.trombosiArray.clear()
    this.optionsSedeTrombosi.forEach(sede => {
      this.addTrombosiItem(sede);
    });
  }

  createComplicanzaItem(item: DizionarioModel, hasNumericValue: boolean = false, disabled: boolean = false): FormGroup {
    const formGroup: any = {
      selected: [{ value: false, disabled: disabled }]
    };
    if (this.isDimissione) {
      formGroup.idDizionario = [item.idDizionario];
    } else {
      // se sono in dati clinici avrò 2 dizionari (checbox e radiobtn)
      formGroup.idDizionario1 = [item.idDizionario];
      formGroup.idDizionario2 = [{ value: null, disabled: true }];
    }

    if (hasNumericValue) {
      formGroup.numero = [{ value: null, disabled: true }];
    }

    return this.fb.group(formGroup);
  }

  // Crea un nuovo FormGroup per ogni complicanza respiratoria
  createComplicanzaRespiratoriaItem(complicanza: DizionarioModel): FormGroup {
    if (this.isDimissione) {
      return this.createComplicanzaItem(complicanza);
    } else {
      return this.fb.group({
        idDizionario1: [complicanza.idDizionario],
        idDizionario2: [{ value: null, disabled: true }],
        selected: [false]
      });
    }
  }

  // Crea un nuovo FormGroup per ogni complicanza urologica
  createComplicanzaUrologicaItem(complicanza: DizionarioModel): FormGroup {
    if (this.isDimissione) {
      return this.createComplicanzaItem(complicanza);
    } else {
      return this.fb.group({
        idDizionario1: [complicanza.idDizionario],
        idDizionario2: [{ value: null, disabled: true }],
        selected: [false]
      });
    }
  }

  // Aggiungi un elemento al FormArray delle complicanze respiratorie
  addComplicanzaRespiratoriaItem(complicanza: DizionarioModel): void {
    this.complicanzeRespiratorieArray.push(this.createComplicanzaRespiratoriaItem(complicanza));
  }

  // Crea il FormArray per le complicanze respiratorie
  createComplicanzeRespiratorieFormArray() {
    const altroIndex = this.optionsComplicanzeRespiratorie.findIndex(
      c => c.idDizionario === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP
    );
    if (altroIndex !== -1) {
      const [altro] = this.optionsComplicanzeRespiratorie.splice(altroIndex, 1);
      this.optionsComplicanzeRespiratorie.push(altro);
    }
    this.complicanzeRespiratorieArray.clear()
    this.optionsComplicanzeRespiratorie.forEach(complicanza => {
      this.addComplicanzaRespiratoriaItem(complicanza);
    });
  }


  // Aggiungi un elemento al FormArray delle complicanze urologiche
  addComplicanzaUrologicaItem(complicanza: DizionarioModel): void {
    this.complicanzeUrologicheArray.push(this.createComplicanzaUrologicaItem(complicanza));
  }

  // Crea il FormArray per le complicanze urologiche
  createComplicanzeUrologicheFormArray() {
    const altroIndex = this.optionsComplicanzeUrologiche.findIndex(
      c => c.idDizionario === EComplicanzeAltroNessuna.ALTRO_URO
    );
    if (altroIndex !== -1) {
      const [altro] = this.optionsComplicanzeUrologiche.splice(altroIndex, 1);
      this.optionsComplicanzeUrologiche.push(altro);
    }
    this.complicanzeUrologicheArray.clear()
    this.optionsComplicanzeUrologiche.forEach(complicanza => {
      this.addComplicanzaUrologicaItem(complicanza);
    });
  }

  createRachideItem(complicanza: DizionarioModel): FormGroup {
    if (this.isDimissione) {
      return this.createComplicanzaItem(complicanza);
    } else {
      return this.fb.group({
        idDizionario1: [complicanza.idDizionario],
        idDizionario2: [{ value: null, disabled: true }],
        selected: [false]
      });
    }
  }

  // Aggiungi un elemento al FormArray del rachide
  addRachideItem(complicanza: DizionarioModel): void {
    this.rachideArray.push(this.createRachideItem(complicanza));
  }

  // Crea il FormArray per le complicanze del rachide
  createRachideFormArray(): void {
    this.rachideArray.clear();
    this.optionsRachide.forEach(complicanza => {
      this.addRachideItem(complicanza);
    });
  }

  createDoloreItem(dolore: DizionarioModel): FormGroup {
    return this.isDimissione ?
      this.fb.group({
        idDizionario: [dolore.idDizionario], // ID della complicanza
        selected: [{ value: false, disabled: true }]  // Checkbox selezionato o no
      })
      : this.fb.group({
        idDizionario1: [dolore.idDizionario], // ID della checkbox
        idDizionario2: [{ value: null, disabled: true }],              // ID limitante (null inizialmente)
        selected: [{ value: false, disabled: true }]                   // Checkbox selezionato o no
      });
  }

  addDoloreItem(dolore: DizionarioModel): void {
    const doloreItem = this.createDoloreItem(dolore);
    this.doloreArray.push(doloreItem);

    // Aggiungi subscription per triggerare validazione quando cambiano i radio Presenti/Intercorse
    if (!this.isDimissione) {
      doloreItem.get('idDizionario2')?.valueChanges.subscribe(() => {
        this.doloreArray.updateValueAndValidity();
      });
      doloreItem.get('selected')?.valueChanges.subscribe(() => {
        this.doloreArray.updateValueAndValidity();
      });
    }
  }

  createDoloreFormArray() {
    this.doloreArray.clear()
    this.optionsDoloreComplicanze.forEach(dolore => {
      this.addDoloreItem(dolore);
    });
  }

  toggleDolore(index: number, checked: boolean): void {
    const item = this.doloreArray.at(index) as FormGroup;
    item.get('selected')?.setValue(checked);

    const radioControl = item.get('idDizionario2');

    if (checked) {
      radioControl?.setValidators(Validators.required);
      radioControl?.enable();
    } else {
      radioControl?.clearValidators();
      radioControl?.reset();
      radioControl?.disable();
    }
    radioControl?.updateValueAndValidity();
  }

  toggleRadioInArray(formArray: FormArray, index: number, controlName: string, value: any): void {
    const item = formArray.at(index) as FormGroup;
    toggleRadioSelection(item, controlName, value);
  }

  getSelectedItems(formArray: FormArray): { idDizionario: number }[] {
    return formArray.value.filter((item: any) => item.selected);
  }

  formatIntercorse(label: string, singolare: 'm' | 'f'): string {
    if (label !== 'INTERCORSE') return label.replace(/Presenti\b/i, 'Presente');
    return singolare === 'f'
      ? label.replace(/Intercorse\b/i, 'Intercorsa')
      : label.replace(/Intercorse\b/i, 'Intercorso');
  }

  get complicanzeRespiratorieArray(): FormArray {
    return this.complicanzeForm.get(
      this.isDimissione ? 'complicanzeRespiratorieDimissioneLista' : 'complicanzeRespiratorie'
    ) as FormArray;
  }

  get complicanzeUrologicheArray(): FormArray {
    return this.complicanzeForm.get(
      this.isDimissione ? 'complicanzeUrologicheDimissioneLista' : 'complicanzeUrologiche'
    ) as FormArray;
  }

  get rachideArray(): FormArray {
    return this.complicanzeForm.get(
      this.isDimissione ? 'rachideDimissioneLista' : 'rachideLista'
    ) as FormArray;
  }

  get trombosiArray(): FormArray {
    return this.complicanzeForm.get(
      this.isDimissione ? 'trombosiDimissioneLista' : 'trombosiLista'
    ) as FormArray;
  }

  get doloreArray(): FormArray {
    return this.complicanzeForm.get(
      this.isDimissione ? 'doloreDimissioneLista' : 'doloreLista'
    ) as FormArray;
  }

  get lesioniPressioneArray(): FormArray {
    return this.complicanzeForm.get('lesioniPressioneLista') as FormArray;
  }

  get ossificazioneArray(): FormArray {
    const key = this.isDimissione
      ? 'ossificazioneDimissioneLista'
      : 'ossificazioneLista';
    return this.complicanzeForm.get(key) as FormArray;
  }

  getArrayByKey(key: string): FormArray | null {
    switch (key) {
      case 'complicanzeRespiratorie':
        return this.complicanzeRespiratorieArray;
      case 'complicanzeUrologiche':
        return this.complicanzeUrologicheArray;
      case 'rachide':
        return this.rachideArray;
      case 'trombosiVenosaProfonda':
      case 'trombosi':
        return this.trombosiArray;
      case 'dolore':
        return this.doloreArray;
      case 'lesioniPressione':
        return this.lesioniPressioneArray;
      case 'ossificazioneEterotopiche':
        return this.ossificazioneArray;
      default:
        return null;
    }
  }

  // parent name serve a capire se è selezionato il radio si/no, se si attivo validazione
  atLeastOneSelectedInArray(errorKey: string, parentName?:string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (parentName) {
        const value = this.complicanzeForm?.get(parentName)?.value
        const isNoSelected = (value?.idDizionario && value?.idDizionario !== 411) || (typeof value === 'boolean' && !value)
        //se seleziono no oppure sono in dimiss.rep acuti non serve validazione
        if (isNoSelected || (this.isDimissione && this.repartoType === TipoRicoveroEnum.ACUTI && !value)) return null
      } else {
        if (this.isDimissione && this.repartoType === TipoRicoveroEnum.ACUTI) return null //se è dimiss acuti non è obbligatorio scegliere un valore dalle liste
      }
      if (!(control instanceof FormArray)) return null;

      const atLeastOne = control.controls.some(item => item.get('selected')?.value === true);
      return atLeastOne ? null : {[errorKey]: true};
    };
  }

// Validatore generalizzato per array con checkbox e radio
  createArrayWithRadioValidator(parentControlName: string,
                                errorKeys: { atLeastOneSelected: string; radioMissing: string },
                                radioControlName: string = 'idDizionario2',
  ): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      // Se il controllo parent non è true, non serve validazione
      if (!this.complicanzeForm?.get(parentControlName)?.value || this.complicanzeForm?.get(parentControlName)?.value.idDizionario === 248) return null;

      if (!(control instanceof FormArray)) return null;

      // Controlla che almeno una checkbox sia selezionata
      const selectedItems = control.controls.filter(item => item.get('selected')?.value === true);
      // disabilito validazione se è selezionato nessuna
      const isNessunaSelected = [EComplicanzeAltroNessuna.NESSUNA_RACHIDE,
        EComplicanzeAltroNessuna.NESSUNA_URO,
        EComplicanzeAltroNessuna.NESSUNO_COMPL_RESP
      ].includes(selectedItems[0]?.get('idDizionario1')?.value)
      if (isNessunaSelected) return null;

      if (selectedItems.length === 0) {
        return { [errorKeys.atLeastOneSelected]: true };
      }

      // Controlla che per ogni checkbox selezionata ci sia il radi
      const missingPresentiIntercorse = selectedItems.some(item => {
        const idDizionario2Value = item.get(radioControlName)?.value;
        return !idDizionario2Value;
      });

      if (missingPresentiIntercorse) {
        return { [errorKeys.radioMissing]: true };
      }

      return null;
    };
  }

  // metodo per controllare all'apertura dell accordion la validità di tutti i radio presenti/intercorse
  checkPresInterOnInit() {
    this.keysPresentiIntercorseSiNokeys.forEach(key => {
      if (this.complicanzeForm.get(key)?.value && this.complicanzeForm.get(key)?.value.idDizionario !== 248 && this.complicanzeForm.get(`${key}Selected`)?.value &&
          this.complicanzeForm.get(`${key}Selected`)?.value.idDizionario ===  this.complicanzeForm.get(key)?.value.idDizionario)  {
        this.complicanzeForm.get(key)?.setErrors({'presentiIntercorseNotSelected': true});
      }
    })
  }

}
