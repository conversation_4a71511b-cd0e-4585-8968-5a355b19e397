<div class="header-menu">
  <div class="container-fluid px-4">
    <div class="row align-items-center justify-content-between g-0">
      <div class="col-auto">
        <button mat-icon-button class="back-button" (click)="onBack()">
                    <span class="back-link">
                        <svg class="icon icon-left">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-arrow-left"></use>
                        </svg>
                        <span class="text-green">Indietro</span>
                    </span>
        </button>
      </div>
      <div class="col patient-section" *ngIf="showHeaderPatientInfo">
        <div class="avatar-container">
          <img src="./assets/images/mielo/UserMaschio.png" class="avatar-icon" alt="Avatar utente"
               style="width: 40px; height: 40px;">
        </div>
        <div class="patient-info-container">
          <div class="patient-info">
            <div class="info-label">Nome e cognome:</div>
            <div class="info-value">{{ datiPaziente?.nomePaziente }} {{ datiPaziente?.cognomePaziente }}</div>
          </div>
          <div class="patient-info">
            <div class="info-label">Codice identificativo:</div>
            <div class="info-value">{{ datiPaziente?.codicePaziente }}</div>
          </div>
          <div class="patient-info">
            <div class="info-label">Data di nascita:</div>
            <div class="info-value">{{ datiPaziente?.dataNascitaPaziente | date:'dd/MM/yyyy':'':'it-IT' }}</div>
          </div>
        </div>
      </div>
      <div class="col-auto" *ngIf="datiPaziente?.datiSocioEconomiciMancanti">
        <div class="missing-data-indicator">
          <svg class="icon icon-danger">
            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-warning-circle"></use>
          </svg>
          <span class="text-danger">Dati mancanti</span>
        </div>
      </div>
      <div class="col-auto ms-auto">
        <div class="col-auto ms-auto">
          <button *ngIf="canEditAnagrafica" (click)="goToEditPatientAnagrafica()" mat-stroked-button
            class="edit-button">
            <svg class="icon icon-primary">
              <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-pencil"></use>
            </svg>
            Modifica
          </button>
        
          <button *ngIf="!canEditAnagrafica && this.operatore.idRuolo !== IdOperatoreEnum.COM" (click)="goToEditPatientAnagrafica()" mat-stroked-button
            class="edit-button">
            <span class="d-flex align-items-center">
              <svg class="icon icon-primary" style="width: 1.5rem; height: 1.5rem;">
                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-password-visible"></use>
              </svg>
              Visualizza
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
