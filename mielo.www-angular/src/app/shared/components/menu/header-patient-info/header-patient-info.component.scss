.container-menu {
    margin-left: 1rem;
    margin-right: 1rem;
    padding: 0 20px;
}

.button-back {
    margin-right: 1rem;
}

.header-menu {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
}

.patient-section {
    margin-left: 2rem;
    display: flex;
    align-items: center;
    min-width: 0;
}

.avatar-container {
    display: flex;
    align-items: center;
    margin-right: 24px;
    background-color: #F0F0F0;
    border-radius: 50%;
    min-width: 40px;
    height: 40px;
    justify-content: center;
    flex-shrink: 0;

    .avatar-icon {
        width: 40px;
        height: 40px;
        fill: #666666;
    }
}

.patient-info-container {
    display: flex;
    gap: 48px;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }
}

.patient-info {
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0;

    .info-label {
        font-size: 14px;
        color: #666;
        margin-right: 8px;
    }

    .info-value {
        font-weight: 600;
        color: #003354;
    }
}

.edit-button {
    border: none !important;
    color: #026C3C !important;
    padding: 4px 16px;
    white-space: nowrap;

    svg {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        fill: #026C3C;
    }
}

button.mat-mdc-stroked-button.mat-stroked-button.edit-button {
    border: none !important;
    border-width: 0 !important;
    color: var(--primary) !important;
    box-shadow: none !important;

    &::after {
        border: none !important;
        border-width: 0 !important;
    }

    &:hover, &:active, &:focus {
        background-color: transparent !important;
        border: none !important;
        border-width: 0 !important;
    }
}

.no-border.mat-stroked-button {
    border: none !important;
    &:hover {
        background-color: transparent !important;
    }
}

@media (max-width: 1200px) {
    .patient-info-container {
        gap: 32px;
    }
}

@media (max-width: 992px) {
    .patient-info-container {
        gap: 24px;
    }
}

.missing-data-indicator {
    display: flex;
    align-items: center;
    padding: 4px 10px;

    svg {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        fill: #D90505 !important;
    }

    .text-danger {
        color: #D90505 !important;
        font-weight: 600;
        white-space: nowrap;
    }
}
