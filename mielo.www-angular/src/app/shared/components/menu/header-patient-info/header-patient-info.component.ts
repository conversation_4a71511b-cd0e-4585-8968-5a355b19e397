import {CommonModule, Location} from '@angular/common';
import {Component, Input, OnChanges, OnDestroy, SimpleChanges} from '@angular/core';
import {Router} from '@angular/router';
import {MaterialModule} from '../../../../core/material.module';
import {RicercaSchedaPazienteService} from '../../../../mielo/services/ricerca-scheda-paziente.service';
import {TDatiPaziente} from "../../../interfaces/dati-paziente.interface";
import {DialogService} from '../../../services/dialog.service';
import {SchedaRicoveroService} from "../../../../mielo/services/scheda-ricovero.service";
import {SchedaRicoveroReqModel} from "../../../interfaces/scheda-ricovero.interface";
import { Subscription} from "rxjs";
import { IdOperatoreEnum, StatoEventoEnum } from '../../../enums/enum';
import { InfoOperatoreDTO } from '../../../../core/interfaces/info-operatore.interface';
import { OperatoreService } from '../../../../core/services/operatore.service';

@Component({
  selector: 'app-header-patient-info',
  standalone: true,
  imports: [CommonModule, MaterialModule],
  templateUrl: './header-patient-info.component.html',
  styleUrl: './header-patient-info.component.scss'
})
export class HeaderPatientInfoComponent implements OnDestroy, OnChanges {
  @Input() datiPaziente: TDatiPaziente | null = null
  @Input() onBackParent: () => void;
  @Input() schedaRicovero!: SchedaRicoveroReqModel;

  fromEventiAssociati = false
  private confermaUscitaDialog$: Subscription;

  operatore: InfoOperatoreDTO;
  catOperatore = IdOperatoreEnum;
  canEditAnagrafica: boolean = false;

  showHeaderPatientInfo: boolean = false;
  protected readonly IdOperatoreEnum = IdOperatoreEnum;


  constructor(
    private dialogService: DialogService,
    private router: Router,
    private ricercaService: RicercaSchedaPazienteService,
    private schedaService: SchedaRicoveroService,
    private operatoreService: OperatoreService,
    private location: Location
  ) {
    this.fromEventiAssociati = router.url.includes('eventi-associati');

    this.operatore = this.operatoreService.getOperatore();

  }
  
  ngOnChanges(changes: SimpleChanges) {
    this.canEditAnagrafica = this.schedaService.getSchedaCorrente()?.existAtLeastOneEventOnSamePresidio;
    if (changes){
      const patientFromSchedaCreation = this.schedaService.schedaCreation$.getValue()?.paziente
      // doppia gestione per quando si modifica da eventi associati
      this.datiPaziente = !patientFromSchedaCreation ? this.datiPaziente : {
        codicePaziente: patientFromSchedaCreation.codiceIdentificativo,
        cognomePaziente: patientFromSchedaCreation.cognome,
        nomePaziente: patientFromSchedaCreation.nome,
        dataNascitaPaziente: patientFromSchedaCreation.dataNascita,
        datiSocioEconomiciMancanti: this.schedaService.datiMancantiSocioEconomiciSubject.getValue()
      }
      this.showHeaderPatientInfo = this.operatore.idRuolo !== IdOperatoreEnum.COM;
    }
  }

  goToEditPatientAnagrafica() {
    if (!this.fromEventiAssociati && this.schedaRicovero.bozza !== false) { //se bozza è false allora è una scheda chiusa
      const testoModale = this.canEditAnagrafica ? 'Vai a modifica anagrafica' : 'Vai a visualizza anagrafica'
      if (this.canEditAnagrafica) {
        const dialog$ = this.dialogService.openConfirmDialog({
          tipo: 'warning',
          titolo: 'Attenzione',
          messaggio: 'Sicuro di voler uscire?\nI dati non salvati andranno persi.',
          bottoni: [
            { testo: 'Continua le modifiche', azione: 'annulla' },
            { testo: testoModale, azione: 'conferma' }
          ],
        }).subscribe(
            result => {
              dialog$.unsubscribe()
              if (result === 'conferma') {
                this.router.navigate(['/scheda-ricovero/paziente/details']);
              }
            }
        )
      } else {
        this.router.navigate(['/scheda-ricovero/paziente/details']);
      }

      return;
    }

    this.router.navigate(['/scheda-ricovero/paziente/details']);
  }

  onBack(): void {
    if (this.onBackParent) {
      this.ricercaService.initFiltersfromBack = true;
      const checkCorrispondenzaPresidi = this.schedaService.getSchedaCorrente().codPresidioOspedaliero === this.operatore.codLivello2Operatore

      if (this.schedaService.getSchedaCorrente()?.evento?.stato?.idDizionario === StatoEventoEnum.CHIUSO || 
         !this.canEditAnagrafica || !checkCorrispondenzaPresidi) {
        this.onBackParent();
        return
      }

      this.confermaUscitaDialog$ = this.dialogService.confermaUscita().subscribe(
        result => {
          if (result === 'conferma') {
            this.onBackParent();
          }
        }
      )
      return
    }

    const navigateTo = () => {
      const currentUrl = this.router.url;
      const isFromSearch = currentUrl.includes('/scheda-ricovero/details') ||
          currentUrl.includes('/eventi-associati');

      if (isFromSearch) {
        //flag per mantenere i filtri
        this.ricercaService.initFiltersfromBack = true;
      }
      this.location.back();
    }


    if (this.fromEventiAssociati) {
      navigateTo()
      return;
    }

    this.confermaUscitaDialog$ = this.dialogService.confermaUscita().subscribe(result => {
      if (result === 'conferma') {
        this.schedaService.idSchedaSelectedSubject.next(undefined);
        this.schedaService.schedaCreation$.next(null);
        const defaultValid = {
          valid: false,
          pristine: true
        }
        this.schedaService._areFormsCreateSchedaValidSubject$.next({
          formDatiAnagrafici: defaultValid,
          formDatiGenerali: defaultValid,
          formDatiSocioEconomici: defaultValid,
        })
        navigateTo()
      }
    });
  }

  ngOnDestroy(): void {
    this.confermaUscitaDialog$?.unsubscribe()
  }
}
