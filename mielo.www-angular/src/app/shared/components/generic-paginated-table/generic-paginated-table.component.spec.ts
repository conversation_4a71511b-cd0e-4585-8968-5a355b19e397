import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GenericPaginatedTableComponent } from './generic-paginated-table.component';

describe('GenericPaginatedTableComponent', () => {
  let component: GenericPaginatedTableComponent;
  let fixture: ComponentFixture<GenericPaginatedTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GenericPaginatedTableComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(GenericPaginatedTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
