<div *ngIf="dataSource && dataSource.length; else nessunRisultato">
    <table mat-table
           [dataSource]="dataSource | paginate: { id: 'server', itemsPerPage, currentPage: currentPage+1, totalItems }">
        <ng-container *ngFor="let column of columns" [matColumnDef]="column.key">
            <th mat-header-cell *matHeaderCellDef> {{ column.label }}</th>
            <td mat-cell *matCellDef="let element">
                <ng-container *ngIf="column.key !== 'actions'">
                    {{ formatCell(element, column.key) }}
                </ng-container>

                <ng-container *ngIf="column.key === 'actions'"
                              style="cursor: pointer">
                    <div *ngIf="actions && actions.length === 1"
                         class="d-flex justify-content-center"
                    >
                        <button type="button" mat-icon-button
                                matTooltip="Visualizza evento"
                                (click)="actions[0](element.idScheda)">
                            <svg>
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-inbox"></use>
                            </svg>
                        </button>
                    </div>

                    <div *ngIf="actions && actions.length > 1">
                        <ng-container *ngFor="let action of actions">

                        </ng-container>
                    </div>
                </ng-container>
            </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="columnKeys"></tr>
        <tr mat-row *matRowDef="let row; columns: columnKeys"></tr>
    </table>
</div>

<div *ngIf="dataSource.length" class="d-flex justify-content-end mt-3 custom-pagination align-items-center">
    <pagination-controls (pageChange)="pageChanged($event)"
                         [directionLinks]="true"
                         previousLabel=""
                         nextLabel=""
                         [responsive]="true"
                         [maxSize]="5"
                         id="server">
    </pagination-controls>

</div>

<ng-template #nessunRisultato>
    <div class="empty-box">
        <div class="mt-5">
            <img src="assets/images/mielo/empty.png" alt="nessun risultato">
        </div>
        <div class="mt-4"><h5>Nessun risultato presente</h5></div>
    </div>
</ng-template>
