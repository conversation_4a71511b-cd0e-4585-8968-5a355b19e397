.mdc-icon-button:focus{
  outline: none !important;
}

.menu-item{
  font-size:18px !important;
  letter-spacing: normal !important;
}

.mat-mdc-menu-item{
  min-height: 55px !important;
}


.inbox{
  height:32px !important;
  width:32px !important;
}

/** MAT TABLE **/
.table{
  width: 100%;
}

.mat-mdc-header-cell {
  border-right: 1px solid white !important;
  font-weight: 700;
}
.mat-mdc-cell{
  border-right: 1px solid #D8D8D8 !important;
}

.mat-mdc-cell, .mat-mdc-header-cell {
  padding: 0.5vw !important;
  padding-top: 27px !important;
  padding-bottom: 27px !important;
  //width: fit-content !important;
  white-space: pre-line;
  font-size: 18px !important;
  height: inherit !important;
  font-family: "Titillium Web", Geneva, Tahoma, sans-serif;
  letter-spacing: normal !important;
  vertical-align: middle !important;
}
.mat-mdc-row {
  min-height: 72px;
  border-radius: 0px !important;
  cursor: auto !important;
  /*fix IE11 centering*/
  background-color: #FFFFFF !important;
  border-bottom: 1px solid #D8D8D8;
  color: #000000 !important;
}

.mat-mdc-row:nth-child(even) {
  background-color: #F9F9F9 !important;
}

.mat-mdc-header-row,
.mat-mdc-header-row.mat-table-sticky {
  min-height: 72px;
  background-color: #E6E9F0 !important;
  color :#003354 !important;
}
.mat-mdc-header-cell:last-child {
  border-right: none !important;
}

.mat-mdc-cell:first-child {
  border-left: 1px solid #D8D8D8 !important;
}

.no-pointer {
  pointer-events: none;
}

.mat-column-azioni{
  width: 10px !important;
}

/** FINE MAT TABLE**/


//ngx-paginator style
.custom-pagination ::ng-deep .ngx-pagination{
  //color: #505051 !important;
  //padding: 10px;
  //font-weight: 600;
  //text-decoration: none !important;
}
.custom-pagination ::ng-deep .ngx-pagination .current {
  border: 2px solid #297A38;
  border-radius: 8px;
  padding: 10px;
  background-color: transparent;
  color: #505051;
  font-weight: 600;
  width: 48px;
  text-align: center;

}
.custom-pagination ::ng-deep .ngx-pagination a{
  width: 48px;
  height: 48px;
  color: #505051 !important;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  padding: 10px;
  text-align: center;
}


.empty-box{
  width: 100%;
  border: 2px solid #BACCD9;
  text-align: center;
  height: 250px;
}

.stato-evento-icon {
  font-size: 20px;
  height: 20px;
  width: 20px;
  color: #FF0000;
  cursor: help;
}

::ng-deep .mat-mdc-tooltip.custom-tooltip {
  background: transparent !important;
}

::ng-deep .mat-mdc-tooltip.custom-tooltip .mdc-tooltip__surface {
  background-color: #4A6278 !important;
  color: white !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  padding: 12px 16px !important;
  margin-bottom: 10px !important;
  line-height: 1.2 !important;
  font-family: "Titillium Web", sans-serif !important;
  max-width: none !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

::ng-deep .mat-mdc-tooltip.custom-tooltip .mdc-tooltip__surface::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #4A6278;
}
