import { Component, Input, OnInit } from '@angular/core';
import { NgxPaginationModule } from "ngx-pagination";
import { CommonModule } from "@angular/common";
import { MaterialModule } from "../../../core/material.module";
import { MatBadgeModule } from "@angular/material/badge";

@Component({
  selector: 'app-generic-table',
  templateUrl: './generic-paginated-table.component.html',
  standalone: true,
  imports: [MaterialModule, CommonModule, NgxPaginationModule, MatBadgeModule],
  styleUrls: ['./generic-paginated-table.component.scss']
})
export class GenericPaginatedTableComponent implements OnInit {
  @Input() columns: { key: string | 'actions', label: string }[] = []; // la key deve essere uguale al nome del campo del dataSource
  @Input() dataSource: any[] = [];
  @Input() totalItems: number | undefined = 0;
  @Input() currentPage: number = 0;
  @Input() itemsPerPage: number = 6;
  @Input() onPageChanged: Function;
  @Input() actions!: any[]; //array delle funz che si vogliono scatenare,associare l indice dell array a cui si vuole associare l azione

  columnKeys: string[];


  constructor() {

  }

  ngOnInit(): void {
    this.columnKeys = this.columns.map(c => c.key);
  }

  formatCell(element: any, columnKey: any): string {
    if (!columnKey || !element) return '/';

    const keys = columnKey.split('.');
    let value = keys.reduce((acc: any, key: any) => acc?.[key], element);

    // Gestione dei valori nulli, undefined o vuoti
    return value !== undefined && value !== null && value !== '' ? value : '/';
  }

  pageChanged($event: number) {
    this.currentPage = $event - 1;
    this.onPageChanged(this.currentPage);
  }

}
