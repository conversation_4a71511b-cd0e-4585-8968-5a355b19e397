# Refactor Componenti Complicanze

## Panoramica

È stato effettuato un refactor dei componenti delle complicanze per separare la logica di `isDimissione = true` da quella di `isDimissione = false`.

## Componenti Risultanti

### 1. ComplicanzeComponent (Modificato)
**Percorso**: `mielo.www-angular/src/app/shared/components/complicanze/`

**Scopo**: Ora gestisce SOLO la logica per `isDimissione = true` (dimissione)

**Caratteristiche**:
- R<PERSON><PERSON> l'input `@Input() isDimissione` (sempre true)
- R<PERSON>ssa dipendenza da `DatiCliniciService`
- Usa solo `DatiDimissioneService`
- Form semplificato con radio button Si/No
- Rimossa logica condizionale basata su `isDimissione`
- Rimossi campi specifici per dati clinici (spasticita, *Selected fields, etc.)

**Utilizzo**:
```html
<app-complicanze 
  [repartoType]="repartoType"
  [readOnly]="readOnly">
</app-complicanze>
```

### 2. ComplicanzeDatiCliniciComponent (Nuovo)
**Percorso**: `mielo.www-angular/src/app/shared/components/complicanze-dati-clinici/`

**Scopo**: Gestisce SOLO la logica per `isDimissione = false` (dati clinici)

**Caratteristiche**:
- Usa solo `DatiCliniciService`
- Contiene tutta la logica complessa con radio button Presenti/Intercorse
- Include tutti i campi *Selected
- Include spasticita e campi correlati
- Gestisce la logica doloreBoolean vs dolore
- Validatori complessi per array con radio button

**Utilizzo**:
```html
<app-complicanze-dati-clinici 
  [repartoType]="repartoType"
  [readOnly]="readOnly">
</app-complicanze-dati-clinici>
```

## Differenze Principali

### ComplicanzeComponent (Dimissione)
- **Form Structure**: Semplice con Si/No radio buttons
- **Arrays**: `*DimissioneLista` (es. `ossificazioneDimissioneLista`)
- **Validation**: Semplificata, no radio button Presenti/Intercorse
- **Service**: Solo `DatiDimissioneService`
- **Fields**: No spasticita, no *Selected fields

### ComplicanzeDatiCliniciComponent (Dati Clinici)
- **Form Structure**: Complessa con Presenti/Intercorse/Si/No
- **Arrays**: `*Lista` (es. `ossificazioneLista`)
- **Validation**: Complessa con validatori per radio button
- **Service**: Solo `DatiCliniciService`
- **Fields**: Include spasticita, tutti i *Selected fields

## Migrazione

### Per utilizzatori del componente esistente:

**Prima**:
```html
<app-complicanze 
  [isDimissione]="true"
  [repartoType]="repartoType"
  [readOnly]="readOnly">
</app-complicanze>
```

**Dopo**:
```html
<app-complicanze 
  [repartoType]="repartoType"
  [readOnly]="readOnly">
</app-complicanze>
```

**Per dati clinici**:
```html
<app-complicanze-dati-clinici 
  [repartoType]="repartoType"
  [readOnly]="readOnly">
</app-complicanze-dati-clinici>
```

## Vantaggi del Refactor

1. **Separazione delle responsabilità**: Ogni componente ha una responsabilità specifica
2. **Codice più pulito**: Rimossa tutta la logica condizionale
3. **Manutenibilità**: Più facile mantenere e debuggare
4. **Performance**: Componenti più leggeri senza logica inutilizzata
5. **Type Safety**: Migliore tipizzazione senza union types

## File Modificati

### Componente Originale (Modificato)
- `complicanze.component.ts` - Rimossa logica dati clinici
- `complicanze.component.html` - Semplificato per solo dimissione

### Nuovo Componente
- `complicanze-dati-clinici.component.ts` - Logica completa dati clinici
- `complicanze-dati-clinici.component.html` - Template complesso con Presenti/Intercorse
- `complicanze-dati-clinici.component.scss` - Stili

## Note Tecniche

- Entrambi i componenti mantengono la stessa API pubblica (inputs)
- I servizi rimangono invariati
- Le interfacce rimangono invariate
- La logica di validazione è stata adattata per ogni caso d'uso specifico
