import { CommonModule } from '@angular/common';
import { Component, HostListener, OnDestroy, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Subject, takeUntil } from 'rxjs';
import { ETipoScheda } from '../../enums/enum';
import { CronologiaMenuService, CronologiaMenuState } from '../../services/cronologia-menu.service';

@Component({
  selector: 'app-cronologia-menu',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule],
  templateUrl: './cronologia-menu.component.html',
  styleUrls: ['./cronologia-menu.component.scss']
})
export class CronologiaMenuComponent implements OnInit, OnDestroy {
  openMenus: { [key: string]: CronologiaMenuState } = {};
  private destroy$ = new Subject<void>();

  constructor(private cronologiaMenuService: CronologiaMenuService) {}

  ngOnInit(): void {
    this.cronologiaMenuService.cronologiaMenus$
      .pipe(takeUntil(this.destroy$))
      .subscribe(menus => {
        this.openMenus = menus;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Chiude un menu specifico
   */
  closeMenu(menuKey: string): void {
    // Estrai l'idTipoScheda dalla chiave del menu
    const idTipoScheda = this.extractIdTipoSchedaFromKey(menuKey);
    this.cronologiaMenuService.closeCronologiaMenu(idTipoScheda);
  }

  /**
   * Ottiene le chiavi dei menu aperti
   */
  getOpenMenuKeys(): string[] {
    return Object.keys(this.openMenus);
  }

  /**
   * Ottiene lo stato di un menu
   */
  getMenuState(menuKey: string): CronologiaMenuState {
    return this.openMenus[menuKey];
  }

  /**
   * Gestisce il click fuori dai menu per chiuderli
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const cronologiaButton = target.closest('.cronologia-button');
    const cronologiaMenu = target.closest('.cronologia-menu');
    
    // Chiudi tutti i menu se il click non è su un bottone o in un menu
    if (!cronologiaButton && !cronologiaMenu) {
      this.cronologiaMenuService.closeAllMenus();
    }
  }

  /**
   * Estrae l'idTipoScheda dalla chiave del menu
   */
  private extractIdTipoSchedaFromKey(menuKey: string): ETipoScheda | null {
    const parts = menuKey.split('_');
    if (parts.length >= 2) {
      const idPart = parts[1];
      if (idPart === 'dati' && parts[2] === 'generali') {
        return null; // Dati generali
      }
      const id = parseInt(idPart, 10);
      return isNaN(id) ? null : id as ETipoScheda;
    }
    return null;
  }
}
