// Import the styles from the original complicanze component
@import '../complicanze/complicanze.component.scss';

// Additional styles specific to dati-clinici component
.complicanze-dati-clinici {
  // Radio button groups styling
  mat-radio-group {
    &.d-flex {
      gap: 1rem;

      mat-radio-button {
        margin-right: 0;

        &.me-3 {
          margin-right: 1rem !important;
        }
      }
    }
  }

  // Error message styling for radio buttons
  .mat-error {
    font-size: 0.75rem;
    color: #f44336;
    margin-top: 0.25rem;
    display: block;
  }

  // Field label styling
  .field-label {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;

    span {
      color: #f44336;
    }
  }
}
