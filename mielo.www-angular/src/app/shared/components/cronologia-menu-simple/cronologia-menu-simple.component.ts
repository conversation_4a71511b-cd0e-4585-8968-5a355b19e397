import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {  of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ETipoScheda } from '../../enums/enum';
import { SchedaRicoveroService } from '../../../mielo/services/scheda-ricovero.service';
import {MatIcon} from "@angular/material/icon";
import {decodeNoteFields} from "../../utils/utils";

@Component({
  selector: 'app-cronologia-menu-simple',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatMenuModule, MatProgressSpinnerModule, MatIcon],
  templateUrl: './cronologia-menu-simple.component.html',
  styles: [`

        .history-button {
            color: #297A38 !important;
            fill: #297A38 !important;
            border: none !important;
            &:disabled {
              background: none !important;
              color: #A0A0A0 !important;
              fill: #A0A0A0 !important;
            }
        }
        
      .icon-history {
        fill: inherit !important;
        color: inherit !important;
      }

      :host ::ng-deep .cronologia-mat-menu .mat-mdc-menu-panel {
          min-width: 400px;
          max-width: 600px;
          max-height: 400px;
      }

      .cronologia-header {
          border-bottom: 1px solid #ddd;
          display: flex;
          color: black !important;
          padding: 10px;
          justify-content: space-between;
          align-items: center;
          height: 64px;

          h5 {
              color: black !important;
              font-size: 18px !important;
              margin: 0 !important;
          }
      }

      .cronologia-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          gap: 8px;

          span {
              font-size: 14px;
              color: #666;
          }
      }

      .cronologia-no-data {
          padding: 20px;
          text-align: center;
          color: #666;
          font-size: 14px;
      }

      .cronologia-items {
          max-height: 300px;
          overflow-y: auto;
          padding: 16px;
          gap: 8px;
          display: flex;
          flex-direction: column;
          background-color: #F5F6FA;

          &:hover {
              background-color: rgb(194, 194, 195);
              cursor: pointer;
          }

          .cronologia-item {
              &:last-child {
                  border-bottom: none;
              }

              .cronologia-item-header {
                  display: flex;
                  flex-direction: column;
                  font-size: 16px;
              }

              .cronologia-item-body {
                  font-size: 16px;
                  font-weight: 600;

                  div {
                      margin-bottom: 4px;

                      strong {
                          color: #555;
                          margin-right: 4px;
                      }
                  }
              }
          }
      }

      ::ng-deep .mat-mdc-menu-panel {
        max-width: 300px !important;
      }

      @media (max-width: 768px) {
          :host ::ng-deep .cronologia-mat-menu .mat-mdc-menu-panel {
              min-width: 300px;
              max-width: 90vw;
          }
      }
  `]
})
export class CronologiaMenuSimpleComponent implements OnInit {
  @Input() idTipoScheda: ETipoScheda | null = null;
  @Input() onInfoClick: (event: any) => void
  @ViewChild(MatMenuTrigger) menuTrigger!: MatMenuTrigger;
  @Output() cronologiaOpened : EventEmitter<boolean> = new EventEmitter(false);

  loading = false;
  data: any | null = null;
  idScheda?: number = undefined

  constructor(private schedaRicoveroService: SchedaRicoveroService) {
    this.idScheda = schedaRicoveroService.idSchedaSelectedSubject.getValue()
  }

  ngOnInit(): void {
    if (!this.idScheda) {
      console.warn('CronologiaMenuSimpleComponent: idScheda è richiesto');
    }
  }

  loadCronologia($event: MouseEvent): void {
    $event.stopPropagation();
    $event.preventDefault();
    if (!this.idScheda) return;

    this.loading = true;
    this.data = null;

    this.schedaRicoveroService.getCronologia({
      idScheda: this.idScheda,
      idTipoScheda: this.idTipoScheda
    }).pipe(
      tap(res => {
        this.cronologiaOpened.emit(true);
        this.data = decodeNoteFields(res)
        this.loading = false;
      }),
      catchError(err => {
        console.error('Errore nel caricamento cronologia:', err);
        this.schedaRicoveroService.cronologiaOpened$.next(false);
        this.cronologiaOpened.emit(false);
        this.loading = false;
        this.data = null;
        return of(null);
      })
    ).subscribe();
  }

  closeMenu(): void {
    this.cronologiaOpened.emit(false);
    this.menuTrigger.closeMenu();
  }
}
