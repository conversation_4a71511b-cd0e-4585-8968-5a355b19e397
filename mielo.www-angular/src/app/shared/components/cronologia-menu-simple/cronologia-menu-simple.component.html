<div class="d-flex">
    <button mat-stroked-button class="history-button p-0" [matMenuTriggerFor]="cronologiaMenu"
        (click)="loadCronologia($event)" >
        <svg class="icon icon-history">
            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-clock"></use>
        </svg>
        Cronologia
    </button>

    <mat-menu id="cronologia-menu" xPosition="before" #cronologiaMenu="matMenu">
        <div (click)="$event.stopPropagation()">
            <!-- Header -->
            <div class="cronologia-header">
                <mat-icon class="d-flex icon-color">access_time</mat-icon>
                <h5>CRONOLOGIA MODIFICHE</h5>
                <button mat-icon-button (click)="closeMenu()">
                    <mat-icon>close</mat-icon>
                </button>
            </div>

            <!-- Loading -->
            <div *ngIf="loading" class="cronologia-loading">
                <mat-spinner diameter="20"></mat-spinner>
                <span>Caricamento...</span>
            </div>

            <!-- No data -->
            <div *ngIf="!loading && !data" class="cronologia-no-data">
                Nessun dato disponibile
            </div>

            <div (click)="onInfoClick(data);closeMenu()" *ngIf="!loading && data" class="cronologia-items">
                <div class="cronologia-item">
                    <span class="cronologia-item-header">Compilato da:</span>
                    <strong class="cronologia-item-body">{{ data.medicoCompilatore }}</strong>
                </div>
                <div class="cronologia-item">
                    <span class="cronologia-item-header">Struttura:</span>
                    <strong class="cronologia-item-body">{{ data.presidioOspedaliero }}</strong>
                </div>
                <div class="cronologia-item">
                    <span class="cronologia-item-header">Data e ora:</span>
                    <strong class="cronologia-item-body">{{ data.dataUltimaModifica | date:'dd/MM/yyyy - HH:mm'
                        }}</strong>
                </div>
            </div>
            <div style="height: 64px; border-top: 1px solid #ddd"></div>
        </div>
    </mat-menu>
</div>