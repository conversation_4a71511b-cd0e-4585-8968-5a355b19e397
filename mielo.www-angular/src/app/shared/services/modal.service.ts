import { Injectable } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef } from "@angular/material/dialog";
import { of, Subject } from 'rxjs';
import { DialogFirmaParam, DialogNormativaParam, DialogNoteParam, DialogSelectionVertebraParam } from '../interfaces/modal-param.interface';
import { DialogNoteComponent } from '../modals/dialog-note/dialog-note.component';

@Injectable({
  providedIn: 'root'
})
export class ModalService {

  noteDialogRef: MatDialogRef<unknown, any> | null;
  normativaDialogRef: MatDialogRef<any>;
  firmaDialogRef: MatDialogRef<any>;
  vertebraDialogRef: MatDialogRef<any>;

  /**Utilizzato dal dialog firma per gestire la richiesta di anteprima del BSO compilato */
  showPreviewBso = new Subject<any>();

  constructor(private dialog: MatDialog) { }

  note(note: DialogNoteParam) {
    if (note) {
      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
      overlay.style.zIndex = '1000';
      document.body.appendChild(overlay);

      const dialogContainer = document.createElement('div');
      dialogContainer.style.position = 'fixed';
      dialogContainer.style.top = '50%';
      dialogContainer.style.left = '50%';
      dialogContainer.style.transform = 'translate(-50%, -50%)';
      dialogContainer.style.width = '800px';
      dialogContainer.style.height = '500px';
      dialogContainer.style.maxWidth = '90%';
      dialogContainer.style.maxHeight = '90%';
      dialogContainer.style.display = 'flex';
      dialogContainer.style.flexDirection = 'column';
      dialogContainer.style.justifyContent = 'space-between';
      dialogContainer.style.backgroundColor = '#F5F6FA';
      dialogContainer.style.borderRadius = '12px';
      dialogContainer.style.padding = '32px';
      dialogContainer.style.boxShadow = '0 4px 24px rgba(0,0,0,0.12)';
      dialogContainer.style.zIndex = '1001';
      overlay.appendChild(dialogContainer);

      const title = document.createElement('h2');
      title.style.margin = '0 0 24px 0';
      title.style.color = '#003354';
      title.style.fontSize = '28px';
      title.style.fontWeight = '700';
      title.innerText = note.textTitle || 'Note';
      dialogContainer.appendChild(title);

      const textArea = document.createElement('textarea');
      textArea.style.width = '100%';
      textArea.style.border = '1px solid #BACCD9';
      textArea.style.minHeight = '220px';
      textArea.style.borderRadius = '8px';
      textArea.style.padding = '12px';
      textArea.style.marginBottom = '32px';
      textArea.style.resize = 'vertical';
      textArea.style.fontSize = '16px';
      textArea.style.fontFamily = '"Titillium Web", sans-serif';
      textArea.style.color = '#333';
      textArea.placeholder = 'Inserisci';
      textArea.maxLength = 3000;
      textArea.value = note.message || '';

      // Imposta il campo come sola lettura se disabled è true
      if (note.disabled) {
        textArea.readOnly = true;
        textArea.style.backgroundColor = '#f5f5f5';
        textArea.style.cursor = 'not-allowed';
      }

      dialogContainer.appendChild(textArea);

      const buttonContainer = document.createElement('div');
      buttonContainer.style.display = 'flex';
      buttonContainer.style.justifyContent = 'flex-end';
      buttonContainer.style.gap = '16px';
      dialogContainer.appendChild(buttonContainer);

      const subject = new Subject<string>();

      // Se siamo in modalità sola lettura, mostra solo il tasto chiudi
      if (note.disabled) {
        const closeButton = document.createElement('button');
        closeButton.style.padding = '12px 32px';
        closeButton.style.borderRadius = '4px';
        closeButton.style.border = 'none';
        closeButton.style.backgroundColor = '#297A38';
        closeButton.style.color = 'white';
        closeButton.style.cursor = 'pointer';
        closeButton.style.fontSize = '16px';
        closeButton.style.fontWeight = 'bold';
        closeButton.innerText = 'Chiudi';
        buttonContainer.appendChild(closeButton);

        closeButton.onclick = () => {
          document.body.removeChild(overlay);
          subject.next(note.message || '');
          subject.complete();
        };
      } else {
        const cancelButton = document.createElement('button');
        cancelButton.style.padding = '12px 32px';
        cancelButton.style.borderRadius = '4px';
        cancelButton.style.border = '1px solid #297A38';
        cancelButton.style.backgroundColor = 'white';
        cancelButton.style.color = '#297A38';
        cancelButton.style.cursor = 'pointer';
        cancelButton.style.fontSize = '16px';
        cancelButton.style.fontWeight = 'bold';
        cancelButton.innerText = 'Annulla';
        buttonContainer.appendChild(cancelButton);

        const saveButton = document.createElement('button');
        saveButton.style.padding = '12px 32px';
        saveButton.style.borderRadius = '4px';
        saveButton.style.border = 'none';
        saveButton.style.backgroundColor = '#297A38';
        saveButton.style.color = 'white';
        saveButton.style.cursor = 'pointer';
        saveButton.style.fontSize = '16px';
        saveButton.style.fontWeight = 'bold';
        saveButton.innerText = note.textButtonConfirm || 'Salva';
        buttonContainer.appendChild(saveButton);

        cancelButton.onclick = () => {
          document.body.removeChild(overlay);
          subject.next(note.message || '');
          subject.complete();
        };

        saveButton.onclick = () => {
          // Verifica se il testo inserito supera i 3000 bytes
          const textValue = textArea.value.trim();
          const byteSize = new TextEncoder().encode(textValue).length;

          if (byteSize > 3000) {
            // Crea e mostra un popup di avviso
            const warningOverlay = document.createElement('div');
            warningOverlay.style.position = 'fixed';
            warningOverlay.style.top = '0';
            warningOverlay.style.left = '0';
            warningOverlay.style.width = '100%';
            warningOverlay.style.height = '100%';
            warningOverlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
            warningOverlay.style.zIndex = '2000';
            document.body.appendChild(warningOverlay);

            const warningContainer = document.createElement('div');
            warningContainer.style.position = 'fixed';
            warningContainer.style.top = '50%';
            warningContainer.style.left = '50%';
            warningContainer.style.transform = 'translate(-50%, -50%)';
            warningContainer.style.width = '400px';
            warningContainer.style.maxWidth = '90%';
            warningContainer.style.backgroundColor = 'white';
            warningContainer.style.borderRadius = '12px';
            warningContainer.style.padding = '32px';
            warningContainer.style.boxShadow = '0 4px 24px rgba(0,0,0,0.12)';
            warningContainer.style.zIndex = '2001';
            warningContainer.style.display = 'flex';
            warningContainer.style.flexDirection = 'column';
            warningContainer.style.alignItems = 'center';
            warningContainer.style.textAlign = 'center';
            warningOverlay.appendChild(warningContainer);

            // Icona di avviso
            const warningIcon = document.createElement('div');
            warningIcon.innerHTML = `
      <svg class="icon" style="width: 64px; height: 64px; fill: #FFA726;">
        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-warning-circle"></use>
      </svg>
    `;
            warningContainer.appendChild(warningIcon);

            // Titolo
            const warningTitle = document.createElement('h2');
            warningTitle.style.margin = '16px 0';
            warningTitle.style.color = '#003354';
            warningTitle.style.fontSize = '24px';
            warningTitle.style.fontWeight = '700';
            warningTitle.innerText = 'Attenzione';
            warningContainer.appendChild(warningTitle);

            // Messaggio
            const warningMessage = document.createElement('p');
            warningMessage.style.margin = '0 0 24px 0';
            warningMessage.style.color = '#1B496D';
            warningMessage.style.fontSize = '16px';
            warningMessage.style.lineHeight = '1.5';
            warningMessage.style.fontWeight = '600';
            warningMessage.style.whiteSpace = 'pre-line';
            warningMessage.innerText = 'Superato il limite massimo di caratteri consentiti. Ridurre la lunghezza del testo.';
            warningContainer.appendChild(warningMessage);

            // Pulsante OK
            const okButton = document.createElement('button');
            okButton.style.padding = '12px 32px';
            okButton.style.borderRadius = '4px';
            okButton.style.border = '2px solid #2A7A38';
            okButton.style.backgroundColor = 'white';
            okButton.style.color = '#297A38';
            okButton.style.cursor = 'pointer';
            okButton.style.fontSize = '16px';
            okButton.style.fontWeight = 'bold';
            okButton.style.width = '100%';
            okButton.style.lineHeight = '1';
            okButton.innerText = 'OK';
            warningContainer.appendChild(okButton);

            okButton.onclick = () => {
              document.body.removeChild(warningOverlay);
            };

            return;
          }

          document.body.removeChild(overlay);
          subject.next(textArea.value);
          subject.complete();
        };
      }

      const escHandler = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          document.body.removeChild(overlay);
          subject.next(note.message || '');
          subject.complete();
          document.removeEventListener('keydown', escHandler);
        }
      };
      document.addEventListener('keydown', escHandler);

      return subject.asObservable();
    }
    return of('');
  }

  selectionVertebra(param: DialogSelectionVertebraParam) {
    if (param) {
      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
      overlay.style.zIndex = '1000';
      document.body.appendChild(overlay);

      const dialogContainer = document.createElement('div');
      dialogContainer.style.position = 'fixed';
      dialogContainer.style.top = '50%';
      dialogContainer.style.left = '50%';
      dialogContainer.style.transform = 'translate(-50%, -50%)';
      dialogContainer.style.width = '650px';
      dialogContainer.style.maxWidth = '90%';
      dialogContainer.style.backgroundColor = '#F5F6FA';
      dialogContainer.style.borderRadius = '12px';
      dialogContainer.style.padding = '32px';
      dialogContainer.style.boxShadow = '0 4px 24px rgba(0,0,0,0.12)';
      dialogContainer.style.zIndex = '1001';
      overlay.appendChild(dialogContainer);

      const title = document.createElement('h2');
      title.style.margin = '0 0 24px 0';
      title.style.color = '#003354';
      title.style.fontSize = '24px';
      title.style.fontWeight = '700';
      title.innerText = param.textTitle || 'Lesione vertebra/e*';
      dialogContainer.appendChild(title);

      // Funzione per creare una sezione di vertebre
      const createVertebraSection = (title: string, vertebre: string[]) => {
        const sectionContainer = document.createElement('div');
        sectionContainer.style.marginBottom = '24px';

        const sectionTitle = document.createElement('h3');
        sectionTitle.style.cssText = `
          font-size: 17px !important;
          font-weight: 600 !important;
          font-style: italic !important;
          color:rgb(31, 58, 86) !important;
          margin-bottom: 16px !important;
        `;
        sectionTitle.innerText = title;
        sectionContainer.appendChild(sectionTitle);

        const checkboxContainer = document.createElement('div');
        checkboxContainer.style.display = 'grid';
        checkboxContainer.style.gridTemplateColumns = 'repeat(3, 1fr)';
        checkboxContainer.style.gap = '12px';
        sectionContainer.appendChild(checkboxContainer);

        // Mappa delle vertebre selezionate
        const selectedMap = new Map<string, boolean>();
        if (param.selectedVertebrae) {
          param.selectedVertebrae.forEach(v => selectedMap.set(v, true));
        }

        vertebre.forEach(vertebra => {
          const checkboxWrapper = document.createElement('div');
          checkboxWrapper.style.display = 'flex';
          checkboxWrapper.style.alignItems = 'center';

          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.id = `vertebra-${vertebra}`;
          checkbox.value = vertebra;
          checkbox.checked = selectedMap.has(vertebra);
          checkbox.style.marginRight = '8px';
          checkbox.style.accentColor = '#2a7a38';
          checkbox.style.width = '16px';
          checkbox.style.height = '16px';
          checkbox.style.cursor = 'pointer'

          const label = document.createElement('label');
          label.htmlFor = `vertebra-${vertebra}`;
          label.innerText = vertebra;
          label.style.fontSize = '16px';
          label.style.marginBottom = '0';
          label.style.fontWeight = '400';
          label.style.color = '#17324D';

          checkboxWrapper.appendChild(checkbox);
          checkboxWrapper.appendChild(label);
          checkboxContainer.appendChild(checkboxWrapper);
        });

        return sectionContainer;
      };

      // Creare le quattro sezioni di vertebre
      const vertebreContainer = document.createElement('div');
      vertebreContainer.style.maxHeight = '400px';
      vertebreContainer.style.overflowY = 'auto';
      vertebreContainer.style.padding = '0 8px';
      dialogContainer.appendChild(vertebreContainer);

      // Sezione Cervicali
      const cervicali = ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7'];
      const sezioneCervicali = createVertebraSection('Cervicali', cervicali);
      vertebreContainer.appendChild(sezioneCervicali);

      // Sezione Toraciche
      const toraciche = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'];
      const sezioneToraciche = createVertebraSection('Toraciche', toraciche);
      vertebreContainer.appendChild(sezioneToraciche);

      // Sezione Lombari
      const lombari = ['L1', 'L2', 'L3', 'L4', 'L5'];
      const sezioneLombari = createVertebraSection('Lombari', lombari);
      vertebreContainer.appendChild(sezioneLombari);

      // Sezione Sacrali
      const sacrali = ['S1', 'S2', 'S3', 'S4', 'S5'];
      const sezioneSacrali = createVertebraSection('Sacrali', sacrali);
      vertebreContainer.appendChild(sezioneSacrali);

      // Pulsanti di azione
      const buttonContainer = document.createElement('div');
      buttonContainer.style.display = 'flex';
      buttonContainer.style.justifyContent = 'flex-end';
      buttonContainer.style.gap = '16px';
      buttonContainer.style.marginTop = '24px';
      dialogContainer.appendChild(buttonContainer);

      const subject = new Subject<string[]>();

      const cancelButton = document.createElement('button');
      cancelButton.style.padding = '12px 32px';
      cancelButton.style.borderRadius = '4px';
      cancelButton.style.border = '1px solid #297A38';
      cancelButton.style.backgroundColor = 'white';
      cancelButton.style.color = '#297A38';
      cancelButton.style.cursor = 'pointer';
      cancelButton.style.fontSize = '16px';
      cancelButton.style.fontWeight = 'bold';
      cancelButton.innerText = param.textButtonCancel || 'Annulla';
      buttonContainer.appendChild(cancelButton);

      const saveButton = document.createElement('button');
      saveButton.style.padding = '12px 32px';
      saveButton.style.borderRadius = '4px';
      saveButton.style.border = 'none';
      saveButton.style.backgroundColor = '#297A38';
      saveButton.style.color = 'white';
      saveButton.style.cursor = 'pointer';
      saveButton.style.fontSize = '16px';
      saveButton.style.fontWeight = 'bold';
      saveButton.innerText = param.textButtonConfirm || 'Salva';
      buttonContainer.appendChild(saveButton);

      cancelButton.onclick = () => {
        document.body.removeChild(overlay);
        subject.next(param.selectedVertebrae || []);
        subject.complete();
      };

      saveButton.onclick = () => {
        const selectedVertebrae: string[] = [];
        const checkboxes = vertebreContainer.querySelectorAll('input[type="checkbox"]:checked');
        checkboxes.forEach(checkbox => {
          selectedVertebrae.push((checkbox as HTMLInputElement).value);
        });

        document.body.removeChild(overlay);
        subject.next(selectedVertebrae);
        subject.complete();
      };

      const escHandler = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          document.body.removeChild(overlay);
          subject.next(param.selectedVertebrae || []);
          subject.complete();
          document.removeEventListener('keydown', escHandler);
        }
      };
      document.addEventListener('keydown', escHandler);

      return subject.asObservable();
    }
    return of([]);
  }

  // normativa(normativa: DialogNormativaParam) {
  //     if (normativa) {
  //         this.normativaDialogRef = this.showModal('NORMATIVA', { panelClass: 'normativa-modal-box', data: normativa });
  //     }
  //     return this.normativaDialogRef.afterClosed();
  // }
  //
  // firma(firma: DialogFirmaParam) {
  //     if (firma) {
  //         this.firmaDialogRef = this.showModal('FIRMA', { panelClass: 'firma-modal-box', data: firma });
  //     }
  //     return this.firmaDialogRef.afterClosed();
  // }

  showModal(type: string, config?: any) {
    let dialogRef;
    let component;

    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.setAttribute('inert', '');
    }

    switch (type) {
      case 'NOTE': { //per scrivere note lunghe o memo
        dialogRef = this.noteDialogRef;
        component = DialogNoteComponent;
        break;
      }
        // case 'NORMATIVA': {
        //     dialogRef = this.normativaDialogRef;
        //     component = DialogNormativaComponent;
        //     break;
        // }
        // case 'FIRMA': {
        //     dialogRef = this.firmaDialogRef;
        //     component = DialogFirmaComponent;
        //     break;
        // }
    }

    if (component) {
      if (dialogRef) {
        dialogRef.close();
      }

      const dialogConfig: MatDialogConfig = {
        ...config,
        ariaLabel: config?.ariaLabel || 'Dialog',
        role: 'dialog',
        panelClass: ['accessible-dialog'],
        container: document.getElementById('modal-container') || undefined
      };

      const dialog = this.dialog.open(component, dialogConfig);

      dialog.afterClosed().subscribe(() => {
        if (mainContent) {
          mainContent.removeAttribute('inert');
        }
      });

      return dialog;
    }
    return null;
  }

  /**Utility per creare i parametri da passare al dialog note */
  createNoteParam(message = "", disabled = false): DialogNoteParam {
    return {
      message: message,
      textTitle: 'Note',
      textButtonConfirm: 'Salva',
      disabled: disabled
    }
  }

  /**Utility per creare i parametri da passare al dialog selezione vertebre */
  createSelectionVertebraParam(selectedVertebrae: string[] = []): DialogSelectionVertebraParam {
    return {
      selectedVertebrae: selectedVertebrae,
      textTitle: 'Lesione vertebra/e*',
      textButtonConfirm: 'Salva',
      textButtonCancel: 'Annulla'
    }
  }

  /**Utility per creare i parametri da passare al dialog normativa */
  createNormativaParam(): DialogNormativaParam {
    return {
      textTitle: 'Dati soggetti a maggior tutela dell\'anonimato',
      textButtonCancel: 'Torna alla checklist',
      textButtonConfirm: 'Procedi',
    }
  }

  /**Utility per creare i parametri da passare al dialog firma */
  createFirmaParam(): DialogFirmaParam {
    return {
      textTitle: 'Firma Bilancio di Salute Ostetrico',
      textButtonCancel: 'Torna alla checklist',
      textButtonConfirm: 'Firma e Archivia',
      disableButtonConfirm: false,
    }
  }
}
