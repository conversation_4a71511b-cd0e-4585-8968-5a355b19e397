import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class FileHandlerService {
  elaborateBodyBlobAsFile(data: HttpResponse<any>) {
    const contentDisposition = data.headers.get('content-disposition');
    const contentType = data.headers.get('content-type');
    const blob = new Blob([data.body], { type: contentType || 'text/csv' });

    let filename = 'report.csv';
    if (contentDisposition && contentDisposition.includes('filename=')) {
      filename = contentDisposition.split('filename=')[1].replace(/['"]/g, '').trim();
    }

    const downloadUrl = URL.createObjectURL(blob);
    return { downloadUrl, filename };
  }

  downloadBodyBlobAsFile(downloadUrl: string, filename: string) {
    if (!downloadUrl || !filename) return;
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    a.remove();
    URL.revokeObjectURL(downloadUrl);
  }
} 