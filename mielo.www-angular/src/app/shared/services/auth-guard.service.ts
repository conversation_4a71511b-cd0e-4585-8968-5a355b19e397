import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { OperatoreService } from "../../core/services/operatore.service";
import { ErrorService } from "../components/dialog-error/error.service";
import { IdOperatoreEnum } from '../enums/enum';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(
    private operatoreService: OperatoreService,
    private router: Router,
    private errorService: ErrorService
  ) { }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.operatoreService.getInfoOperatore().pipe(
      map(operatore => {
        if (!operatore) {
          this.errorService.showError('Operatore non abilitato');
          this.router.navigate(['/']);
          return false;
        }

        const cannotAccessTavoloTecnico = function (): boolean {
          return operatore.idRuolo === IdOperatoreEnum.COM &&
            (state.url !== '/estrazioni' &&
              !state.url.includes('/eventi-associati') &&
              state.url !== '/' &&
              !state.url.startsWith('/scheda-ricovero/details'));
        }

        const cannotAccessPS = function (): boolean {
          return operatore.idRuolo === IdOperatoreEnum.PS &&
            (state.url.includes('/eventi-associati') ||
              state.url.includes('estrazioni') ||
              (state.url.includes('/scheda-ricovero') &&
                (state.url.includes('/details') || state.url.includes('/nuovo-evento'))));
        }

        // BLOCCO SPECIFICO PER /estrazioni
        if (cannotAccessTavoloTecnico() || cannotAccessPS()) {
          this.errorService.showError('Accesso non consentito, ruolo non abilitato a questa funzionalità');
          this.router.navigate(['/']);
          return false;
        }

        return true;
      })
    );
  }
}
