import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { ModaleConfermaComponent } from '../modals/modale-conferma/modale-conferma.component';
import {OutputCreazioneScheda} from "../interfaces/scheda-ricovero.interface";

export interface DialogConfig {
    tipo: 'warning' | 'success' | 'error';
    titolo: string;
    messaggio?: string;
    bottoni: Array<{
        testo: string;
        azione: string;
    }>;
    width?: string;
    panelClass?: string;
}

export interface SchedaResponse {
    idPaziente: string;
    nomePaziente: string;
    cognomePaziente: string;
}

@Injectable({
    providedIn: 'root'
})
export class DialogService {
    constructor(private dialog: MatDialog) { }

    private openDialog(config: DialogConfig): Observable<string> {
        const dialogRef = this.dialog.open(ModaleConfermaComponent, {
            data: {
                tipo: config.tipo,
                titolo: config.titolo,
                messaggio: config.messaggio,
                bottoni: config.bottoni
            },
            width: config.width || 'auto',
            panelClass: config.panelClass,
            disableClose: true
        });

        return new Observable<string>(observer => {
            const subscription = dialogRef.afterClosed().subscribe(result => {
                observer.next(result);
                observer.complete();
            });

            return () => {
                subscription.unsubscribe();
            };
        });
    }

    openConfirmDialog(config: DialogConfig): Observable<string> {
        return this.openDialog(config);
    }

    confermaUscita(): Observable<string> {
        return this.openDialog({
            tipo: 'warning',
            titolo: 'Attenzione',
            messaggio: 'Sicuro di voler uscire?\nI dati non salvati andranno persi.',
            bottoni: [
                { testo: 'Continua le modifiche', azione: 'annulla' },
                { testo: 'Esci dalla scheda', azione: 'conferma' }
            ]
        });
    }

    confermaTornaAllaScheda(): Observable<string> {
        return this.openDialog({
            tipo: 'warning',
            titolo: 'Attenzione',
            messaggio: 'Sicuro di voler uscire?\nI dati non salvati andranno persi.',
            bottoni: [
                { testo: 'Continua le modifiche', azione: 'annulla' },
                { testo: 'Torna alla scheda', azione: 'conferma' }
            ],
            width: '400px',
            panelClass: 'no-padding-dialog'
        });
    }

    schedaSalvataConSuccesso(response: OutputCreazioneScheda) {
        return this.openDialog({
            tipo: 'success',
            titolo: 'Scheda creata',
            messaggio: `La scheda con ID PAZIENTE N° ${response.idPaziente}\nper ${response.nomePaziente} ${response.cognomePaziente}\nè stata creata con successo.`,
            bottoni: [
                {testo: 'Torna a Ricerca Scheda Paziente', azione: 'conferma'}
            ]
        });
    }

    datiPazienteAggiornati(): Observable<string> {
        return this.openDialog({
            tipo: 'success',
            titolo: 'Dati paziente aggiornati',
            bottoni: [
                { testo: 'Continua le modifiche', azione: 'annulla' },
                { testo: 'Torna alla scheda', azione: 'conferma' }
            ],
            width: '400px',
            panelClass: 'no-padding-dialog'
        });
    }

    datiRicoveroAggiornati(): Observable<string> {
        return this.openDialog({
            tipo: 'success',
            titolo: 'Scheda aggiornata',
            bottoni: [
                { testo: 'Continua le modifiche', azione: 'annulla' },
                { testo: 'Torna a Ricerca scheda paziente', azione: 'conferma' }
            ],
            width: '400px',
            panelClass: 'no-padding-dialog'
        });
    }

    annullaSchedaPaziente(): Observable<string> {
        return this.openDialog({
            tipo: 'success',
            titolo: 'Scheda annullata',
            messaggio: "La scheda Paziente è stata annullata: non sarà più visibile all'interno del Registro",
            bottoni: [
                { testo: 'Torna a Ricerca Scheda Paziente', azione: 'conferma' }
            ],
            width: '400px',
            panelClass: 'no-padding-dialog'
        });
    }
}
