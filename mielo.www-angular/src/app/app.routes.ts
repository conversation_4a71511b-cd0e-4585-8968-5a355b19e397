import {Routes} from '@angular/router';
import {AuthGuard} from "./shared/services/auth-guard.service";

export const routes: Routes = [
  //{ path: '', redirectTo: 'home', pathMatch: 'full' },
  {
    path: '',
    loadComponent: () => import('./mielo/pages/ricerca-scheda-paziente/ricerca-scheda-paziente.component').then(m => m.RicercaSchedaPazienteComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'scheda-ricovero/details/:componentFrom',
    loadComponent: () => import('./mielo/pages/scheda-ricovero/scheda-ricovero.component').then(m => m.SchedaRicoveroComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'scheda-ricovero/nuovo-evento',
    loadComponent: () => import('./mielo/pages/scheda-ricovero/scheda-ricovero.component').then(m => m.SchedaRicoveroComponent),
    canActivate: [AuthGuard],
    children: [
      {
        path: ':componentFrom',
        loadComponent: () => import('./mielo/pages/scheda-ricovero/scheda-ricovero.component').then(m => m.SchedaRicoveroComponent),
      }
    ]
  },
  {
    path: 'scheda-ricovero/paziente/details',
    loadComponent: () => import('./mielo/components/dati/dati-paziente/dati-paziente.component').then(m => m.DatiPazienteComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'scheda-ricovero',
    loadComponent: () => import('./mielo/pages/scheda-ricovero/scheda-ricovero.component').then(m => m.SchedaRicoveroComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'eventi-associati/:idScheda',
    loadComponent: () => import('./mielo/pages/eventi-associati/eventi-associati.component').then(m => m.EventiAssociatiComponent),
    children: [
      {
        path: 'new',
        loadComponent: () => import('./mielo/pages/eventi-associati/new-evento/new-evento.component').then(m => m.NewEventoComponent)
      }
    ],
    canActivate: [AuthGuard]
  },
  {
    path: 'estrazioni',
    loadComponent: () => import('./mielo/pages/estrazioni/estrazioni.component').then(m => m.EstrazioniComponent),
    canActivate: [AuthGuard]
  },
  {path: '**', redirectTo: ''}
];
