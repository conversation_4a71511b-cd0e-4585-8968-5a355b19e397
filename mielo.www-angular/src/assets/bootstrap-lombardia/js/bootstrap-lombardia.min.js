/*!
 * 'Bootstrap Lombardia è una customizzazione di Boostrap Italia per la creazione di applicazioni web per la Regione Lombardia nel pieno rispetto delle Linee guida di design per i servizi web della PA
 * @version v0.3.5
 * @link https://regionelombardia.github.io/bootstrap-lombardia/
 * @license BSD-3-Clause
 */

if (typeof jQuery === 'undefined') {
  throw new Error('Bootstrap\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\'s JavaScript.')
}

+function ($) {
  var version = $.fn.jquery.split(' ')[0].split('.')
  if ((version[0] < 2 && version[1] < 9) || (version[0] == 1 && version[1] == 9 && version[2] < 1) || (version[0] >= 4)) {
    throw new Error('Bootstrap\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')
  }
}(jQuery);

function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass2(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}function showCapsLockMsg(t){$(".password-caps").remove(),t.parents(".form-group").append('<small class="password-caps form-text text-warning position-absolute bg-white w-100">CAPS LOCK inserito</small>')}function isIe(){return 0<window.navigator.userAgent.indexOf("MSIE ")||!!navigator.userAgent.match(/Trident.*rv\:11\./)}!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e((t=t||self).bootstrap={},t.jQuery,t.Popper)}(this,function(t,f,d){"use strict";function n(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function a(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}function e(e,t){var i,n=Object.keys(e);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(e),t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)),n}function l(s){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?e(Object(o),!0).forEach(function(t){var e,i,n;e=s,n=o[i=t],i in e?Object.defineProperty(e,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach(function(t){Object.defineProperty(s,t,Object.getOwnPropertyDescriptor(o,t))})}return s}f=f&&Object.prototype.hasOwnProperty.call(f,"default")?f.default:f,d=d&&Object.prototype.hasOwnProperty.call(d,"default")?d.default:d;var i="transitionend";function s(t){var e=this,i=!1;return f(this).one(m.TRANSITION_END,function(){i=!0}),setTimeout(function(){i||m.triggerTransitionEnd(e)},t),this}var m={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e,i=t.getAttribute("data-target");i&&"#"!==i||(i=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(i)?i:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var e=f(t).css("transition-duration"),i=f(t).css("transition-delay"),n=parseFloat(e),s=parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(parseFloat(e)+parseFloat(i))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){f(t).trigger(i)},supportsTransitionEnd:function(){return Boolean(i)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,i){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var s=i[n],o=e[n],a=o&&m.isElement(o)?"element":null==(r=o)?""+r:{}.toString.call(r).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(a))throw new Error(t.toUpperCase()+': Option "'+n+'" provided type "'+a+'" but expected type "'+s+'".')}var r},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?m.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===f)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=f.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};m.jQueryDetection(),f.fn.emulateTransitionEnd=s,f.event.special[m.TRANSITION_END]={bindType:i,delegateType:i,handle:function(t){if(f(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var o="alert",r="bs.alert",c=f.fn[o],h=function(){function n(t){this._element=t}var t=n.prototype;return t.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},t.dispose=function(){f.removeData(this._element,r),this._element=null},t._getRootElement=function(t){var e=m.getSelectorFromElement(t),i=!1;return e&&(i=document.querySelector(e)),i=i||f(t).closest(".alert")[0]},t._triggerCloseEvent=function(t){var e=f.Event("close.bs.alert");return f(t).trigger(e),e},t._removeElement=function(e){var t,i=this;f(e).removeClass("show"),f(e).hasClass("fade")?(t=m.getTransitionDurationFromElement(e),f(e).one(m.TRANSITION_END,function(t){return i._destroyElement(e,t)}).emulateTransitionEnd(t)):this._destroyElement(e)},t._destroyElement=function(t){f(t).detach().trigger("closed.bs.alert").remove()},n._jQueryInterface=function(i){return this.each(function(){var t=f(this),e=t.data(r);e||(e=new n(this),t.data(r,e)),"close"===i&&e[i](this)})},n._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},a(n,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),n}();f(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',h._handleDismiss(new h)),f.fn[o]=h._jQueryInterface,f.fn[o].Constructor=h,f.fn[o].noConflict=function(){return f.fn[o]=c,h._jQueryInterface};var u="button",p="bs.button",g=f.fn[u],v="active",b='[data-toggle^="button"]',y='input:not([type="hidden"])',_=function(){function i(t){this._element=t}var t=i.prototype;return t.toggle=function(){var t,e,i=!0,n=!0,s=f(this._element).closest('[data-toggle="buttons"]')[0];!s||(t=this._element.querySelector(y))&&("radio"===t.type&&(t.checked&&this._element.classList.contains(v)?i=!1:(e=s.querySelector(".active"))&&f(e).removeClass(v)),i&&("checkbox"!==t.type&&"radio"!==t.type||(t.checked=!this._element.classList.contains(v)),f(t).trigger("change")),t.focus(),n=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(v)),i&&f(this._element).toggleClass(v))},t.dispose=function(){f.removeData(this._element,p),this._element=null},i._jQueryInterface=function(e){return this.each(function(){var t=f(this).data(p);t||(t=new i(this),f(this).data(p,t)),"toggle"===e&&t[e]()})},a(i,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),i}();f(document).on("click.bs.button.data-api",b,function(t){var e=t.target,i=e;if(f(e).hasClass("btn")||(e=f(e).closest(".btn")[0]),!e||e.hasAttribute("disabled")||e.classList.contains("disabled"))t.preventDefault();else{var n=e.querySelector(y);if(n&&(n.hasAttribute("disabled")||n.classList.contains("disabled")))return void t.preventDefault();"LABEL"===i.tagName&&n&&"checkbox"===n.type&&t.preventDefault(),_._jQueryInterface.call(f(e),"toggle")}}).on("focus.bs.button.data-api blur.bs.button.data-api",b,function(t){var e=f(t.target).closest(".btn")[0];f(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),f(window).on("load.bs.button.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,i=t.length;e<i;e++){var n=t[e],s=n.querySelector(y);s.checked||s.hasAttribute("checked")?n.classList.add(v):n.classList.remove(v)}for(var o=0,a=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<a;o++){var r=t[o];"true"===r.getAttribute("aria-pressed")?r.classList.add(v):r.classList.remove(v)}}),f.fn[u]=_._jQueryInterface,f.fn[u].Constructor=_,f.fn[u].noConflict=function(){return f.fn[u]=g,_._jQueryInterface};var w="carousel",C="bs.carousel",k="."+C,$=f.fn[w],x={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},E={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},T="next",D="prev",I="slid"+k,A="active",L=".active.carousel-item",S={TOUCH:"touch",PEN:"pen"},O=function(){function o(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var t=o.prototype;return t.next=function(){this._isSliding||this._slide(T)},t.nextWhenVisible=function(){!document.hidden&&f(this._element).is(":visible")&&"hidden"!==f(this._element).css("visibility")&&this.next()},t.prev=function(){this._isSliding||this._slide(D)},t.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(m.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(t){var e=this;this._activeElement=this._element.querySelector(L);var i=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)f(this._element).one(I,function(){return e.to(t)});else{if(i===t)return this.pause(),void this.cycle();var n=i<t?T:D;this._slide(n,this._items[t])}},t.dispose=function(){f(this._element).off(k),f.removeData(this._element,C),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(t){return t=l(l({},x),t),m.typeCheckConfig(w,t,E),t},t._handleSwipe=function(){var t,e=Math.abs(this.touchDeltaX);e<=40||(t=e/this.touchDeltaX,(this.touchDeltaX=0)<t&&this.prev(),t<0&&this.next())},t._addEventListeners=function(){var e=this;this._config.keyboard&&f(this._element).on("keydown.bs.carousel",function(t){return e._keydown(t)}),"hover"===this._config.pause&&f(this._element).on("mouseenter.bs.carousel",function(t){return e.pause(t)}).on("mouseleave.bs.carousel",function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var t,e,i=this;this._touchSupported&&(t=function(t){i._pointerEvent&&S[t.originalEvent.pointerType.toUpperCase()]?i.touchStartX=t.originalEvent.clientX:i._pointerEvent||(i.touchStartX=t.originalEvent.touches[0].clientX)},e=function(t){i._pointerEvent&&S[t.originalEvent.pointerType.toUpperCase()]&&(i.touchDeltaX=t.originalEvent.clientX-i.touchStartX),i._handleSwipe(),"hover"===i._config.pause&&(i.pause(),i.touchTimeout&&clearTimeout(i.touchTimeout),i.touchTimeout=setTimeout(function(t){return i.cycle(t)},500+i._config.interval))},f(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(t){return t.preventDefault()}),this._pointerEvent?(f(this._element).on("pointerdown.bs.carousel",t),f(this._element).on("pointerup.bs.carousel",e),this._element.classList.add("pointer-event")):(f(this._element).on("touchstart.bs.carousel",t),f(this._element).on("touchmove.bs.carousel",function(t){var e;(e=t).originalEvent.touches&&1<e.originalEvent.touches.length?i.touchDeltaX=0:i.touchDeltaX=e.originalEvent.touches[0].clientX-i.touchStartX}),f(this._element).on("touchend.bs.carousel",e)))},t._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},t._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(t)},t._getItemByDirection=function(t,e){var i=t===T,n=t===D,s=this._getItemIndex(e),o=this._items.length-1;if((n&&0===s||i&&s===o)&&!this._config.wrap)return e;var a=(s+(t===D?-1:1))%this._items.length;return-1==a?this._items[this._items.length-1]:this._items[a]},t._triggerSlideEvent=function(t,e){var i=this._getItemIndex(t),n=this._getItemIndex(this._element.querySelector(L)),s=f.Event("slide.bs.carousel",{relatedTarget:t,direction:e,from:n,to:i});return f(this._element).trigger(s),s},t._setActiveIndicatorElement=function(t){var e,i;this._indicatorsElement&&(e=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),f(e).removeClass(A),(i=this._indicatorsElement.children[this._getItemIndex(t)])&&f(i).addClass(A))},t._slide=function(t,e){var i,n,s,o,a,r=this,l=this._element.querySelector(L),c=this._getItemIndex(l),h=e||l&&this._getItemByDirection(t,l),d=this._getItemIndex(h),u=Boolean(this._interval),p=t===T?(i="carousel-item-left",n="carousel-item-next","left"):(i="carousel-item-right",n="carousel-item-prev","right");h&&f(h).hasClass(A)?this._isSliding=!1:this._triggerSlideEvent(h,p).isDefaultPrevented()||l&&h&&(this._isSliding=!0,u&&this.pause(),this._setActiveIndicatorElement(h),s=f.Event(I,{relatedTarget:h,direction:p,from:c,to:d}),f(this._element).hasClass("slide")?(f(h).addClass(n),m.reflow(h),f(l).addClass(i),f(h).addClass(i),(o=parseInt(h.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=o):this._config.interval=this._config.defaultInterval||this._config.interval,a=m.getTransitionDurationFromElement(l),f(l).one(m.TRANSITION_END,function(){f(h).removeClass(i+" "+n).addClass(A),f(l).removeClass(A+" "+n+" "+i),r._isSliding=!1,setTimeout(function(){return f(r._element).trigger(s)},0)}).emulateTransitionEnd(a)):(f(l).removeClass(A),f(h).addClass(A),this._isSliding=!1,f(this._element).trigger(s)),u&&this.cycle())},o._jQueryInterface=function(n){return this.each(function(){var t=f(this).data(C),e=l(l({},x),f(this).data());"object"==typeof n&&(e=l(l({},e),n));var i="string"==typeof n?n:e.slide;if(t||(t=new o(this,e),f(this).data(C,t)),"number"==typeof n)t.to(n);else if("string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}else e.interval&&e.ride&&(t.pause(),t.cycle())})},o._dataApiClickHandler=function(t){var e,i,n,s=m.getSelectorFromElement(this);!s||(e=f(s)[0])&&f(e).hasClass("carousel")&&(i=l(l({},f(e).data()),f(this).data()),(n=this.getAttribute("data-slide-to"))&&(i.interval=!1),o._jQueryInterface.call(f(e),i),n&&f(e).data(C).to(n),t.preventDefault())},a(o,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return x}}]),o}();f(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",O._dataApiClickHandler),f(window).on("load.bs.carousel.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),e=0,i=t.length;e<i;e++){var n=f(t[e]);O._jQueryInterface.call(n,n.data())}}),f.fn[w]=O._jQueryInterface,f.fn[w].Constructor=O,f.fn[w].noConflict=function(){return f.fn[w]=$,O._jQueryInterface};var P="collapse",M="bs.collapse",N=f.fn[P],B={toggle:!0,parent:""},U={toggle:"boolean",parent:"(string|element)"},F="show",R="collapse",H="collapsing",j="collapsed",W='[data-toggle="collapse"]',z=function(){function r(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var i=[].slice.call(document.querySelectorAll(W)),n=0,s=i.length;n<s;n++){var o=i[n],a=m.getSelectorFromElement(o),r=[].slice.call(document.querySelectorAll(a)).filter(function(t){return t===e});null!==a&&0<r.length&&(this._selector=a,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var t=r.prototype;return t.toggle=function(){f(this._element).hasClass(F)?this.hide():this.show()},t.show=function(){var t,e,i,n,s,o,a=this;this._isTransitioning||f(this._element).hasClass(F)||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(t){return"string"==typeof a._config.parent?t.getAttribute("data-parent")===a._config.parent:t.classList.contains(R)})).length&&(t=null),t&&(e=f(t).not(this._selector).data(M))&&e._isTransitioning||(i=f.Event("show.bs.collapse"),f(this._element).trigger(i),i.isDefaultPrevented()||(t&&(r._jQueryInterface.call(f(t).not(this._selector),"hide"),e||f(t).data(M,null)),n=this._getDimension(),f(this._element).removeClass(R).addClass(H),this._element.style[n]=0,this._triggerArray.length&&f(this._triggerArray).removeClass(j).attr("aria-expanded",!0),this.setTransitioning(!0),s="scroll"+(n[0].toUpperCase()+n.slice(1)),o=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,function(){f(a._element).removeClass(H).addClass(R+" "+F),a._element.style[n]="",a.setTransitioning(!1),f(a._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(o),this._element.style[n]=this._element[s]+"px")))},t.hide=function(){var t=this;if(!this._isTransitioning&&f(this._element).hasClass(F)){var e=f.Event("hide.bs.collapse");if(f(this._element).trigger(e),!e.isDefaultPrevented()){var i=this._getDimension();this._element.style[i]=this._element.getBoundingClientRect()[i]+"px",m.reflow(this._element),f(this._element).addClass(H).removeClass(R+" "+F);var n=this._triggerArray.length;if(0<n)for(var s=0;s<n;s++){var o=this._triggerArray[s],a=m.getSelectorFromElement(o);null!==a&&(f([].slice.call(document.querySelectorAll(a))).hasClass(F)||f(o).addClass(j).attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[i]="";var r=m.getTransitionDurationFromElement(this._element);f(this._element).one(m.TRANSITION_END,function(){t.setTransitioning(!1),f(t._element).removeClass(H).addClass(R).trigger("hidden.bs.collapse")}).emulateTransitionEnd(r)}}},t.setTransitioning=function(t){this._isTransitioning=t},t.dispose=function(){f.removeData(this._element,M),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(t){return(t=l(l({},B),t)).toggle=Boolean(t.toggle),m.typeCheckConfig(P,t,U),t},t._getDimension=function(){return f(this._element).hasClass("width")?"width":"height"},t._getParent=function(){var t,i=this;m.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var e='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',n=[].slice.call(t.querySelectorAll(e));return f(n).each(function(t,e){i._addAriaAndCollapsedClass(r._getTargetFromElement(e),[e])}),t},t._addAriaAndCollapsedClass=function(t,e){var i=f(t).hasClass(F);e.length&&f(e).toggleClass(j,!i).attr("aria-expanded",i)},r._getTargetFromElement=function(t){var e=m.getSelectorFromElement(t);return e?document.querySelector(e):null},r._jQueryInterface=function(n){return this.each(function(){var t=f(this),e=t.data(M),i=l(l(l({},B),t.data()),"object"==typeof n&&n?n:{});if(!e&&i.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(i.toggle=!1),e||(e=new r(this,i),t.data(M,e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},a(r,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return B}}]),r}();f(document).on("click.bs.collapse.data-api",W,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var i=f(this),e=m.getSelectorFromElement(this),n=[].slice.call(document.querySelectorAll(e));f(n).each(function(){var t=f(this),e=t.data(M)?"toggle":i.data();z._jQueryInterface.call(t,e)})}),f.fn[P]=z._jQueryInterface,f.fn[P].Constructor=z,f.fn[P].noConflict=function(){return f.fn[P]=N,z._jQueryInterface};var Y="dropdown",q="bs.dropdown",K="."+q,G=".data-api",V=f.fn[Y],Q=new RegExp("38|40|27"),X="hide"+K,J="hidden"+K,Z="click"+K+G,tt="keydown"+K+G,et="disabled",it="show",nt="dropdown-menu-right",st='[data-toggle="dropdown"]',ot=".dropdown-menu",at={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},rt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},lt=function(){function c(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var t=c.prototype;return t.toggle=function(){var t;this._element.disabled||f(this._element).hasClass(et)||(t=f(this._menu).hasClass(it),c._clearMenus(),t||this.show(!0))},t.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||f(this._element).hasClass(et)||f(this._menu).hasClass(it))){var e={relatedTarget:this._element},i=f.Event("show.bs.dropdown",e),n=c._getParentFromElement(this._element);if(f(n).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&t){if(void 0===d)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var s=this._element;"parent"===this._config.reference?s=n:m.isElement(this._config.reference)&&(s=this._config.reference,void 0!==this._config.reference.jquery&&(s=this._config.reference[0])),"scrollParent"!==this._config.boundary&&f(n).addClass("position-static"),this._popper=new d(s,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===f(n).closest(".navbar-nav").length&&f(document.body).children().on("mouseover",null,f.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),f(this._menu).toggleClass(it),f(n).toggleClass(it).trigger(f.Event("shown.bs.dropdown",e))}}},t.hide=function(){var t,e,i;this._element.disabled||f(this._element).hasClass(et)||!f(this._menu).hasClass(it)||(t={relatedTarget:this._element},e=f.Event(X,t),i=c._getParentFromElement(this._element),f(i).trigger(e),e.isDefaultPrevented()||(this._popper&&this._popper.destroy(),f(this._menu).toggleClass(it),f(i).toggleClass(it).trigger(f.Event(J,t))))},t.dispose=function(){f.removeData(this._element,q),f(this._element).off(K),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;f(this._element).on("click.bs.dropdown",function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},t._getConfig=function(t){return t=l(l(l({},this.constructor.Default),f(this._element).data()),t),m.typeCheckConfig(Y,t,this.constructor.DefaultType),t},t._getMenuElement=function(){var t;return this._menu||(t=c._getParentFromElement(this._element))&&(this._menu=t.querySelector(ot)),this._menu},t._getPlacement=function(){var t=f(this._element.parentNode),e="bottom-start";return t.hasClass("dropup")?e=f(this._menu).hasClass(nt)?"top-end":"top-start":t.hasClass("dropright")?e="right-start":t.hasClass("dropleft")?e="left-start":f(this._menu).hasClass(nt)&&(e="bottom-end"),e},t._detectNavbar=function(){return 0<f(this._element).closest(".navbar").length},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=l(l({},t.offsets),e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),l(l({},t),this._config.popperConfig)},c._jQueryInterface=function(e){return this.each(function(){var t=f(this).data(q);if(t||(t=new c(this,"object"==typeof e?e:null),f(this).data(q,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},c._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=[].slice.call(document.querySelectorAll(st)),i=0,n=e.length;i<n;i++){var s,o,a=c._getParentFromElement(e[i]),r=f(e[i]).data(q),l={relatedTarget:e[i]};t&&"click"===t.type&&(l.clickEvent=t),r&&(s=r._menu,f(a).hasClass(it)&&(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&f.contains(a,t.target)||(o=f.Event(X,l),f(a).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&f(document.body).children().off("mouseover",null,f.noop),e[i].setAttribute("aria-expanded","false"),r._popper&&r._popper.destroy(),f(s).removeClass(it),f(a).removeClass(it).trigger(f.Event(J,l))))))}},c._getParentFromElement=function(t){var e,i=m.getSelectorFromElement(t);return i&&(e=document.querySelector(i)),e||t.parentNode},c._dataApiKeydownHandler=function(t){if((/input|textarea/i.test(t.target.tagName)?!(32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||f(t.target).closest(ot).length)):Q.test(t.which))&&!this.disabled&&!f(this).hasClass(et)){var e=c._getParentFromElement(this),i=f(e).hasClass(it);if(i||27!==t.which){if(t.preventDefault(),t.stopPropagation(),!i||i&&(27===t.which||32===t.which))return 27===t.which&&f(e.querySelector(st)).trigger("focus"),void f(this).trigger("click");var n,s=[].slice.call(e.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(t){return f(t).is(":visible")});0!==s.length&&(n=s.indexOf(t.target),38===t.which&&0<n&&n--,40===t.which&&n<s.length-1&&n++,n<0&&(n=0),s[n].focus())}}},a(c,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return at}},{key:"DefaultType",get:function(){return rt}}]),c}();f(document).on(tt,st,lt._dataApiKeydownHandler).on(tt,ot,lt._dataApiKeydownHandler).on(Z+" keyup.bs.dropdown.data-api",lt._clearMenus).on(Z,st,function(t){t.preventDefault(),t.stopPropagation(),lt._jQueryInterface.call(f(this),"toggle")}).on(Z,".dropdown form",function(t){t.stopPropagation()}),f.fn[Y]=lt._jQueryInterface,f.fn[Y].Constructor=lt,f.fn[Y].noConflict=function(){return f.fn[Y]=V,lt._jQueryInterface};var ct="modal",ht="bs.modal",dt="."+ht,ut=f.fn[ct],pt={backdrop:!0,keyboard:!0,focus:!0,show:!0},ft={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},mt="hidden"+dt,gt="show"+dt,vt="focusin"+dt,bt="resize"+dt,yt="click.dismiss"+dt,_t="keydown.dismiss"+dt,wt="mousedown.dismiss"+dt,Ct="modal-open",kt="fade",$t="show",xt="modal-static",Et=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Tt=".sticky-top",Dt=function(){function s(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var t=s.prototype;return t.toggle=function(t){return this._isShown?this.hide():this.show(t)},t.show=function(t){var e,i=this;this._isShown||this._isTransitioning||(f(this._element).hasClass(kt)&&(this._isTransitioning=!0),e=f.Event(gt,{relatedTarget:t}),f(this._element).trigger(e),this._isShown||e.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),f(this._element).on(yt,'[data-dismiss="modal"]',function(t){return i.hide(t)}),f(this._dialog).on(wt,function(){f(i._element).one("mouseup.dismiss.bs.modal",function(t){f(t.target).is(i._element)&&(i._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return i._showElement(t)})))},t.hide=function(t){var e,i,n,s=this;t&&t.preventDefault(),this._isShown&&!this._isTransitioning&&(e=f.Event("hide.bs.modal"),f(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(i=f(this._element).hasClass(kt))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),f(document).off(vt),f(this._element).removeClass($t),f(this._element).off(yt),f(this._dialog).off(wt),i?(n=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,function(t){return s._hideModal(t)}).emulateTransitionEnd(n)):this._hideModal()))},t.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return f(t).off(dt)}),f(document).off(vt),f.removeData(this._element,ht),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(t){return t=l(l({},pt),t),m.typeCheckConfig(ct,t,ft),t},t._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var e=f.Event("hidePrevented.bs.modal");if(f(this._element).trigger(e),e.defaultPrevented)return;this._element.classList.add(xt);var i=m.getTransitionDurationFromElement(this._element);f(this._element).one(m.TRANSITION_END,function(){t._element.classList.remove(xt)}).emulateTransitionEnd(i),this._element.focus()}else this.hide()},t._showElement=function(t){var e=this,i=f(this._element).hasClass(kt),n=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),f(this._dialog).hasClass("modal-dialog-scrollable")&&n?n.scrollTop=0:this._element.scrollTop=0,i&&m.reflow(this._element),f(this._element).addClass($t),this._config.focus&&this._enforceFocus();function s(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,f(e._element).trigger(a)}var o,a=f.Event("shown.bs.modal",{relatedTarget:t});i?(o=m.getTransitionDurationFromElement(this._dialog),f(this._dialog).one(m.TRANSITION_END,s).emulateTransitionEnd(o)):s()},t._enforceFocus=function(){var e=this;f(document).off(vt).on(vt,function(t){document!==t.target&&e._element!==t.target&&0===f(e._element).has(t.target).length&&e._element.focus()})},t._setEscapeEvent=function(){var e=this;this._isShown?f(this._element).on(_t,function(t){e._config.keyboard&&27===t.which?(t.preventDefault(),e.hide()):e._config.keyboard||27!==t.which||e._triggerBackdropTransition()}):this._isShown||f(this._element).off(_t)},t._setResizeEvent=function(){var e=this;this._isShown?f(window).on(bt,function(t){return e.handleUpdate(t)}):f(window).off(bt)},t._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(function(){f(document.body).removeClass(Ct),t._resetAdjustments(),t._resetScrollbar(),f(t._element).trigger(mt)})},t._removeBackdrop=function(){this._backdrop&&(f(this._backdrop).remove(),this._backdrop=null)},t._showBackdrop=function(t){var e,i,n=this,s=f(this._element).hasClass(kt)?kt:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",s&&this._backdrop.classList.add(s),f(this._backdrop).appendTo(document.body),f(this._element).on(yt,function(t){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:t.target===t.currentTarget&&n._triggerBackdropTransition()}),s&&m.reflow(this._backdrop),f(this._backdrop).addClass($t),!t)return;if(!s)return void t();var o=m.getTransitionDurationFromElement(this._backdrop);f(this._backdrop).one(m.TRANSITION_END,t).emulateTransitionEnd(o)}else{!this._isShown&&this._backdrop?(f(this._backdrop).removeClass($t),e=function(){n._removeBackdrop(),t&&t()},f(this._element).hasClass(kt)?(i=m.getTransitionDurationFromElement(this._backdrop),f(this._backdrop).one(m.TRANSITION_END,e).emulateTransitionEnd(i)):e()):t&&t()}},t._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var t,e,i,n,s=this;this._isBodyOverflowing&&(t=[].slice.call(document.querySelectorAll(Et)),e=[].slice.call(document.querySelectorAll(Tt)),f(t).each(function(t,e){var i=e.style.paddingRight,n=f(e).css("padding-right");f(e).data("padding-right",i).css("padding-right",parseFloat(n)+s._scrollbarWidth+"px")}),f(e).each(function(t,e){var i=e.style.marginRight,n=f(e).css("margin-right");f(e).data("margin-right",i).css("margin-right",parseFloat(n)-s._scrollbarWidth+"px")}),i=document.body.style.paddingRight,n=f(document.body).css("padding-right"),f(document.body).data("padding-right",i).css("padding-right",parseFloat(n)+this._scrollbarWidth+"px")),f(document.body).addClass(Ct)},t._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(Et));f(t).each(function(t,e){var i=f(e).data("padding-right");f(e).removeData("padding-right"),e.style.paddingRight=i||""});var e=[].slice.call(document.querySelectorAll(Tt));f(e).each(function(t,e){var i=f(e).data("margin-right");void 0!==i&&f(e).css("margin-right",i).removeData("margin-right")});var i=f(document.body).data("padding-right");f(document.body).removeData("padding-right"),document.body.style.paddingRight=i||""},t._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},s._jQueryInterface=function(i,n){return this.each(function(){var t=f(this).data(ht),e=l(l(l({},pt),f(this).data()),"object"==typeof i&&i?i:{});if(t||(t=new s(this,e),f(this).data(ht,t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i](n)}else e.show&&t.show(n)})},a(s,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return pt}}]),s}();f(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var e,i=this,n=m.getSelectorFromElement(this);n&&(e=document.querySelector(n));var s=f(e).data(ht)?"toggle":l(l({},f(e).data()),f(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var o=f(e).one(gt,function(t){t.isDefaultPrevented()||o.one(mt,function(){f(i).is(":visible")&&i.focus()})});Dt._jQueryInterface.call(f(e),s,this)}),f.fn[ct]=Dt._jQueryInterface,f.fn[ct].Constructor=Dt,f.fn[ct].noConflict=function(){return f.fn[ct]=ut,Dt._jQueryInterface};var It=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],At={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Lt=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,St=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Ot(t,o,e){if(0===t.length)return t;if(e&&"function"==typeof e)return e(t);for(var i=(new window.DOMParser).parseFromString(t,"text/html"),a=Object.keys(o),r=[].slice.call(i.body.querySelectorAll("*")),n=function(t){var e=r[t],i=e.nodeName.toLowerCase();if(-1===a.indexOf(e.nodeName.toLowerCase()))return e.parentNode.removeChild(e),"continue";var n=[].slice.call(e.attributes),s=[].concat(o["*"]||[],o[i]||[]);n.forEach(function(t){!function(t,e){var i=t.nodeName.toLowerCase();if(-1!==e.indexOf(i))return-1===It.indexOf(i)||Boolean(t.nodeValue.match(Lt)||t.nodeValue.match(St));for(var n=e.filter(function(t){return t instanceof RegExp}),s=0,o=n.length;s<o;s++)if(i.match(n[s]))return 1}(t,s)&&e.removeAttribute(t.nodeName)})},s=0,l=r.length;s<l;s++)n(s);return i.body.innerHTML}var Pt="tooltip",Mt="bs.tooltip",Nt="."+Mt,Bt=f.fn[Pt],Ut="bs-tooltip",Ft=new RegExp("(^|\\s)"+Ut+"\\S+","g"),Rt=["sanitize","whiteList","sanitizeFn"],Ht={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},jt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Wt={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:At,popperConfig:null},zt="show",Yt={HIDE:"hide"+Nt,HIDDEN:"hidden"+Nt,SHOW:"show"+Nt,SHOWN:"shown"+Nt,INSERTED:"inserted"+Nt,CLICK:"click"+Nt,FOCUSIN:"focusin"+Nt,FOCUSOUT:"focusout"+Nt,MOUSEENTER:"mouseenter"+Nt,MOUSELEAVE:"mouseleave"+Nt},qt="fade",Kt="show",Gt="hover",Vt="focus",Qt=function(){function n(t,e){if(void 0===d)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var t=n.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,i=f(t.currentTarget).data(e);i||(i=new this.constructor(t.currentTarget,this._getDelegateConfig()),f(t.currentTarget).data(e,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)}else{if(f(this.getTipElement()).hasClass(Kt))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),f.removeData(this.element,this.constructor.DATA_KEY),f(this.element).off(this.constructor.EVENT_KEY),f(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&f(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===f(this.element).css("display"))throw new Error("Please use show on visible elements");var t=f.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){f(this.element).trigger(t);var i=m.findShadowRoot(this.element),n=f.contains(null!==i?i:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!n)return;var s=this.getTipElement(),o=m.getUID(this.constructor.NAME);s.setAttribute("id",o),this.element.setAttribute("aria-describedby",o),this.setContent(),this.config.animation&&f(s).addClass(qt);var a="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,r=this._getAttachment(a);this.addAttachmentClass(r);var l=this._getContainer();f(s).data(this.constructor.DATA_KEY,this),f.contains(this.element.ownerDocument.documentElement,this.tip)||f(s).appendTo(l),f(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new d(this.element,s,this._getPopperConfig(r)),f(s).addClass(Kt),"ontouchstart"in document.documentElement&&f(document.body).children().on("mouseover",null,f.noop);var c,h=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,f(e.element).trigger(e.constructor.Event.SHOWN),"out"===t&&e._leave(null,e)};f(this.tip).hasClass(qt)?(c=m.getTransitionDurationFromElement(this.tip),f(this.tip).one(m.TRANSITION_END,h).emulateTransitionEnd(c)):h()}},t.hide=function(t){function e(){n._hoverState!==zt&&s.parentNode&&s.parentNode.removeChild(s),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),f(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),t&&t()}var i,n=this,s=this.getTipElement(),o=f.Event(this.constructor.Event.HIDE);f(this.element).trigger(o),o.isDefaultPrevented()||(f(s).removeClass(Kt),"ontouchstart"in document.documentElement&&f(document.body).children().off("mouseover",null,f.noop),this._activeTrigger.click=!1,this._activeTrigger[Vt]=!1,this._activeTrigger[Gt]=!1,f(this.tip).hasClass(qt)?(i=m.getTransitionDurationFromElement(s),f(s).one(m.TRANSITION_END,e).emulateTransitionEnd(i)):e(),this._hoverState="")},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.addAttachmentClass=function(t){f(this.getTipElement()).addClass(Ut+"-"+t)},t.getTipElement=function(){return this.tip=this.tip||f(this.config.template)[0],this.tip},t.setContent=function(){var t=this.getTipElement();this.setElementContent(f(t.querySelectorAll(".tooltip-inner")),this.getTitle()),f(t).removeClass(qt+" "+Kt)},t.setElementContent=function(t,e){"object"!=typeof e||!e.nodeType&&!e.jquery?this.config.html?(this.config.sanitize&&(e=Ot(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e):this.config.html?f(e).parent().is(t)||t.empty().append(e):t.text(f(e).text())},t.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},t._getPopperConfig=function(t){var e=this;return l(l({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}}),this.config.popperConfig)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=l(l({},t.offsets),e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:m.isElement(this.config.container)?f(this.config.container):f(document).find(this.config.container)},t._getAttachment=function(t){return jt[t.toUpperCase()]},t._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(t){var e,i;"click"===t?f(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(t){return n.toggle(t)}):"manual"!==t&&(e=t===Gt?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,i=t===Gt?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,f(n.element).on(e,n.config.selector,function(t){return n._enter(t)}).on(i,n.config.selector,function(t){return n._leave(t)}))}),this._hideModalHandler=function(){n.element&&n.hide()},f(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=l(l({},this.config),{},{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(t,e){var i=this.constructor.DATA_KEY;(e=e||f(t.currentTarget).data(i))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),f(t.currentTarget).data(i,e)),t&&(e._activeTrigger["focusin"===t.type?Vt:Gt]=!0),f(e.getTipElement()).hasClass(Kt)||e._hoverState===zt?e._hoverState=zt:(clearTimeout(e._timeout),e._hoverState=zt,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===zt&&e.show()},e.config.delay.show):e.show())},t._leave=function(t,e){var i=this.constructor.DATA_KEY;(e=e||f(t.currentTarget).data(i))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),f(t.currentTarget).data(i,e)),t&&(e._activeTrigger["focusout"===t.type?Vt:Gt]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){"out"===e._hoverState&&e.hide()},e.config.delay.hide):e.hide())},t._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},t._getConfig=function(t){var e=f(this.element).data();return Object.keys(e).forEach(function(t){-1!==Rt.indexOf(t)&&delete e[t]}),"number"==typeof(t=l(l(l({},this.constructor.Default),e),"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),m.typeCheckConfig(Pt,t,this.constructor.DefaultType),t.sanitize&&(t.template=Ot(t.template,t.whiteList,t.sanitizeFn)),t},t._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},t._cleanTipClass=function(){var t=f(this.getTipElement()),e=t.attr("class").match(Ft);null!==e&&e.length&&t.removeClass(e.join(""))},t._handlePopperPlacementChange=function(t){this.tip=t.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},t._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(f(t).removeClass(qt),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},n._jQueryInterface=function(i){return this.each(function(){var t=f(this).data(Mt),e="object"==typeof i&&i;if((t||!/dispose|hide/.test(i))&&(t||(t=new n(this,e),f(this).data(Mt,t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},a(n,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return Wt}},{key:"NAME",get:function(){return Pt}},{key:"DATA_KEY",get:function(){return Mt}},{key:"Event",get:function(){return Yt}},{key:"EVENT_KEY",get:function(){return Nt}},{key:"DefaultType",get:function(){return Ht}}]),n}();f.fn[Pt]=Qt._jQueryInterface,f.fn[Pt].Constructor=Qt,f.fn[Pt].noConflict=function(){return f.fn[Pt]=Bt,Qt._jQueryInterface};var Xt="popover",Jt="bs.popover",Zt="."+Jt,te=f.fn[Xt],ee="bs-popover",ie=new RegExp("(^|\\s)"+ee+"\\S+","g"),ne=l(l({},Qt.Default),{},{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),se=l(l({},Qt.DefaultType),{},{content:"(string|element|function)"}),oe={HIDE:"hide"+Zt,HIDDEN:"hidden"+Zt,SHOW:"show"+Zt,SHOWN:"shown"+Zt,INSERTED:"inserted"+Zt,CLICK:"click"+Zt,FOCUSIN:"focusin"+Zt,FOCUSOUT:"focusout"+Zt,MOUSEENTER:"mouseenter"+Zt,MOUSELEAVE:"mouseleave"+Zt},ae=function(t){var e,i;function n(){return t.apply(this,arguments)||this}i=t,(e=n).prototype=Object.create(i.prototype),(e.prototype.constructor=e).__proto__=i;var s=n.prototype;return s.isWithContent=function(){return this.getTitle()||this._getContent()},s.addAttachmentClass=function(t){f(this.getTipElement()).addClass(ee+"-"+t)},s.getTipElement=function(){return this.tip=this.tip||f(this.config.template)[0],this.tip},s.setContent=function(){var t=f(this.getTipElement());this.setElementContent(t.find(".popover-header"),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(".popover-body"),e),t.removeClass("fade show")},s._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},s._cleanTipClass=function(){var t=f(this.getTipElement()),e=t.attr("class").match(ie);null!==e&&0<e.length&&t.removeClass(e.join(""))},n._jQueryInterface=function(i){return this.each(function(){var t=f(this).data(Jt),e="object"==typeof i?i:null;if((t||!/dispose|hide/.test(i))&&(t||(t=new n(this,e),f(this).data(Jt,t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},a(n,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return ne}},{key:"NAME",get:function(){return Xt}},{key:"DATA_KEY",get:function(){return Jt}},{key:"Event",get:function(){return oe}},{key:"EVENT_KEY",get:function(){return Zt}},{key:"DefaultType",get:function(){return se}}]),n}(Qt);f.fn[Xt]=ae._jQueryInterface,f.fn[Xt].Constructor=ae,f.fn[Xt].noConflict=function(){return f.fn[Xt]=te,ae._jQueryInterface};var re="scrollspy",le="bs.scrollspy",ce="."+le,he=f.fn[re],de={offset:10,method:"auto",target:""},ue={offset:"number",method:"string",target:"(string|element)"},pe="active",fe=".nav, .list-group",me=".nav-link",ge=".list-group-item",ve="position",be=function(){function i(t,e){var i=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+me+","+this._config.target+" "+ge+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,f(this._scrollElement).on("scroll.bs.scrollspy",function(t){return i._process(t)}),this.refresh(),this._process()}var t=i.prototype;return t.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?"offset":ve,s="auto"===this._config.method?t:this._config.method,o=s===ve?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var e,i=m.getSelectorFromElement(t);if(i&&(e=document.querySelector(i)),e){var n=e.getBoundingClientRect();if(n.width||n.height)return[f(e)[s]().top+o,i]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},t.dispose=function(){f.removeData(this._element,le),f(this._scrollElement).off(ce),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(t){var e;return"string"!=typeof(t=l(l({},de),"object"==typeof t&&t?t:{})).target&&m.isElement(t.target)&&((e=f(t.target).attr("id"))||(e=m.getUID(re),f(t.target).attr("id",e)),t.target="#"+e),m.typeCheckConfig(re,t,ue),t},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),i=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),i<=t){var n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var s=this._offsets.length;s--;){this._activeTarget!==this._targets[s]&&t>=this._offsets[s]&&(void 0===this._offsets[s+1]||t<this._offsets[s+1])&&this._activate(this._targets[s])}}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),i=f([].slice.call(document.querySelectorAll(t.join(","))));i.hasClass("dropdown-item")?(i.closest(".dropdown").find(".dropdown-toggle").addClass(pe),i.addClass(pe)):(i.addClass(pe),i.parents(fe).prev(me+", "+ge).addClass(pe),i.parents(fe).prev(".nav-item").children(me).addClass(pe)),f(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:e})},t._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains(pe)}).forEach(function(t){return t.classList.remove(pe)})},i._jQueryInterface=function(e){return this.each(function(){var t=f(this).data(le);if(t||(t=new i(this,"object"==typeof e&&e),f(this).data(le,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},a(i,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return de}}]),i}();f(window).on("load.bs.scrollspy.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),e=t.length;e--;){var i=f(t[e]);be._jQueryInterface.call(i,i.data())}}),f.fn[re]=be._jQueryInterface,f.fn[re].Constructor=be,f.fn[re].noConflict=function(){return f.fn[re]=he,be._jQueryInterface};var ye="bs.tab",_e=f.fn.tab,we="active",Ce=".active",ke="> li > .active",$e=function(){function n(t){this._element=t}var t=n.prototype;return t.show=function(){var t,e,i,n,s,o,a,r,l=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&f(this._element).hasClass(we)||f(this._element).hasClass("disabled")||(e=f(this._element).closest(".nav, .list-group")[0],i=m.getSelectorFromElement(this._element),e&&(n="UL"===e.nodeName||"OL"===e.nodeName?ke:Ce,s=(s=f.makeArray(f(e).find(n)))[s.length-1]),o=f.Event("hide.bs.tab",{relatedTarget:this._element}),a=f.Event("show.bs.tab",{relatedTarget:s}),s&&f(s).trigger(o),f(this._element).trigger(a),a.isDefaultPrevented()||o.isDefaultPrevented()||(i&&(t=document.querySelector(i)),this._activate(this._element,e),r=function(){var t=f.Event("hidden.bs.tab",{relatedTarget:l._element}),e=f.Event("shown.bs.tab",{relatedTarget:s});f(s).trigger(t),f(l._element).trigger(e)},t?this._activate(t,t.parentNode,r):r()))},t.dispose=function(){f.removeData(this._element,ye),this._element=null},t._activate=function(t,e,i){function n(){return o._transitionComplete(t,a,i)}var s,o=this,a=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?f(e).children(Ce):f(e).find(ke))[0],r=i&&a&&f(a).hasClass("fade");a&&r?(s=m.getTransitionDurationFromElement(a),f(a).removeClass("show").one(m.TRANSITION_END,n).emulateTransitionEnd(s)):n()},t._transitionComplete=function(t,e,i){var n,s,o;e&&(f(e).removeClass(we),(n=f(e.parentNode).find("> .dropdown-menu .active")[0])&&f(n).removeClass(we),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)),f(t).addClass(we),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),m.reflow(t),t.classList.contains("fade")&&t.classList.add("show"),t.parentNode&&f(t.parentNode).hasClass("dropdown-menu")&&((s=f(t).closest(".dropdown")[0])&&(o=[].slice.call(s.querySelectorAll(".dropdown-toggle")),f(o).addClass(we)),t.setAttribute("aria-expanded",!0)),i&&i()},n._jQueryInterface=function(i){return this.each(function(){var t=f(this),e=t.data(ye);if(e||(e=new n(this),t.data(ye,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},a(n,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),n}();f(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(t){t.preventDefault(),$e._jQueryInterface.call(f(this),"show")}),f.fn.tab=$e._jQueryInterface,f.fn.tab.Constructor=$e,f.fn.tab.noConflict=function(){return f.fn.tab=_e,$e._jQueryInterface};var xe="toast",Ee="bs.toast",Te="."+Ee,De=f.fn[xe],Ie="click.dismiss"+Te,Ae="show",Le="showing",Se={animation:"boolean",autohide:"boolean",delay:"number"},Oe={animation:!0,autohide:!0,delay:500},Pe=function(){function n(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}var t=n.prototype;return t.show=function(){var t,e,i=this,n=f.Event("show.bs.toast");f(this._element).trigger(n),n.isDefaultPrevented()||(this._config.animation&&this._element.classList.add("fade"),t=function(){i._element.classList.remove(Le),i._element.classList.add(Ae),f(i._element).trigger("shown.bs.toast"),i._config.autohide&&(i._timeout=setTimeout(function(){i.hide()},i._config.delay))},this._element.classList.remove("hide"),m.reflow(this._element),this._element.classList.add(Le),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t())},t.hide=function(){var t;this._element.classList.contains(Ae)&&(t=f.Event("hide.bs.toast"),f(this._element).trigger(t),t.isDefaultPrevented()||this._close())},t.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains(Ae)&&this._element.classList.remove(Ae),f(this._element).off(Ie),f.removeData(this._element,Ee),this._element=null,this._config=null},t._getConfig=function(t){return t=l(l(l({},Oe),f(this._element).data()),"object"==typeof t&&t?t:{}),m.typeCheckConfig(xe,t,this.constructor.DefaultType),t},t._setListeners=function(){var t=this;f(this._element).on(Ie,'[data-dismiss="toast"]',function(){return t.hide()})},t._close=function(){function t(){i._element.classList.add("hide"),f(i._element).trigger("hidden.bs.toast")}var e,i=this;this._element.classList.remove(Ae),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t()},n._jQueryInterface=function(i){return this.each(function(){var t=f(this),e=t.data(Ee);if(e||(e=new n(this,"object"==typeof i&&i),t.data(Ee,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},a(n,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"DefaultType",get:function(){return Se}},{key:"Default",get:function(){return Oe}}]),n}();f.fn[xe]=Pe._jQueryInterface,f.fn[xe].Constructor=Pe,f.fn[xe].noConflict=function(){return f.fn[xe]=De,Pe._jQueryInterface},t.Alert=h,t.Button=_,t.Carousel=O,t.Collapse=z,t.Dropdown=lt,t.Modal=Dt,t.Popover=ae,t.Scrollspy=be,t.Tab=$e,t.Toast=Pe,t.Tooltip=Qt,t.Util=m,Object.defineProperty(t,"__esModule",{value:!0})}),function(L){"use strict";var h=["sanitize","whiteList","sanitizeFn"],g=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],t={"*":["class","dir","id","lang","role","tabindex","style",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},v=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,b=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function U(t,e,i){if(i&&"function"==typeof i)return i(t);for(var n=Object.keys(e),s=0,o=t.length;s<o;s++)for(var a=t[s].querySelectorAll("*"),r=0,l=a.length;r<l;r++){var c=a[r],h=c.nodeName.toLowerCase();if(-1!==n.indexOf(h))for(var d=[].slice.call(c.attributes),u=[].concat(e["*"]||[],e[h]||[]),p=0,f=d.length;p<f;p++){var m=d[p];!function(t,e){var i=t.nodeName.toLowerCase();if(-1!==L.inArray(i,e))return-1===L.inArray(i,g)||Boolean(t.nodeValue.match(v)||t.nodeValue.match(b));for(var n=L(e).filter(function(t,e){return e instanceof RegExp}),s=0,o=n.length;s<o;s++)if(i.match(n[s]))return 1}(m,u)&&c.removeAttribute(m.nodeName)}else c.parentNode.removeChild(c)}}"classList"in document.createElement("_")||function(t){if("Element"in t){var e="classList",i="prototype",n=t.Element[i],s=Object,o=function(){var i=L(this);return{add:function(t){return t=Array.prototype.slice.call(arguments).join(" "),i.addClass(t)},remove:function(t){return t=Array.prototype.slice.call(arguments).join(" "),i.removeClass(t)},toggle:function(t,e){return i.toggleClass(t,e)},contains:function(t){return i.hasClass(t)}}};if(s.defineProperty){var a={get:o,enumerable:!0,configurable:!0};try{s.defineProperty(n,e,a)}catch(t){void 0!==t.number&&-2146823252!==t.number||(a.enumerable=!1,s.defineProperty(n,e,a))}}else s[i].__defineGetter__&&n.__defineGetter__(e,o)}}(window);var e,i,n,s,d,o=document.createElement("_");function a(t,e){if(null==this)throw new TypeError;var i=String(this);if(t&&"[object RegExp]"==d.call(t))throw new TypeError;var n=i.length,s=String(t),o=s.length,a=1<arguments.length?e:void 0,r=a?Number(a):0;r!=r&&(r=0);var l=Math.min(Math.max(r,0),n);if(n<o+l)return!1;for(var c=-1;++c<o;)if(i.charCodeAt(l+c)!=s.charCodeAt(c))return!1;return!0}function D(t,e){var i,n=t.selectedOptions,s=[];if(e){for(var o=0,a=n.length;o<a;o++)(i=n[o]).disabled||"OPTGROUP"===i.parentNode.tagName&&i.parentNode.disabled||s.push(i);return s}return n}function I(t,e){for(var i,n=[],s=e||t.selectedOptions,o=0,a=s.length;o<a;o++)(i=s[o]).disabled||"OPTGROUP"===i.parentNode.tagName&&i.parentNode.disabled||n.push(i.value);return t.multiple?n:n.length?n[0]:null}o.classList.add("c1","c2"),o.classList.contains("c2")||(e=DOMTokenList.prototype.add,i=DOMTokenList.prototype.remove,DOMTokenList.prototype.add=function(){Array.prototype.forEach.call(arguments,e.bind(this))},DOMTokenList.prototype.remove=function(){Array.prototype.forEach.call(arguments,i.bind(this))}),o.classList.toggle("c3",!1),o.classList.contains("c3")&&(n=DOMTokenList.prototype.toggle,DOMTokenList.prototype.toggle=function(t,e){return 1 in arguments&&!this.contains(t)==!e?e:n.call(this,t)}),o=null,String.prototype.startsWith||(s=function(){try{var t={},e=Object.defineProperty,i=e(t,t,t)&&e}catch(t){}return i}(),d={}.toString,s?s(String.prototype,"startsWith",{value:a,configurable:!0,writable:!0}):String.prototype.startsWith=a),Object.keys||(Object.keys=function(t,e,i){for(e in i=[],t)i.hasOwnProperty.call(t,e)&&i.push(e);return i}),HTMLSelectElement&&!HTMLSelectElement.prototype.hasOwnProperty("selectedOptions")&&Object.defineProperty(HTMLSelectElement.prototype,"selectedOptions",{get:function(){return this.querySelectorAll(":checked")}});var r={useDefault:!1,_set:L.valHooks.select.set};L.valHooks.select.set=function(t,e){return e&&!r.useDefault&&L(t).data("selected",!0),r._set.apply(this,arguments)};var A=null,l=function(){try{return new Event("change"),!0}catch(t){return!1}}();function C(t,e,i,n){for(var s=["display","subtext","tokens"],o=!1,a=0;a<s.length;a++){var r=s[a],l=t[r];if(l&&(l=l.toString(),"display"===r&&(l=l.replace(/<[^>]+>/g,"")),n&&(l=m(l)),l=l.toUpperCase(),o="contains"===i?0<=l.indexOf(e):l.startsWith(e)))break}return o}function S(t){return parseInt(t,10)||0}L.fn.triggerNative=function(t){var e,i=this[0];i.dispatchEvent?(l?e=new Event(t,{bubbles:!0}):(e=document.createEvent("Event")).initEvent(t,!0,!1),i.dispatchEvent(e)):i.fireEvent?((e=document.createEventObject()).eventType=t,i.fireEvent("on"+t,e)):this.trigger(t)};var c={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"},u=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,p=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\u1ab0-\\u1aff\\u1dc0-\\u1dff]","g");function f(t){return c[t]}function m(t){return(t=t.toString())&&t.replace(u,f).replace(p,"")}var y,_,w,k,$=(y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},_="(?:"+Object.keys(y).join("|")+")",w=RegExp(_),k=RegExp(_,"g"),function(t){return t=null==t?"":""+t,w.test(t)?t.replace(k,x):t});function x(t){return y[t]}var E={32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},T=27,O=13,P=32,M=9,N=38,B=40,F={success:!1,major:"3"};try{F.full=(L.fn.dropdown.Constructor.VERSION||"").split(" ")[0].split("."),F.major=F.full[0],F.success=!0}catch(t){}var R=0,H=".bs.select",j={DISABLED:"disabled",DIVIDER:"divider",SHOW:"open",DROPUP:"dropup",MENU:"dropdown-menu",MENURIGHT:"dropdown-menu-right",MENULEFT:"dropdown-menu-left",BUTTONCLASS:"btn-default",POPOVERHEADER:"popover-title",ICONBASE:"glyphicon",TICKICON:"glyphicon-ok"},W={MENU:"."+j.MENU},z={div:document.createElement("div"),span:document.createElement("span"),i:document.createElement("i"),subtext:document.createElement("small"),a:document.createElement("a"),li:document.createElement("li"),whitespace:document.createTextNode(" "),fragment:document.createDocumentFragment()};z.noResults=z.li.cloneNode(!1),z.noResults.className="no-results",z.a.setAttribute("role","option"),z.a.className="dropdown-item",z.subtext.className="text-muted",z.text=z.span.cloneNode(!1),z.text.className="text",z.checkMark=z.span.cloneNode(!1);var Y=new RegExp(N+"|"+B),q=new RegExp("^"+M+"$|"+T),K={li:function(t,e,i){var n=z.li.cloneNode(!1);return t&&(1===t.nodeType||11===t.nodeType?n.appendChild(t):n.innerHTML=t),void 0!==e&&""!==e&&(n.className=e),null!=i&&n.classList.add("optgroup-"+i),n},a:function(t,e,i){var n=z.a.cloneNode(!0);return t&&(11===t.nodeType?n.appendChild(t):n.insertAdjacentHTML("beforeend",t)),void 0!==e&&""!==e&&n.classList.add.apply(n.classList,e.split(/\s+/)),i&&n.setAttribute("style",i),n},text:function(t,e){var i,n,s,o=z.text.cloneNode(!1);if(t.content?o.innerHTML=t.content:(o.textContent=t.text,t.icon&&(n=z.whitespace.cloneNode(!1),(s=(!0===e?z.i:z.span).cloneNode(!1)).className=this.options.iconBase+" "+t.icon,z.fragment.appendChild(s),z.fragment.appendChild(n)),t.subtext&&((i=z.subtext.cloneNode(!1)).textContent=t.subtext,o.appendChild(i))),!0===e)for(;0<o.childNodes.length;)z.fragment.appendChild(o.childNodes[0]);else z.fragment.appendChild(o);return z.fragment},label:function(t){var e,i,n,s=z.text.cloneNode(!1);return s.innerHTML=t.display,t.icon&&(i=z.whitespace.cloneNode(!1),(n=z.span.cloneNode(!1)).className=this.options.iconBase+" "+t.icon,z.fragment.appendChild(n),z.fragment.appendChild(i)),t.subtext&&((e=z.subtext.cloneNode(!1)).textContent=t.subtext,s.appendChild(e)),z.fragment.appendChild(s),z.fragment}};function G(t,e){var i=this;r.useDefault||(L.valHooks.select.set=r._set,r.useDefault=!0),this.$element=L(t),this.$newElement=null,this.$button=null,this.$menu=null,this.options=e,this.selectpicker={main:{},search:{},current:{},view:{},isSearching:!1,keydown:{keyHistory:"",resetKeyHistory:{start:function(){return setTimeout(function(){i.selectpicker.keydown.keyHistory=""},800)}}}},this.sizeInfo={},null===this.options.title&&(this.options.title=this.$element.attr("title"));var n=this.options.windowPadding;"number"==typeof n&&(this.options.windowPadding=[n,n,n,n]),this.val=G.prototype.val,this.render=G.prototype.render,this.refresh=G.prototype.refresh,this.setStyle=G.prototype.setStyle,this.selectAll=G.prototype.selectAll,this.deselectAll=G.prototype.deselectAll,this.destroy=G.prototype.destroy,this.remove=G.prototype.remove,this.show=G.prototype.show,this.hide=G.prototype.hide,this.init()}function V(t){var r,l=arguments,c=t;if([].shift.apply(l),!F.success){try{F.full=(L.fn.dropdown.Constructor.VERSION||"").split(" ")[0].split(".")}catch(t){G.BootstrapVersion?F.full=G.BootstrapVersion.split(" ")[0].split("."):(F.full=[F.major,"0","0"],console.warn("There was an issue retrieving Bootstrap's version. Ensure Bootstrap is being loaded before bootstrap-select and there is no namespace collision. If loading Bootstrap asynchronously, the version may need to be manually specified via $.fn.selectpicker.Constructor.BootstrapVersion.",t))}F.major=F.full[0],F.success=!0}if("4"===F.major){var e=[];G.DEFAULTS.style===j.BUTTONCLASS&&e.push({name:"style",className:"BUTTONCLASS"}),G.DEFAULTS.iconBase===j.ICONBASE&&e.push({name:"iconBase",className:"ICONBASE"}),G.DEFAULTS.tickIcon===j.TICKICON&&e.push({name:"tickIcon",className:"TICKICON"}),j.DIVIDER="dropdown-divider",j.SHOW="show",j.BUTTONCLASS="btn-light",j.POPOVERHEADER="popover-header",j.ICONBASE="",j.TICKICON="bs-ok-default";for(var i=0;i<e.length;i++){t=e[i];G.DEFAULTS[t.name]=j[t.className]}}var n=this.each(function(){var t=L(this);if(t.is("select")){var e=t.data("selectpicker"),i="object"==typeof c&&c;if(e){if(i)for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e.options[n]=i[n])}else{var s=t.data();for(var o in s)Object.prototype.hasOwnProperty.call(s,o)&&-1!==L.inArray(o,h)&&delete s[o];var a=L.extend({},G.DEFAULTS,L.fn.selectpicker.defaults||{},s,i);a.template=L.extend({},G.DEFAULTS.template,L.fn.selectpicker.defaults?L.fn.selectpicker.defaults.template:{},s.template,i.template),t.data("selectpicker",e=new G(this,a))}"string"==typeof c&&(r=e[c]instanceof Function?e[c].apply(e,l):e.options[c])}});return void 0!==r?r:n}G.VERSION="1.13.17",G.DEFAULTS={noneSelectedText:"Nothing selected",noneResultsText:"No results matched {0}",countSelectedText:function(t){return 1==t?"{0} item selected":"{0} items selected"},maxOptionsText:function(t,e){return[1==t?"Limit reached ({n} item max)":"Limit reached ({n} items max)",1==e?"Group limit reached ({n} item max)":"Group limit reached ({n} items max)"]},selectAllText:"Select All",deselectAllText:"Deselect All",doneButton:!1,doneButtonText:"Close",multipleSeparator:", ",styleBase:"btn",style:j.BUTTONCLASS,size:"auto",title:null,selectedTextFormat:"values",width:!1,container:!1,hideDisabled:!1,showSubtext:!1,showIcon:!0,showContent:!0,dropupAuto:!0,header:!1,liveSearch:!1,liveSearchPlaceholder:null,liveSearchNormalize:!1,liveSearchStyle:"contains",actionsBox:!1,iconBase:j.ICONBASE,tickIcon:j.TICKICON,showTick:!1,template:{caret:'<span class="caret"></span>'},maxOptions:!1,mobile:!1,selectOnTab:!1,dropdownAlignRight:!1,windowPadding:0,virtualScroll:600,display:!1,sanitize:!0,sanitizeFn:null,whiteList:t},G.prototype={constructor:G,init:function(){var i=this,t=this.$element.attr("id");R++,this.selectId="bs-select-"+R,this.$element[0].classList.add("bs-select-hidden"),this.multiple=this.$element.prop("multiple"),this.autofocus=this.$element.prop("autofocus"),this.$element[0].classList.contains("show-tick")&&(this.options.showTick=!0),this.$newElement=this.createDropdown(),this.buildData(),this.$element.after(this.$newElement).prependTo(this.$newElement),this.$button=this.$newElement.children("button"),this.$menu=this.$newElement.children(W.MENU),this.$menuInner=this.$menu.children(".inner"),this.$searchbox=this.$menu.find("input"),this.$element[0].classList.remove("bs-select-hidden"),!0===this.options.dropdownAlignRight&&this.$menu[0].classList.add(j.MENURIGHT),void 0!==t&&this.$button.attr("data-id",t),this.checkDisabled(),this.clickListener(),this.options.liveSearch?(this.liveSearchListener(),this.focusedParent=this.$searchbox[0]):this.focusedParent=this.$menuInner[0],this.setStyle(),this.render(),this.setWidth(),this.options.container?this.selectPosition():this.$element.on("hide"+H,function(){var t,e;i.isVirtual()&&(e=(t=i.$menuInner[0]).firstChild.cloneNode(!1),t.replaceChild(e,t.firstChild),t.scrollTop=0)}),this.$menu.data("this",this),this.$newElement.data("this",this),this.options.mobile&&this.mobile(),this.$newElement.on({"hide.bs.dropdown":function(t){i.$element.trigger("hide"+H,t)},"hidden.bs.dropdown":function(t){i.$element.trigger("hidden"+H,t)},"show.bs.dropdown":function(t){i.$element.trigger("show"+H,t)},"shown.bs.dropdown":function(t){i.$element.trigger("shown"+H,t)}}),i.$element[0].hasAttribute("required")&&this.$element.on("invalid"+H,function(){i.$button[0].classList.add("bs-invalid"),i.$element.on("shown"+H+".invalid",function(){i.$element.val(i.$element.val()).off("shown"+H+".invalid")}).on("rendered"+H,function(){this.validity.valid&&i.$button[0].classList.remove("bs-invalid"),i.$element.off("rendered"+H)}),i.$button.on("blur"+H,function(){i.$element.trigger("focus").trigger("blur"),i.$button.off("blur"+H)})}),setTimeout(function(){i.buildList(),i.$element.trigger("loaded"+H)})},createDropdown:function(){var t=this.multiple||this.options.showTick?" show-tick":"",e=this.multiple?' aria-multiselectable="true"':"",i="",n=this.autofocus?" autofocus":"";F.major<4&&this.$element.parent().hasClass("input-group")&&(i=" input-group-btn");var s,o="",a="",r="",l="";return this.options.header&&(o='<div class="'+j.POPOVERHEADER+'"><button type="button" class="close" aria-hidden="true">&times;</button>'+this.options.header+"</div>"),this.options.liveSearch&&(a='<div class="bs-searchbox"><input type="search" class="form-control" autocomplete="off"'+(null===this.options.liveSearchPlaceholder?"":' placeholder="'+$(this.options.liveSearchPlaceholder)+'"')+' role="combobox" aria-label="Search" aria-controls="'+this.selectId+'" aria-autocomplete="list"></div>'),this.multiple&&this.options.actionsBox&&(r='<div class="bs-actionsbox"><div class="btn-group btn-group-sm btn-block"><button type="button" class="actions-btn bs-select-all btn '+j.BUTTONCLASS+'">'+this.options.selectAllText+'</button><button type="button" class="actions-btn bs-deselect-all btn '+j.BUTTONCLASS+'">'+this.options.deselectAllText+"</button></div></div>"),this.multiple&&this.options.doneButton&&(l='<div class="bs-donebutton"><div class="btn-group btn-block"><button type="button" class="btn btn-sm '+j.BUTTONCLASS+'">'+this.options.doneButtonText+"</button></div></div>"),s='<div class="dropdown bootstrap-select'+t+i+'"><button type="button" tabindex="-1" class="'+this.options.styleBase+' dropdown-toggle" '+("static"===this.options.display?'data-display="static"':"")+'data-toggle="dropdown"'+n+' role="combobox" aria-owns="'+this.selectId+'" aria-haspopup="listbox" aria-expanded="false"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner"></div></div> </div>'+("4"===F.major?"":'<span class="bs-caret">'+this.options.template.caret+"</span>")+'</button><div class="'+j.MENU+" "+("4"===F.major?"":j.SHOW)+'">'+o+a+r+'<div class="inner '+j.SHOW+'" role="listbox" id="'+this.selectId+'" tabindex="-1" '+e+'><ul class="'+j.MENU+" inner "+("4"===F.major?j.SHOW:"")+'" role="presentation"></ul></div>'+l+"</div></div>",L(s)},setPositionData:function(){this.selectpicker.view.canHighlight=[];for(var t=this.selectpicker.view.size=0;t<this.selectpicker.current.data.length;t++){var e=this.selectpicker.current.data[t],i=!0;"divider"===e.type?(i=!1,e.height=this.sizeInfo.dividerHeight):"optgroup-label"===e.type?(i=!1,e.height=this.sizeInfo.dropdownHeaderHeight):e.height=this.sizeInfo.liHeight,e.disabled&&(i=!1),this.selectpicker.view.canHighlight.push(i),i&&(this.selectpicker.view.size++,e.posinset=this.selectpicker.view.size),e.position=(0===t?0:this.selectpicker.current.data[t-1].position)+e.height}},isVirtual:function(){return!1!==this.options.virtualScroll&&this.selectpicker.main.elements.length>=this.options.virtualScroll||!0===this.options.virtualScroll},createView:function(O,t,e){var P,M,i,n,s,o,N=this,a=0,B=[];function r(t,e){var i,n,s,o,a,r,l,c,h=N.selectpicker.current.elements.length,d=[],u=!0,p=N.isVirtual();N.selectpicker.view.scrollTop=t,i=Math.ceil(N.sizeInfo.menuInnerHeight/N.sizeInfo.liHeight*1.5),n=Math.round(h/i)||1;for(var f,m,g,v,b=0;b<n;b++){var y=b===n-1?h:(b+1)*i;if(d[b]=[b*i+(b?1:0),y],!h)break;void 0===a&&t-1<=N.selectpicker.current.data[y-1].position-N.sizeInfo.menuInnerHeight&&(a=b)}if(void 0===a&&(a=0),r=[N.selectpicker.view.position0,N.selectpicker.view.position1],s=Math.max(0,a-1),o=Math.min(n-1,a+1),N.selectpicker.view.position0=!1!==p&&Math.max(0,d[s][0])||0,N.selectpicker.view.position1=!1===p?h:Math.min(h,d[o][1])||0,l=r[0]!==N.selectpicker.view.position0||r[1]!==N.selectpicker.view.position1,void 0!==N.activeIndex&&(M=N.selectpicker.main.elements[N.prevActiveIndex],B=N.selectpicker.main.elements[N.activeIndex],P=N.selectpicker.main.elements[N.selectedIndex],e&&(N.activeIndex!==N.selectedIndex&&N.defocusItem(B),N.activeIndex=void 0),N.activeIndex&&N.activeIndex!==N.selectedIndex&&N.defocusItem(P)),void 0!==N.prevActiveIndex&&N.prevActiveIndex!==N.activeIndex&&N.prevActiveIndex!==N.selectedIndex&&N.defocusItem(M),(e||l)&&(c=N.selectpicker.view.visibleElements?N.selectpicker.view.visibleElements.slice():[],N.selectpicker.view.visibleElements=!1===p?N.selectpicker.current.elements:N.selectpicker.current.elements.slice(N.selectpicker.view.position0,N.selectpicker.view.position1),N.setOptionStatus(),(O||!1===p&&e)&&(f=c,m=N.selectpicker.view.visibleElements,u=!(f.length===m.length&&f.every(function(t,e){return t===m[e]}))),(e||!0===p)&&u)){var _,w,C=N.$menuInner[0],k=document.createDocumentFragment(),$=C.firstChild.cloneNode(!1),x=N.selectpicker.view.visibleElements,E=[];C.replaceChild($,C.firstChild);for(var T,D,b=0,I=x.length;b<I;b++){var A,L,S=x[b];N.options.sanitize&&(A=S.lastChild)&&(L=N.selectpicker.current.data[b+N.selectpicker.view.position0])&&L.content&&!L.sanitized&&(E.push(A),L.sanitized=!0),k.appendChild(S)}N.options.sanitize&&E.length&&U(E,N.options.whiteList,N.options.sanitizeFn),!0===p?(_=0===N.selectpicker.view.position0?0:N.selectpicker.current.data[N.selectpicker.view.position0-1].position,w=N.selectpicker.view.position1>h-1?0:N.selectpicker.current.data[h-1].position-N.selectpicker.current.data[N.selectpicker.view.position1-1].position,C.firstChild.style.marginTop=_+"px",C.firstChild.style.marginBottom=w+"px"):(C.firstChild.style.marginTop=0,C.firstChild.style.marginBottom=0),C.firstChild.appendChild(k),!0===p&&N.sizeInfo.hasScrollBar&&(T=C.firstChild.offsetWidth,e&&T<N.sizeInfo.menuInnerInnerWidth&&N.sizeInfo.totalMenuWidth>N.sizeInfo.selectWidth?C.firstChild.style.minWidth=N.sizeInfo.menuInnerInnerWidth+"px":T>N.sizeInfo.menuInnerInnerWidth&&(N.$menu[0].style.minWidth=0,(D=C.firstChild.offsetWidth)>N.sizeInfo.menuInnerInnerWidth&&(N.sizeInfo.menuInnerInnerWidth=D,C.firstChild.style.minWidth=N.sizeInfo.menuInnerInnerWidth+"px"),N.$menu[0].style.minWidth=""))}N.prevActiveIndex=N.activeIndex,N.options.liveSearch?O&&e&&(g=0,N.selectpicker.view.canHighlight[g]||(g=1+N.selectpicker.view.canHighlight.slice(1).indexOf(!0)),v=N.selectpicker.view.visibleElements[g],N.defocusItem(N.selectpicker.view.currentActive),N.activeIndex=(N.selectpicker.current.data[g]||{}).index,N.focusItem(v)):N.$menuInner.trigger("focus")}this.selectpicker.isSearching=O,this.selectpicker.current=O?this.selectpicker.search:this.selectpicker.main,this.setPositionData(),t&&(e?a=this.$menuInner[0].scrollTop:N.multiple||("number"!=typeof(n=((i=N.$element[0]).options[i.selectedIndex]||{}).liIndex)||!1===N.options.size||(o=(s=N.selectpicker.main.data[n])&&s.position)&&(a=o-(N.sizeInfo.menuInnerHeight+N.sizeInfo.liHeight)/2))),r(a,!0),this.$menuInner.off("scroll.createView").on("scroll.createView",function(t,e){N.noScroll||r(this.scrollTop,e),N.noScroll=!1}),L(window).off("resize"+H+"."+this.selectId+".createView").on("resize"+H+"."+this.selectId+".createView",function(){N.$newElement.hasClass(j.SHOW)&&r(N.$menuInner[0].scrollTop)})},focusItem:function(t,e,i){var n;t&&(e=e||this.selectpicker.main.data[this.activeIndex],(n=t.firstChild)&&(n.setAttribute("aria-setsize",this.selectpicker.view.size),n.setAttribute("aria-posinset",e.posinset),!0!==i&&(this.focusedParent.setAttribute("aria-activedescendant",n.id),t.classList.add("active"),n.classList.add("active"))))},defocusItem:function(t){t&&(t.classList.remove("active"),t.firstChild&&t.firstChild.classList.remove("active"))},setPlaceholder:function(){var t,e,i,n,s,o,a=this,r=!1;return this.options.title&&!this.multiple&&(this.selectpicker.view.titleOption||(this.selectpicker.view.titleOption=document.createElement("option")),r=!0,t=this.$element[0],e=!1,i=!this.selectpicker.view.titleOption.parentNode,n=t.selectedIndex,s=t.options[n],o=window.performance&&window.performance.getEntriesByType("navigation"),i&&(this.selectpicker.view.titleOption.className="bs-title-option",this.selectpicker.view.titleOption.value="",e=!s||0===n&&!1===s.defaultSelected&&void 0===this.$element.data("selected")),!i&&0===this.selectpicker.view.titleOption.index||t.insertBefore(this.selectpicker.view.titleOption,t.firstChild),e&&o.length&&"back_forward"!==o[0].type?t.selectedIndex=0:"complete"!==document.readyState&&window.addEventListener("pageshow",function(){a.selectpicker.view.displayedValue!==t.value&&a.render()})),r},buildData:function(){var u=':not([hidden]):not([data-hidden="true"])',p=[],f=0,m=this.setPlaceholder()?1:0;this.options.hideDisabled&&(u+=":not(:disabled)");var t=this.$element[0].querySelectorAll("select > *"+u);function g(t){var e=p[p.length-1];e&&"divider"===e.type&&(e.optID||t.optID)||((t=t||{}).type="divider",p.push(t))}function v(t,e){var i,n,s,o;(e=e||{}).divider="true"===t.getAttribute("data-divider"),e.divider?g({optID:e.optID}):(i=p.length,s=(n=t.style.cssText)?$(n):"",o=(t.className||"")+(e.optgroupClass||""),e.optID&&(o="opt "+o),e.optionClass=o.trim(),e.inlineStyle=s,e.text=t.textContent,e.content=t.getAttribute("data-content"),e.tokens=t.getAttribute("data-tokens"),e.subtext=t.getAttribute("data-subtext"),e.icon=t.getAttribute("data-icon"),t.liIndex=i,e.display=e.content||e.text,e.type="option",e.index=i,e.option=t,e.selected=!!t.selected,e.disabled=e.disabled||!!t.disabled,p.push(e))}for(var e=t.length,i=m;i<e;i++){var n=t[i];"OPTGROUP"!==n.tagName?v(n,{}):function(t,e){var i=e[t],n=!(t-1<m)&&e[t-1],s=e[t+1],o=i.querySelectorAll("option"+u);if(o.length){var a,r,l={display:$(i.label),subtext:i.getAttribute("data-subtext"),icon:i.getAttribute("data-icon"),type:"optgroup-label",optgroupClass:" "+(i.className||"")};f++,n&&g({optID:f}),l.optID=f,p.push(l);for(var c=0,h=o.length;c<h;c++){var d=o[c];0===c&&(r=(a=p.length-1)+h),v(d,{headerIndex:a,lastIndex:r,optID:l.optID,optgroupClass:l.optgroupClass,disabled:i.disabled})}s&&g({optID:f})}}(i,t)}this.selectpicker.main.data=this.selectpicker.current.data=p},buildList:function(){var n=this,t=this.selectpicker.main.data,s=[],o=0;!n.options.showTick&&!n.multiple||z.checkMark.parentNode||(z.checkMark.className=this.options.iconBase+" "+n.options.tickIcon+" check-mark",z.a.appendChild(z.checkMark));for(var e=t.length,i=0;i<e;i++){!function(t){var e,i=0;switch(t.type){case"divider":e=K.li(!1,j.DIVIDER,t.optID?t.optID+"div":void 0);break;case"option":(e=K.li(K.a(K.text.call(n,t),t.optionClass,t.inlineStyle),"",t.optID)).firstChild&&(e.firstChild.id=n.selectId+"-"+t.index);break;case"optgroup-label":e=K.li(K.label.call(n,t),"dropdown-header"+t.optgroupClass,t.optID)}t.element=e,s.push(e),t.display&&(i+=t.display.length),t.subtext&&(i+=t.subtext.length),t.icon&&(i+=1),o<i&&(o=i,n.selectpicker.view.widestOption=s[s.length-1])}(t[i])}this.selectpicker.main.elements=this.selectpicker.current.elements=s},findLis:function(){return this.$menuInner.find(".inner > li")},render:function(){var t,e,i,n=this,s=this.$element[0],o=this.setPlaceholder()&&0===s.selectedIndex,a=D(s,this.options.hideDisabled),r=a.length,l=this.$button[0],c=l.querySelector(".filter-option-inner-inner"),h=document.createTextNode(this.options.multipleSeparator),d=z.fragment.cloneNode(!1),u=!1;if(l.classList.toggle("bs-placeholder",n.multiple?!r:!I(s,a)),n.multiple||1!==a.length||(n.selectpicker.view.displayedValue=I(s,a)),"static"===this.options.selectedTextFormat)d=K.text.call(this,{text:this.options.title},!0);else if(!1===(this.multiple&&-1!==this.options.selectedTextFormat.indexOf("count")&&1<r&&(1<(t=this.options.selectedTextFormat.split(">")).length&&r>t[1]||1===t.length&&2<=r))){if(!o){for(var p=0;p<r&&p<50;p++){var f=a[p],m=this.selectpicker.main.data[f.liIndex],g={};this.multiple&&0<p&&d.appendChild(h.cloneNode(!1)),f.title?g.text=f.title:m&&(m.content&&n.options.showContent?(g.content=m.content.toString(),u=!0):(n.options.showIcon&&(g.icon=m.icon),n.options.showSubtext&&!n.multiple&&m.subtext&&(g.subtext=" "+m.subtext),g.text=f.textContent.trim())),d.appendChild(K.text.call(this,g,!0))}49<r&&d.appendChild(document.createTextNode("..."))}}else{var v=':not([hidden]):not([data-hidden="true"]):not([data-divider="true"])';this.options.hideDisabled&&(v+=":not(:disabled)");var b=this.$element[0].querySelectorAll("select > option"+v+", optgroup"+v+" option"+v).length,y="function"==typeof this.options.countSelectedText?this.options.countSelectedText(r,b):this.options.countSelectedText,d=K.text.call(this,{text:y.replace("{0}",r.toString()).replace("{1}",b.toString())},!0)}null==this.options.title&&(this.options.title=this.$element.attr("title")),d.childNodes.length||(d=K.text.call(this,{text:void 0!==this.options.title?this.options.title:this.options.noneSelectedText},!0)),l.title=d.textContent.replace(/<[^>]*>?/g,"").trim(),this.options.sanitize&&u&&U([d],n.options.whiteList,n.options.sanitizeFn),c.innerHTML="",c.appendChild(d),F.major<4&&this.$newElement[0].classList.contains("bs3-has-addon")&&(e=l.querySelector(".filter-expand"),(i=c.cloneNode(!0)).className="filter-expand",e?l.replaceChild(i,e):l.appendChild(i)),this.$element.trigger("rendered"+H)},setStyle:function(t,e){var i,n=this.$button[0],s=this.$newElement[0],o=this.options.style.trim();this.$element.attr("class")&&this.$newElement.addClass(this.$element.attr("class").replace(/selectpicker|mobile-device|bs-select-hidden|validate\[.*\]/gi,"")),F.major<4&&(s.classList.add("bs3"),s.parentNode.classList&&s.parentNode.classList.contains("input-group")&&(s.previousElementSibling||s.nextElementSibling)&&(s.previousElementSibling||s.nextElementSibling).classList.contains("input-group-addon")&&s.classList.add("bs3-has-addon")),i=t?t.trim():o,"add"==e?i&&n.classList.add.apply(n.classList,i.split(" ")):"remove"==e?i&&n.classList.remove.apply(n.classList,i.split(" ")):(o&&n.classList.remove.apply(n.classList,o.split(" ")),i&&n.classList.add.apply(n.classList,i.split(" ")))},liHeight:function(t){if(t||!1!==this.options.size&&!Object.keys(this.sizeInfo).length){var e,i,n=z.div.cloneNode(!1),s=z.div.cloneNode(!1),o=z.div.cloneNode(!1),a=document.createElement("ul"),r=z.li.cloneNode(!1),l=z.li.cloneNode(!1),c=z.a.cloneNode(!1),h=z.span.cloneNode(!1),d=this.options.header&&0<this.$menu.find("."+j.POPOVERHEADER).length?this.$menu.find("."+j.POPOVERHEADER)[0].cloneNode(!0):null,u=this.options.liveSearch?z.div.cloneNode(!1):null,p=this.options.actionsBox&&this.multiple&&0<this.$menu.find(".bs-actionsbox").length?this.$menu.find(".bs-actionsbox")[0].cloneNode(!0):null,f=this.options.doneButton&&this.multiple&&0<this.$menu.find(".bs-donebutton").length?this.$menu.find(".bs-donebutton")[0].cloneNode(!0):null,m=this.$element.find("option")[0];if(this.sizeInfo.selectWidth=this.$newElement[0].offsetWidth,h.className="text",c.className="dropdown-item "+(m?m.className:""),n.className=this.$menu[0].parentNode.className+" "+j.SHOW,n.style.width=0,"auto"===this.options.width&&(s.style.minWidth=0),s.className=j.MENU+" "+j.SHOW,o.className="inner "+j.SHOW,a.className=j.MENU+" inner "+("4"===F.major?j.SHOW:""),r.className=j.DIVIDER,l.className="dropdown-header",h.appendChild(document.createTextNode("​")),this.selectpicker.current.data.length)for(var g=0;g<this.selectpicker.current.data.length;g++){var v=this.selectpicker.current.data[g];if("option"===v.type){e=v.element;break}}else e=z.li.cloneNode(!1),c.appendChild(h),e.appendChild(c);l.appendChild(h.cloneNode(!0)),this.selectpicker.view.widestOption&&a.appendChild(this.selectpicker.view.widestOption.cloneNode(!0)),a.appendChild(e),a.appendChild(r),a.appendChild(l),d&&s.appendChild(d),u&&(i=document.createElement("input"),u.className="bs-searchbox",i.className="form-control",u.appendChild(i),s.appendChild(u)),p&&s.appendChild(p),o.appendChild(a),s.appendChild(o),f&&s.appendChild(f),n.appendChild(s),document.body.appendChild(n);var b,y=e.offsetHeight,_=l?l.offsetHeight:0,w=d?d.offsetHeight:0,C=u?u.offsetHeight:0,k=p?p.offsetHeight:0,$=f?f.offsetHeight:0,x=L(r).outerHeight(!0),E=!!window.getComputedStyle&&window.getComputedStyle(s),T=s.offsetWidth,D=E?null:L(s),I={vert:S(E?E.paddingTop:D.css("paddingTop"))+S(E?E.paddingBottom:D.css("paddingBottom"))+S(E?E.borderTopWidth:D.css("borderTopWidth"))+S(E?E.borderBottomWidth:D.css("borderBottomWidth")),horiz:S(E?E.paddingLeft:D.css("paddingLeft"))+S(E?E.paddingRight:D.css("paddingRight"))+S(E?E.borderLeftWidth:D.css("borderLeftWidth"))+S(E?E.borderRightWidth:D.css("borderRightWidth"))},A={vert:I.vert+S(E?E.marginTop:D.css("marginTop"))+S(E?E.marginBottom:D.css("marginBottom"))+2,horiz:I.horiz+S(E?E.marginLeft:D.css("marginLeft"))+S(E?E.marginRight:D.css("marginRight"))+2};o.style.overflowY="scroll",b=s.offsetWidth-T,document.body.removeChild(n),this.sizeInfo.liHeight=y,this.sizeInfo.dropdownHeaderHeight=_,this.sizeInfo.headerHeight=w,this.sizeInfo.searchHeight=C,this.sizeInfo.actionsHeight=k,this.sizeInfo.doneButtonHeight=$,this.sizeInfo.dividerHeight=x,this.sizeInfo.menuPadding=I,this.sizeInfo.menuExtras=A,this.sizeInfo.menuWidth=T,this.sizeInfo.menuInnerInnerWidth=T-I.horiz,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth,this.sizeInfo.scrollBarWidth=b,this.sizeInfo.selectHeight=this.$newElement[0].offsetHeight,this.setPositionData()}},getSelectPosition:function(){var t,e=L(window),i=this.$newElement.offset(),n=L(this.options.container);this.options.container&&n.length&&!n.is("body")?((t=n.offset()).top+=parseInt(n.css("borderTopWidth")),t.left+=parseInt(n.css("borderLeftWidth"))):t={top:0,left:0};var s=this.options.windowPadding;this.sizeInfo.selectOffsetTop=i.top-t.top-e.scrollTop(),this.sizeInfo.selectOffsetBot=e.height()-this.sizeInfo.selectOffsetTop-this.sizeInfo.selectHeight-t.top-s[2],this.sizeInfo.selectOffsetLeft=i.left-t.left-e.scrollLeft(),this.sizeInfo.selectOffsetRight=e.width()-this.sizeInfo.selectOffsetLeft-this.sizeInfo.selectWidth-t.left-s[1],this.sizeInfo.selectOffsetTop-=s[0],this.sizeInfo.selectOffsetLeft-=s[3]},setMenuSize:function(){this.getSelectPosition();var t,e,i,n,s,o,a,r,l=this.sizeInfo.selectWidth,c=this.sizeInfo.liHeight,h=this.sizeInfo.headerHeight,d=this.sizeInfo.searchHeight,u=this.sizeInfo.actionsHeight,p=this.sizeInfo.doneButtonHeight,f=this.sizeInfo.dividerHeight,m=this.sizeInfo.menuPadding,g=0;if(this.options.dropupAuto&&(a=c*this.selectpicker.current.elements.length+m.vert,r=this.sizeInfo.selectOffsetTop-this.sizeInfo.selectOffsetBot>this.sizeInfo.menuExtras.vert&&a+this.sizeInfo.menuExtras.vert+50>this.sizeInfo.selectOffsetBot,!0===this.selectpicker.isSearching&&(r=this.selectpicker.dropup),this.$newElement.toggleClass(j.DROPUP,r),this.selectpicker.dropup=r),"auto"===this.options.size)n=3<this.selectpicker.current.elements.length?3*this.sizeInfo.liHeight+this.sizeInfo.menuExtras.vert-2:0,e=this.sizeInfo.selectOffsetBot-this.sizeInfo.menuExtras.vert,i=n+h+d+u+p,o=Math.max(n-m.vert,0),this.$newElement.hasClass(j.DROPUP)&&(e=this.sizeInfo.selectOffsetTop-this.sizeInfo.menuExtras.vert),t=(s=e)-h-d-u-p-m.vert;else if(this.options.size&&"auto"!=this.options.size&&this.selectpicker.current.elements.length>this.options.size){for(var v=0;v<this.options.size;v++)"divider"===this.selectpicker.current.data[v].type&&g++;t=(e=c*this.options.size+g*f+m.vert)-m.vert,s=e+h+d+u+p,i=o=""}this.$menu.css({"max-height":s+"px",overflow:"hidden","min-height":i+"px"}),this.$menuInner.css({"max-height":t+"px","overflow-y":"auto","min-height":o+"px"}),this.sizeInfo.menuInnerHeight=Math.max(t,1),this.selectpicker.current.data.length&&this.selectpicker.current.data[this.selectpicker.current.data.length-1].position>this.sizeInfo.menuInnerHeight&&(this.sizeInfo.hasScrollBar=!0,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth+this.sizeInfo.scrollBarWidth),"auto"===this.options.dropdownAlignRight&&this.$menu.toggleClass(j.MENURIGHT,this.sizeInfo.selectOffsetLeft>this.sizeInfo.selectOffsetRight&&this.sizeInfo.selectOffsetRight<this.sizeInfo.totalMenuWidth-l),this.dropdown&&this.dropdown._popper&&this.dropdown._popper.update()},setSize:function(t){var e,i;this.liHeight(t),this.options.header&&this.$menu.css("padding-top",0),!1!==this.options.size&&(e=this,i=L(window),this.setMenuSize(),this.options.liveSearch&&this.$searchbox.off("input.setMenuSize propertychange.setMenuSize").on("input.setMenuSize propertychange.setMenuSize",function(){return e.setMenuSize()}),"auto"===this.options.size?i.off("resize"+H+"."+this.selectId+".setMenuSize scroll"+H+"."+this.selectId+".setMenuSize").on("resize"+H+"."+this.selectId+".setMenuSize scroll"+H+"."+this.selectId+".setMenuSize",function(){return e.setMenuSize()}):this.options.size&&"auto"!=this.options.size&&this.selectpicker.current.elements.length>this.options.size&&i.off("resize"+H+"."+this.selectId+".setMenuSize scroll"+H+"."+this.selectId+".setMenuSize")),this.createView(!1,!0,t)},setWidth:function(){var i=this;"auto"===this.options.width?requestAnimationFrame(function(){i.$menu.css("min-width","0"),i.$element.on("loaded"+H,function(){i.liHeight(),i.setMenuSize();var t=i.$newElement.clone().appendTo("body"),e=t.css("width","auto").children("button").outerWidth();t.remove(),i.sizeInfo.selectWidth=Math.max(i.sizeInfo.totalMenuWidth,e),i.$newElement.css("width",i.sizeInfo.selectWidth+"px")})}):"fit"===this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width","").addClass("fit-width")):this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width",this.options.width)):(this.$menu.css("min-width",""),this.$newElement.css("width","")),this.$newElement.hasClass("fit-width")&&"fit"!==this.options.width&&this.$newElement[0].classList.remove("fit-width")},selectPosition:function(){this.$bsContainer=L('<div class="bs-container" />');function t(t){var e={},i=a.options.display||!!L.fn.dropdown.Constructor.Default&&L.fn.dropdown.Constructor.Default.display;a.$bsContainer.addClass(t.attr("class").replace(/form-control|fit-width/gi,"")).toggleClass(j.DROPUP,t.hasClass(j.DROPUP)),n=t.offset(),r.is("body")?s={top:0,left:0}:((s=r.offset()).top+=parseInt(r.css("borderTopWidth"))-r.scrollTop(),s.left+=parseInt(r.css("borderLeftWidth"))-r.scrollLeft()),o=t.hasClass(j.DROPUP)?0:t[0].offsetHeight,(F.major<4||"static"===i)&&(e.top=n.top-s.top+o,e.left=n.left-s.left),e.width=t[0].offsetWidth,a.$bsContainer.css(e)}var n,s,o,a=this,r=L(this.options.container);this.$button.on("click.bs.dropdown.data-api",function(){a.isDisabled()||(t(a.$newElement),a.$bsContainer.appendTo(a.options.container).toggleClass(j.SHOW,!a.$button.hasClass(j.SHOW)).append(a.$menu))}),L(window).off("resize"+H+"."+this.selectId+" scroll"+H+"."+this.selectId).on("resize"+H+"."+this.selectId+" scroll"+H+"."+this.selectId,function(){a.$newElement.hasClass(j.SHOW)&&t(a.$newElement)}),this.$element.on("hide"+H,function(){a.$menu.data("height",a.$menu.height()),a.$bsContainer.detach()})},setOptionStatus:function(t){var e=this;if(e.noScroll=!1,e.selectpicker.view.visibleElements&&e.selectpicker.view.visibleElements.length)for(var i=0;i<e.selectpicker.view.visibleElements.length;i++){var n=e.selectpicker.current.data[i+e.selectpicker.view.position0],s=n.option;s&&(!0!==t&&e.setDisabled(n.index,n.disabled),e.setSelected(n.index,s.selected))}},setSelected:function(t,e){var i,n,s=this.selectpicker.main.elements[t],o=this.selectpicker.main.data[t],a=void 0!==this.activeIndex,r=this.activeIndex===t||e&&!this.multiple&&!a;o.selected=e,n=s.firstChild,e&&(this.selectedIndex=t),s.classList.toggle("selected",e),r?(this.focusItem(s,o),this.selectpicker.view.currentActive=s,this.activeIndex=t):this.defocusItem(s),n&&(n.classList.toggle("selected",e),e?n.setAttribute("aria-selected",!0):this.multiple?n.setAttribute("aria-selected",!1):n.removeAttribute("aria-selected")),r||a||!e||void 0===this.prevActiveIndex||(i=this.selectpicker.main.elements[this.prevActiveIndex],this.defocusItem(i))},setDisabled:function(t,e){var i,n=this.selectpicker.main.elements[t];this.selectpicker.main.data[t].disabled=e,i=n.firstChild,n.classList.toggle(j.DISABLED,e),i&&("4"===F.major&&i.classList.toggle(j.DISABLED,e),e?(i.setAttribute("aria-disabled",e),i.setAttribute("tabindex",-1)):(i.removeAttribute("aria-disabled"),i.setAttribute("tabindex",0)))},isDisabled:function(){return this.$element[0].disabled},checkDisabled:function(){this.isDisabled()?(this.$newElement[0].classList.add(j.DISABLED),this.$button.addClass(j.DISABLED).attr("aria-disabled",!0)):this.$button[0].classList.contains(j.DISABLED)&&(this.$newElement[0].classList.remove(j.DISABLED),this.$button.removeClass(j.DISABLED).attr("aria-disabled",!1))},clickListener:function(){var T=this,e=L(document);function t(){T.options.liveSearch?T.$searchbox.trigger("focus"):T.$menuInner.trigger("focus")}function i(){T.dropdown&&T.dropdown._popper&&T.dropdown._popper.state.isCreated?t():requestAnimationFrame(i)}e.data("spaceSelect",!1),this.$button.on("keyup",function(t){/(32)/.test(t.keyCode.toString(10))&&e.data("spaceSelect")&&(t.preventDefault(),e.data("spaceSelect",!1))}),this.$newElement.on("show.bs.dropdown",function(){3<F.major&&!T.dropdown&&(T.dropdown=T.$button.data("bs.dropdown"),T.dropdown._menu=T.$menu[0])}),this.$button.on("click.bs.dropdown.data-api",function(){T.$newElement.hasClass(j.SHOW)||T.setSize()}),this.$element.on("shown"+H,function(){T.$menuInner[0].scrollTop!==T.selectpicker.view.scrollTop&&(T.$menuInner[0].scrollTop=T.selectpicker.view.scrollTop),3<F.major?requestAnimationFrame(i):t()}),this.$menuInner.on("mouseenter","li a",function(t){var e=this.parentElement,i=T.isVirtual()?T.selectpicker.view.position0:0,n=Array.prototype.indexOf.call(e.parentElement.children,e),s=T.selectpicker.current.data[n+i];T.focusItem(e,s,!0)}),this.$menuInner.on("click","li a",function(t,e){var i=L(this),n=T.$element[0],s=T.isVirtual()?T.selectpicker.view.position0:0,o=T.selectpicker.current.data[i.parent().index()+s],a=o.index,r=I(n),l=n.selectedIndex,c=n.options[l],h=!0;if(T.multiple&&1!==T.options.maxOptions&&t.stopPropagation(),t.preventDefault(),!T.isDisabled()&&!i.parent().hasClass(j.DISABLED)){var d=o.option,u=L(d),p=d.selected,f=u.parent("optgroup"),m=f.find("option"),g=T.options.maxOptions,v=f.data("maxOptions")||!1;if(a===T.activeIndex&&(e=!0),e||(T.prevActiveIndex=T.activeIndex,T.activeIndex=void 0),T.multiple){if(d.selected=!p,T.setSelected(a,!p),i.trigger("blur"),!1!==g||!1!==v){var b=g<D(n).length,y=v<f.find("option:selected").length;if(g&&b||v&&y)if(g&&1==g)n.selectedIndex=-1,d.selected=!0,T.setOptionStatus(!0);else if(v&&1==v){for(var _=0;_<m.length;_++){var w=m[_];w.selected=!1,T.setSelected(w.liIndex,!1)}d.selected=!0,T.setSelected(a,!0)}else{var C="string"==typeof T.options.maxOptionsText?[T.options.maxOptionsText,T.options.maxOptionsText]:T.options.maxOptionsText,k="function"==typeof C?C(g,v):C,$=k[0].replace("{n}",g),x=k[1].replace("{n}",v),E=L('<div class="notify"></div>');k[2]&&($=$.replace("{var}",k[2][1<g?0:1]),x=x.replace("{var}",k[2][1<v?0:1])),d.selected=!1,T.$menu.append(E),g&&b&&(E.append(L("<div>"+$+"</div>")),h=!1,T.$element.trigger("maxReached"+H)),v&&y&&(E.append(L("<div>"+x+"</div>")),h=!1,T.$element.trigger("maxReachedGrp"+H)),setTimeout(function(){T.setSelected(a,!1)},10),E[0].classList.add("fadeOut"),setTimeout(function(){E.remove()},1050)}}}else c&&(c.selected=!1),d.selected=!0,T.setSelected(a,!0);!T.multiple||T.multiple&&1===T.options.maxOptions?T.$button.trigger("focus"):T.options.liveSearch&&T.$searchbox.trigger("focus"),h&&(!T.multiple&&l===n.selectedIndex||(A=[d.index,u.prop("selected"),r],T.$element.triggerNative("change")))}}),this.$menu.on("click","li."+j.DISABLED+" a, ."+j.POPOVERHEADER+", ."+j.POPOVERHEADER+" :not(.close)",function(t){t.currentTarget==this&&(t.preventDefault(),t.stopPropagation(),T.options.liveSearch&&!L(t.target).hasClass("close")?T.$searchbox.trigger("focus"):T.$button.trigger("focus"))}),this.$menuInner.on("click",".divider, .dropdown-header",function(t){t.preventDefault(),t.stopPropagation(),T.options.liveSearch?T.$searchbox.trigger("focus"):T.$button.trigger("focus")}),this.$menu.on("click","."+j.POPOVERHEADER+" .close",function(){T.$button.trigger("click")}),this.$searchbox.on("click",function(t){t.stopPropagation()}),this.$menu.on("click",".actions-btn",function(t){T.options.liveSearch?T.$searchbox.trigger("focus"):T.$button.trigger("focus"),t.preventDefault(),t.stopPropagation(),L(this).hasClass("bs-select-all")?T.selectAll():T.deselectAll()}),this.$button.on("focus"+H,function(t){var e=T.$element[0].getAttribute("tabindex");void 0!==e&&t.originalEvent&&t.originalEvent.isTrusted&&(this.setAttribute("tabindex",e),T.$element[0].setAttribute("tabindex",-1),T.selectpicker.view.tabindex=e)}).on("blur"+H,function(t){void 0!==T.selectpicker.view.tabindex&&t.originalEvent&&t.originalEvent.isTrusted&&(T.$element[0].setAttribute("tabindex",T.selectpicker.view.tabindex),this.setAttribute("tabindex",-1),T.selectpicker.view.tabindex=void 0)}),this.$element.on("change"+H,function(){T.render(),T.$element.trigger("changed"+H,A),A=null}).on("focus"+H,function(){T.options.mobile||T.$button.trigger("focus")})},liveSearchListener:function(){var p=this;this.$button.on("click.bs.dropdown.data-api",function(){p.$searchbox.val()&&p.$searchbox.val("")}),this.$searchbox.on("click.bs.dropdown.data-api focus.bs.dropdown.data-api touchend.bs.dropdown.data-api",function(t){t.stopPropagation()}),this.$searchbox.on("input propertychange",function(){var t=p.$searchbox.val();if(p.selectpicker.search.elements=[],p.selectpicker.search.data=[],t){var e=[],i=t.toUpperCase(),n={},s=[],o=p._searchStyle(),a=p.options.liveSearchNormalize;a&&(i=m(i));for(var r=0;r<p.selectpicker.main.data.length;r++){var l=p.selectpicker.main.data[r];n[r]||(n[r]=C(l,i,o,a)),n[r]&&void 0!==l.headerIndex&&-1===s.indexOf(l.headerIndex)&&(0<l.headerIndex&&(n[l.headerIndex-1]=!0,s.push(l.headerIndex-1)),n[l.headerIndex]=!0,s.push(l.headerIndex),n[l.lastIndex+1]=!0),n[r]&&"optgroup-label"!==l.type&&s.push(r)}for(var r=0,c=s.length;r<c;r++){var h=s[r],d=s[r-1],l=p.selectpicker.main.data[h],u=p.selectpicker.main.data[d];("divider"!==l.type||"divider"===l.type&&u&&"divider"!==u.type&&c-1!==r)&&(p.selectpicker.search.data.push(l),e.push(p.selectpicker.main.elements[h]))}p.activeIndex=void 0,p.noScroll=!0,p.$menuInner.scrollTop(0),p.selectpicker.search.elements=e,p.createView(!0),function(t,e){t.length||(z.noResults.innerHTML=this.options.noneResultsText.replace("{0}",'"'+$(e)+'"'),this.$menuInner[0].firstChild.appendChild(z.noResults))}.call(p,e,t)}else p.$menuInner.scrollTop(0),p.createView(!1)})},_searchStyle:function(){return this.options.liveSearchStyle||"contains"},val:function(t){var e=this.$element[0];if(void 0===t)return this.$element.val();var i,n=I(e);return A=[null,null,n],this.$element.val(t).trigger("changed"+H,A),this.$newElement.hasClass(j.SHOW)&&(this.multiple?this.setOptionStatus(!0):"number"==typeof(i=(e.options[e.selectedIndex]||{}).liIndex)&&(this.setSelected(this.selectedIndex,!1),this.setSelected(i,!0))),this.render(),A=null,this.$element},changeAll:function(t){if(this.multiple){void 0===t&&(t=!0);var e=this.$element[0],i=0,n=0,s=I(e);e.classList.add("bs-select-hidden");for(var o=0,a=this.selectpicker.current.data,r=a.length;o<r;o++){var l=a[o],c=l.option;c&&!l.disabled&&"divider"!==l.type&&(l.selected&&i++,!0===(c.selected=t)&&n++)}e.classList.remove("bs-select-hidden"),i!==n&&(this.setOptionStatus(),A=[null,null,s],this.$element.triggerNative("change"))}},selectAll:function(){return this.changeAll(!0)},deselectAll:function(){return this.changeAll(!1)},toggle:function(t){(t=t||window.event)&&t.stopPropagation(),this.$button.trigger("click.bs.dropdown.data-api")},keydown:function(t){var e,i,n,s,o,a=L(this),r=a.hasClass("dropdown-toggle"),l=(r?a.closest(".dropdown"):a.closest(W.MENU)).data("this"),c=l.findLis(),h=!1,d=t.which===M&&!r&&!l.options.selectOnTab,u=Y.test(t.which)||d,p=l.$menuInner[0].scrollTop,f=!0===l.isVirtual()?l.selectpicker.view.position0:0;if(!(112<=t.which&&t.which<=123))if(!(i=l.$newElement.hasClass(j.SHOW))&&(u||48<=t.which&&t.which<=57||96<=t.which&&t.which<=105||65<=t.which&&t.which<=90)&&(l.$button.trigger("click.bs.dropdown.data-api"),l.options.liveSearch))l.$searchbox.trigger("focus");else{if(t.which===T&&i&&(t.preventDefault(),l.$button.trigger("click.bs.dropdown.data-api").trigger("focus")),u){if(!c.length)return;-1!==(e=(n=l.selectpicker.main.elements[l.activeIndex])?Array.prototype.indexOf.call(n.parentElement.children,n):-1)&&l.defocusItem(n),t.which===N?(-1!==e&&e--,e+f<0&&(e+=c.length),l.selectpicker.view.canHighlight[e+f]||-1===(e=l.selectpicker.view.canHighlight.slice(0,e+f).lastIndexOf(!0)-f)&&(e=c.length-1)):t.which!==B&&!d||(++e+f>=l.selectpicker.view.canHighlight.length&&(e=0),l.selectpicker.view.canHighlight[e+f]||(e=e+1+l.selectpicker.view.canHighlight.slice(e+f+1).indexOf(!0))),t.preventDefault();var m=f+e;t.which===N?0===f&&e===c.length-1?(l.$menuInner[0].scrollTop=l.$menuInner[0].scrollHeight,m=l.selectpicker.current.elements.length-1):h=(o=(s=l.selectpicker.current.data[m]).position-s.height)<p:t.which!==B&&!d||(0===e?m=l.$menuInner[0].scrollTop=0:h=p<(o=(s=l.selectpicker.current.data[m]).position-l.sizeInfo.menuInnerHeight)),n=l.selectpicker.current.elements[m],l.activeIndex=l.selectpicker.current.data[m].index,l.focusItem(n),l.selectpicker.view.currentActive=n,h&&(l.$menuInner[0].scrollTop=o),l.options.liveSearch?l.$searchbox.trigger("focus"):a.trigger("focus")}else if(!a.is("input")&&!q.test(t.which)||t.which===P&&l.selectpicker.keydown.keyHistory){var g,v,b=[];t.preventDefault(),l.selectpicker.keydown.keyHistory+=E[t.which],l.selectpicker.keydown.resetKeyHistory.cancel&&clearTimeout(l.selectpicker.keydown.resetKeyHistory.cancel),l.selectpicker.keydown.resetKeyHistory.cancel=l.selectpicker.keydown.resetKeyHistory.start(),v=l.selectpicker.keydown.keyHistory,/^(.)\1+$/.test(v)&&(v=v.charAt(0));for(var y,_=0;_<l.selectpicker.current.data.length;_++){var w=l.selectpicker.current.data[_];C(w,v,"startsWith",!0)&&l.selectpicker.view.canHighlight[_]&&b.push(w.index)}b.length&&(y=0,c.removeClass("active").find("a").removeClass("active"),1===v.length&&(-1===(y=b.indexOf(l.activeIndex))||y===b.length-1?y=0:y++),g=b[y],h=0<p-(s=l.selectpicker.main.data[g]).position?(o=s.position-s.height,!0):(o=s.position-l.sizeInfo.menuInnerHeight,s.position>p+l.sizeInfo.menuInnerHeight),n=l.selectpicker.main.elements[g],l.activeIndex=b[y],l.focusItem(n),n&&n.firstChild.focus(),h&&(l.$menuInner[0].scrollTop=o),a.trigger("focus"))}i&&(t.which===P&&!l.selectpicker.keydown.keyHistory||t.which===O||t.which===M&&l.options.selectOnTab)&&(t.which!==P&&t.preventDefault(),l.options.liveSearch&&t.which===P||(l.$menuInner.find(".active a").trigger("click",!0),a.trigger("focus"),l.options.liveSearch||(t.preventDefault(),L(document).data("spaceSelect",!0))))}},mobile:function(){this.options.mobile=!0,this.$element[0].classList.add("mobile-device")},refresh:function(){var t=L.extend({},this.options,this.$element.data());this.options=t,this.checkDisabled(),this.buildData(),this.setStyle(),this.render(),this.buildList(),this.setWidth(),this.setSize(!0),this.$element.trigger("refreshed"+H)},hide:function(){this.$newElement.hide()},show:function(){this.$newElement.show()},remove:function(){this.$newElement.remove(),this.$element.remove()},destroy:function(){this.$newElement.before(this.$element).remove(),this.$bsContainer?this.$bsContainer.remove():this.$menu.remove(),this.$element.off(H).removeData("selectpicker").removeClass("bs-select-hidden selectpicker"),L(window).off(H+"."+this.selectId)}};var Q=L.fn.selectpicker;function X(){if(L.fn.dropdown)return(L.fn.dropdown.Constructor._dataApiKeydownHandler||L.fn.dropdown.Constructor.prototype.keydown).apply(this,arguments)}L.fn.selectpicker=V,L.fn.selectpicker.Constructor=G,L.fn.selectpicker.noConflict=function(){return L.fn.selectpicker=Q,this},L(document).off("keydown.bs.dropdown.data-api").on("keydown.bs.dropdown.data-api",':not(.bootstrap-select) > [data-toggle="dropdown"]',X).on("keydown.bs.dropdown.data-api",":not(.bootstrap-select) > .dropdown-menu",X).on("keydown"+H,'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',G.prototype.keydown).on("focusin.modal",'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',function(t){t.stopPropagation()}),L(window).on("load"+H+".data-api",function(){L(".selectpicker").each(function(){var t=L(this);V.call(t,t.data())})})}(jQuery),jQuery.fn.selectpicker.defaults={noneSelectedText:"Nessuna selezione",noneResultsText:"Nessun risultato per {0}",countSelectedText:function(t){return 1==t?"Selezionato {0} di {1}":"Selezionati {0} di {1}"},maxOptionsText:["Limite raggiunto ({n} {var} max)","Limite del gruppo raggiunto ({n} {var} max)",["elementi","elemento"]],multipleSeparator:", ",selectAllText:"Seleziona Tutto",deselectAllText:"Deseleziona Tutto"},function(){"use strict";var b,t,y,_,w,e,i;"undefined"!=typeof window&&window.addEventListener&&(b=Object.create(null),y=function(){clearTimeout(t),t=setTimeout(e,100)},_=function(){},w="http://www.w3.org/1999/xlink",e=function(){var t,e,i,n,s,o,a,r,l,c,h,d,u,p=0;function f(){var t;0===--p&&(_(),window.addEventListener("resize",y,!1),window.addEventListener("orientationchange",y,!1),_=window.MutationObserver?((t=new MutationObserver(y)).observe(document.documentElement,{childList:!0,subtree:!0,attributes:!0}),function(){try{t.disconnect(),window.removeEventListener("resize",y,!1),window.removeEventListener("orientationchange",y,!1)}catch(t){}}):(document.documentElement.addEventListener("DOMSubtreeModified",y,!1),function(){document.documentElement.removeEventListener("DOMSubtreeModified",y,!1),window.removeEventListener("resize",y,!1),window.removeEventListener("orientationchange",y,!1)}))}function m(t){return function(){!0!==b[t.base]&&(t.useEl.setAttributeNS(w,"xlink:href","#"+t.hash),t.useEl.hasAttribute("href")&&t.useEl.setAttribute("href","#"+t.hash))}}function g(t){return function(){t.onerror=null,t.ontimeout=null,f()}}for(_(),r=document.getElementsByTagName("use"),s=0;s<r.length;s+=1){try{e=r[s].getBoundingClientRect()}catch(t){e=!1}t=(a=(n=r[s].getAttribute("href")||r[s].getAttributeNS(w,"href")||r[s].getAttribute("xlink:href"))&&n.split?n.split("#"):["",""])[0],i=a[1],o=e&&0===e.left&&0===e.right&&0===e.top&&0===e.bottom,e&&0===e.width&&0===e.height&&!o?(r[s].hasAttribute("href")&&r[s].setAttributeNS(w,"xlink:href",n),t.length&&(!0!==(l=b[t])&&setTimeout(m({useEl:r[s],base:t,hash:i}),0),void 0===l&&(c=t,u=d=h=void 0,window.XMLHttpRequest&&(h=new XMLHttpRequest,d=v(location),u=v(c),h=void 0===h.withCredentials&&""!==u&&u!==d?XDomainRequest||void 0:XMLHttpRequest),void 0!==h&&(l=new h,(b[t]=l).onload=function(n){return function(){var t,e=document.body,i=document.createElement("x");n.onload=null,i.innerHTML=n.responseText,(t=i.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",e.insertBefore(t,e.firstChild)),f()}}(l),l.onerror=g(l),l.ontimeout=g(l),l.open("GET",t),l.send(),p+=1)))):o?t.length&&b[t]&&setTimeout(m({useEl:r[s],base:t,hash:i}),0):void 0===b[t]?b[t]=!0:b[t].onload&&(b[t].abort(),delete b[t].onload,b[t]=!0)}function v(t){var e;return void 0!==t.protocol?e=t:(e=document.createElement("a")).href=t,e.protocol.replace(/:/g,"")+e.host}r="",p+=1,f()},i=function(){window.removeEventListener("load",i,!1),t=setTimeout(e,0)},"complete"!==document.readyState?window.addEventListener("load",i,!1):i())}(),Array.from||(Array.from=function(){function h(t){return"function"==typeof t||"[object Function]"===e.call(t)}function d(t){var e,i=(e=Number(t),isNaN(e)?0:0!==e&&isFinite(e)?(0<e?1:-1)*Math.floor(Math.abs(e)):e);return Math.min(Math.max(i,0),n)}var e=Object.prototype.toString,n=Math.pow(2,53)-1;return function(t,e,i){var n=Object(t);if(null==t)throw new TypeError("Array.from requires an array-like object - not null or undefined");var s,o=1<arguments.length?e:void 0;if(void 0!==o){if(!h(o))throw new TypeError("Array.from: when provided, the second argument must be a function");2<arguments.length&&(s=i)}for(var a,r=d(n.length),l=h(this)?Object(new this(r)):new Array(r),c=0;c<r;)a=n[c],l[c]=o?void 0===s?o(a,c):o.call(s,a,c):a,c+=1;return l.length=r,l}}()),function(p){function l(t){return t*Math.PI/180}function f(t,e,i){var n=p("#"+i+"canvas")[0],s=p("#"+i+"canvas"),o=n.getContext("2d"),a=n.width/2,r=n.height/2;o.beginPath(),o.arc(a,r,p(s).attr("data-radius"),0,2*Math.PI,!1),o.fillStyle="transparent",o.fill(),o.lineWidth=p(s).attr("data-width"),o.strokeStyle=p(s).attr("data-progressBarBackground"),o.stroke(),o.closePath(),o.beginPath(),o.arc(a,r,p(s).attr("data-radius"),-l(90),-l(90)+l(t/100*360),!1),o.fillStyle="transparent",o.fill(),o.lineWidth=p(s).attr("data-width"),o.strokeStyle=p(s).attr("data-stroke"),o.stroke(),o.closePath(),"true"==p(s).attr("data-text").toLocaleLowerCase()&&p("#"+i+" .clProg").val(e+("true"==p(s).attr("data-percent").toLocaleLowerCase()?"%":""))}p.fn.circularloader=function(t){var e,i,n,s,o,a,r,l,c,h,d=this[0],u=d.id;return 0==p("#"+u+"canvas").length?(e=p.extend({backgroundColor:"#ffffff",fontColor:"#000000",fontSize:"40px",radius:70,progressBarBackground:"#cdcdcd",progressBarColor:"#aaaaaa",progressBarWidth:25,progressPercent:0,progressValue:0,showText:!0,title:""},t),i=parseInt(e.radius),n=parseInt(e.progressBarWidth),h=parseInt(0<parseInt(e.progressValue)?e.progressValue:e.progressPercent),c=parseInt(e.progressPercent),s="color:"+e.fontColor+";font-size:"+parseInt(e.fontSize)+"px;width:"+2*(i+n)+"px;vertical-align:middle;position:relative;background-color:transparent;border:0 none;transform:translateY(-48%);-webkit-transform: translateY(-48%);-ms-transform: translateY(-48%);height:"+2*(i+n)+"px;margin-left:-"+2*(i+n)+"px;text-align:center;padding:0;"+(e.showText?"":"display:none;"),p('<canvas data-width="'+n+'" data-radius="'+i+'" data-stroke="'+e.progressBarColor+'" data-progressBarBackground="'+e.progressBarBackground+'" data-backgroundColor="'+e.backgroundColor+'" data-text='+e.showText+" data-percent="+(null==t.progressValue)+' id="'+u+'canvas" width='+2*(i+n)+" height="+2*(i+n)+"></canvas>").appendTo(d),p('<input class="clProg" style="'+s+'" value="'+h+(null==t.progressValue?"%":"")+'" aria-hidden="true"></input>').appendTo(d),""==e.title?p("#"+u).css("height",2*(i+n)):(p("#"+u).css("height",2*(i+n)+20),p("#"+u+"canvas").before("<div class='titleCircularLoader' style='height:19px;text-align:center;'>"+e.title+"</div>"),p(".titleCircularLoader").css("width",2*(i+n))),o=p("#"+u+"canvas")[0],a=o.getContext("2d"),r=o.width/2,l=o.height/2,p("#"+u+"canvas").offset().left,p("#"+u+"canvas").offset().top,a.beginPath(),a.arc(r,l,i,0,2*Math.PI,!1),a.fillStyle=e.backgroundColor,a.fill(),a.lineWidth=n,a.strokeStyle=e.progressBarBackground,a.stroke(),a.closePath(),0<c&&f(c,h,u)):null==t.progressPercent&&null==t.progressValue||(h=c=0,c=null==t.progressPercent?100<parseInt(t.progressValue)?100:parseInt(t.progressValue):100<parseInt(t.progressPercent)?100:parseInt(t.progressPercent),h=null==t.progressValue?100<parseInt(t.progressPercent)?100:parseInt(t.progressPercent):parseInt(t.progressValue),f(c,h,u)),this}}(jQuery),function(h){"use strict";function t(a,r){function l(t){return-1===t?"danger":-2===t?"muted":(t=t<0?0:t)<26?"danger":t<51?"warning":"success"}function c(t,e){for(var i="",n=!1,s=0;s<e.length;s++){n=!0;for(var o=0;o<t&&o+s+t<e.length;o++)n=n&&e.charAt(o+s)===e.charAt(o+s+t);o<t&&(n=!1),n?(s+=t-1,n=!1):i+=e.charAt(s)}return i}return r=h.extend({},{shortPass:"Password molto debole",badPass:"Password debole",goodPass:"Password sicura",strongPass:"Password molto sicura",enterPass:"Inserisci almeno 8 caratteri e una lettera maiuscola",showText:!0,minimumLength:4},r),function(){var s=r.showText,t=h("<div>").addClass("password-meter progress rounded-0 position-absolute");t.append('<div class="row position-absolute w-100 m-0">\n        <div class="col-3 border-left border-right border-white"></div>\n        <div class="col-3 border-left border-right border-white"></div>\n        <div class="col-3 border-left border-right border-white"></div>\n        <div class="col-3 border-left border-right border-white"></div>\n      </div>');var o=h("<div>").attr({class:"progress-bar",role:"progressbar","aria-valuenow":"0","aria-valuemin":"0","aria-valuemax":"100"}),e=h("<div>").attr({class:"password-strength-meter"}).append(t.append(o));return r.showText&&(s=h("<small>").addClass("form-text text-muted").html(r.enterPass),e.prepend(s)),a.after(e),a.keyup(function(){var t=function(t){var e=0;if(0===t.trim().length)return-2;if(t.length<r.minimumLength)return-1;e+=4*t.length,e+=c(1,t).length-t.length,e+=c(2,t).length-t.length,e+=c(3,t).length-t.length,e+=c(4,t).length-t.length,t.match(/(.*[0-9].*[0-9].*[0-9])/)&&(e+=5);var i=".*[!,@,#,$,%,^,&,*,?,_,~]",i=new RegExp("("+i+i+")");return t.match(i)&&(e+=5),t.match(/([a-z].*[A-Z])|([A-Z].*[a-z])/)&&(e+=10),t.match(/([a-zA-Z])/)&&t.match(/([0-9])/)&&(e+=15),t.match(/([!,@,#,$,%,^,&,*,?,_,~])/)&&t.match(/([0-9])/)&&(e+=15),t.match(/([!,@,#,$,%,^,&,*,?,_,~])/)&&t.match(/([a-zA-Z])/)&&(e+=15),(t.match(/^\w+$/)||t.match(/^\d+$/))&&(e-=10),100<e&&(e=100),e<0&&(e=0),e}(a.val());a.trigger("password.score",[t]);var e,i,n=t<0?0:t;o.removeClass(function(t,e){return(e.match(/(^|\s)bg-\S+/g)||[]).join(" ")}),o.addClass("bg-"+l(t)),o.css({width:n+"%"}),o.attr("aria-valuenow",n),r.showText&&(e=-1===(i=t)||(i=i<0?0:i)<26?r.shortPass:i<51?r.badPass:i<76?r.goodPass:r.strongPass,!a.val().length&&t<=0&&(e=r.enterPass),s.html()!==h("<div>").html(e).html()&&(s.html(e),s.removeClass(function(t,e){return(e.match(/(^|\s)text-\S+/g)||[]).join(" ")}),s.addClass("text-"+l(t)),a.trigger("password.text",[e,t])))}),this}.call(this)}h.fn.password=function(){return this.each(function(){new t(h(this),h(this).data())})}}(jQuery),$(function(){var i=!1,n=null;$(".input-password").on("keydown",function(t){16==(t.keyCode?t.keyCode:t.which)&&(i=!0)}).on("keyup",function(t){var e=t.keyCode?t.keyCode:t.which;16==e&&(i=!1),20==e&&(n?(n=!1,$(".password-caps").remove()):(n=!0,$("input:focus").each(function(t){showCapsLockMsg($(this))})))}).on("keypress",function(t){var e=t.keyCode?t.keyCode:t.which;65<=e&&e<=90&&!i&&(n=!0,showCapsLockMsg($(this)))}),$(".input-password-strength-meter").password(),$(".password-icon").on("click",function(t){$(this).find('[class^="password-icon"]').toggleClass("d-none");var e=$(this).siblings(".input-password"),i="password"===e.attr("type")?"text":"password";e.attr("type",i)})}),function(){"use strict";void 0===Date.dp_locales&&(Date.dp_locales={texts:{buttonTitle:"Scegli la data ...",buttonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per aprire il calendario",prevButtonLabel:"Vai al mese precedente",nextButtonLabel:"Vai al mese successivo",closeButtonTitle:"Chiudere",closeButtonLabel:"Chiudere il calendario",prevMonthButtonLabel:"Vai all'anno precedente",prevYearButtonLabel:"Vai a vent'anni precedenti",nextMonthButtonLabel:"Vai al prossimo anno",nextYearButtonLabel:"Vai ai prossimi 20 anni",changeMonthButtonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per cambiare il mese",changeYearButtonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per cambiare l'anno",changeRangeButtonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per andare ai prossimi 20 anni",calendarHelp:"- Freccia e Freccia giù - va allo stesso giorno della settimana in settimana precedente o successiva, rispettivamente. Se viene raggiunta la fine del mese, continua nel mese precedente o successivo a seconda dei casi.\r\n- Freccia Sinistra e Freccia destra - avanza un giorno all'altro, anche in un continuum. Visivamente fuoco viene spostato da un giorno all'altro e avvolge da riga a riga nella griglia di giorni.\r\n- Control + Pagina Su - Passa alla stessa data dell'anno precedente.\r\n- Control + Pagina giù - Passa alla stessa data nel prossimo anno.\r\n- Home - Passa al primo giorno del mese in corso.\r\n- End - Passa l'ultimo giorno del mese corrente.\r\n- Pagina Su - Passa alla stessa data del mese precedente.\r\n- Pagina giù - Passa alla stessa data del mese successivo.\r\n- Invio o Espace - chiude il calendario e la data selezionata viene visualizzata nella casella di testo associato.\r\n- Escape - chiude il calendario senza alcuna azione."},directionality:"LTR",month_names:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"],month_names_abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],month_names_narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],day_names:["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],day_names_abbreviated:["dom","lun","mar","mer","gio","ven","sab"],day_names_short:["dom","lun","mar","mer","gio","ven","sab"],day_names_narrow:["D","L","M","M","G","V","S"],day_periods:{am:"AM",noon:"mezzogiorno",pm:"PM"},day_periods_abbreviated:{am:"AM",noon:"mezzogiorno",pm:"PM"},day_periods_narrow:{am:"m.",noon:"n",pm:"p."},quarter_names:["1º trimestre","2º trimestre","3º trimestre","4º trimestre"],quarter_names_abbreviated:["T1","T2","T3","T4"],quarter_names_narrow:["1","2","3","4"],era_names:["a.C.","d.C."],era_names_abbreviated:["aC","dC"],era_names_narrow:["aC","dC"],full_format:"EEEE d MMMM y",long_format:"d MMMM y",medium_format:"dd MMM y",short_format:"dd/MM/yy",firstday_of_week:1})}(),function(){"use strict";void 0===Date.dp_locales&&(Date.dp_locales={texts:{buttonTitle:"Select date ...",buttonLabel:"Click or press the Enter key or the spacebar to open the calendar",prevButtonLabel:"Go to previous month",prevMonthButtonLabel:"Go to the previous year",prevYearButtonLabel:"Go to the previous twenty years",nextButtonLabel:"Go to next month",nextMonthButtonLabel:"Go to the next year",nextYearButtonLabel:"Go to the next twenty years",changeMonthButtonLabel:"Click or press the Enter key or the spacebar to change the month",changeYearButtonLabel:"Click or press the Enter key or the spacebar to change the year",changeRangeButtonLabel:"Click or press the Enter key or the spacebar to go to the next twenty years",closeButtonTitle:"Close",closeButtonLabel:"Close the calendar",calendarHelp:"- Up Arrow and Down Arrow - goes to the same day of the week in the previous or next week respectively. If the end of the month is reached, continues into the next or previous month as appropriate.\r\n- Left Arrow and Right Arrow - advances one day to the next, also in a continuum. Visually focus is moved from day to day and wraps from row to row in the grid of days.\r\n- Control+Page Up - Moves to the same date in the previous year.\r\n- Control+Page Down - Moves to the same date in the next year.\r\n- Home - Moves to the first day of the current month.\r\n- End - Moves to the last day of the current month.\r\n- Page Up - Moves to the same date in the previous month.\r\n- Page Down - Moves to the same date in the next month.\r\n- Enter or Espace - closes the calendar, and the selected date is shown in the associated text box.\r\n- Escape - closes the calendar without any action."},directionality:"LTR",month_names:["January","February","March","April","May","June","July","August","September","October","November","December"],month_names_abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],month_names_narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],day_names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],day_names_abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],day_names_short:["Su","Mo","Tu","We","Th","Fr","Sa"],day_names_narrow:["S","M","T","W","T","F","S"],day_periods:{am:"AM",noon:"noon",pm:"PM"},day_periods_abbreviated:{am:"AM",noon:"noon",pm:"PM"},day_periods_narrow:{am:"a",noon:"n",pm:"p"},quarter_names:["1st quarter","2nd quarter","3rd quarter","4th quarter"],quarter_names_abbreviated:["Q1","Q2","Q3","Q4"],quarter_names_narrow:["1","2","3","4"],era_names:["Before Christ","Anno Domini"],era_names_abbreviated:["BC","AD"],era_names_narrow:["B","A"],full_format:"EEEE, MMMM d, y",long_format:"MMMM d, y",medium_format:"MMM d, y",short_format:"M/d/yy",firstday_of_week:0})}(),function(t){if("function"==typeof define&&define.amd)define(["jquery"],t);else if("object"==typeof exports)t(require("jquery"));else{if("undefined"==typeof jQuery)throw new Error("Datepicker's JavaScript requires jQuery");t(jQuery)}}(function(b,d){"use strict";function o(t,e){var n=this;this.$target=b(t),this.options=b.extend({},o.DEFAULTS,e),this.locales=Date.dp_locales,this.startview(this.options.startView),"string"==typeof this.options.inputFormat&&(this.options.inputFormat=[this.options.inputFormat]),b.isArray(this.options.datesDisabled)||(this.options.datesDisabled=[this.options.datesDisabled]),b.each(this.options.datesDisabled,function(t,e){var i;"string"==typeof e?(i=n.parseDate(e),n.options.datesDisabled[t]=null===i?null:n.format(i)):e instanceof Date&&!isNaN(e.valueOf())?n.options.datesDisabled[t]=n.format(e):n.options.datesDisabled[t]=null}),null!=this.options.min?this.options.min=this.parseDate(this.options.min):this.$target.attr("min")&&(this.options.min=this.parseDate(this.$target.attr("min"))),null!=this.options.max?this.options.max=this.parseDate(this.options.max):this.$target.attr("max")&&(this.options.max=this.parseDate(this.$target.attr("max"))),"string"==typeof this.options.previous?this.options.previous=b(this.options.previous):this.options.previous instanceof jQuery||(this.options.previous=null),"string"==typeof this.options.next?this.options.next=b(this.options.next):this.options.next instanceof jQuery||(this.options.next=null),this.id=this.$target.attr("id")||"datepicker-"+Math.floor(1e5*Math.random());var i=(i=r.join("")).replace(/CALENDARID/g,this.id+"");0==this.$target.parent(".input-group").length&&this.$target.wrap('<div class="input-group"></div>'),this.$label=this.$target.parents().find("label[for="+this.id+"]"),this.$group=this.$target.parent(".input-group"),this.$target.attr("aria-autocomplete","none"),this.$target.css("min-width","7em"),this.$target.addClass("form-control"),this.$target.attr("placeholder")||this.$target.attr("placeholder",this.options.inputFormat[0]);var s=(s=a.join("")).replace(/CALENDARID/g,this.id+"");this.$button=b(s),this.$button.addClass(this.options.theme),this.$calendar=b(i),this.$calendar.addClass(this.options.theme),this.$target.after(this.$button),"static"===this.$calendar.parent().css("position")&&this.$calendar.parent().css("position","relative"),this.$calendar.find(".datepicker-bn-open-label").html(this.options.buttonLabel),this.$target.attr("id")&&this.$calendar.attr("aria-controls",this.$target.attr("id")),this.$button.find("span").attr("title",this.options.buttonTitle),this.$calendar.css("left",this.$target.parent().position().left+"px"),this.$monthObj=this.$calendar.find(".datepicker-month"),this.$prev=this.$calendar.find(".datepicker-month-prev"),this.$next=this.$calendar.find(".datepicker-month-next"),this.$fastprev=this.$calendar.find(".datepicker-month-fast-prev"),this.$fastnext=this.$calendar.find(".datepicker-month-fast-next"),this.$grid=this.$calendar.find(".datepicker-grid"),"RTL"===this.locales.directionality&&this.$grid.addClass("rtl"),this.$grid.find("th.datepicker-day abbr"),this.drawCalendarHeader(),0==this.options.inline&&1==this.options.modal?(this.$close=this.$calendar.find(".datepicker-close"),this.$close.html(this.options.closeButtonTitle).attr("title",this.options.closeButtonLabel),this.$calendar.find(".datepicker-bn-close-label").html(this.options.closeButtonLabel)):(this.hideObject(this.$calendar.find(".datepicker-close-wrap")),this.hideObject(this.$calendar.find(".datepicker-bn-close-label"))),0!=this.options.inline?(this.hideObject(this.$button),("string"==typeof this.options.inline?b("#"+this.options.inline):this.options.inline).append(this.$calendar),this.$calendar.css({position:"relative",left:"0px"}),this.initializeDate()):(this.$calendar.css({display:"none"}),this.$target.parent().after(this.$calendar),this.hide(!this.options.gainFocusOnConstruction)),this.keys={tab:9,enter:13,esc:27,space:32,pageup:33,pagedown:34,end:35,home:36,left:37,up:38,right:39,down:40},this.bindHandlers(),this.$button.click(function(t){return b(this).hasClass("disabled")||("true"===n.$calendar.attr("aria-hidden")?(n.initializeDate(),n.show()):n.hide(),n.selectGridCell(n.$grid.attr("aria-activedescendant"))),t.stopPropagation(),!1}),this.$button.keydown(function(t){var e=t||event;if(e.keyCode==n.keys.enter||e.keyCode==n.keys.space)return b(this).trigger("click"),!1}),this.$calendar.on("blur",function(t){"false"===n.$calendar.attr("aria-hidden")&&n.hide()})}var a=['<a class="datepicker-button input-group-addon btn" role="button" aria-haspopup="true" tabindex="0" aria-labelledby="datepicker-bn-open-label-CALENDARID">','\t<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><title>it-calendar</title><g><path d="M21,9V8a3,3,0,0,0-3-3h-.55V4a1,1,0,0,0-2,0V5h-7V4a1,1,0,1,0-2,0V5H6A3,3,0,0,0,3,8V18a3,3,0,0,0,3,3H18a3,3,0,0,0,3-3V9ZM15.46,5h2V6a1,1,0,1,1-2,0Zm-9,0h2V6a1,1,0,1,1-2,0ZM20,18a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V10H20Z"/></g></svg>',"</a>"],r=['<div class="datepicker-calendar" id="datepicker-calendar-CALENDARID" aria-hidden="false">','\t<div class="datepicker-month-wrap">','\t\t<div class="datepicker-month-fast-next pull-right" role="button" aria-labelledby="datepicker-bn-fast-next-label-CALENDARID" tabindex="0"><span class="icon-right"></span><span class="icon-right"></span></div>','\t\t<div class="datepicker-month-next pull-right" role="button" aria-labelledby="datepicker-bn-next-label-CALENDARID" tabindex="0"><span class="icon-right"></span></div>','\t\t<div class="datepicker-month-fast-prev pull-left" role="button" aria-labelledby="datepicker-bn-fast-prev-label-CALENDARID" tabindex="0"><span class="icon-left"></span><span class="icon-left"></span></div>','\t\t<div class="datepicker-month-prev pull-left" role="button" aria-labelledby="datepicker-bn-prev-label-CALENDARID" tabindex="0"><span class="icon-left"></span></div>','\t\t<div id="datepicker-month-CALENDARID" class="datepicker-month" tabindex="0" role="heading" aria-live="assertive" aria-atomic="true" title="Click or press the Enter key or the spacebar to change the month">July 2015</div>',"\t</div>",'\t<table class="datepicker-grid" role="grid" aria-readonly="true" aria-activedescendant="datepicker-err-msg-CALENDARID" aria-labelledby="datepicker-month-CALENDARID" tabindex="0">',"\t\t<thead>",'\t\t\t<tr class="datepicker-weekdays" role="row">','\t\t\t\t<th scope="col" id="day0-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Sunday"><abbr title="Sunday">Su</abbr></th>','\t\t\t\t<th scope="col" id="day1-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Monday"><abbr title="Monday">Mo</abbr></th>','\t\t\t\t<th scope="col" id="day2-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Tuesday"><abbr title="Tuesday">Tu</abbr></th>','\t\t\t\t<th scope="col" id="day3-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Wednesday"><abbr title="Wednesday">We</abbr></th>','\t\t\t\t<th scope="col" id="day4-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Thursday"><abbr title="Thursday">Th</abbr></th>','\t\t\t\t<th scope="col" id="day5-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Friday"><abbr title="Friday">Fr</abbr></th>','\t\t\t\t<th scope="col" id="day6-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Saturday"><abbr title="Saturday">Sa</abbr></th>',"\t\t\t</tr>","\t\t</thead>","\t\t<tbody>","\t\t\t<tr>",'\t\t\t\t<td id="datepicker-err-msg-CALENDARID" colspan="7"><span>Javascript must be enabled</span></td>',"\t\t\t</tr>","\t\t</tbody>","\t</table>",'\t<div class="datepicker-close-wrap">','\t\t<button class="datepicker-close" id="datepicker-close-CALENDARID" aria-labelledby="datepicker-bn-close-label-CALENDARID">Close</button>',"\t</div>",'\t<div id="datepicker-bn-open-label-CALENDARID" class="datepicker-bn-open-label offscreen">Click or press the Enter key or the spacebar to open the calendar</div>','\t<div id="datepicker-bn-prev-label-CALENDARID" class="datepicker-bn-prev-label offscreen">Go to previous month</div>','\t<div id="datepicker-bn-next-label-CALENDARID" class="datepicker-bn-next-label offscreen">Go to next month</div>','\t<div id="datepicker-bn-fast-prev-label-CALENDARID" class="datepicker-bn-fast-prev-label offscreen">Go to previous year</div>','\t<div id="datepicker-bn-fast-next-label-CALENDARID" class="datepicker-bn-fast-next-label offscreen">Go to next year</div>','\t<div id="datepicker-bn-close-label-CALENDARID" class="datepicker-bn-close-label offscreen">Close the date picker</div>',"</div>"];o.VERSION="2.1.10",o.DEFAULTS={firstDayOfWeek:Date.dp_locales.firstday_of_week,weekDayFormat:"short",startView:0,daysOfWeekDisabled:[],datesDisabled:[],isDateDisabled:null,isMonthDisabled:null,isYearDisabled:null,inputFormat:[Date.dp_locales.short_format],outputFormat:Date.dp_locales.short_format,titleFormat:Date.dp_locales.full_format,buttonTitle:Date.dp_locales.texts.buttonTitle,buttonLabel:Date.dp_locales.texts.buttonLabel,prevButtonLabel:Date.dp_locales.texts.prevButtonLabel,prevMonthButtonLabel:Date.dp_locales.texts.prevMonthButtonLabel,prevYearButtonLabel:Date.dp_locales.texts.prevYearButtonLabel,nextButtonLabel:Date.dp_locales.texts.nextButtonLabel,nextMonthButtonLabel:Date.dp_locales.texts.nextMonthButtonLabel,nextYearButtonLabel:Date.dp_locales.texts.nextYearButtonLabel,changeMonthButtonLabel:Date.dp_locales.texts.changeMonthButtonLabel,changeYearButtonLabel:Date.dp_locales.texts.changeYearButtonLabel,changeRangeButtonLabel:Date.dp_locales.texts.changeRangeButtonLabel,closeButtonTitle:Date.dp_locales.texts.closeButtonTitle,closeButtonLabel:Date.dp_locales.texts.closeButtonLabel,onUpdate:function(){},previous:null,next:null,theme:"default",modal:!1,inline:!1,gainFocusOnConstruction:!1,min:null,max:null},o.prototype.initializeDate=function(){var t=this.$target.val(),e=""===t?new Date:this.parseDate(t);this.setDate(e,!0)},o.prototype.getDate=function(){var t=this.$target.val();return""===t?new Date:this.parseDate(t)},o.prototype.setDate=function(t,e){switch(this.dateObj=t,e=void 0!==e&&e,null==this.dateObj&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=new Date,this.dateObj.setHours(0,0,0,0)),null!=this.options.min&&this.dateObj<this.options.min?(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.min):null!=this.options.max&&this.dateObj>this.options.max&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.max),e&&""==this.$target.val()||this.$target.val(this.format(this.dateObj)),this.curYear=this.dateObj.getFullYear(),this.year=this.curYear,this.curMonth=this.dateObj.getMonth(),this.month=this.curMonth,this.date=this.dateObj.getDate(),this.options.startView){case 1:this.populateMonthsCalendar(),this.$grid.attr("aria-activedescendant",this.$grid.find(".curMonth").attr("id"));break;case 2:this.populateYearsCalendar(),this.$grid.attr("aria-activedescendant",this.$grid.find(".curYear").attr("id"));break;default:this.populateDaysCalendar(),this.$grid.attr("aria-activedescendant",this.$grid.find(".curDay").attr("id"))}},o.prototype.drawCalendarHeader=function(){for(var t=this.$grid.find("th.datepicker-day"),e=this.options.firstDayOfWeek,i=0;i<7;i++)t.eq(i).attr("aria-label",this.locales.day_names[e]),t.children("abbr").eq(i).attr("title",this.locales.day_names[e]).text("short"===this.options.weekDayFormat?this.locales.day_names_short[e]:this.locales.day_names_narrow[e]),e=(e+1)%7},o.prototype.populateDaysCalendar=function(){this.$calendar.find(".datepicker-bn-prev-label").html(this.options.prevButtonLabel),this.$calendar.find(".datepicker-bn-next-label").html(this.options.nextButtonLabel),this.$calendar.find(".datepicker-bn-fast-prev-label").html(this.options.prevMonthButtonLabel),this.$calendar.find(".datepicker-bn-fast-next-label").html(this.options.nextMonthButtonLabel),null!=this.options.min&&(this.year-1<this.options.min.getFullYear()||this.year-1==this.options.min.getFullYear()&&this.month<this.options.min.getMonth())?(this.$fastprev.attr("title",""),this.$fastprev.addClass("disabled"),this.$fastprev.removeClass("enabled")):(this.$fastprev.attr("title",this.options.prevMonthButtonLabel),this.$fastprev.addClass("enabled"),this.$fastprev.removeClass("disabled"));var t=this.previousMonth(this.year,this.month);null!=this.options.min&&(t.year<this.options.min.getFullYear()||t.year==this.options.min.getFullYear()&&t.month<this.options.min.getMonth())?(this.$prev.attr("title",""),this.$prev.addClass("disabled"),this.$prev.removeClass("enabled")):(this.$prev.attr("title",this.options.prevButtonLabel),this.$prev.addClass("enabled"),this.$prev.removeClass("disabled")),this.$monthObj.attr("title",this.options.changeMonthButtonLabel);var e=this.nextMonth(this.year,this.month);null!=this.options.max&&(e.year>this.options.max.getFullYear()||e.year==this.options.max.getFullYear()&&e.month>this.options.max.getMonth())?(this.$next.attr("title",""),this.$next.addClass("disabled"),this.$next.removeClass("enabled")):(this.$next.attr("title",this.options.nextButtonLabel),this.$next.addClass("enabled"),this.$next.removeClass("disabled")),null!=this.options.max&&(this.year+1>this.options.max.getFullYear()||this.year+1==this.options.max.getFullYear()&&this.month>this.options.max.getMonth())?(this.$fastnext.attr("title",""),this.$fastnext.addClass("disabled"),this.$fastnext.removeClass("enabled")):(this.$fastnext.attr("title",this.options.nextMonthButtonLabel),this.$fastnext.addClass("enabled"),this.$fastnext.removeClass("disabled")),this.showObject(this.$fastprev),this.showObject(this.$fastnext);var i=this.getDaysInMonth(this.year,this.month),n=this.getDaysInMonth(t.year,t.month),s=new Date(this.year,this.month,1).getDay(),o=(this.options.firstDayOfWeek+6)%7,a=1,r=1;this.$monthObj.html(this.locales.month_names[this.month]+" "+this.year),this.showObject(this.$grid.find("thead"));for(var l='\t<tr id="row0-'+this.id+'" role="row">\n',c=0,h=this.options.firstDayOfWeek;h!=s;)c++,h=(h+1)%7;for(;0<c;c--)l+='\t\t<td class="empty">'+(n-c+1)+"</td>\n";for(var d=this.options.isYearDisabled&&this.options.isYearDisabled(this.year),u=this.options.isMonthDisabled&&this.options.isMonthDisabled(this.year,this.month+1),a=1;a<=i;a++){var p=new Date(this.year,this.month,a,0,0,0,0),f=this.formatDate(p,this.options.titleFormat),m=a==this.date&&this.month==this.curMonth&&this.year==this.curYear?" curDay":"";d||u||-1<b.inArray(h,this.options.daysOfWeekDisabled)||null!=this.options.min&&p<this.options.min||null!=this.options.max&&p>this.options.max||-1<b.inArray(this.format(p),this.options.datesDisabled)||this.options.isDateDisabled&&this.options.isDateDisabled(p)?l+='\t\t<td id="cell'+a+"-"+this.id+'" class="day unselectable'+m+'"':l+='\t\t<td id="cell'+a+"-"+this.id+'" class="day selectable'+m+'"',l+=' data-value="'+a+'"',l+=' title="'+f+'"',l+=' aria-label="'+f+'"',l+=' headers="day'+h+"-header-"+this.id+'" role="gridcell" tabindex="-1" aria-selected="false"><span>'+a+"</span>",l+="</td>",h==o&&a<i&&(l+='\t</tr>\n\t<tr id="row'+r+"-"+this.id+'" role="row">\n',r++),a<i&&(h=(h+1)%7)}for(;h!=o;)l+='\t\t<td class="empty">'+ ++c+"</td>\n",h=(h+1)%7;l+="\t</tr>";var g=this.$grid.find("tbody");g.empty(),g.append(l),this.gridType=0},o.prototype.populateMonthsCalendar=function(){this.$calendar.find(".datepicker-bn-prev-label").html(this.options.prevMonthButtonLabel),this.$calendar.find(".datepicker-bn-next-label").html(this.options.nextMonthButtonLabel),this.hideObject(this.$fastprev),this.hideObject(this.$fastnext),null!=this.options.min&&this.year-1<this.options.min.getFullYear()?(this.$prev.attr("title",""),this.$prev.addClass("disabled"),this.$prev.removeClass("enabled")):(this.$prev.attr("title",this.options.prevMonthButtonLabel),this.$prev.addClass("enabled"),this.$prev.removeClass("disabled")),this.$monthObj.attr("title",this.options.changeYearButtonLabel),null!=this.options.max&&this.year+1>this.options.max.getFullYear()?(this.$next.attr("title",""),this.$next.addClass("disabled"),this.$next.removeClass("enabled")):(this.$next.attr("title",this.options.nextMonthButtonLabel),this.$next.addClass("enabled"),this.$next.removeClass("disabled"));var t=0,e=1,i=this.$grid.find("tbody");this.$monthObj.html(this.year),this.hideObject(this.$grid.find("thead")),i.empty(),b("#datepicker-err-msg-"+this.id).empty();for(var n='\t<tr id="row0-'+this.id+'" role="row">\n',s=this.options.isYearDisabled&&this.options.isYearDisabled(this.year),t=0;t<12;t++)s?n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month unselectable"':t==this.month&&this.year==this.curYear?n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month curMonth selectable"':null!=this.options.min&&(this.year<this.options.min.getFullYear()||this.year==this.options.min.getFullYear()&&t<this.options.min.getMonth())||null!=this.options.max&&(this.year>this.options.max.getFullYear()||this.year==this.options.max.getFullYear()&&t>this.options.max.getMonth())||this.options.isMonthDisabled&&this.options.isMonthDisabled(this.year,t+1)?n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month unselectable"':n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month selectable"',n+=' data-value="'+t+'"',n+=' title="'+this.locales.month_names[t]+" "+this.year+'"',n+=' aria-label="'+this.locales.month_names[t]+" "+this.year+'"',n+=' role="gridcell" tabindex="-1" aria-selected="false">'+this.locales.month_names_abbreviated[t],n+="</td>",3!=t&&7!=t||(n+='\t</tr>\n\t<tr id="row'+e+"-"+this.id+'" role="row">\n',e++);n+="\t</tr>",i.append(n),this.gridType=1},o.prototype.populateYearsCalendar=function(){this.$calendar.find(".datepicker-bn-prev-label").html(this.options.prevYearButtonLabel),this.$calendar.find(".datepicker-bn-next-label").html(this.options.nextYearButtonLabel),this.hideObject(this.$fastprev),this.hideObject(this.$fastnext),null!=this.options.min&&this.year-20<this.options.min.getFullYear()?(this.$prev.attr("title",""),this.$prev.addClass("disabled"),this.$prev.removeClass("enabled")):(this.$prev.attr("title",this.options.prevYearButtonLabel),this.$prev.addClass("enabled"),this.$prev.removeClass("disabled")),this.$monthObj.attr("title",this.options.changeRangeButtonLabel),null!=this.options.max&&this.year+20>this.options.max.getFullYear()?(this.$next.attr("title",""),this.$next.addClass("disabled"),this.$next.removeClass("enabled")):(this.$next.attr("title",this.options.nextYearButtonLabel),this.$next.addClass("enabled"),this.$next.removeClass("disabled"));var t=10*Math.floor(this.year/10),e=19+t,i=1,n=this.$grid.find("tbody");this.$monthObj.html(t+"-"+e),this.hideObject(this.$grid.find("thead")),n.empty(),b("#datepicker-err-msg-"+this.id).empty();for(var s='\t<tr id="row0-'+this.id+'" role="row">\n',o=t;o<=e;o++){o==this.year?s+='\t\t<td id="cell'+(o-t+1)+"-"+this.id+'" class="year curYear selectable"':null!=this.options.min&&o<this.options.min.getFullYear()||null!=this.options.max&&o>this.options.max.getFullYear()||this.options.isYearDisabled&&this.options.isYearDisabled(o)?s+='\t\t<td id="cell'+(o-t+1)+"-"+this.id+'" class="year unselectable"':s+='\t\t<td id="cell'+(o-t+1)+"-"+this.id+'" class="year selectable"',s+=' data-value="'+o+'"',s+=' title="'+o+'"',s+=' role="gridcell" tabindex="-1" aria-selected="false">'+o,s+="</td>";var a=o-t;4!=a&&9!=a&&14!=a||(s+='\t</tr>\n\t<tr id="row'+i+"-"+this.id+'" role="row">\n',i++)}s+="\t</tr>",n.append(s),this.gridType=2},o.prototype.showDaysOfPrevMonth=function(t){var e,i=this.previousMonth(this.year,this.month);return(null==this.options.min||!(i.year<this.options.min.getFullYear()||i.year==this.options.min.getFullYear()&&i.month<this.options.min.getMonth()))&&(this.month=i.month,this.year=i.year,this.populateDaysCalendar(),null!=t&&(e="cell"+(this.getDaysInMonth(this.year,this.month)-t)+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0)},o.prototype.showDaysOfMonth=function(t){if(null!=this.options.min&&(this.year<this.options.min.getFullYear()||this.year==this.options.min.getFullYear()&&t<this.options.min.getMonth()))return!1;if(null!=this.options.max&&(this.year>this.options.max.getFullYear()||this.year==this.options.max.getFullYear()&&t>this.options.max.getMonth()))return!1;this.month=t,this.date=Math.min(this.date,this.getDaysInMonth(this.year,this.month)),this.populateDaysCalendar();var e=this.$grid.find("tbody td[data-value='"+this.date+"']");return this.selectGridCell(e.attr("id")),!0},o.prototype.showMonthsOfPrevYear=function(t){return!(null!=this.options.min&&this.year-1<this.options.min.getFullYear())&&(this.year--,this.populateMonthsCalendar(),null!=t&&(e="cell"+(12-t)+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showMonthsOfYear=function(t){if(null!=this.options.min&&t<this.options.min.getFullYear())return!1;if(null!=this.options.max&&t>this.options.max.getFullYear())return!1;this.year=t,this.populateMonthsCalendar();var e=this.$grid.find("tbody td[data-value='"+this.month+"']");return this.$grid.attr("aria-activedescendant",e.attr("id")),this.selectGridCell(e.attr("id")),!0},o.prototype.showYearsOfPrevRange=function(t){return!(null!=this.options.min&&this.year-20<this.options.min.getFullYear())&&(this.year-=20,this.populateYearsCalendar(),null!=t&&(e="cell"+(20-t)+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showDaysOfNextMonth=function(t){var e,i=this.nextMonth(this.year,this.month);return(null==this.options.max||!(i.year>this.options.max.getFullYear()||i.year==this.options.max.getFullYear()&&i.month>this.options.max.getMonth()))&&(this.month=i.month,this.year=i.year,this.populateDaysCalendar(),null!=t&&(e="cell"+t+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0)},o.prototype.showMonthsOfNextYear=function(t){return!(null!=this.options.max&&this.year+1>this.options.max.getFullYear())&&(this.year++,this.populateMonthsCalendar(),null!=t&&(e="cell"+t+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showYearsOfNextRange=function(t){return!(null!=this.options.max&&this.year+20>this.options.max.getFullYear())&&(this.year+=20,this.populateYearsCalendar(),null!=t&&(e="cell"+t+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showDaysOfPrevYear=function(){return(null==this.options.min||!(this.year-1<this.options.min.getFullYear()||this.year-1==this.options.min.getFullYear()&&this.month<this.options.min.getMonth()))&&(this.year--,this.populateDaysCalendar(),!0)},o.prototype.showDaysOfNextYear=function(){return(null==this.options.max||!(this.year+1>this.options.max.getFullYear()||this.year+1==this.options.max.getFullYear()&&this.month>this.options.max.getMonth()))&&(this.year++,this.populateDaysCalendar(),!0)},o.prototype.bindHandlers=function(){var i=this;this.$fastprev.click(function(t){return i.handleFastPrevClick(t)}),this.$prev.click(function(t){return i.handlePrevClick(t)}),this.$next.click(function(t){return i.handleNextClick(t)}),this.$fastnext.click(function(t){return i.handleFastNextClick(t)}),this.$monthObj.click(function(t){return i.handleMonthClick(t)}),this.$monthObj.keydown(function(t){return i.handleMonthKeyDown(t)}),this.$fastprev.keydown(function(t){return i.handleFastPrevKeyDown(t)}),this.$prev.keydown(function(t){return i.handlePrevKeyDown(t)}),this.$next.keydown(function(t){return i.handleNextKeyDown(t)}),this.$fastnext.keydown(function(t){return i.handleFastNextKeyDown(t)}),1==this.options.modal&&(this.$close.click(function(t){return i.handleCloseClick(t)}),this.$close.keydown(function(t){return i.handleCloseKeyDown(t)})),this.$grid.keydown(function(t){return i.handleGridKeyDown(t)}),this.$grid.keypress(function(t){return i.handleGridKeyPress(t)}),this.$grid.focus(function(t){return i.handleGridFocus(t)}),this.$grid.blur(function(t){return i.handleGridBlur(t)}),this.$grid.delegate("td","click",function(t){return i.handleGridClick(this,t)}),this.$target.change(function(t){var e=i.parseDate(b(this).val());i.updateLinked(e)})},o.prototype.handleFastPrevClick=function(t){var e;return this.showDaysOfPrevYear()&&(e=this.$grid.attr("aria-activedescendant"),this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e))),t.stopPropagation(),!1},o.prototype.handlePrevClick=function(t){var e=this.$grid.attr("aria-activedescendant");switch(this.gridType){case 0:var i=t.ctrlKey?this.showDaysOfPrevYear():this.showDaysOfPrevMonth();i&&(this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 1:this.showMonthsOfPrevYear()&&(this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 2:this.showYearsOfPrevRange()&&(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id))}return t.stopPropagation(),!1},o.prototype.handleMonthClick=function(t){return this.changeGrid(t),t.stopPropagation(),!1},o.prototype.handleNextClick=function(t){var e=this.$grid.attr("aria-activedescendant");switch(this.gridType){case 0:var i=t.ctrlKey?this.showDaysOfNextYear():this.showDaysOfNextMonth();i&&(this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 1:this.showMonthsOfNextYear()&&(this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 2:this.showYearsOfNextRange()&&(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id))}return t.stopPropagation(),!1},o.prototype.handleFastNextClick=function(t){var e;return this.showDaysOfNextYear()&&(e=this.$grid.attr("aria-activedescendant"),this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e))),t.stopPropagation(),!1},o.prototype.handleCloseClick=function(t){return this.hide(),t.stopPropagation(),!1},o.prototype.handleFastPrevKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$close.focus():this.$prev.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:return!(!t.shiftKey&&!t.ctrlKey)||(this.showDaysOfPrevYear(),t.stopPropagation(),!1);case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handlePrevKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?0==this.gridType?this.$fastprev.focus():this.$close.focus():this.$monthObj.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:if(t.shiftKey)return!0;switch(this.gridType){case 0:t.ctrlKey?this.showDaysOfPrevYear():this.showDaysOfPrevMonth();break;case 1:this.showMonthsOfPrevYear();break;case 2:this.showYearsOfPrevRange()}return t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleMonthKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$prev.focus():this.$next.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:return this.changeGrid(t),t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleNextKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$monthObj.focus():0==this.gridType?this.$fastnext.focus():this.$grid.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:switch(this.gridType){case 0:t.ctrlKey?this.showDaysOfNextYear():this.showDaysOfNextMonth();break;case 1:this.showMonthsOfNextYear();break;case 2:this.showYearsOfNextRange()}return t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleFastNextKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$next.focus():this.$grid.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:return this.showDaysOfNextYear(),t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleCloseKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!!t.ctrlKey||(t.shiftKey?this.$grid.focus():0==this.gridType?this.$fastprev.focus():this.$prev.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.esc:case this.keys.space:return!(!t.shiftKey&&!t.ctrlKey)||(this.hide(),t.stopPropagation(),!1)}return!0},o.prototype.handleGridKeyDown=function(t){var e=b("#"+this.$grid.attr("aria-activedescendant")),i=this.$grid.find("td.selectable"),n=this.$grid.find("tbody tr").eq(0).find("td").length;if(t.altKey&&t.keyCode!=this.keys.pageup&&t.keyCode!=this.keys.pagedown)return!0;switch(t.keyCode){case this.keys.tab:return 1==this.options.modal?t.shiftKey?0==this.gridType?this.$fastnext.focus():this.$next.focus():this.$close.focus():(this.hide(),this.handleTabOut(t)),t.stopPropagation(),!1;case this.keys.enter:case this.keys.space:if(t.ctrlKey)return!0;switch(this.gridType){case 0:this.update(),this.hide();break;case 1:this.showDaysOfMonth(parseInt(e.attr("data-value"),10));break;case 2:this.showMonthsOfYear(parseInt(e.attr("data-value"),10))}return t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1;case this.keys.left:case this.keys.right:if(t.keyCode==this.keys.left&&"LTR"===this.locales.directionality||t.keyCode==this.keys.right&&"RTL"===this.locales.directionality){if(t.ctrlKey||t.shiftKey)return!0;var s=null;if(0<=(a=i.index(e)-1))s=i.eq(a),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",s.attr("id")),this.selectGridCell(s.attr("id"));else switch(this.gridType){case 0:this.showDaysOfPrevMonth(0);break;case 1:this.showMonthsOfPrevYear(0);break;case 2:this.showYearsOfPrevRange(0)}return t.stopPropagation(),!1}if(t.ctrlKey||t.shiftKey)return!0;var o=null;if((a=i.index(e)+1)<i.length)o=i.eq(a),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",o.attr("id")),this.selectGridCell(o.attr("id"));else switch(this.gridType){case 0:this.showDaysOfNextMonth(1);break;case 1:this.showMonthsOfNextYear(1);break;case 2:this.showYearsOfNextRange(1)}return t.stopPropagation(),!1;case this.keys.up:if(t.ctrlKey||t.shiftKey)return!0;s=null;if(0<=(a=i.index(e)-n))s=i.eq(a),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",s.attr("id")),this.selectGridCell(s.attr("id"));else switch(a=n-1-i.index(e),this.gridType){case 0:this.showDaysOfPrevMonth(a);break;case 1:this.showMonthsOfPrevYear(a);break;case 2:this.showYearsOfPrevRange(a)}return t.stopPropagation(),!1;case this.keys.down:if(t.ctrlKey||t.shiftKey)return!0;var a,o=null;if((a=i.index(e)+n)<i.length)o=i.eq(a),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",o.attr("id")),this.selectGridCell(o.attr("id"));else switch(a=n+1-(i.length-i.index(e)),this.gridType){case 0:this.showDaysOfNextMonth(a);break;case 1:this.showMonthsOfNextYear(a);break;case 2:this.showYearsOfNextRange(a)}return t.stopPropagation(),!1;case this.keys.pageup:var r=this.$grid.attr("aria-activedescendant");if(t.shiftKey||t.ctrlKey)return!0;t.preventDefault();var l=!1;switch(this.gridType){case 0:l=t.altKey?(t.stopImmediatePropagation(),this.showDaysOfPrevYear()):this.showDaysOfPrevMonth();break;case 1:l=this.showMonthsOfPrevYear();break;case 2:l=this.showYearsOfPrevRange()}return l&&(b("#"+r).attr("id")==d?(h=(i=this.$grid.find("td.selectable")).eq(i.length-1),this.$grid.attr("aria-activedescendant",h.attr("id")),this.selectGridCell(h.attr("id"))):this.selectGridCell(r)),t.stopPropagation(),!1;case this.keys.pagedown:r=this.$grid.attr("aria-activedescendant");if(t.shiftKey||t.ctrlKey)return!0;t.preventDefault();l=!1;switch(this.gridType){case 0:l=t.altKey?(t.stopImmediatePropagation(),this.showDaysOfNextYear()):this.showDaysOfNextMonth();break;case 1:l=this.showMonthsOfNextYear();break;case 2:l=this.showYearsOfNextRange()}return l&&(b("#"+r).attr("id")==d?(h=(i=this.$grid.find("td.selectable")).eq(i.length-1),this.$grid.attr("aria-activedescendant",h.attr("id")),this.selectGridCell(h.attr("id"))):this.selectGridCell(r)),t.stopPropagation(),!1;case this.keys.home:if(t.ctrlKey||t.shiftKey)return!0;var c=i.eq(0);return this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",c.attr("id")),this.selectGridCell(c.attr("id")),t.stopPropagation(),!1;case this.keys.end:if(t.ctrlKey||t.shiftKey)return!0;var h=i.eq(i.length-1);return this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",h.attr("id")),this.selectGridCell(h.attr("id")),t.stopPropagation(),!1}return!0},o.prototype.handleGridKeyPress=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:case this.keys.enter:case this.keys.space:case this.keys.esc:case this.keys.left:case this.keys.right:case this.keys.up:case this.keys.down:case this.keys.pageup:case this.keys.pagedown:case this.keys.home:case this.keys.end:return t.stopPropagation(),!1}return!0},o.prototype.handleGridClick=function(t,e){var i=b(t);if(i.is(".empty")||i.is(".unselectable"))return!0;switch(this.$grid.find(".focus").removeClass("focus").attr("aria-selected","false").attr("tabindex",-1),this.gridType){case 0:this.$grid.attr("aria-activedescendant",i.attr("id")),this.selectGridCell(i.attr("id")),this.update(),this.hide();break;case 1:this.showDaysOfMonth(parseInt(i.attr("data-value"),10));break;case 2:this.showMonthsOfYear(parseInt(i.attr("data-value"),10))}return e.stopPropagation(),!1},o.prototype.handleGridFocus=function(t){var e,i,n=this.$grid.attr("aria-activedescendant");return b("#"+n).attr("id")==d?(i=(e=this.$grid.find("td.selectable")).eq(e.length-1),this.$grid.attr("aria-activedescendant",i.attr("id")),this.selectGridCell(i.attr("id"))):this.selectGridCell(n),!0},o.prototype.handleGridBlur=function(t){return this.unSelectGridCell(this.$grid.attr("aria-activedescendant")),!0},o.prototype.handleTabOut=function(t){var e=b("body").find("input:visible,textarea:visible,select:visible"),i=e.index(this.$target);return-1<i&&i<e.length&&(t.shiftKey?0<i&&i--:i+1<e.length&&i++,e.eq(i).focus()),!0},o.prototype.changeGrid=function(t){switch(this.gridType){case 0:this.populateMonthsCalendar(),this.year!=this.curYear?(e=this.$grid.find("td.selectable"),this.$grid.attr("aria-activedescendant",e.eq(0).attr("id"))):this.$grid.attr("aria-activedescendant",this.$grid.find(".curMonth").attr("id")),this.selectGridCell(this.$grid.attr("aria-activedescendant"));break;case 2:t.shiftKey?this.year-=20:this.year+=20;case 1:var e;this.populateYearsCalendar(),this.year!=this.curYear?(e=this.$grid.find("td.selectable"),this.$grid.attr("aria-activedescendant",e.eq(0).attr("id"))):this.$grid.attr("aria-activedescendant",this.$grid.find(".curYear").attr("id")),this.selectGridCell(this.$grid.attr("aria-activedescendant"))}return!0},o.prototype.selectGridCell=function(t){b("#"+t).addClass("focus").attr("aria-selected","true").attr("tabindex",0).focus()},o.prototype.unSelectGridCell=function(t){b("#"+t).removeClass("focus").attr("aria-selected","false").attr("tabindex",-1)},o.prototype.update=function(){var t=b("#"+this.$grid.attr("aria-activedescendant")),e=new Date(this.year,this.month,parseInt(t.attr("data-value"),10)),i=this.formatDate(e,this.options.outputFormat);this.$target.val(i),this.$target.removeAttr("aria-invalid"),this.$target.parents(".form-group").removeClass("has-error"),this.$target.trigger("change"),this.options.onUpdate&&this.options.onUpdate(i)},o.prototype.updateLinked=function(t){var e,i;null!==this.options.previous&&""!==this.options.previous.val()&&t<this.options.previous.datepicker("getDate")&&(e=this.formatDate(t,this.options.previous.datepicker("outputFormat")),this.options.previous.val(e)),null!==this.options.next&&""!==this.options.next.val()&&this.options.next.datepicker("getDate")<t&&(i=this.formatDate(t,this.options.next.datepicker("outputFormat")),this.options.next.val(i))},o.prototype.hideObject=function(t){t.attr("aria-hidden",!0),t.fadeOut(100)},o.prototype.showObject=function(t){t.attr("aria-hidden",!1),t.fadeIn(100)},o.prototype.show=function(){var t,i=this;b(".datepicker-calendar").trigger("ab.datepicker.opening",[i.id]),1==this.options.modal?(this.modalEventHandler||(this.modalEventHandler=function(t){return i.$grid.focus(),t.stopPropagation,!1}),b(document).on("click mousedown mouseup",this.modalEventHandler),this.greyOut(!0),t=parseInt(b("#datepicker-overlay").css("z-index"),10)||40,this.$calendar.css("z-index",t+1)):(b(document).on("click",b.proxy(this.handleDocumentClick,this)),this.$calendar.on("ab.datepicker.opening",function(t,e){e!=i.id?i.hide():i.$grid.focus()})),this.$calendar.on("ab.datepicker.opened",function(t,e){e==i.id&&i.$grid.focus()});var e=Math.max(0,Math.floor(this.$group.offset().top-this.$label.offset().top)),n=Math.max(0,Math.floor(this.$group.offset().left-this.$label.offset().left)),s=parseInt(this.$calendar.parent().css("padding-left"),10),o=this.$calendar.outerHeight(),a=this.$group.offset().top,r=(this.$group.offset().left,this.$group.outerHeight(!0)),l=Math.floor(a-b(window).scrollTop()),c=Math.floor(b(window).height()-(a+r-b(window).scrollTop()));c<o&&c<l?(this.$calendar.addClass("above"),this.$calendar.css({top:e-o+"px",left:n+s+"px"})):(this.$calendar.addClass("below"),this.$calendar.css({top:r+e+"px",left:n+s+"px"})),this.$calendar.attr("aria-hidden","false"),this.$calendar.fadeIn(100),b(".datepicker-calendar").trigger("ab.datepicker.opened",[i.id])},o.prototype.refresh=function(){switch(this.drawCalendarHeader(),this.gridType){case 0:this.populateDaysCalendar();break;case 1:this.populateMonthsCalendar();break;case 2:this.populateYearsCalendar()}},o.prototype.handleDocumentClick=function(t){return 0==b(t.target).parents("#datepicker-calendar-"+this.id).length?(this.hide(),!0):(this.$grid.focus(),t.stopPropagation,!1)},o.prototype.hide=function(t){0==this.options.inline&&(1==this.options.modal?(this.modalEventHandler&&b(document).off("click mousedown mouseup",this.modalEventHandler),this.greyOut(!1)):(b(document).off("click",this.handleDocumentClick),this.$calendar.off("ab.datepicker.opening")),this.$calendar.removeClass("above below"),this.$calendar.attr("aria-hidden","true"),this.$calendar.fadeOut(100),b(".datepicker-calendar").trigger("ab.datepicker.closed",[this.id]),t||this.$target.focus())},o.prototype.greyOut=function(t){var e=b("#datepicker-overlay");0==e.length&&t&&(b("body").append('<div id="datepicker-overlay" class="datepicker-overlay"></div>'),e=b("#datepicker-overlay")),t?e.fadeIn(100):e.fadeOut(100)},o.prototype.absolutePosition=function(t){var e=0,i=0;if(t.getBoundingClientRect)var n=t.getBoundingClientRect(),s=document.body,o=document.documentElement,a=window.pageYOffset||o.scrollTop||s.scrollTop,r=window.pageXOffset||o.scrollLeft||s.scrollLeft,l=o.clientTop||s.clientTop||0,c=o.clientLeft||s.clientLeft||0,e=Math.round(n.top+a-l),i=Math.round(n.left+r-c);else for(;t;)e+=parseInt(t.offsetTop,10),i+=parseInt(t.offsetLeft,10),t=t.offsetParent;return{top:e,left:i}},o.prototype.getDaysInMonth=function(t,e){return 32-new Date(t,e,32).getDate()},o.prototype.previousMonth=function(t,e){return 0==e?(e=11,t--):e--,{year:t,month:e}},o.prototype.nextMonth=function(t,e){return 11==e?(e=0,t++):e++,{year:t,month:e}},o.prototype.formatDate=function(t,e){function i(t){return(t<0||9<t?"":"0")+t}function n(t){var e=new Date(t.getTime());return e.setHours(0),t-e}var s,o,a,r,l,c=t.getFullYear()+"",h=t.getMonth()+1,d=t.getDate(),u=(s=t,o=new Date(s.getFullYear(),0,0),Math.floor((s-o)/864e5)),p=t.getDay(),f=t.getHours(),m=t.getMinutes(),g=t.getSeconds(),v=(a=t,r=new Date(a.getFullYear(),0,1),Math.ceil(((a-r)/864e5+r.getDay()+1)/7)),b=(l=t,Math.ceil((l.getDate()-1-l.getDay())/7)),y=Math.floor(t.getDate()/7)+1,_=Math.ceil((t.getMonth()+1)/3),w=t.getFullYear()<1?0:1,C={y:""+c,yyyy:c,yy:c.substring(2,4),L:h,LL:i(h),LLL:this.locales.month_names_abbreviated[h-1],LLLL:this.locales.month_names[h-1],LLLLL:this.locales.month_names_narrow[h-1],M:h,MM:i(h),MMM:this.locales.month_names_abbreviated[h-1],MMMM:this.locales.month_names[h-1],MMMMM:this.locales.month_names_narrow[h-1],d:d,dd:i(d),D:u,DD:u,DDD:u,A:Math.round(n(t)*Math.pow(10,-2)),AA:Math.round(n(t)*Math.pow(10,-1)),AAA:Math.round(n(t)*Math.pow(10,0)),AAAA:Math.round(n(t)*Math.pow(10,1)),AAAAA:Math.round(n(t)*Math.pow(10,2)),AAAAAA:Math.round(n(t)*Math.pow(10,3)),E:this.locales.day_names_abbreviated[p],EE:this.locales.day_names_abbreviated[p],EEE:this.locales.day_names_abbreviated[p],EEEE:this.locales.day_names[p],EEEEE:this.locales.day_names_narrow[p],EEEEEE:this.locales.day_names_short[p],e:p,ee:p,eee:this.locales.day_names_abbreviated[p],eeee:this.locales.day_names[p],eeeee:this.locales.day_names_narrow[p],eeeeee:this.locales.day_names_short[p],c:p,cc:p,ccc:this.locales.day_names_abbreviated[p],cccc:this.locales.day_names[p],ccccc:this.locales.day_names_narrow[p],cccccc:this.locales.day_names_short[p],F:y,G:this.locales.era_names_abbreviated[w],GG:this.locales.era_names_abbreviated[w],GGG:this.locales.era_names_abbreviated[w],GGGG:this.locales.era_names[w],GGGGG:this.locales.era_names_narrow[w],Q:_,QQ:i(_),QQQ:this.locales.quarter_names_abbreviated[_-1],QQQQ:this.locales.quarter_names[_-1],QQQQQ:this.locales.quarter_names_narrow[_-1],q:_,qq:i(_),qqq:this.locales.quarter_names_abbreviated[_-1],qqqq:this.locales.quarter_names[_-1],qqqqq:this.locales.quarter_names_narrow[_-1],H:f,HH:i(f),h:0==f?12:12<f?f-12:f,hh:i(0==f?12:12<f?f-12:f),K:11<f?f-12:f,k:f+1,KK:i(11<f?f-12:f),kk:i(f+1),a:11<f?this.locales.day_periods.pm:this.locales.day_periods.am,m:m,mm:i(m),s:g,ss:i(g),w:v,ww:i(v),W:b};return e.replace(/('[^']+'|y{1,4}|L{1,5}|M{1,5}|c{1,6}|d{1,2}|D{1,3}|E{1,6}|e{1,6}|F{1,1}|G{1,5}|Q{1,5}|q{1,5}|H{1,2}|h{1,2}|K{1,2}|k{1,2}|m{1,2}|s{1,2}|w{1,2}|W{1,1}|A{1,6})/g,function(t){return"'"===t.charAt(0)?t.substr(1,t.length-2):C[t]||t})},o.prototype.createDateFromFormat=function(t,a){function r(t,e,i,n){for(var s=n;i<=s;s--){var o=t.substring(e,e+s);if(o.length<i)return null;if(/^\d+$/.test(o))return o}return null}function l(t,e){for(var i=0;i<t.length;i++){var n=t[i];if(a.substring(e,e+n.length).toLowerCase()==n.toLowerCase())return n.length}return 0}var c=0,e=new Date,h=e.getYear(),d=e.getMonth()+1,u=1,p=0,f=0,m=0,g="",v=this;if(b.each(t.match(/(.).*?\1*/g),function(t,e){switch(e){case"yyyy":null!=(h=r(a,c,4,4))&&(c+=h.length);break;case"yy":null!=(h=r(a,c,2,2))&&(c+=h.length);break;case"y":null!=(h=r(a,c,2,4))&&(c+=h.length);break;case"MMM":case"LLL":for(var i=d=0;i<v.locales.month_names_abbreviated.length;i++){var n=v.locales.month_names_abbreviated[i];if(a.substring(c,c+n.length).toLowerCase()==n.toLowerCase()){d=i+1,c+=n.length;break}}break;case"MMMM":case"LLLL":for(i=d=0;i<v.locales.month_names.length;i++){n=v.locales.month_names[i];if(a.substring(c,c+n.length).toLowerCase()==n.toLowerCase()){d=i+1,c+=n.length;break}}break;case"EEE":case"EE":case"E":case"eee":c+=l(v.locales.day_names_abbreviated,c);break;case"EEEE":case"eeee":case"cccc":c+=l(v.locales.day_names,c);break;case"EEEEEE":case"eeeeee":case"cccccc":c+=l(v.locales.day_names_short,c);break;case"MM":case"M":case"LL":case"L":if(null==(d=r(a,c,e.length,2))||d<1||12<d)return null;c+=d.length;break;case"dd":case"d":if(null==(u=r(a,c,e.length,2))||u<1||31<u)return null;c+=u.length;break;case"hh":case"h":if(null==(p=r(a,c,e.length,2))||p<1||12<p)return null;c+=p.length;break;case"HH":case"H":if(null==(p=r(a,c,e.length,2))||p<0||23<p)return null;c+=p.length;break;case"KK":case"K":if(null==(p=r(a,c,e.length,2))||p<0||11<p)return null;c+=p.length;break;case"kk":case"k":if(null==(p=r(a,c,e.length,2))||p<1||24<p)return null;c+=p.length,p--;break;case"mm":case"m":if(null==(f=r(a,c,e.length,2))||f<0||59<f)return null;c+=f.length;break;case"ss":case"s":if(null==(m=r(a,c,e.length,2))||m<0||59<m)return null;c+=m.length;break;case"a":var s=v.locales.day_periods.am.length,o=v.locales.day_periods.pm.length;if(a.substring(c,c+s)==v.locales.day_periods.am)g="AM",c+=s;else{if(a.substring(c,c+o)!=v.locales.day_periods.pm)return null;g="PM",c+=o}break;default:if(a.substring(c,c+e.length)!=e)return null;c+=e.length}}),c!=a.length)return null;if(null==h)return null;if(2==h.length&&(h=50<h?+h+1900:+h+2e3),d<1||12<d)return null;if(2==d)if(h%4==0&&h%100!=0||h%400==0){if(29<u)return null}else if(28<u)return null;return(4==d||6==d||9==d||11==d)&&30<u?null:(p<12&&"PM"==g?p=+p+12:11<p&&"AM"==g&&(p-=12),new Date(h,d-1,u,p,f,m))},o.prototype.parseDate=function(i){var n=null,s=this;return b.each(this.options.inputFormat,function(t,e){if(null!=(n=s.createDateFromFormat(e,i)))return!1}),null==n&&(n=s.createDateFromFormat(this.options.outputFormat,i)),n},o.prototype.min=function(t){return null!=t&&(this.options.min=t instanceof Date?t:this.parseDate(t),null!=this.options.min&&this.dateObj<this.options.min&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.min),0!=this.options.inline&&this.refresh()),this.options.min},o.prototype.max=function(t){return null!=t&&(this.options.max=t instanceof Date?t:this.parseDate(t),null!=this.options.max&&this.dateObj>this.options.max&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.max),0!=this.options.inline&&this.refresh()),this.options.max},o.prototype.theme=function(t){return null!=t&&(this.$button.removeClass(this.options.theme),this.$calendar.removeClass(this.options.theme),this.options.theme=t,this.$button.addClass(this.options.theme),this.$calendar.addClass(this.options.theme)),this.options.theme},o.prototype.firstDayOfWeek=function(t){return null!=t&&(this.options.firstDayOfWeek=parseInt(t,10),0==this.options.inline?this.drawCalendarHeader():this.refresh()),this.options.firstDayOfWeek},o.prototype.daysOfWeekDisabled=function(t){var i;return null!=t&&(this.options.daysOfWeekDisabled=[],b.isArray(t)||(t=[t]),i=this,b.each(t,function(t,e){"number"==typeof e?i.options.daysOfWeekDisabled.push(e):"string"==typeof e&&i.options.daysOfWeekDisabled.push(parseInt(e,10))})),this.options.daysOfWeekDisabled},o.prototype.weekDayFormat=function(t){return null!=t&&(this.options.weekDayFormat=t,this.drawCalendarHeader()),this.options.weekDayFormat},o.prototype.inputFormat=function(t){return null!=t&&(b.isArray(t)||(t=[t]),this.$target.attr("placeholder")==this.options.inputFormat[0]&&this.$target.attr("placeholder",t[0]),this.options.inputFormat=t),this.options.inputFormat},o.prototype.outputFormat=function(t){return null!=t&&(this.options.outputFormat=t),this.options.outputFormat},o.prototype.modal=function(t){var e;return null!=t&&(this.options.modal=t,1==this.options.modal?(0==this.options.inline&&(this.showObject(this.$calendar.find(".datepicker-close-wrap")),this.showObject(this.$calendar.find(".datepicker-bn-close-label"))),this.$close=this.$calendar.find(".datepicker-close"),this.$close.html(this.options.closeButtonTitle).attr("title",this.options.closeButtonLabel),this.$calendar.find(".datepicker-bn-close-label").html(this.options.closeButtonLabel),(e=this).$close.click(function(t){return e.handleCloseClick(t)}),this.$close.keydown(function(t){return e.handleCloseKeyDown(t)})):(this.hideObject(this.$calendar.find(".datepicker-close-wrap")),this.hideObject(this.$calendar.find(".datepicker-bn-close-label")))),this.options.modal},o.prototype.inline=function(t){return null!=t&&(0!=t?(this.hideObject(this.$button),this.hideObject(this.$calendar.find(".datepicker-close-wrap")),this.hideObject(this.$calendar.find(".datepicker-bn-close-label")),("string"==typeof t?b("#"+t):t).append(this.$calendar),this.$calendar.css({position:"relative",left:"0px",top:"0px"}),this.options.inline=t,this.initializeDate(),this.showObject(this.$calendar)):(this.$target.parent().after(this.$calendar),this.showObject(this.$button),1==this.options.modal&&(this.showObject(this.$calendar.find(".datepicker-close-wrap")),this.showObject(this.$calendar.find(".datepicker-bn-close-label"))),"static"===this.$calendar.parent().css("position")&&this.$calendar.parent().css("position","relative"),this.$calendar.css({position:"absolute"}),this.options.inline=t,this.hide())),this.options.inline},o.prototype.format=function(t){return this.formatDate(t,this.options.outputFormat)},o.prototype.enable=function(){this.$button.removeClass("disabled"),this.$button.attr("aria-disabled",!1),this.$button.attr("tabindex",0)},o.prototype.disable=function(){this.hide(),this.$button.addClass("disabled"),this.$button.attr("aria-disabled",!0),this.$button.attr("tabindex",-1)},o.prototype.datesDisabled=function(t){this.options.datesDisabled=[],b.isArray(t)||(t=[t]);var n=this;b.each(t,function(t,e){var i;"string"==typeof e?null!==(i=n.parseDate(e))&&n.options.datesDisabled.push(n.format(i)):e instanceof Date&&!isNaN(e.valueOf())&&n.options.datesDisabled.push(n.format(e))})},o.prototype.startview=function(t){switch(t){case 1:case"months":this.options.startView=1;break;case 2:case"years":this.options.startView=2;break;default:this.options.startView=0}},o.prototype.setLocales=function(t){this.locales=t,this.options.inputFormat=[this.locales.short_format],this.options.outputFormat=this.locales.short_format,this.options.titleFormat=this.locales.full_format,this.options.firstDayOfWeek=this.locales.firstday_of_week,this.options.buttonTitle=this.locales.texts.buttonTitle,this.$button.find("span").attr("title",this.options.buttonTitle),this.options.buttonLabel=this.locales.texts.buttonLabel,this.options.prevButtonLabel=this.locales.texts.prevButtonLabel,this.options.prevMonthButtonLabel=this.locales.texts.prevMonthButtonLabel,this.options.prevYearButtonLabel=this.locales.texts.prevYearButtonLabel,this.options.nextButtonLabel=this.locales.texts.nextButtonLabel,this.options.nextMonthButtonLabel=this.locales.texts.nextMonthButtonLabel,this.options.nextYearButtonLabel=this.locales.texts.nextYearButtonLabel,this.options.changeMonthButtonLabel=this.locales.texts.changeMonthButtonLabel,this.options.changeYearButtonLabel=this.locales.texts.changeYearButtonLabel,this.options.changeRangeButtonLabel=this.locales.texts.changeRangeButtonLabel,this.options.closeButtonTitle=this.locales.texts.closeButtonTitle,this.options.closeButtonLabel=this.locales.texts.closeButtonLabel,this.options.calendarHelp=this.locales.texts.calendarHelp,this.drawCalendarHeader(),"RTL"===this.locales.directionality?this.$grid.addClass("rtl"):this.$grid.removeClass("rtl")};var t=b.fn.datepicker;b.fn.datepicker=function(n,s){if("string"!=typeof n||1!=b(this).length)return this.each(function(){var t=b(this),e=t.data("ab.datepicker"),i=b.extend({},o.DEFAULTS,t.data(),"object"==typeof n&&n);e||t.data("ab.datepicker",e=new o(this,i)),"string"==typeof n&&e[n](s)});var t=b(this).eq(0).data("ab.datepicker");return t?t[n](s):void 0},b.fn.datepicker.Constructor=o,b.fn.datepicker.noConflict=function(){return b.fn.datepicker=t,this}}),function(r){var t,e=["","-webkit-","-ms-","-moz-","-o-"],i=document.createElement("div"),n=!1,s=!1,y=!1,_=0,o=["56"],a=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame,l=0,w=[],c={unstick:function(){for(var t,e,i=r(this).data("sticky-id"),n=w.length-1;0<=n;n--)if(w[n].id==i){t=n;break}return void 0!==t&&(e=w.splice(t,1)),void 0!==e&&r(this).removeAttr("style").next("."+e[0].options.holderClass).remove(),this}};if(!function(){if(!window.chrome)return!1;var t=/Chrom(e|ium)\/(\d+)/.exec(navigator.appVersion);return t&&~o.indexOf(t[2])}())for(var h=0,d=e.length;h<d;h++)if(i.setAttribute("style","position:"+e[h]+"sticky"),""!==i.style.position){n=!0;break}function C(t){var e,i,n,s,o,a,r,l={top:0,left:0},c=t&&t.ownerDocument;if(c)return(i=c.body)===t?{top:i.offsetTop,left:i.offsetLeft}:(e=c.documentElement,void 0!==t.getBoundingClientRect&&(l=t.getBoundingClientRect()),n=window,s=e.clientTop||i.clientTop||0,o=e.clientLeft||i.clientLeft||0,a=n.pageYOffset||e.scrollTop,r=n.pageXOffset||e.scrollLeft,{top:l.top+a-s,left:l.left+r-o});options.debug&&console.error("i-sticky: no element.ownerDocument defined")}function u(){var t=_,e=window.pageXOffset||document.documentElement.scrollLeft,i=t+(window.innerHeight||document.documentElement.clientHeight);y=!1;for(var n=0,s=w.length;n<s;n++){var o=w[n],a=o.el.offsetHeight,r=C(o.parent),l=o.style.isStuck?C(o.holder):C(o.el),c=o.style.top?o.style.top.px:0,h=o.style.bottom?o.style.bottom.px:0,d=o.style.home,u=!0,p=o.el.className.split(" "),f=0,m=(r.top,l.top-c),g=r.top+o.parent.offsetHeight-a-c,v=r.top+a+h,b=l.top+a+h;for(f in p)p[f]===o.options.stuckClass&&p.splice(f,1);o.el.className=p.join(" "),o.style.bottom&&i<=v?d=o.style.bottom.opposite:o.style.bottom&&v<i&&i<b?d=o.style.bottom.fixed:o.style.top&&m<t&&t<g?d=o.style.top.fixed:o.style.top&&g<=t?d=o.style.top.opposite:u=!1,o.style.isStuck!==u&&(o.holder.style.display=u?"block":"none"),u&&o.options.stuckClass&&(o.el.className+=" "+o.options.stuckClass),u&&(d+="margin-left:-"+(e-o.style.margin.left)+"px;"),o.options.fixWidth?d+="width:"+(u?o.holder.offsetWidth+"px;":"auto;"):d+="min-width:"+(u?o.holder.offsetWidth+"px;":"auto;"),d!==o.style.current&&(o.el.setAttribute("style",d),o.style.isStuck=u,o.style.current=d),o.options.holderAutoHeight&&u&&a!=o.style.height&&(o.holder.style.height=a+"px",o.style.height=a)}0}function p(){w.length&&(_=document.documentElement.scrollTop||document.body.scrollTop,y||(y=!0,a?a(u):(t&&clearTimeout(t),t=setTimeout(u,15))))}function f(){s||(r(window).on("scroll",p).on("resize",p),p(),s=!0)}r.fn.iSticky=function(t){if(n){if("object"!=typeof t||!t.force)return this;f()}if("string"==typeof t&&c[t])return c[t].apply(this,Array.prototype.slice.call(arguments,1));var o=r.extend({holderClass:"i-sticky__holder",holderAutoHeight:!0,debug:!1,fixWidth:!0,stuckClass:""},t),a=this.selector;return this.each(function(){var t,e,i,n=r(this),s="sticky-"+ ++l;n.hide(),t=n.css("top"),e=n.css("bottom"),n.show(),t||e?(n.data("sticky-id",s).after('<span class="'+o.holderClass+'" style="display:none;"></span>'),i={id:s,el:this,parent:this.parentElement,holder:this.nextSibling,style:{home:"position:static;",top:void 0,bottom:void 0,current:"",height:0,isStuck:!1,margin:{left:parseInt(n.css("margin-left"),10)}},options:{holderClass:o.holderClass,holderAutoHeight:o.holderAutoHeight,fixWidth:o.fixWidth,stuckClass:o.stuckClass||""}},"auto"!==t&&(i.style.top={fixed:"position:fixed;top:"+t+";bottom:auto;",opposite:"position:absolute;bottom:0;top:auto;",px:parseInt(t,10)}),"auto"!==e&&(i.style.bottom={fixed:"position:fixed;bottom:"+e+";top:auto;",opposite:"position:absolute;top:0;bottom:auto;",px:parseInt(e,10)}),w.push(i),p()):o.debug&&console.warn("i-sticky: element `top` or `bottom` properties must be set in pixels",a,this)})},n||f()}(jQuery),function(){var t,a,p,r,e,i,n,f=document.querySelector(".it-header-sticky");f&&(t=document.querySelector(".custom-navbar-toggler"),n=!1,(e=t)&&(n="none"===(i=window.getComputedStyle(e)).display||"hidden"===i.visibility),a=!1,r=void(p=0),function(e){var t=document.querySelector(".it-header-slim-wrapper"),i=document.querySelector(".it-header-center-wrapper"),n=document.querySelector(".it-header-navbar-wrapper"),d=n&&n.offsetHeight||0,s=t&&t.offsetHeight||0,u=s;e&&d&&(u=s+i?i.offsetHeight:0);function o(t,e,i){var n,s,o,a,r,l,c,h;void 0===e&&(e=!0),t&&(n=document.querySelector(".menu-wrapper"),e?(s=document.querySelector(".it-brand-wrapper"),o=document.querySelector(".it-search-wrapper"),a=document.querySelector(".it-user-wrapper"),r=s?s.cloneNode(!0):null,l=o?o.cloneNode(!0):null,c=a?a.cloneNode(!0):null,r&&n.insertBefore(r,n.childNodes[0]).classList.add("cloned"),l&&n.appendChild(l).classList.add("cloned"),c&&n.appendChild(c).classList.add("cloned").remove("show")):((h=document.getElementsByClassName("cloned"))&&Array.from(h).forEach(function(t){t.parentElement.removeChild(t)}),"function"==typeof i&&i())),f.nextElementSibling.style.paddingTop=e?d+(t?u-p:d-p)+"px":"0px"}r=function(){var t=d;window.scrollY+p>=u&&!a?(a=!0,f.classList.add("is-sticky"),o(e,!0),t!==d&&(p=d-t)):window.scrollY+p<u&&a&&(a=!1,f.classList.remove("is-sticky"),o(e,!1))},window.addEventListener("scroll",r),r()}(n))}(),function(){var p,e,i=document.getElementsByClassName("sticky-wrapper"),n=document.querySelector(".custom-navbar-toggler"),t=o(n),s=void 0;function o(t){var e,i=!1;return t&&(i="none"===(e=window.getComputedStyle(t)).display||"hidden"===e.visibility),i}i&&i.length&&(p=!1,e=function(h){function d(t,e){return h?parseInt((window.getComputedStyle?getComputedStyle(t,null):t.currentStyle)[e]):0}function u(t,e,i){return!h&&t&&"bottom"===i?"0px":!h&&t&&"top"===i?"auto":"bottom"===i?"":e+"px"}var t=h?document.querySelector(".it-header-navbar-wrapper"):document.querySelector(".it-header-center-wrapper");s=function(){var c=t?t.offsetHeight:0;Array.from(i).forEach(function(t){!function(t){var e=t.offsetHeight,i=t.parentNode;i.style.position="relative";var n=i.offsetWidth||0,s=i.offsetHeight,o=d(i,"paddingTop"),a=d(i,"paddingRight"),r=i.getBoundingClientRect().top||0,l=c+o;r<=c?(p=!0,t.classList.add("is-sticky"),t.style.top=u(!0,l,"top"),t.style.bottom=u(!0,l,"bottom"),t.style.width=h?n-a+"px":""):p&&c<r&&(p=!1,t.classList.remove("is-sticky"),t.style.top="",t.style.bottom="",t.style.width=""),p&&h&&(r<0&&Math.abs(r)+e+o+c>s?t.classList.add("at-bottom"):t.classList.remove("at-bottom"))}(t)})},window.addEventListener("scroll",s),s()},window.addEventListener("resize",function(){var t;s&&(window.removeEventListener("scroll",s),t=o(n),e(t))}),e(t))}();var styleNode=document.createElement("style"),__PUBLIC_PATH__=window.__PUBLIC_PATH__?window.__PUBLIC_PATH__:"/bootstrap-italia/dist/fonts";styleNode.innerHTML="\n/* Titillium+Web:300,400,600,700 */\n\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 300;\n  src: local('Titillium Web Light'), local('TitilliumWeb-Light'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 300;\n  src: local('Titillium Web Light'), local('TitilliumWeb-Light'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Titillium Web Regular'), local('TitilliumWeb-Regular'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Titillium Web Regular'), local('TitilliumWeb-Regular'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 600;\n  src: local('Titillium Web SemiBold'), local('TitilliumWeb-SemiBold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 600;\n  src: local('Titillium Web SemiBold'), local('TitilliumWeb-SemiBold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Titillium Web Bold'), local('TitilliumWeb-Bold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Titillium Web Bold'), local('TitilliumWeb-Bold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n\n/* Lora:400,700 */\n\n/* latin-ext */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Lora Regular'), local('Lora-Regular'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Lora Regular'), local('Lora-Regular'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Lora Bold'), local('Lora-Bold'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Lora Bold'), local('Lora-Bold'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n\n/* Roboto+Mono:400,700 */\n\n/* latin-ext */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Roboto Mono'), local('RobotoMono-Regular'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Roboto Mono'), local('RobotoMono-Regular'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Roboto Mono Bold'), local('RobotoMono-Bold'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Roboto Mono Bold'), local('RobotoMono-Bold'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n",document.getElementsByTagName("head")[0].appendChild(styleNode),$(function(){$.fn.autocomplete=function(t){return t=$.extend({data:{}},t),this.each(function(){var c=$(this),h=null,d=c.data("autocomplete");d&&Object.keys(d).length&&((h=$('<ul class="autocomplete-list"></ul>')).insertAfter($(this).next()),c.on("keyup",function(t){var e=c.val();if(h.empty(),e.length)for(var i in d){var n,s=new RegExp("("+e+")","gi"),o=d[i].text.replace(s,"<mark>$1</mark>"),a=d[i].label?"<em>"+d[i].label+"</em>":"",r=d[i].icon?d[i].icon:"",l=d[i].link?d[i].link:"#";-1!==o.toLowerCase().indexOf(e.toLowerCase())&&($(this).closest(".form-group").find(".autocomplete-list").addClass("autocomplete-list-show"),n=$('<li>\n              <a href="'+l+'">\n                '+r+'\n                <span class="autocomplete-list-text">\n                  <span>'+o+"</span>\n                  "+a+"\n                </span>\n              </a>\n             </li>"),h.append(n))}else $(this).closest(".form-group").find(".autocomplete-list").removeClass("autocomplete-list-show")}))})},$(".autocomplete").autocomplete()}),$(function(){var t=$('a[data-attribute*="back-to-top"]');$(window).on("scroll",function(){t.toggleClass("back-to-top-show",t.length&&100<=$(this).scrollTop())}),t.on("click",function(){$("body,html").animate({scrollTop:0},800)})});var ComponenteBase=function(s){var t="componenteBase",o="bs.componente-base",e=s.fn[t],i={CLICK_DATA_API:"click.bs.componente-base.data-api"},n=function(){function n(t){this._element=t}return n.prototype.publicFunction=function(t){console.log(s(t).data("value"),n.VERSION)},n._jQueryInterface=function(i){return this.each(function(){var t=s(this),e=t.data(o);if(e||(e=new n(this),t.data(o,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},n._handleConsole=function(e){return function(t){t&&t.preventDefault(),e.publicFunction(this)}},_createClass2(n,null,[{key:"VERSION",get:function(){return"1.0.0"}}]),n}();return s(document).on(i.CLICK_DATA_API,".componente-base",n._handleConsole(new n)),s.fn[t]=n._jQueryInterface,s.fn[t].Constructor=n,s.fn[t].noConflict=function(){return s.fn[t]=e,n._jQueryInterface},n}($),Cookiebar=function(s){var t="cookiebar",o="bs.cookiebar",e="."+o,i=".data-api",n=s.fn[t],a="cookies_consent",r=".cookiebar",l='[data-accept="cookiebar"]',c={CLOSE:"close"+e,CLOSED:"closed"+e,LOAD_DATA_API:"load"+e+i,CLICK_DATA_API:"click"+e+i},h="cookiebar",d="show",u=function(){function n(t){this._element=t}var t=n.prototype;return t.show=function(t){s(t).addClass(d).attr("aria-hidden","false").attr("aria-live","polite")},t.close=function(t){t=t||this._element;var e=this._getRootElement(t);this._triggerCloseEvent(e).isDefaultPrevented()||(this._setCookieEU(),this._removeElement(e))},t.dispose=function(){s.removeData(this._element,o),this._element=null},t._setCookieEU=function(){var t=new Date;t.setDate(t.getDate()+30);var e=escape("true")+("; expires="+t.toUTCString());document.cookie=a+"="+e+"; path=/"},t._getSelectorFromElement=function(t){var e,i=t.getAttribute("data-target");i&&"#"!==i||(i=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(i)?i:null}catch(t){return null}},t._getRootElement=function(t){var e=this._getSelectorFromElement(t),i=!1;return e&&(i=s(e)[0]),i=i||s(t).closest("."+h)[0]},t._triggerCloseEvent=function(t){var e=s.Event(c.CLOSE);return s(t).trigger(e),e},t._removeElement=function(t){s(t).removeClass(d).attr("aria-hidden","true").attr("aria-live","off"),this._destroyElement(t)},t._destroyElement=function(t){s(t).detach().trigger(c.CLOSED).remove()},n._jQueryInterface=function(i){return this.each(function(){var t=s(this),e=t.data(o);if(e||(e=new n(this),t.data(o,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},n._handleAccept=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},n._handleConsent=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},n._getCookieEU=function(){for(var t,e,i=document.cookie.split(";"),n=0;n<i.length;n++)if(t=i[n].substr(0,i[n].indexOf("=")),e=i[n].substr(i[n].indexOf("=")+1),(t=t.replace(/^\s+|\s+$/g,""))==a)return unescape(e)},_createClass2(n,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),n}();return s(document).on(c.CLICK_DATA_API,l,u._handleAccept(new u)),s(window).on(c.LOAD_DATA_API,function(){var t=s.makeArray(s(r));if(!u._getCookieEU())for(var e=t.length;e--;){var i=s(t[e]);u._jQueryInterface.call(i,"show")}}),s.fn[t]=u._jQueryInterface,s.fn[t].Constructor=u,s.fn[t].noConflict=function(){return s.fn[t]=n,u._jQueryInterface},u}($);function notificationShow(t,e){var i;$("#"+t).fadeIn(300),$("#"+t).hasClass("dismissable")||($("#"+t).fadeIn(300),i="number"==typeof e?e:7e3,setTimeout(function(){$("#"+t).fadeOut(100)},i))}$(function(){$(".navbar .dropdown").on("show.bs.dropdown",function(){window.matchMedia("(max-width: 992px)").matches&&$(this).find(".dropdown-menu").first().stop(!0,!0).slideDown(180)}),$(".navbar .dropdown").on("hide.bs.dropdown",function(){window.matchMedia("(max-width: 992px)").matches&&$(this).find(".dropdown-menu").first().stop(!0,!0).slideUp(180)}),$(window).resize(function(){window.matchMedia("(min-width: 993px)").matches&&$(".navbar .dropdown-menu.show").removeAttr("style")})}),$(function(){function o(t){var e,i=t.siblings("label:not(.active)");i&&i.length&&(e=i[0].offsetWidth>t[0].offsetWidth-20?t[0].offsetWidth:"auto",$(i[0]).css("width",e))}var t='input[type="text"],input[type="password"],input[type="email"],input[type="email"],input[type="url"],input[type="tel"],input[type="number"],input[type="search"],textarea',e='input[type="file"]';$(document).on("focus",t,function(t){var e=$(t.target);e.siblings("label, i").addClass("active");var i=e.siblings("label");i&&i.length&&$(i[0]).css("width","auto")}).on("blur",t,function(t){var e=$(t.target),i=!e.val(),n=!e.attr("placeholder");i&&n&&(e.siblings("label, i").removeClass("active"),o(e))}).on("change",t,function(t){var e=$(t.target);s(e),a(e)}).on("blur",e,function(t){$(t.target).siblings("label").addClass("active")}).on("change",e,function(t){var e=$(t.target),n=t.currentTarget.files.length,s="",o="";for(i=0;i<n;i++)fileSize=parseInt(t.currentTarget.files[i].size,10)/1024,filesize=Math.round(fileSize),s=s+t.currentTarget.files[i].name+" ("+filesize+"kb); ";1<n&&(o=n+" file da caricare: "),e.siblings(".form-file-name").text(o+s)});function n(){$("body").find(t).removeClass("valid invalid").each(function(t,e){var i=$(e),n=!!i.val(),s=!!i.attr("placeholder");(n||s)&&i.siblings("label, i").css("transition","none").addClass("active"),n||s||(i.siblings("label, i").removeClass("active"),o(i))})}var s=function(t){var e=t.siblings("label, i"),i=t.val().length,n=!!t.attr("placeholder");i||n?e.addClass("active"):e.removeClass("active")},a=function(t){var e,i,n,s,o;t.hasClass("validate")&&(i=!(e=t.val()).length,n=!t[0].validity.badInput,i&&n?t.removeClass("valid").removeClass("invalid"):(s=t.is(":valid"),o=Number(t.attr("length"))||0,s&&(!o||o>e.length)?t.removeClass("invalid").addClass("valid"):t.removeClass("valid").addClass("invalid")))};$(window).resize(function(){$(t).each(function(t,e){var i=$(e);o(i)})}),n(),$(document).on("changed.bs.form-control",n)}),$(function(){var e;$(document).on("keydown mousedown",function(t){e="mousedown"===t.type}).on("focusin",function(t){e&&t.target&&$(t.target).addClass("focus--mouse")}).on("focusout",function(t){t.target&&$(t.target).removeClass("focus--mouse")})}),$(function(){$('a[data-attribute*="forward"]').on("click",function(t){var e=$(this.hash);e.length&&(t.preventDefault(),$("html, body").animate({scrollTop:e.offset().top},500))})}),$(function(){var s=$(".custom-navbar-toggler"),t=$(".close-div"),o=$(".it25-close-div"),e=$(".overlay"),n=$(".it-back-button"),i=$(".navbar-collapsable a");$(s).on("click",function(t){var e=$(this).attr("data-target"),i=$(e).find(".overlay");$(this).attr("aria-expanded","true"),$(n).fadeIn(),$(e).show(),$(i).fadeIn(),$(e).addClass("expanded"),$(this).hide(),$(o).show(),$(this).hasClass("it25-btn-sidebar")&&$("body,html").animate({scrollTop:0},800)}),$(e).on("click",function(){var t=$(this).closest(".navbar-collapsable"),e=$(this).closest(".navbar").find(".custom-navbar-toggler"),i=$(t).find(".overlay");$(e).attr("aria-expanded","false"),$(t).removeClass("expanded"),$(i).fadeOut(),$(o).hide(),$(s).show(),setTimeout(function(){$(t).hide()},300)}),$(t).on("click",function(t){var e=$(this).closest(".navbar-collapsable"),i=$(this).closest(".navbar").find(".custom-navbar-toggler"),n=$(e).find(".overlay");$(i).attr("aria-expanded","false"),$(e).removeClass("expanded"),$(n).fadeOut(),$(o).hide(),$(s).show(),$(e).hide()}),$(o).on("click",function(t){var e=$(this).find(".close-menu").attr("data-target"),i=$(".it25-barra-ist").find(".custom-navbar-toggler"),n=$(e).find(".overlay");$(i).attr("aria-expanded","false"),$(e).removeClass("expanded"),$(n).fadeOut(),$(this).hide(),$(s).show(),setTimeout(function(){$(e).hide()},300)}),$(i).on("blur",function(t){closemenu=$(this).closest(".navbar-collapsable").find(".close-div .btn"),$(this).closest(".navbar-collapsable").hasClass("expanded")&&setTimeout(function(){var t=document.activeElement;0==$(t).closest(".navbar-collapsable").length&&$(closemenu).trigger("click")},50)}),$(t).on("blur",function(t){closemenu=$(this),$(this).closest(".navbar-collapsable").hasClass("expanded")&&setTimeout(function(){var t=document.activeElement;0==$(t).closest(".navbar-collapsable").length&&$(closemenu).trigger("click")},50)})}),$(function(){$(".custom-navbar-toggler"),$(".close-div");var i=$(".overlay"),t=$(".it-back-button");$(".navbar-collapsable a");$('.it-bottom-navscroll ul li a[href^="#"]').on("click",function(t){t.preventDefault();var e=this.hash;$("html, body").animate({scrollTop:$(e).offset().top},600,function(){history.pushState?history.pushState(null,null,e):location.hash=e}),$(i).trigger("click")}),$(t).click(function(t){$(i).trigger("click"),$(this).fadeOut(),t.preventDefault()}),$(window).on("scroll",function(){var t=$(".it-page-sections-container").length?$(".it-page-sections-container").offset().top:0,a=$(window).scrollTop()-t;$(".it-page-section").each(function(t){var e,i,n,s,o;$(this).position().top<=a&&($(".it-navscroll-wrapper .menu-wrapper a.active").removeClass("active"),$(".it-navscroll-wrapper .menu-wrapper a").eq(t).addClass("active"),e=$(".it-navscroll-wrapper .menu-wrapper a").eq(t).closest("ul").prev("a"),i=$(e).closest("ul").prev("a"),$(e).addClass("active"),$(i).addClass("active"),n=$(".it-navscroll-wrapper .menu-wrapper a").eq(t).find("span").text(),o=(s=$(".it-navscroll-wrapper .custom-navbar-toggler")).find("span.it-list"),s.text(n),s.prepend(o))})}).scroll()}),$(function(){$(".go-back").on("click",function(){return window.history.back(),!1})}),$(document).on("click",".notification-close",function(){$(this).closest(".notification").fadeOut(100)}),$(function(){$(".upload-dragdrop:not(.success)").on("drag dragstart dragend dragover dragenter dragleave drop",function(t){t.preventDefault(),t.stopPropagation()}).on("dragover dragenter",function(){$(this).addClass("dragover")}).on("dragleave dragend drop",function(){$(this).removeClass("dragover")}).on("drop",function(t){$(this).addClass("loading"),$(this).find(".upload-progress").circularloader({backgroundColor:"#ffffff",fontColor:"#000000",fontSize:"40px",radius:130,progressBarBackground:"transparent",progressBarColor:"#0073e6",progressBarWidth:96,progressPercent:0})})});var progressDonut=function(i){return{generate:function(t,e){i(t).circularloader({backgroundColor:"#fff",fontColor:"#000",fontSize:40,radius:130,progressBarBackground:"transparent",progressBarColor:"#0073e6",progressBarWidth:96,progressPercent:e}),i(t).next().html("Progresso "+e+"%")},update:function(t,e){i(t).circularloader({progressPercent:e})}}}($);$(function(){$(".it-has-checkbox").on("click",function(t){var e=$(this).find("input");$(e).prop("checked")?($(e).prop("checked",!1),$(this).removeClass("active")):($(e).prop("checked",!0),$(this).addClass("active")),t.preventDefault()})}),$(function(){var t=window.navigator.userAgent;/msie|Trident.*rv[ :]*11\./gi.test(t)&&$(".img-wrapper").each(function(){var t=$(this),e=t.find("img").prop("src");e&&t.css("backgroundImage","url("+e+")").addClass("custom-object-fit")})});var numbers=[48,49,50,51,52,53,54,55,56,57],timeRegEx=/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/i;$(document).ready(function(){function w(t){return[8,9,13].includes(t)}function C(t,e,i,n,s){var o;t.hasClass("is-open")&&(t.fadeOut(100).toggleClass("is-open").attr("aria-hidden","true"),i&&n&&(o=i.attr("value")+":"+n.attr("value"),e.val(o)),x(e,s))}var k={};function x(t,e){var i,n=t.val();n&&(i=t.siblings("label"),(""!=n?n.match(timeRegEx):"")?i.removeClass("error-label").html(k[e]):i.addClass("error-label").html("Formato ora non valido (hh:mm)"))}$(".it-timepicker-wrapper").each(function(s){function n(t){return t<0&&(t=0),t<10?"0"+t:t}function o(t,e){switch(h=e.closest(".spinner").find("input"),a=parseInt(h.attr("aria-valuemin")),r=parseInt(h.attr("aria-valuemax")),l=parseInt(h.attr("aria-valuenow")),c=parseInt(h.attr("bb-skip")),t){case"up":(!r||l<r)&&l++;break;case"down":(!a||a<l)&&l--}if(t&&-1<c)switch(!0){case"up"===t&&c===l:l++;break;case"down"===t&&c===l:l--}switch(!0){case e.hasClass("btnHourUp")||e.hasClass("btnHourDown"):d=n(l);break;case e.hasClass("btnMinUp")||e.hasClass("btnMinDown"):u=n(l)}h.val(n(l)),h.attr("value",n(l)),h.attr("aria-valuenow",n(l)),p.val(d+":"+u).change()}function i(t,e){var i=n(t.val());t.attr("aria-valuenow",i),o(null,e)}var a,r,l,c,h,d="00",u="00",t=$(this),p=t.find(".txtTime"),e=t.find(".btn-time"),f=t.find(".spinner-control"),m=t.find(".spinnerHour"),g=t.find(".spinnerMin"),v=t.find(".btnHourUp"),b=t.find(".btnHourDown"),y=t.find(".btnMinUp"),_=t.find(".btnMinDown");k[s]=p.siblings("label").text(),t.find(".spinner-control button").attr("aria-hidden","true").attr("tabindex","-1"),e.on("click",function(t){t.stopPropagation(),f.hasClass("is-open")?C(f,p,m,g,s):f.toggleClass("is-open").attr("aria-hidden","false").fadeIn(100)}),p.on("keyup",function(t){var e,i=t.which||t.keyCode,n=p.val();if(n.includes(":")?(e=n.split(":"),m.attr("aria-valuenow",e[0].substring(0,2)),m.attr("value",e[0].substring(0,2)),m.val(e[0].substring(0,2)),d=e[0].substring(0,2),g.attr("aria-valuenow",e[1].substring(0,2)),g.attr("value",e[1].substring(0,2)),g.val(e[1].substring(0,2)),u=e[1].substring(0,2)):(m.attr("aria-valuenow",n.substring(0,2)),m.attr("value",n.substring(0,2)),m.val(n.substring(0,2)),d=n.substring(0,2)),13===i)return x(p,s)}).on("focus",function(t){t.stopPropagation(),p.val()&&x(p,s)}).on("blur",function(t){p.val()&&x(p,s)}),v.on("click",function(t){o("up",v)}),b.on("click",function(t){o("down",b)}),y.on("click",function(t){o("up",y)}),_.on("click",function(t){o("down",_)}),m.on("keydown",function(t){var e=t.which||t.keyCode,i=numbers.includes(e);switch(!0){case 38===e:v.trigger("click");break;case 40===e:b.trigger("click");break;case w(e)||i:return!0}return!1}).on("keyup",function(t){var e=t.which||t.keyCode;numbers.includes(e)&&i(m,v)}),g.on("keydown",function(t){var e=t.which||t.keyCode,i=numbers.includes(e);switch(!0){case 38===e:y.trigger("click");break;case 40===e:_.trigger("click");break;case w(e)||i:return!0}return!1}).on("keyup",function(t){var e=t.which||t.keyCode;numbers.includes(e)&&i(g,y)}),$(document).on("click",function(t){C(f,p,m,g,s)}),f.on("click",function(t){t.stopPropagation()})})}),$(function(){function r(t){var e=t.closest(".input-number");e.hasClass("input-number-adaptive")&&(e.hasClass("input-number-percentage")||(t.css("width","calc(44px + "+t.val().length+"ch)"),isIe()&&t.css("width","calc(44px + (1.5 * "+t.val().length+"ch))")),e.hasClass("input-number-currency")&&(t.css("width","calc(40px + 44px + "+t.val().length+"ch)"),isIe()&&t.css("width","calc(40px + 44px + (1.5 * "+t.val().length+"ch))")))}$(".input-number input[type=number]").each(function(t){r($(this))}),$(".input-number button").click(function(t){t.preventDefault();var e,i,n,s,o=$(this).closest(".input-number").find("input[type=number]"),a=parseFloat(o.val());isNaN(a)||(e=0,i=parseFloat(o.attr("max")),n=parseFloat(o.attr("min")),s=(s=parseFloat(o.attr("step")))||1,$(this).hasClass("input-number-add")&&(e=!isNaN(i)&&i<=a+s?i:a+s,o.val(e)),$(this).hasClass("input-number-sub")&&(e=!isNaN(n)&&a-s<=n?n:a-s,o.val(e))),r(o)}),$(".input-number input[type=number]").change(function(t){var e,i,n=$(this),s=parseFloat(n.val());isNaN(s)||(e=parseFloat(n.attr("max")),(i=parseFloat(n.attr("min")))&&s<i&&(s=i),e&&e<s&&(s=e),n.val(s)),r(n)}),$("input[type=number]").on("keyup",function(t){var e=t&&t.target.value;this.value=e.replace(/[^0-9,.]/g,"")})}),$(function(){var t=$(".transfer-header input"),e=$(".transfer-group input"),o=$(".it-transfer-block").find("a.transfer"),a=$(".it-transfer-block").find("a.backtransfer"),n=$(".it-transfer-block").find("a.reset"),d=$(".source .transfer-group .form-check"),u=$(".target .transfer-group .form-check"),p=d.length,f=u.length;function m(t){t.removeClass("active").attr("disabled","disabled").attr("aria-disabled","true")}function r(t){t.addClass("active").removeAttr("disabled").removeAttr("aria-disabled")}function s(t,e){var i=t.find(".transfer-group input:checked"),n=i.closest(".form-check"),s=t.find(".transfer-header input"),o=t.find(".transfer-header span.num"),a=i.length,r=t.find(".transfer-group input").length,l=e.find(".transfer-group"),c=e.find(".transfer-group input").length+a,h=e.find(".transfer-header span.num"),d=e.find(".transfer-header input");n.each(function(){$(this).detach().appendTo(l).find("input").prop("checked",!1)});var u=r-a,p=c;o.text(u),h.text(p),0==u&&s.prop("disabled",!0),0<p&&d.prop("disabled",!1),s.removeClass("semi-checked").prop("checked",!1)}$(d).each(function(t){0}),$(u).each(function(t){0}),$(t).on("click",function(){var t,e,i,n,s=$(this).closest(".it-transfer-wrapper");t=s,e=$(t).find(".transfer-group input"),i=$(t).find(".transfer-group input:checked"),n=$(t).find(".transfer-header input"),o=t.closest(".it-transfer-block").find("a.transfer"),a=t.closest(".it-transfer-block").find("a.backtransfer"),0<i.length?($(e).prop("checked",!1),$(n).removeClass("semi-checked").prop("checked",!1),t.hasClass("source")?m(o):m(a)):($(e).prop("checked",!0),t.hasClass("source")?r(o):r(a))}),$(e).on("click",function(){var t,e,i,n,s=$(this).closest(".it-transfer-wrapper");t=s,e=$(t).find(".transfer-group input"),i=$(t).find(".transfer-group input:checked"),n=$(t).find(".transfer-header input"),o=t.closest(".it-transfer-block").find("a.transfer"),a=t.closest(".it-transfer-block").find("a.backtransfer"),0==i.length?(n.removeClass("semi-checked").prop("checked",!1),t.hasClass("source")?m(o):m(a)):(i.length==e.length?n.removeClass("semi-checked").prop("checked",!0):n.addClass("semi-checked").prop("checked",!1),t.hasClass("source")?r(o):r(a))}),$(o).on("click",function(t){var e=$(this).closest(".it-transfer-block").find(".source"),i=$(this).closest(".it-transfer-block").find(".target");r(n=$(this).closest(".it-transfer-block").find("a.reset")),m($(this)),s(e,i),t.preventDefault()}),$(a).on("click",function(t){var e=$(this).closest(".it-transfer-block").find(".source"),i=$(this).closest(".it-transfer-block").find(".target");r(n=$(this).closest(".it-transfer-block").find("a.reset")),m($(this)),s(i,e),t.preventDefault()}),$(n).on("click",function(t){var e,i,n,s,o,a,r,l=$(this).closest(".it-transfer-block"),c=l.find("a.transfer"),h=l.find("a.backtransfer");m(c),m(h),m($(this)),i=(e=l).find(".source .transfer-group"),n=e.find(".target .transfer-group"),s=e.find(".source .transfer-header span.num"),o=e.find(".target .transfer-header span.num"),a=e.find(".transfer-header input"),r=e.find(".transfer-group input"),$(i).find(".form-check").detach(),$(n).find(".form-check").detach(),$(i).append(d),$(n).append(u),$(s).text(p),$(o).text(f),$(a).prop("disabled",!1).removeClass("semi-checked"),$(a).prop("checked",!1),$(r).prop("checked",!1),t.preventDefault()})}),$(function(){function i(){$(".dropdown-menu li.selected").find('input[type="checkbox"]').prop("checked",!0),$(".dropdown-menu li:not(.selected)").find('input[type="checkbox"]').prop("checked",!1)}jQuery.fn.setOptionsToSelect=function(t){var e=$(this).find("select");return $(e).off("changed.bs.select").selectpicker("destroy").empty(),t.forEach(function(t){$(e).append($("<option>",{value:t.value,text:t.text}))}),$(e).selectpicker("refresh").on("changed.bs.select",i),this},$(".bootstrap-select-wrapper select").selectpicker().on("changed.bs.select",i);var t=$(".bootstrap-select-wrapper");t.find("select option.bs-title-option").text("Nessuna opzione"),t.find("select option[data-content]").text("Annulla"),t.find("button.dropdown-toggle").removeAttr("role"),t.find("div.filter-option").replaceWith(function(){return $("<span />").addClass("filter-option").append($(this).contents())}),t.find("div.filter-option-inner").replaceWith(function(){return $("<span />").addClass("filter-option-inner").append($(this).contents())}),t.find("div.filter-option-inner-inner").replaceWith(function(){return $("<span />").addClass("filter-option-inner-inner").append($(this).contents())}),t.find(".bs-searchbox input").attr("title","Cerca").attr("aria-expanded","false")}),$(function(){$(".rating.rating-label input[type=radio]").on("click",function(t){var e=$(this).val(),i=1==e?"stella":"stelle";$(this).closest(".rating-label").find("legend span:not(.sr-only)").text(e+" "+i)})}),$(function(){$.fn.dimmerShow=function(){return this.each(function(){"flex"!=$(this).css("display")&&$(this).css("display","flex").hide().fadeIn(200)})},$.fn.dimmerHide=function(t){return this.each(function(){$(this).fadeOut(200)})}}),$(function(){var t,e,i,n;$(".bd-example")[0]||(t=function(){var t=$(".it25-top-bar").outerHeight(!0)+$(".it25-barra-ist").outerHeight(!0);$(".it25-search-container").css({top:t+"px"})},e=function(){$(document).scrollTop()>i?($(".it25-top-bar").addClass("shrink"),$(".it-header-slim-wrapper-content").hide(),$(".it25-barra-ist").addClass("shrink"),$(".it25-menu-principale").addClass("header-shrinked")):$(document).scrollTop()<n&&($(".it25-top-bar").removeClass("shrink"),$(".it-header-slim-wrapper-content").show(),$(".it25-barra-ist").removeClass("shrink"),$(".it25-menu-principale").removeClass("header-shrinked")),t()},i=90,n=2,function(){if(null!=$("header").attr("class")){var t=$("header").attr("class").split("-");if("it25"==t[0])switch(t[1]){case"menu":$(".it25-hamburger-btn-wrapper").addClass("d-lg-none");break;case"sidebar":$(".it25-hamburger-btn-wrapper").addClass("d-md-none"),$(".it25-btn-menu").addClass("it25-btn-sidebar");break;case"none":$(".it25-hamburger-btn-wrapper").addClass("d-none");break;default:$(".it25-hamburger-btn-wrapper").addClass("d-lg-none")}else $(".it25-hamburger-btn-wrapper").addClass("d-lg-none")}else $(".it25-hamburger-btn-wrapper").addClass("d-lg-none")}(),0<$(".it25-barra-utente").outerHeight(!0)&&$(".it25-menu-principale").addClass("postUserBar"),e(),$(window).on("scroll",e),$(window).on("resize",e),$(".it-search-wrapper").on("show.bs.collapse",t))}),$(function(){function e(t){for(var e=0;e<t.length;e++)label=t[e],label.hasAttribute("style")&&(attrStyle=label.getAttribute("style"),pos=attrStyle.search("width: 0px;"),0<=pos&&(result=attrStyle.replace("width: 0px","width: auto"),label.setAttribute("style",result)))}$('a[data-toggle="tab"]').on("shown.bs.tab",function(t){rif=t.target.getAttribute("href").substr(1),panel=document.getElementById(rif),labels=panel.getElementsByTagName("label"),e(labels)}),$('div[role="tabpanel"]').on("shown.bs.collapse",function(t){labels=t.target.getElementsByTagName("label"),e(labels)})}),$(function(){var t,e,i,n,s,o,a,r,l,c,h,d,u,p,f,m,g,v,b,y,_,w,C,k,x,E,T,D,I,A,L,S,O,P=$(".owl-carousel.it-carousel-all");$(P).each(function(){$(this).closest(".it-carousel-wrapper").hasClass("it-carousel-landscape-abstract-three-cols")&&(n=[],s=300,o=500,a=200,h=!(c=l=r=!(e=!(t=!0))),f=!(p=!(d="page")),m=1,y=!(b=!(v=24)),_=2,w=g=40,x=!(k=!(C=24)),A=!(I=!(E=i=3)),L=3,O=S=D=T=0,$(this).hasClass("it-card-bg")&&(w=g=40,S=T=12,O=D=C=v=24),$(this).hasClass("it-img-card")&&(w=g=40,S=T=0,O=D=C=v=24),$(this).hasClass("it-img-card")&&$(this).hasClass("it-big-img")&&(e=!0,v=g=0,w=160,C=24,S=T=320,O=D=48,L=E=_=1),$(this).hasClass("it-img-card")&&$(this).hasClass("it-standard-image")&&(e=!0,g=40,m=1,_=2,T=68,S=w=48,O=D=C=v=24,L=E=3)),$(this).closest(".it-carousel-wrapper").hasClass("it-calendar-wrapper")&&(e=!(t=!0),n=[],s=300,o=500,a=200,h=!(c=l=r=!0),f=!(p=!(d="page")),m=1,y=!(b=!(g=40)),_=2,x=!(k=!(w=40)),A=!(I=!(E=i=4)),L=4,O=S=D=T=C=v=0,$(this).hasClass("it-card-bg")&&(w=g=40,O=S=D=T=C=v=0),$(this).hasClass("it-img-card")&&(w=g=40,O=S=D=T=C=v=0),$(this).hasClass("it-img-card")&&$(this).hasClass("it-big-img")&&(e=!0,w=160,S=T=320,O=D=C=v=g=0,L=E=_=1),$(this).hasClass("it-img-card")&&$(this).hasClass("it-standard-image")&&(e=!0,g=40,m=1,C=_=2,T=68,S=w=48,O=D=v=0,L=E=4)),$(this).closest(".it-carousel-wrapper").hasClass("it-carousel-landscape-abstract")&&(n=[],s=300,o=500,a=200,h=!(c=l=r=!(e=!(t=!0))),f=!(p=!(d="page")),y=!(b=!(v=24)),x=!(k=!(C=w=24)),A=!(I=!(E=_=m=i=1)),L=1,O=S=D=T=g=0),$(this).hasClass("it25-carousel-nav")&&(I=k=b=p=t=!0,n=['<span class="icon icon-primary">&ltrif;</span>','<span class="icon icon-primary">&rtrif;</span>']),$(this).hasClass("it25-carousel-autoplay")?(cautoplay=!0,cautoplayHoverPause=!0,e=!0):(cautoplay=!1,cautoplayHoverPause=!1),$(this).owlCarousel&&$(this).owlCarousel({nav:t,autoplay:cautoplay,autoplayHoverPause:cautoplayHoverPause,loop:e,margin:24,items:i,navText:n,navSpeed:s,smartSpeed:o,dotsSpeed:a,navElement:"button",dotElement:"button",controlsAriaHidden:r,mouseDrag:l,touchDrag:c,dots:h,slideBy:d,stagePadding:u,responsive:{0:{nav:p,dots:f,items:m,stagePadding:g,margin:v},768:{nav:b,dots:y,stagePadding:w,items:_,margin:C},992:{nav:k,dots:x,items:E,stagePadding:T,margin:D},1200:{nav:I,dots:A,stagePadding:S,items:L,margin:O}},onInitialized:function(t){$(t.target).find(".owl-dot").each(function(t){$(this).attr("aria-labelledby","owl-dot-"+t)})}})})});
//# sourceMappingURL=bootstrap-lombardia.min.js.map
