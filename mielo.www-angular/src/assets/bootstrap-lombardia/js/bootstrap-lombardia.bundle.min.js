/*!
 * 'Bootstrap Lombardia è una customizzazione di Boostrap Italia per la creazione di applicazioni web per la Regione Lombardia nel pieno rispetto delle Linee guida di design per i servizi web della PA
 * @version v0.3.5
 * @link https://regionelombardia.github.io/bootstrap-lombardia/
 * @license BSD-3-Clause
 */
function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass2(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}function showCapsLockMsg(t){$(".password-caps").remove(),t.parents(".form-group").append('<small class="password-caps form-text text-warning position-absolute bg-white w-100">CAPS LOCK inserito</small>')}function isIe(){return 0<window.navigator.userAgent.indexOf("MSIE ")||!!navigator.userAgent.match(/Trident.*rv\:11\./)}!function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(C,t){"use strict";function m(t){return null!=t&&t===t.window}var e=[],n=Object.getPrototypeOf,a=e.slice,g=e.flat?function(t){return e.flat.call(t)}:function(t){return e.concat.apply([],t)},l=e.push,s=e.indexOf,i={},o=i.toString,v=i.hasOwnProperty,r=v.toString,c=r.call(Object),y={},b=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType},k=C.document,h={type:!0,src:!0,nonce:!0,noModule:!0};function w(t,e,i){var n,s,o=(i=i||k).createElement("script");if(o.text=t,e)for(n in h)(s=e[n]||e.getAttribute&&e.getAttribute(n))&&o.setAttribute(n,s);i.head.appendChild(o).parentNode.removeChild(o)}function _(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?i[o.call(t)]||"object":typeof t}var d="3.5.1",$=function t(e,i){return new t.fn.init(e,i)};function u(t){var e=!!t&&"length"in t&&t.length,i=_(t);return!b(t)&&!m(t)&&("array"===i||0===e||"number"==typeof e&&0<e&&e-1 in t)}$.fn=$.prototype={jquery:d,constructor:$,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=$.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return $.each(this,t)},map:function(i){return this.pushStack($.map(this,function(t,e){return i.call(t,e,t)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack($.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack($.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(0<=i&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:e.sort,splice:e.splice},$.extend=$.fn.extend=function(){var t,e,i,n,s,o,r=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[a]||{},a++),"object"==typeof r||b(r)||(r={}),a===l&&(r=this,a--);a<l;a++)if(null!=(t=arguments[a]))for(e in t)n=t[e],"__proto__"!==e&&r!==n&&(c&&n&&($.isPlainObject(n)||(s=Array.isArray(n)))?(i=r[e],o=s&&!Array.isArray(i)?[]:s||$.isPlainObject(i)?i:{},s=!1,r[e]=$.extend(c,o,n)):void 0!==n&&(r[e]=n));return r},$.extend({expando:"jQuery"+(d+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,i;return!(!t||"[object Object]"!==o.call(t))&&(!(e=n(t))||"function"==typeof(i=v.call(e,"constructor")&&e.constructor)&&r.call(i)===c)},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,i){w(t,{nonce:e&&e.nonce},i)},each:function(t,e){var i,n=0;if(u(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},makeArray:function(t,e){var i=e||[];return null!=t&&(u(Object(t))?$.merge(i,"string"==typeof t?[t]:t):l.call(i,t)),i},inArray:function(t,e,i){return null==e?-1:s.call(e,t,i)},merge:function(t,e){for(var i=+e.length,n=0,s=t.length;n<i;n++)t[s++]=e[n];return t.length=s,t},grep:function(t,e,i){for(var n=[],s=0,o=t.length,r=!i;s<o;s++)!e(t[s],s)!=r&&n.push(t[s]);return n},map:function(t,e,i){var n,s,o=0,r=[];if(u(t))for(n=t.length;o<n;o++)null!=(s=e(t[o],o,i))&&r.push(s);else for(o in t)null!=(s=e(t[o],o,i))&&r.push(s);return g(r)},guid:1,support:y}),"function"==typeof Symbol&&($.fn[Symbol.iterator]=e[Symbol.iterator]),$.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){i["[object "+e+"]"]=e.toLowerCase()});var p=function(i){function d(t,e){var i="0x"+t.slice(1)-65536;return e||(i<0?String.fromCharCode(65536+i):String.fromCharCode(i>>10|55296,1023&i|56320))}function s(){x()}var t,p,w,o,r,f,u,m,_,l,c,x,C,a,k,g,h,v,y,$="sizzle"+ +new Date,b=i.document,T=0,n=0,E=lt(),D=lt(),A=lt(),S=lt(),L=function(t,e){return t===e&&(c=!0),0},I={}.hasOwnProperty,e=[],O=e.pop,N=e.push,P=e.push,M=e.slice,B=function(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1},j="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",F="[\\x20\\t\\r\\n\\f]",H="(?:\\\\[\\da-fA-F]{1,6}"+F+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",U="\\["+F+"*("+H+")(?:"+F+"*([*^$|!~]?=)"+F+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+H+"))|)"+F+"*\\]",R=":("+H+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+U+")*)|.*)\\)|)",z=new RegExp(F+"+","g"),W=new RegExp("^"+F+"+|((?:^|[^\\\\])(?:\\\\.)*)"+F+"+$","g"),q=new RegExp("^"+F+"*,"+F+"*"),Y=new RegExp("^"+F+"*([>+~]|"+F+")"+F+"*"),V=new RegExp(F+"|>"),G=new RegExp(R),K=new RegExp("^"+H+"$"),Q={ID:new RegExp("^#("+H+")"),CLASS:new RegExp("^\\.("+H+")"),TAG:new RegExp("^("+H+"|[*])"),ATTR:new RegExp("^"+U),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+F+"*(even|odd|(([+-]|)(\\d*)n|)"+F+"*(?:([+-]|)"+F+"*(\\d+)|))"+F+"*\\)|)","i"),bool:new RegExp("^(?:"+j+")$","i"),needsContext:new RegExp("^"+F+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+F+"*((?:-\\d)?\\d*)"+F+"*\\)|)(?=[^-]|$)","i")},X=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,tt=/^[^{]+\{\s*\[native \w/,et=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,it=/[+~]/,nt=new RegExp("\\\\[\\da-fA-F]{1,6}"+F+"?|\\\\([^\\r\\n\\f])","g"),st=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ot=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},rt=yt(function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{P.apply(e=M.call(b.childNodes),b.childNodes),e[b.childNodes.length].nodeType}catch(t){P={apply:e.length?function(t,e){N.apply(t,M.call(e))}:function(t,e){for(var i=t.length,n=0;t[i++]=e[n++];);t.length=i-1}}}function at(e,t,i,n){var s,o,r,a,l,c,h,d=t&&t.ownerDocument,u=t?t.nodeType:9;if(i=i||[],"string"!=typeof e||!e||1!==u&&9!==u&&11!==u)return i;if(!n&&(x(t),t=t||C,k)){if(11!==u&&(l=et.exec(e)))if(s=l[1]){if(9===u){if(!(r=t.getElementById(s)))return i;if(r.id===s)return i.push(r),i}else if(d&&(r=d.getElementById(s))&&y(t,r)&&r.id===s)return i.push(r),i}else{if(l[2])return P.apply(i,t.getElementsByTagName(e)),i;if((s=l[3])&&p.getElementsByClassName&&t.getElementsByClassName)return P.apply(i,t.getElementsByClassName(s)),i}if(p.qsa&&!S[e+" "]&&(!g||!g.test(e))&&(1!==u||"object"!==t.nodeName.toLowerCase())){if(h=e,d=t,1===u&&(V.test(e)||Y.test(e))){for((d=it.test(e)&&mt(t.parentNode)||t)===t&&p.scope||((a=t.getAttribute("id"))?a=a.replace(st,ot):t.setAttribute("id",a=$)),o=(c=f(e)).length;o--;)c[o]=(a?"#"+a:":scope")+" "+vt(c[o]);h=c.join(",")}try{return P.apply(i,d.querySelectorAll(h)),i}catch(t){S(e,!0)}finally{a===$&&t.removeAttribute("id")}}}return m(e.replace(W,"$1"),t,i,n)}function lt(){var i=[];function n(t,e){return i.push(t+" ")>w.cacheLength&&delete n[i.shift()],n[t+" "]=e}return n}function ct(t){return t[$]=!0,t}function ht(t){var e=C.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function dt(t,e){for(var i=t.split("|"),n=i.length;n--;)w.attrHandle[i[n]]=e}function ut(t,e){var i=e&&t,n=i&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(n)return n;if(i)for(;i=i.nextSibling;)if(i===e)return-1;return t?1:-1}function pt(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&rt(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function ft(r){return ct(function(o){return o=+o,ct(function(t,e){for(var i,n=r([],t.length,o),s=n.length;s--;)t[i=n[s]]&&(t[i]=!(e[i]=t[i]))})})}function mt(t){return t&&void 0!==t.getElementsByTagName&&t}for(t in p=at.support={},r=at.isXML=function(t){var e=t.namespaceURI,i=(t.ownerDocument||t).documentElement;return!X.test(e||i&&i.nodeName||"HTML")},x=at.setDocument=function(t){var e,i,n=t?t.ownerDocument||t:b;return n!=C&&9===n.nodeType&&n.documentElement&&(a=(C=n).documentElement,k=!r(C),b!=C&&(i=C.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",s,!1):i.attachEvent&&i.attachEvent("onunload",s)),p.scope=ht(function(t){return a.appendChild(t).appendChild(C.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length}),p.attributes=ht(function(t){return t.className="i",!t.getAttribute("className")}),p.getElementsByTagName=ht(function(t){return t.appendChild(C.createComment("")),!t.getElementsByTagName("*").length}),p.getElementsByClassName=tt.test(C.getElementsByClassName),p.getById=ht(function(t){return a.appendChild(t).id=$,!C.getElementsByName||!C.getElementsByName($).length}),p.getById?(w.filter.ID=function(t){var e=t.replace(nt,d);return function(t){return t.getAttribute("id")===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&k){var i=e.getElementById(t);return i?[i]:[]}}):(w.filter.ID=function(t){var i=t.replace(nt,d);return function(t){var e=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return e&&e.value===i}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&k){var i,n,s,o=e.getElementById(t);if(o){if((i=o.getAttributeNode("id"))&&i.value===t)return[o];for(s=e.getElementsByName(t),n=0;o=s[n++];)if((i=o.getAttributeNode("id"))&&i.value===t)return[o]}return[]}}),w.find.TAG=p.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):p.qsa?e.querySelectorAll(t):void 0}:function(t,e){var i,n=[],s=0,o=e.getElementsByTagName(t);if("*"!==t)return o;for(;i=o[s++];)1===i.nodeType&&n.push(i);return n},w.find.CLASS=p.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&k)return e.getElementsByClassName(t)},h=[],g=[],(p.qsa=tt.test(C.querySelectorAll))&&(ht(function(t){var e;a.appendChild(t).innerHTML="<a id='"+$+"'></a><select id='"+$+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&g.push("[*^$]="+F+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||g.push("\\["+F+"*(?:value|"+j+")"),t.querySelectorAll("[id~="+$+"-]").length||g.push("~="),(e=C.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||g.push("\\["+F+"*name"+F+"*="+F+"*(?:''|\"\")"),t.querySelectorAll(":checked").length||g.push(":checked"),t.querySelectorAll("a#"+$+"+*").length||g.push(".#.+[+~]"),t.querySelectorAll("\\\f"),g.push("[\\r\\n\\f]")}),ht(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=C.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&g.push("name"+F+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&g.push(":enabled",":disabled"),a.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&g.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),g.push(",.*:")})),(p.matchesSelector=tt.test(v=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ht(function(t){p.disconnectedMatch=v.call(t,"*"),v.call(t,"[s!='']:x"),h.push("!=",R)}),g=g.length&&new RegExp(g.join("|")),h=h.length&&new RegExp(h.join("|")),e=tt.test(a.compareDocumentPosition),y=e||tt.test(a.contains)?function(t,e){var i=9===t.nodeType?t.documentElement:t,n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(i.contains?i.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},L=e?function(t,e){if(t===e)return c=!0,0;var i=!t.compareDocumentPosition-!e.compareDocumentPosition;return i||(1&(i=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!p.sortDetached&&e.compareDocumentPosition(t)===i?t==C||t.ownerDocument==b&&y(b,t)?-1:e==C||e.ownerDocument==b&&y(b,e)?1:l?B(l,t)-B(l,e):0:4&i?-1:1)}:function(t,e){if(t===e)return c=!0,0;var i,n=0,s=t.parentNode,o=e.parentNode,r=[t],a=[e];if(!s||!o)return t==C?-1:e==C?1:s?-1:o?1:l?B(l,t)-B(l,e):0;if(s===o)return ut(t,e);for(i=t;i=i.parentNode;)r.unshift(i);for(i=e;i=i.parentNode;)a.unshift(i);for(;r[n]===a[n];)n++;return n?ut(r[n],a[n]):r[n]==b?-1:a[n]==b?1:0}),C},at.matches=function(t,e){return at(t,null,null,e)},at.matchesSelector=function(t,e){if(x(t),p.matchesSelector&&k&&!S[e+" "]&&(!h||!h.test(e))&&(!g||!g.test(e)))try{var i=v.call(t,e);if(i||p.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(t){S(e,!0)}return 0<at(e,C,null,[t]).length},at.contains=function(t,e){return(t.ownerDocument||t)!=C&&x(t),y(t,e)},at.attr=function(t,e){(t.ownerDocument||t)!=C&&x(t);var i=w.attrHandle[e.toLowerCase()],n=i&&I.call(w.attrHandle,e.toLowerCase())?i(t,e,!k):void 0;return void 0!==n?n:p.attributes||!k?t.getAttribute(e):(n=t.getAttributeNode(e))&&n.specified?n.value:null},at.escape=function(t){return(t+"").replace(st,ot)},at.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},at.uniqueSort=function(t){var e,i=[],n=0,s=0;if(c=!p.detectDuplicates,l=!p.sortStable&&t.slice(0),t.sort(L),c){for(;e=t[s++];)e===t[s]&&(n=i.push(s));for(;n--;)t.splice(i[n],1)}return l=null,t},o=at.getText=function(t){var e,i="",n=0,s=t.nodeType;if(s){if(1===s||9===s||11===s){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)i+=o(t)}else if(3===s||4===s)return t.nodeValue}else for(;e=t[n++];)i+=o(e);return i},(w=at.selectors={cacheLength:50,createPseudo:ct,match:Q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(nt,d),t[3]=(t[3]||t[4]||t[5]||"").replace(nt,d),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||at.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&at.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return Q.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&G.test(i)&&(e=f(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(nt,d).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=E[t+" "];return e||(e=new RegExp("(^|"+F+")"+t+"("+F+"|$)"))&&E(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(i,n,s){return function(t){var e=at.attr(t,i);return null==e?"!="===n:!n||(e+="","="===n?e===s:"!="===n?e!==s:"^="===n?s&&0===e.indexOf(s):"*="===n?s&&-1<e.indexOf(s):"$="===n?s&&e.slice(-s.length)===s:"~="===n?-1<(" "+e.replace(z," ")+" ").indexOf(s):"|="===n&&(e===s||e.slice(0,s.length+1)===s+"-"))}},CHILD:function(f,t,e,m,g){var v="nth"!==f.slice(0,3),y="last"!==f.slice(-4),b="of-type"===t;return 1===m&&0===g?function(t){return!!t.parentNode}:function(t,e,i){var n,s,o,r,a,l,c=v!=y?"nextSibling":"previousSibling",h=t.parentNode,d=b&&t.nodeName.toLowerCase(),u=!i&&!b,p=!1;if(h){if(v){for(;c;){for(r=t;r=r[c];)if(b?r.nodeName.toLowerCase()===d:1===r.nodeType)return!1;l=c="only"===f&&!l&&"nextSibling"}return!0}if(l=[y?h.firstChild:h.lastChild],y&&u){for(p=(a=(n=(s=(o=(r=h)[$]||(r[$]={}))[r.uniqueID]||(o[r.uniqueID]={}))[f]||[])[0]===T&&n[1])&&n[2],r=a&&h.childNodes[a];r=++a&&r&&r[c]||(p=a=0)||l.pop();)if(1===r.nodeType&&++p&&r===t){s[f]=[T,a,p];break}}else if(u&&(p=a=(n=(s=(o=(r=t)[$]||(r[$]={}))[r.uniqueID]||(o[r.uniqueID]={}))[f]||[])[0]===T&&n[1]),!1===p)for(;(r=++a&&r&&r[c]||(p=a=0)||l.pop())&&((b?r.nodeName.toLowerCase()!==d:1!==r.nodeType)||!++p||(u&&((s=(o=r[$]||(r[$]={}))[r.uniqueID]||(o[r.uniqueID]={}))[f]=[T,p]),r!==t)););return(p-=g)===m||p%m==0&&0<=p/m}}},PSEUDO:function(t,o){var e,r=w.pseudos[t]||w.setFilters[t.toLowerCase()]||at.error("unsupported pseudo: "+t);return r[$]?r(o):1<r.length?(e=[t,t,"",o],w.setFilters.hasOwnProperty(t.toLowerCase())?ct(function(t,e){for(var i,n=r(t,o),s=n.length;s--;)t[i=B(t,n[s])]=!(e[i]=n[s])}):function(t){return r(t,0,e)}):r}},pseudos:{not:ct(function(t){var n=[],s=[],a=u(t.replace(W,"$1"));return a[$]?ct(function(t,e,i,n){for(var s,o=a(t,null,n,[]),r=t.length;r--;)(s=o[r])&&(t[r]=!(e[r]=s))}):function(t,e,i){return n[0]=t,a(n,null,i,s),n[0]=null,!s.pop()}}),has:ct(function(e){return function(t){return 0<at(e,t).length}}),contains:ct(function(e){return e=e.replace(nt,d),function(t){return-1<(t.textContent||o(t)).indexOf(e)}}),lang:ct(function(i){return K.test(i||"")||at.error("unsupported lang: "+i),i=i.replace(nt,d).toLowerCase(),function(t){var e;do{if(e=k?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(e=e.toLowerCase())===i||0===e.indexOf(i+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var e=i.location&&i.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===a},focus:function(t){return t===C.activeElement&&(!C.hasFocus||C.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:pt(!1),disabled:pt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!w.pseudos.empty(t)},header:function(t){return Z.test(t.nodeName)},input:function(t){return J.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:ft(function(){return[0]}),last:ft(function(t,e){return[e-1]}),eq:ft(function(t,e,i){return[i<0?i+e:i]}),even:ft(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:ft(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:ft(function(t,e,i){for(var n=i<0?i+e:e<i?e:i;0<=--n;)t.push(n);return t}),gt:ft(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[t]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(t);for(t in{submit:!0,reset:!0})w.pseudos[t]=function(i){return function(t){var e=t.nodeName.toLowerCase();return("input"===e||"button"===e)&&t.type===i}}(t);function gt(){}function vt(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function yt(a,t,e){var l=t.dir,c=t.next,h=c||l,d=e&&"parentNode"===h,u=n++;return t.first?function(t,e,i){for(;t=t[l];)if(1===t.nodeType||d)return a(t,e,i);return!1}:function(t,e,i){var n,s,o,r=[T,u];if(i){for(;t=t[l];)if((1===t.nodeType||d)&&a(t,e,i))return!0}else for(;t=t[l];)if(1===t.nodeType||d)if(s=(o=t[$]||(t[$]={}))[t.uniqueID]||(o[t.uniqueID]={}),c&&c===t.nodeName.toLowerCase())t=t[l]||t;else{if((n=s[h])&&n[0]===T&&n[1]===u)return r[2]=n[2];if((s[h]=r)[2]=a(t,e,i))return!0}return!1}}function bt(s){return 1<s.length?function(t,e,i){for(var n=s.length;n--;)if(!s[n](t,e,i))return!1;return!0}:s[0]}function wt(t,e,i,n,s){for(var o,r=[],a=0,l=t.length,c=null!=e;a<l;a++)(o=t[a])&&(i&&!i(o,n,s)||(r.push(o),c&&e.push(a)));return r}function _t(p,f,m,g,v,t){return g&&!g[$]&&(g=_t(g)),v&&!v[$]&&(v=_t(v,t)),ct(function(t,e,i,n){var s,o,r,a=[],l=[],c=e.length,h=t||function(t,e,i){for(var n=0,s=e.length;n<s;n++)at(t,e[n],i);return i}(f||"*",i.nodeType?[i]:i,[]),d=!p||!t&&f?h:wt(h,a,p,i,n),u=m?v||(t?p:c||g)?[]:e:d;if(m&&m(d,u,i,n),g)for(s=wt(u,l),g(s,[],i,n),o=s.length;o--;)(r=s[o])&&(u[l[o]]=!(d[l[o]]=r));if(t){if(v||p){if(v){for(s=[],o=u.length;o--;)(r=u[o])&&s.push(d[o]=r);v(null,u=[],s,n)}for(o=u.length;o--;)(r=u[o])&&-1<(s=v?B(t,r):a[o])&&(t[s]=!(e[s]=r))}}else u=wt(u===e?u.splice(c,u.length):u),v?v(null,e,u,n):P.apply(e,u)})}function xt(g,v){function t(t,e,i,n,s){var o,r,a,l=0,c="0",h=t&&[],d=[],u=_,p=t||b&&w.find.TAG("*",s),f=T+=null==u?1:Math.random()||.1,m=p.length;for(s&&(_=e==C||e||s);c!==m&&null!=(o=p[c]);c++){if(b&&o){for(r=0,e||o.ownerDocument==C||(x(o),i=!k);a=g[r++];)if(a(o,e||C,i)){n.push(o);break}s&&(T=f)}y&&((o=!a&&o)&&l--,t&&h.push(o))}if(l+=c,y&&c!==l){for(r=0;a=v[r++];)a(h,d,e,i);if(t){if(0<l)for(;c--;)h[c]||d[c]||(d[c]=O.call(n));d=wt(d)}P.apply(n,d),s&&!t&&0<d.length&&1<l+v.length&&at.uniqueSort(n)}return s&&(T=f,_=u),h}var y=0<v.length,b=0<g.length;return y?ct(t):t}return gt.prototype=w.filters=w.pseudos,w.setFilters=new gt,f=at.tokenize=function(t,e){var i,n,s,o,r,a,l,c=D[t+" "];if(c)return e?0:c.slice(0);for(r=t,a=[],l=w.preFilter;r;){for(o in i&&!(n=q.exec(r))||(n&&(r=r.slice(n[0].length)||r),a.push(s=[])),i=!1,(n=Y.exec(r))&&(i=n.shift(),s.push({value:i,type:n[0].replace(W," ")}),r=r.slice(i.length)),w.filter)!(n=Q[o].exec(r))||l[o]&&!(n=l[o](n))||(i=n.shift(),s.push({value:i,type:o,matches:n}),r=r.slice(i.length));if(!i)break}return e?r.length:r?at.error(t):D(t,a).slice(0)},u=at.compile=function(t,e){var i,n=[],s=[],o=A[t+" "];if(!o){for(i=(e=e||f(t)).length;i--;)(o=function t(e){for(var s,i,n,o=e.length,r=w.relative[e[0].type],a=r||w.relative[" "],l=r?1:0,c=yt(function(t){return t===s},a,!0),h=yt(function(t){return-1<B(s,t)},a,!0),d=[function(t,e,i){var n=!r&&(i||e!==_)||((s=e).nodeType?c:h)(t,e,i);return s=null,n}];l<o;l++)if(i=w.relative[e[l].type])d=[yt(bt(d),i)];else{if((i=w.filter[e[l].type].apply(null,e[l].matches))[$]){for(n=++l;n<o&&!w.relative[e[n].type];n++);return _t(1<l&&bt(d),1<l&&vt(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(W,"$1"),i,l<n&&t(e.slice(l,n)),n<o&&t(e=e.slice(n)),n<o&&vt(e))}d.push(i)}return bt(d)}(e[i]))[$]?n.push(o):s.push(o);(o=A(t,xt(s,n))).selector=t}return o},m=at.select=function(t,e,i,n){var s,o,r,a,l,c="function"==typeof t&&t,h=!n&&f(t=c.selector||t);if(i=i||[],1===h.length){if(2<(o=h[0]=h[0].slice(0)).length&&"ID"===(r=o[0]).type&&9===e.nodeType&&k&&w.relative[o[1].type]){if(!(e=(w.find.ID(r.matches[0].replace(nt,d),e)||[])[0]))return i;c&&(e=e.parentNode),t=t.slice(o.shift().value.length)}for(s=Q.needsContext.test(t)?0:o.length;s--&&(r=o[s],!w.relative[a=r.type]);)if((l=w.find[a])&&(n=l(r.matches[0].replace(nt,d),it.test(o[0].type)&&mt(e.parentNode)||e))){if(o.splice(s,1),!(t=n.length&&vt(o)))return P.apply(i,n),i;break}}return(c||u(t,h))(n,e,!k,i,!e||it.test(t)&&mt(e.parentNode)||e),i},p.sortStable=$.split("").sort(L).join("")===$,p.detectDuplicates=!!c,x(),p.sortDetached=ht(function(t){return 1&t.compareDocumentPosition(C.createElement("fieldset"))}),ht(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||dt("type|href|height|width",function(t,e,i){if(!i)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),p.attributes&&ht(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||dt("value",function(t,e,i){if(!i&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),ht(function(t){return null==t.getAttribute("disabled")})||dt(j,function(t,e,i){var n;if(!i)return!0===t[e]?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null}),at}(C);$.find=p,$.expr=p.selectors,$.expr[":"]=$.expr.pseudos,$.uniqueSort=$.unique=p.uniqueSort,$.text=p.getText,$.isXMLDoc=p.isXML,$.contains=p.contains,$.escapeSelector=p.escape;function f(t,e,i){for(var n=[],s=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(s&&$(t).is(i))break;n.push(t)}return n}function x(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i}var T=$.expr.match.needsContext;function E(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var D=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function A(t,i,n){return b(i)?$.grep(t,function(t,e){return!!i.call(t,e,t)!==n}):i.nodeType?$.grep(t,function(t){return t===i!==n}):"string"!=typeof i?$.grep(t,function(t){return-1<s.call(i,t)!==n}):$.filter(i,t,n)}$.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?$.find.matchesSelector(n,t)?[n]:[]:$.find.matches(t,$.grep(e,function(t){return 1===t.nodeType}))},$.fn.extend({find:function(t){var e,i,n=this.length,s=this;if("string"!=typeof t)return this.pushStack($(t).filter(function(){for(e=0;e<n;e++)if($.contains(s[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)$.find(t,s[e],i);return 1<n?$.uniqueSort(i):i},filter:function(t){return this.pushStack(A(this,t||[],!1))},not:function(t){return this.pushStack(A(this,t||[],!0))},is:function(t){return!!A(this,"string"==typeof t&&T.test(t)?$(t):t||[],!1).length}});var S,L=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;($.fn.init=function(t,e,i){var n,s;if(!t)return this;if(i=i||S,"string"!=typeof t)return t.nodeType?(this[0]=t,this.length=1,this):b(t)?void 0!==i.ready?i.ready(t):t($):$.makeArray(t,this);if(!(n="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:L.exec(t))||!n[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof $?e[0]:e,$.merge(this,$.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:k,!0)),D.test(n[1])&&$.isPlainObject(e))for(n in e)b(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}return(s=k.getElementById(n[2]))&&(this[0]=s,this.length=1),this}).prototype=$.fn,S=$(k);var I=/^(?:parents|prev(?:Until|All))/,O={children:!0,contents:!0,next:!0,prev:!0};function N(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}$.fn.extend({has:function(t){var e=$(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if($.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,s=this.length,o=[],r="string"!=typeof t&&$(t);if(!T.test(t))for(;n<s;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?-1<r.index(i):1===i.nodeType&&$.find.matchesSelector(i,t))){o.push(i);break}return this.pushStack(1<o.length?$.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?s.call($(t),this[0]):s.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack($.uniqueSort($.merge(this.get(),$(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),$.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return f(t,"parentNode")},parentsUntil:function(t,e,i){return f(t,"parentNode",i)},next:function(t){return N(t,"nextSibling")},prev:function(t){return N(t,"previousSibling")},nextAll:function(t){return f(t,"nextSibling")},prevAll:function(t){return f(t,"previousSibling")},nextUntil:function(t,e,i){return f(t,"nextSibling",i)},prevUntil:function(t,e,i){return f(t,"previousSibling",i)},siblings:function(t){return x((t.parentNode||{}).firstChild,t)},children:function(t){return x(t.firstChild)},contents:function(t){return null!=t.contentDocument&&n(t.contentDocument)?t.contentDocument:(E(t,"template")&&(t=t.content||t),$.merge([],t.childNodes))}},function(n,s){$.fn[n]=function(t,e){var i=$.map(this,s,t);return"Until"!==n.slice(-5)&&(e=t),e&&"string"==typeof e&&(i=$.filter(e,i)),1<this.length&&(O[n]||$.uniqueSort(i),I.test(n)&&i.reverse()),this.pushStack(i)}});var P=/[^\x20\t\r\n\f]+/g;function M(t){return t}function B(t){throw t}function j(t,e,i,n){var s;try{t&&b(s=t.promise)?s.call(t).done(e).fail(i):t&&b(s=t.then)?s.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}$.Callbacks=function(n){var t,i;n="string"==typeof n?(t=n,i={},$.each(t.match(P)||[],function(t,e){i[e]=!0}),i):$.extend({},n);function s(){for(a=a||n.once,r=o=!0;c.length;h=-1)for(e=c.shift();++h<l.length;)!1===l[h].apply(e[0],e[1])&&n.stopOnFalse&&(h=l.length,e=!1);n.memory||(e=!1),o=!1,a&&(l=e?[]:"")}var o,e,r,a,l=[],c=[],h=-1,d={add:function(){return l&&(e&&!o&&(h=l.length-1,c.push(e)),function i(t){$.each(t,function(t,e){b(e)?n.unique&&d.has(e)||l.push(e):e&&e.length&&"string"!==_(e)&&i(e)})}(arguments),e&&!o&&s()),this},remove:function(){return $.each(arguments,function(t,e){for(var i;-1<(i=$.inArray(e,l,i));)l.splice(i,1),i<=h&&h--}),this},has:function(t){return t?-1<$.inArray(t,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return a=c=[],l=e="",this},disabled:function(){return!l},lock:function(){return a=c=[],e||o||(l=e=""),this},locked:function(){return!!a},fireWith:function(t,e){return a||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),o||s()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!r}};return d},$.extend({Deferred:function(t){var o=[["notify","progress",$.Callbacks("memory"),$.Callbacks("memory"),2],["resolve","done",$.Callbacks("once memory"),$.Callbacks("once memory"),0,"resolved"],["reject","fail",$.Callbacks("once memory"),$.Callbacks("once memory"),1,"rejected"]],s="pending",r={state:function(){return s},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return r.then(null,t)},pipe:function(){var s=arguments;return $.Deferred(function(n){$.each(o,function(t,e){var i=b(s[e[4]])&&s[e[4]];a[e[1]](function(){var t=i&&i.apply(this,arguments);t&&b(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[e[0]+"With"](this,i?[t]:arguments)})}),s=null}).promise()},then:function(e,i,n){var l=0;function c(s,o,r,a){return function(){function t(){var t,e;if(!(s<l)){if((t=r.apply(i,n))===o.promise())throw new TypeError("Thenable self-resolution");e=t&&("object"==typeof t||"function"==typeof t)&&t.then,b(e)?a?e.call(t,c(l,o,M,a),c(l,o,B,a)):(l++,e.call(t,c(l,o,M,a),c(l,o,B,a),c(l,o,M,o.notifyWith))):(r!==M&&(i=void 0,n=[t]),(a||o.resolveWith)(i,n))}}var i=this,n=arguments,e=a?t:function(){try{t()}catch(t){$.Deferred.exceptionHook&&$.Deferred.exceptionHook(t,e.stackTrace),l<=s+1&&(r!==B&&(i=void 0,n=[t]),o.rejectWith(i,n))}};s?e():($.Deferred.getStackHook&&(e.stackTrace=$.Deferred.getStackHook()),C.setTimeout(e))}}return $.Deferred(function(t){o[0][3].add(c(0,t,b(n)?n:M,t.notifyWith)),o[1][3].add(c(0,t,b(e)?e:M)),o[2][3].add(c(0,t,b(i)?i:B))}).promise()},promise:function(t){return null!=t?$.extend(t,r):r}},a={};return $.each(o,function(t,e){var i=e[2],n=e[5];r[e[1]]=i.add,n&&i.add(function(){s=n},o[3-t][2].disable,o[3-t][3].disable,o[0][2].lock,o[0][3].lock),i.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=i.fireWith}),r.promise(a),t&&t.call(a,a),a},when:function(t){function e(e){return function(t){s[e]=this,o[e]=1<arguments.length?a.call(arguments):t,--i||r.resolveWith(s,o)}}var i=arguments.length,n=i,s=Array(n),o=a.call(arguments),r=$.Deferred();if(i<=1&&(j(t,r.done(e(n)).resolve,r.reject,!i),"pending"===r.state()||b(o[n]&&o[n].then)))return r.then();for(;n--;)j(o[n],e(n),r.reject);return r.promise()}});var F=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;$.Deferred.exceptionHook=function(t,e){C.console&&C.console.warn&&t&&F.test(t.name)&&C.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},$.readyException=function(t){C.setTimeout(function(){throw t})};var H=$.Deferred();function U(){k.removeEventListener("DOMContentLoaded",U),C.removeEventListener("load",U),$.ready()}$.fn.ready=function(t){return H.then(t).catch(function(t){$.readyException(t)}),this},$.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--$.readyWait:$.isReady)||($.isReady=!0)!==t&&0<--$.readyWait||H.resolveWith(k,[$])}}),$.ready.then=H.then,"complete"===k.readyState||"loading"!==k.readyState&&!k.documentElement.doScroll?C.setTimeout($.ready):(k.addEventListener("DOMContentLoaded",U),C.addEventListener("load",U));function R(t,e,i,n,s,o,r){var a=0,l=t.length,c=null==i;if("object"===_(i))for(a in s=!0,i)R(t,e,a,i[a],!0,o,r);else if(void 0!==n&&(s=!0,b(n)||(r=!0),c&&(e=r?(e.call(t,n),null):(c=e,function(t,e,i){return c.call($(t),i)})),e))for(;a<l;a++)e(t[a],i,r?n:n.call(t[a],a,e(t[a],i)));return s?t:c?e.call(t):l?e(t[0],i):o}var z=/^-ms-/,W=/-([a-z])/g;function q(t,e){return e.toUpperCase()}function Y(t){return t.replace(z,"ms-").replace(W,q)}function V(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function G(){this.expando=$.expando+G.uid++}G.uid=1,G.prototype={cache:function(t){var e=t[this.expando];return e||(e={},V(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,s=this.cache(t);if("string"==typeof e)s[Y(e)]=i;else for(n in e)s[Y(n)]=e[n];return s},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][Y(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(Y):(e=Y(e))in n?[e]:e.match(P)||[]).length;for(;i--;)delete n[e[i]]}void 0!==e&&!$.isEmptyObject(n)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!$.isEmptyObject(e)}};var K=new G,Q=new G,X=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,J=/[A-Z]/g;function Z(t,e,i){var n,s;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(J,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i="true"===(s=i)||"false"!==s&&("null"===s?null:s===+s+""?+s:X.test(s)?JSON.parse(s):s)}catch(t){}Q.set(t,e,i)}else i=void 0;return i}$.extend({hasData:function(t){return Q.hasData(t)||K.hasData(t)},data:function(t,e,i){return Q.access(t,e,i)},removeData:function(t,e){Q.remove(t,e)},_data:function(t,e,i){return K.access(t,e,i)},_removeData:function(t,e){K.remove(t,e)}}),$.fn.extend({data:function(i,t){var e,n,s,o=this[0],r=o&&o.attributes;if(void 0!==i)return"object"==typeof i?this.each(function(){Q.set(this,i)}):R(this,function(t){var e;return o&&void 0===t?void 0!==(e=Q.get(o,i))||void 0!==(e=Z(o,i))?e:void 0:void this.each(function(){Q.set(this,i,t)})},null,t,1<arguments.length,null,!0);if(this.length&&(s=Q.get(o),1===o.nodeType&&!K.get(o,"hasDataAttrs"))){for(e=r.length;e--;)r[e]&&0===(n=r[e].name).indexOf("data-")&&(n=Y(n.slice(5)),Z(o,n,s[n]));K.set(o,"hasDataAttrs",!0)}return s},removeData:function(t){return this.each(function(){Q.remove(this,t)})}}),$.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=K.get(t,e),i&&(!n||Array.isArray(i)?n=K.access(t,e,$.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=$.queue(t,e),n=i.length,s=i.shift(),o=$._queueHooks(t,e);"inprogress"===s&&(s=i.shift(),n--),s&&("fx"===e&&i.unshift("inprogress"),delete o.stop,s.call(t,function(){$.dequeue(t,e)},o)),!n&&o&&o.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return K.get(t,i)||K.access(t,i,{empty:$.Callbacks("once memory").add(function(){K.remove(t,[e+"queue",i])})})}}),$.fn.extend({queue:function(e,i){var t=2;return"string"!=typeof e&&(i=e,e="fx",t--),arguments.length<t?$.queue(this[0],e):void 0===i?this:this.each(function(){var t=$.queue(this,e,i);$._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&$.dequeue(this,e)})},dequeue:function(t){return this.each(function(){$.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function i(){--s||o.resolveWith(r,[r])}var n,s=1,o=$.Deferred(),r=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(n=K.get(r[a],t+"queueHooks"))&&n.empty&&(s++,n.empty.add(i));return i(),o.promise(e)}});var tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+tt+")([a-z%]*)$","i"),it=["Top","Right","Bottom","Left"],nt=k.documentElement,st=function(t){return $.contains(t.ownerDocument,t)},ot={composed:!0};nt.getRootNode&&(st=function(t){return $.contains(t.ownerDocument,t)||t.getRootNode(ot)===t.ownerDocument});var rt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&st(t)&&"none"===$.css(t,"display")};function at(t,e,i,n){var s,o,r=20,a=n?function(){return n.cur()}:function(){return $.css(t,e,"")},l=a(),c=i&&i[3]||($.cssNumber[e]?"":"px"),h=t.nodeType&&($.cssNumber[e]||"px"!==c&&+l)&&et.exec($.css(t,e));if(h&&h[3]!==c){for(l/=2,c=c||h[3],h=+l||1;r--;)$.style(t,e,h+c),(1-o)*(1-(o=a()/l||.5))<=0&&(r=0),h/=o;h*=2,$.style(t,e,h+c),i=i||[]}return i&&(h=+h||+l||0,s=i[1]?h+(i[1]+1)*i[2]:+i[2],n&&(n.unit=c,n.start=h,n.end=s)),s}var lt={};function ct(t,e){for(var i,n,s,o,r,a,l,c=[],h=0,d=t.length;h<d;h++)(n=t[h]).style&&(i=n.style.display,e?("none"===i&&(c[h]=K.get(n,"display")||null,c[h]||(n.style.display="")),""===n.style.display&&rt(n)&&(c[h]=(l=r=o=void 0,r=(s=n).ownerDocument,a=s.nodeName,(l=lt[a])||(o=r.body.appendChild(r.createElement(a)),l=$.css(o,"display"),o.parentNode.removeChild(o),"none"===l&&(l="block"),lt[a]=l)))):"none"!==i&&(c[h]="none",K.set(n,"display",i)));for(h=0;h<d;h++)null!=c[h]&&(t[h].style.display=c[h]);return t}$.fn.extend({show:function(){return ct(this,!0)},hide:function(){return ct(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){rt(this)?$(this).show():$(this).hide()})}});var ht,dt,ut=/^(?:checkbox|radio)$/i,pt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ft=/^$|^module$|\/(?:java|ecma)script/i;ht=k.createDocumentFragment().appendChild(k.createElement("div")),(dt=k.createElement("input")).setAttribute("type","radio"),dt.setAttribute("checked","checked"),dt.setAttribute("name","t"),ht.appendChild(dt),y.checkClone=ht.cloneNode(!0).cloneNode(!0).lastChild.checked,ht.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!ht.cloneNode(!0).lastChild.defaultValue,ht.innerHTML="<option></option>",y.option=!!ht.lastChild;var mt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function gt(t,e){var i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&E(t,e)?$.merge([t],i):i}function vt(t,e){for(var i=0,n=t.length;i<n;i++)K.set(t[i],"globalEval",!e||K.get(e[i],"globalEval"))}mt.tbody=mt.tfoot=mt.colgroup=mt.caption=mt.thead,mt.th=mt.td,y.option||(mt.optgroup=mt.option=[1,"<select multiple='multiple'>","</select>"]);var yt=/<|&#?\w+;/;function bt(t,e,i,n,s){for(var o,r,a,l,c,h,d=e.createDocumentFragment(),u=[],p=0,f=t.length;p<f;p++)if((o=t[p])||0===o)if("object"===_(o))$.merge(u,o.nodeType?[o]:o);else if(yt.test(o)){for(r=r||d.appendChild(e.createElement("div")),a=(pt.exec(o)||["",""])[1].toLowerCase(),l=mt[a]||mt._default,r.innerHTML=l[1]+$.htmlPrefilter(o)+l[2],h=l[0];h--;)r=r.lastChild;$.merge(u,r.childNodes),(r=d.firstChild).textContent=""}else u.push(e.createTextNode(o));for(d.textContent="",p=0;o=u[p++];)if(n&&-1<$.inArray(o,n))s&&s.push(o);else if(c=st(o),r=gt(d.appendChild(o),"script"),c&&vt(r),i)for(h=0;o=r[h++];)ft.test(o.type||"")&&i.push(o);return d}var wt=/^key/,_t=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,xt=/^([^.]*)(?:\.(.+)|)/;function Ct(){return!0}function kt(){return!1}function $t(t,e){return t===function(){try{return k.activeElement}catch(t){}}()==("focus"===e)}function Tt(t,e,i,n,s,o){var r,a;if("object"==typeof e){for(a in"string"!=typeof i&&(n=n||i,i=void 0),e)Tt(t,a,i,n,e[a],o);return t}if(null==n&&null==s?(s=i,n=i=void 0):null==s&&("string"==typeof i?(s=n,n=void 0):(s=n,n=i,i=void 0)),!1===s)s=kt;else if(!s)return t;return 1===o&&(r=s,(s=function(t){return $().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=$.guid++)),t.each(function(){$.event.add(this,e,s,n,i)})}function Et(t,s,o){o?(K.set(t,s,!1),$.event.add(t,s,{namespace:!1,handler:function(t){var e,i,n=K.get(this,s);if(1&t.isTrigger&&this[s]){if(n.length)($.event.special[s]||{}).delegateType&&t.stopPropagation();else if(n=a.call(arguments),K.set(this,s,n),e=o(this,s),this[s](),n!==(i=K.get(this,s))||e?K.set(this,s,!1):i={},n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i.value}else n.length&&(K.set(this,s,{value:$.event.trigger($.extend(n[0],$.Event.prototype),n.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===K.get(t,s)&&$.event.add(t,s,Ct)}$.event={global:{},add:function(e,t,i,n,s){var o,r,a,l,c,h,d,u,p,f,m,g=K.get(e);if(V(e))for(i.handler&&(i=(o=i).handler,s=o.selector),s&&$.find.matchesSelector(nt,s),i.guid||(i.guid=$.guid++),(l=g.events)||(l=g.events=Object.create(null)),(r=g.handle)||(r=g.handle=function(t){return void 0!==$&&$.event.triggered!==t.type?$.event.dispatch.apply(e,arguments):void 0}),c=(t=(t||"").match(P)||[""]).length;c--;)p=m=(a=xt.exec(t[c])||[])[1],f=(a[2]||"").split(".").sort(),p&&(d=$.event.special[p]||{},p=(s?d.delegateType:d.bindType)||p,d=$.event.special[p]||{},h=$.extend({type:p,origType:m,data:n,handler:i,guid:i.guid,selector:s,needsContext:s&&$.expr.match.needsContext.test(s),namespace:f.join(".")},o),(u=l[p])||((u=l[p]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(e,n,f,r)||e.addEventListener&&e.addEventListener(p,r)),d.add&&(d.add.call(e,h),h.handler.guid||(h.handler.guid=i.guid)),s?u.splice(u.delegateCount++,0,h):u.push(h),$.event.global[p]=!0)},remove:function(t,e,i,n,s){var o,r,a,l,c,h,d,u,p,f,m,g=K.hasData(t)&&K.get(t);if(g&&(l=g.events)){for(c=(e=(e||"").match(P)||[""]).length;c--;)if(p=m=(a=xt.exec(e[c])||[])[1],f=(a[2]||"").split(".").sort(),p){for(d=$.event.special[p]||{},u=l[p=(n?d.delegateType:d.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=o=u.length;o--;)h=u[o],!s&&m!==h.origType||i&&i.guid!==h.guid||a&&!a.test(h.namespace)||n&&n!==h.selector&&("**"!==n||!h.selector)||(u.splice(o,1),h.selector&&u.delegateCount--,d.remove&&d.remove.call(t,h));r&&!u.length&&(d.teardown&&!1!==d.teardown.call(t,f,g.handle)||$.removeEvent(t,p,g.handle),delete l[p])}else for(p in l)$.event.remove(t,p+e[c],i,n,!0);$.isEmptyObject(l)&&K.remove(t,"handle events")}},dispatch:function(t){var e,i,n,s,o,r,a=new Array(arguments.length),l=$.event.fix(t),c=(K.get(this,"events")||Object.create(null))[l.type]||[],h=$.event.special[l.type]||{};for(a[0]=l,e=1;e<arguments.length;e++)a[e]=arguments[e];if(l.delegateTarget=this,!h.preDispatch||!1!==h.preDispatch.call(this,l)){for(r=$.event.handlers.call(this,l,c),e=0;(s=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=s.elem,i=0;(o=s.handlers[i++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(n=(($.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,a))&&!1===(l.result=n)&&(l.preventDefault(),l.stopPropagation()));return h.postDispatch&&h.postDispatch.call(this,l),l.result}},handlers:function(t,e){var i,n,s,o,r,a=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(o=[],r={},i=0;i<l;i++)void 0===r[s=(n=e[i]).selector+" "]&&(r[s]=n.needsContext?-1<$(s,this).index(c):$.find(s,this,null,[c]).length),r[s]&&o.push(n);o.length&&a.push({elem:c,handlers:o})}return c=this,l<e.length&&a.push({elem:c,handlers:e.slice(l)}),a},addProp:function(e,t){Object.defineProperty($.Event.prototype,e,{enumerable:!0,configurable:!0,get:b(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(t){return t[$.expando]?t:new $.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return ut.test(e.type)&&e.click&&E(e,"input")&&Et(e,"click",Ct),!1},trigger:function(t){var e=this||t;return ut.test(e.type)&&e.click&&E(e,"input")&&Et(e,"click"),!0},_default:function(t){var e=t.target;return ut.test(e.type)&&e.click&&E(e,"input")&&K.get(e,"click")||E(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},$.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},$.Event=function(t,e){if(!(this instanceof $.Event))return new $.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Ct:kt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&$.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[$.expando]=!0},$.Event.prototype={constructor:$.Event,isDefaultPrevented:kt,isPropagationStopped:kt,isImmediatePropagationStopped:kt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Ct,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Ct,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Ct,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},$.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(t){var e=t.button;return null==t.which&&wt.test(t.type)?null!=t.charCode?t.charCode:t.keyCode:!t.which&&void 0!==e&&_t.test(t.type)?1&e?1:2&e?3:4&e?2:0:t.which}},$.event.addProp),$.each({focus:"focusin",blur:"focusout"},function(t,e){$.event.special[t]={setup:function(){return Et(this,t,$t),!1},trigger:function(){return Et(this,t),!0},delegateType:e}}),$.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,s){$.event.special[t]={delegateType:s,bindType:s,handle:function(t){var e,i=t.relatedTarget,n=t.handleObj;return i&&(i===this||$.contains(this,i))||(t.type=n.origType,e=n.handler.apply(this,arguments),t.type=s),e}}}),$.fn.extend({on:function(t,e,i,n){return Tt(this,t,e,i,n)},one:function(t,e,i,n){return Tt(this,t,e,i,n,1)},off:function(t,e,i){var n,s;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,$(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=kt),this.each(function(){$.event.remove(this,t,i,e)});for(s in t)this.off(s,e,t[s]);return this}});var Dt=/<script|<style|<link/i,At=/checked\s*(?:[^=]|=\s*.checked.)/i,St=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Lt(t,e){return E(t,"table")&&E(11!==e.nodeType?e:e.firstChild,"tr")&&$(t).children("tbody")[0]||t}function It(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Ot(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Nt(t,e){var i,n,s,o,r,a;if(1===e.nodeType){if(K.hasData(t)&&(a=K.get(t).events))for(s in K.remove(e,"handle events"),a)for(i=0,n=a[s].length;i<n;i++)$.event.add(e,s,a[s][i]);Q.hasData(t)&&(o=Q.access(t),r=$.extend({},o),Q.set(e,r))}}function Pt(i,n,s,o){n=g(n);var t,e,r,a,l,c,h=0,d=i.length,u=d-1,p=n[0],f=b(p);if(f||1<d&&"string"==typeof p&&!y.checkClone&&At.test(p))return i.each(function(t){var e=i.eq(t);f&&(n[0]=p.call(this,t,e.html())),Pt(e,n,s,o)});if(d&&(e=(t=bt(n,i[0].ownerDocument,!1,i,o)).firstChild,1===t.childNodes.length&&(t=e),e||o)){for(a=(r=$.map(gt(t,"script"),It)).length;h<d;h++)l=t,h!==u&&(l=$.clone(l,!0,!0),a&&$.merge(r,gt(l,"script"))),s.call(i[h],l,h);if(a)for(c=r[r.length-1].ownerDocument,$.map(r,Ot),h=0;h<a;h++)l=r[h],ft.test(l.type||"")&&!K.access(l,"globalEval")&&$.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?$._evalUrl&&!l.noModule&&$._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):w(l.textContent.replace(St,""),l,c))}return i}function Mt(t,e,i){for(var n,s=e?$.filter(e,t):t,o=0;null!=(n=s[o]);o++)i||1!==n.nodeType||$.cleanData(gt(n)),n.parentNode&&(i&&st(n)&&vt(gt(n,"script")),n.parentNode.removeChild(n));return t}$.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,s,o,r,a,l,c,h=t.cloneNode(!0),d=st(t);if(!(y.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||$.isXMLDoc(t)))for(r=gt(h),n=0,s=(o=gt(t)).length;n<s;n++)a=o[n],l=r[n],"input"===(c=l.nodeName.toLowerCase())&&ut.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(e)if(i)for(o=o||gt(t),r=r||gt(h),n=0,s=o.length;n<s;n++)Nt(o[n],r[n]);else Nt(t,h);return 0<(r=gt(h,"script")).length&&vt(r,!d&&gt(t,"script")),h},cleanData:function(t){for(var e,i,n,s=$.event.special,o=0;void 0!==(i=t[o]);o++)if(V(i)){if(e=i[K.expando]){if(e.events)for(n in e.events)s[n]?$.event.remove(i,n):$.removeEvent(i,n,e.handle);i[K.expando]=void 0}i[Q.expando]&&(i[Q.expando]=void 0)}}}),$.fn.extend({detach:function(t){return Mt(this,t,!0)},remove:function(t){return Mt(this,t)},text:function(t){return R(this,function(t){return void 0===t?$.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Pt(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Lt(this,t).appendChild(t)})},prepend:function(){return Pt(this,arguments,function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=Lt(this,t)).insertBefore(t,e.firstChild)})},before:function(){return Pt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Pt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&($.cleanData(gt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return $.clone(this,t,e)})},html:function(t){return R(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Dt.test(t)&&!mt[(pt.exec(t)||["",""])[1].toLowerCase()]){t=$.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&($.cleanData(gt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var i=[];return Pt(this,arguments,function(t){var e=this.parentNode;$.inArray(this,i)<0&&($.cleanData(gt(this)),e&&e.replaceChild(t,this))},i)}}),$.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,r){$.fn[t]=function(t){for(var e,i=[],n=$(t),s=n.length-1,o=0;o<=s;o++)e=o===s?this:this.clone(!0),$(n[o])[r](e),l.apply(i,e.get());return this.pushStack(i)}});function Bt(t,e,i){var n,s,o={};for(s in e)o[s]=t.style[s],t.style[s]=e[s];for(s in n=i.call(t),e)t.style[s]=o[s];return n}var jt,Ft,Ht,Ut,Rt,zt,Wt,qt,Yt=new RegExp("^("+tt+")(?!px)[a-z%]+$","i"),Vt=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=C),e.getComputedStyle(t)},Gt=new RegExp(it.join("|"),"i");function Kt(){var t;qt&&(Wt.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",qt.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",nt.appendChild(Wt).appendChild(qt),t=C.getComputedStyle(qt),jt="1%"!==t.top,zt=12===Qt(t.marginLeft),qt.style.right="60%",Ut=36===Qt(t.right),Ft=36===Qt(t.width),qt.style.position="absolute",Ht=12===Qt(qt.offsetWidth/3),nt.removeChild(Wt),qt=null)}function Qt(t){return Math.round(parseFloat(t))}function Xt(t,e,i){var n,s,o,r,a=t.style;return(i=i||Vt(t))&&(""!==(r=i.getPropertyValue(e)||i[e])||st(t)||(r=$.style(t,e)),!y.pixelBoxStyles()&&Yt.test(r)&&Gt.test(e)&&(n=a.width,s=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=r,r=i.width,a.width=n,a.minWidth=s,a.maxWidth=o)),void 0!==r?r+"":r}function Jt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}Wt=k.createElement("div"),(qt=k.createElement("div")).style&&(qt.style.backgroundClip="content-box",qt.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===qt.style.backgroundClip,$.extend(y,{boxSizingReliable:function(){return Kt(),Ft},pixelBoxStyles:function(){return Kt(),Ut},pixelPosition:function(){return Kt(),jt},reliableMarginLeft:function(){return Kt(),zt},scrollboxSize:function(){return Kt(),Ht},reliableTrDimensions:function(){var t,e,i,n;return null==Rt&&(t=k.createElement("table"),e=k.createElement("tr"),i=k.createElement("div"),t.style.cssText="position:absolute;left:-11111px",e.style.height="1px",i.style.height="9px",nt.appendChild(t).appendChild(e).appendChild(i),n=C.getComputedStyle(e),Rt=3<parseInt(n.height),nt.removeChild(t)),Rt}}));var Zt=["Webkit","Moz","ms"],te=k.createElement("div").style,ee={};function ie(t){var e=$.cssProps[t]||ee[t];return e||(t in te?t:ee[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=Zt.length;i--;)if((t=Zt[i]+e)in te)return t}(t)||t)}var ne=/^(none|table(?!-c[ea]).+)/,se=/^--/,oe={position:"absolute",visibility:"hidden",display:"block"},re={letterSpacing:"0",fontWeight:"400"};function ae(t,e,i){var n=et.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function le(t,e,i,n,s,o){var r="width"===e?1:0,a=0,l=0;if(i===(n?"border":"content"))return 0;for(;r<4;r+=2)"margin"===i&&(l+=$.css(t,i+it[r],!0,s)),n?("content"===i&&(l-=$.css(t,"padding"+it[r],!0,s)),"margin"!==i&&(l-=$.css(t,"border"+it[r]+"Width",!0,s))):(l+=$.css(t,"padding"+it[r],!0,s),"padding"!==i?l+=$.css(t,"border"+it[r]+"Width",!0,s):a+=$.css(t,"border"+it[r]+"Width",!0,s));return!n&&0<=o&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-a-.5))||0),l}function ce(t,e,i){var n=Vt(t),s=(!y.boxSizingReliable()||i)&&"border-box"===$.css(t,"boxSizing",!1,n),o=s,r=Xt(t,e,n),a="offset"+e[0].toUpperCase()+e.slice(1);if(Yt.test(r)){if(!i)return r;r="auto"}return(!y.boxSizingReliable()&&s||!y.reliableTrDimensions()&&E(t,"tr")||"auto"===r||!parseFloat(r)&&"inline"===$.css(t,"display",!1,n))&&t.getClientRects().length&&(s="border-box"===$.css(t,"boxSizing",!1,n),(o=a in t)&&(r=t[a])),(r=parseFloat(r)||0)+le(t,e,i||(s?"border":"content"),o,n,r)+"px"}function he(t,e,i,n,s){return new he.prototype.init(t,e,i,n,s)}$.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=Xt(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,o,r,a=Y(e),l=se.test(e),c=t.style;if(l||(e=ie(a)),r=$.cssHooks[e]||$.cssHooks[a],void 0===i)return r&&"get"in r&&void 0!==(s=r.get(t,!1,n))?s:c[e];"string"===(o=typeof i)&&(s=et.exec(i))&&s[1]&&(i=at(t,e,s),o="number"),null!=i&&i==i&&("number"!==o||l||(i+=s&&s[3]||($.cssNumber[a]?"":"px")),y.clearCloneStyle||""!==i||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(i=r.set(t,i,n))||(l?c.setProperty(e,i):c[e]=i))}},css:function(t,e,i,n){var s,o,r,a=Y(e);return se.test(e)||(e=ie(a)),(r=$.cssHooks[e]||$.cssHooks[a])&&"get"in r&&(s=r.get(t,!0,i)),void 0===s&&(s=Xt(t,e,n)),"normal"===s&&e in re&&(s=re[e]),""===i||i?(o=parseFloat(s),!0===i||isFinite(o)?o||0:s):s}}),$.each(["height","width"],function(t,l){$.cssHooks[l]={get:function(t,e,i){if(e)return!ne.test($.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?ce(t,l,i):Bt(t,oe,function(){return ce(t,l,i)})},set:function(t,e,i){var n,s=Vt(t),o=!y.scrollboxSize()&&"absolute"===s.position,r=(o||i)&&"border-box"===$.css(t,"boxSizing",!1,s),a=i?le(t,l,i,r,s):0;return r&&o&&(a-=Math.ceil(t["offset"+l[0].toUpperCase()+l.slice(1)]-parseFloat(s[l])-le(t,l,"border",!1,s)-.5)),a&&(n=et.exec(e))&&"px"!==(n[3]||"px")&&(t.style[l]=e,e=$.css(t,l)),ae(0,e,a)}}}),$.cssHooks.marginLeft=Jt(y.reliableMarginLeft,function(t,e){if(e)return(parseFloat(Xt(t,"marginLeft"))||t.getBoundingClientRect().left-Bt(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),$.each({margin:"",padding:"",border:"Width"},function(s,o){$.cssHooks[s+o]={expand:function(t){for(var e=0,i={},n="string"==typeof t?t.split(" "):[t];e<4;e++)i[s+it[e]+o]=n[e]||n[e-2]||n[0];return i}},"margin"!==s&&($.cssHooks[s+o].set=ae)}),$.fn.extend({css:function(t,e){return R(this,function(t,e,i){var n,s,o={},r=0;if(Array.isArray(e)){for(n=Vt(t),s=e.length;r<s;r++)o[e[r]]=$.css(t,e[r],!1,n);return o}return void 0!==i?$.style(t,e,i):$.css(t,e)},t,e,1<arguments.length)}}),($.Tween=he).prototype={constructor:he,init:function(t,e,i,n,s,o){this.elem=t,this.prop=i,this.easing=s||$.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=o||($.cssNumber[i]?"":"px")},cur:function(){var t=he.propHooks[this.prop];return t&&t.get?t.get(this):he.propHooks._default.get(this)},run:function(t){var e,i=he.propHooks[this.prop];return this.options.duration?this.pos=e=$.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):he.propHooks._default.set(this),this}},he.prototype.init.prototype=he.prototype,he.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=$.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){$.fx.step[t.prop]?$.fx.step[t.prop](t):1!==t.elem.nodeType||!$.cssHooks[t.prop]&&null==t.elem.style[ie(t.prop)]?t.elem[t.prop]=t.now:$.style(t.elem,t.prop,t.now+t.unit)}}},he.propHooks.scrollTop=he.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},$.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},$.fx=he.prototype.init,$.fx.step={};var de,ue,pe,fe,me=/^(?:toggle|show|hide)$/,ge=/queueHooks$/;function ve(){ue&&(!1===k.hidden&&C.requestAnimationFrame?C.requestAnimationFrame(ve):C.setTimeout(ve,$.fx.interval),$.fx.tick())}function ye(){return C.setTimeout(function(){de=void 0}),de=Date.now()}function be(t,e){var i,n=0,s={height:t};for(e=e?1:0;n<4;n+=2-e)s["margin"+(i=it[n])]=s["padding"+i]=t;return e&&(s.opacity=s.width=t),s}function we(t,e,i){for(var n,s=(_e.tweeners[e]||[]).concat(_e.tweeners["*"]),o=0,r=s.length;o<r;o++)if(n=s[o].call(i,e,t))return n}function _e(o,t,e){var i,r,n=0,s=_e.prefilters.length,a=$.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=de||ye(),e=Math.max(0,c.startTime+c.duration-t),i=1-(e/c.duration||0),n=0,s=c.tweens.length;n<s;n++)c.tweens[n].run(i);return a.notifyWith(o,[c,i,e]),i<1&&s?e:(s||a.notifyWith(o,[c,1,0]),a.resolveWith(o,[c]),!1)},c=a.promise({elem:o,props:$.extend({},t),opts:$.extend(!0,{specialEasing:{},easing:$.easing._default},e),originalProperties:t,originalOptions:e,startTime:de||ye(),duration:e.duration,tweens:[],createTween:function(t,e){var i=$.Tween(o,c.opts,t,e,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(i),i},stop:function(t){var e=0,i=t?c.tweens.length:0;if(r)return this;for(r=!0;e<i;e++)c.tweens[e].run(1);return t?(a.notifyWith(o,[c,1,0]),a.resolveWith(o,[c,t])):a.rejectWith(o,[c,t]),this}}),h=c.props;for(!function(t,e){var i,n,s,o,r;for(i in t)if(s=e[n=Y(i)],o=t[i],Array.isArray(o)&&(s=o[1],o=t[i]=o[0]),i!==n&&(t[n]=o,delete t[i]),(r=$.cssHooks[n])&&"expand"in r)for(i in o=r.expand(o),delete t[n],o)i in t||(t[i]=o[i],e[i]=s);else e[n]=s}(h,c.opts.specialEasing);n<s;n++)if(i=_e.prefilters[n].call(c,o,h,c.opts))return b(i.stop)&&($._queueHooks(c.elem,c.opts.queue).stop=i.stop.bind(i)),i;return $.map(h,we,c),b(c.opts.start)&&c.opts.start.call(o,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),$.fx.timer($.extend(l,{elem:o,anim:c,queue:c.opts.queue})),c}$.Animation=$.extend(_e,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return at(i.elem,t,et.exec(e),i),i}]},tweener:function(t,e){for(var i,n=0,s=(t=b(t)?(e=t,["*"]):t.match(P)).length;n<s;n++)i=t[n],_e.tweeners[i]=_e.tweeners[i]||[],_e.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,s,o,r,a,l,c,h,d="width"in e||"height"in e,u=this,p={},f=t.style,m=t.nodeType&&rt(t),g=K.get(t,"fxshow");for(n in i.queue||(null==(r=$._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,a=r.empty.fire,r.empty.fire=function(){r.unqueued||a()}),r.unqueued++,u.always(function(){u.always(function(){r.unqueued--,$.queue(t,"fx").length||r.empty.fire()})})),e)if(s=e[n],me.test(s)){if(delete e[n],o=o||"toggle"===s,s===(m?"hide":"show")){if("show"!==s||!g||void 0===g[n])continue;m=!0}p[n]=g&&g[n]||$.style(t,n)}if((l=!$.isEmptyObject(e))||!$.isEmptyObject(p))for(n in d&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=K.get(t,"display")),"none"===(h=$.css(t,"display"))&&(c?h=c:(ct([t],!0),c=t.style.display||c,h=$.css(t,"display"),ct([t]))),("inline"===h||"inline-block"===h&&null!=c)&&"none"===$.css(t,"float")&&(l||(u.done(function(){f.display=c}),null==c&&(h=f.display,c="none"===h?"":h)),f.display="inline-block")),i.overflow&&(f.overflow="hidden",u.always(function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]})),l=!1,p)l||(g?"hidden"in g&&(m=g.hidden):g=K.access(t,"fxshow",{display:c}),o&&(g.hidden=!m),m&&ct([t],!0),u.done(function(){for(n in m||ct([t]),K.remove(t,"fxshow"),p)$.style(t,n,p[n])})),l=we(m?g[n]:0,n,u),n in g||(g[n]=l.start,m&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?_e.prefilters.unshift(t):_e.prefilters.push(t)}}),$.speed=function(t,e,i){var n=t&&"object"==typeof t?$.extend({},t):{complete:i||!i&&e||b(t)&&t,duration:t,easing:i&&e||e&&!b(e)&&e};return $.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in $.fx.speeds?n.duration=$.fx.speeds[n.duration]:n.duration=$.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){b(n.old)&&n.old.call(this),n.queue&&$.dequeue(this,n.queue)},n},$.fn.extend({fadeTo:function(t,e,i,n){return this.filter(rt).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(e,t,i,n){function s(){var t=_e(this,$.extend({},e),r);(o||K.get(this,"finish"))&&t.stop(!0)}var o=$.isEmptyObject(e),r=$.speed(t,i,n);return s.finish=s,o||!1===r.queue?this.each(s):this.queue(r.queue,s)},stop:function(s,t,o){function r(t){var e=t.stop;delete t.stop,e(o)}return"string"!=typeof s&&(o=t,t=s,s=void 0),t&&this.queue(s||"fx",[]),this.each(function(){var t=!0,e=null!=s&&s+"queueHooks",i=$.timers,n=K.get(this);if(e)n[e]&&n[e].stop&&r(n[e]);else for(e in n)n[e]&&n[e].stop&&ge.test(e)&&r(n[e]);for(e=i.length;e--;)i[e].elem!==this||null!=s&&i[e].queue!==s||(i[e].anim.stop(o),t=!1,i.splice(e,1));!t&&o||$.dequeue(this,s)})},finish:function(r){return!1!==r&&(r=r||"fx"),this.each(function(){var t,e=K.get(this),i=e[r+"queue"],n=e[r+"queueHooks"],s=$.timers,o=i?i.length:0;for(e.finish=!0,$.queue(this,r,[]),n&&n.stop&&n.stop.call(this,!0),t=s.length;t--;)s[t].elem===this&&s[t].queue===r&&(s[t].anim.stop(!0),s.splice(t,1));for(t=0;t<o;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete e.finish})}}),$.each(["toggle","show","hide"],function(t,n){var s=$.fn[n];$.fn[n]=function(t,e,i){return null==t||"boolean"==typeof t?s.apply(this,arguments):this.animate(be(n,!0),t,e,i)}}),$.each({slideDown:be("show"),slideUp:be("hide"),slideToggle:be("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,n){$.fn[t]=function(t,e,i){return this.animate(n,t,e,i)}}),$.timers=[],$.fx.tick=function(){var t,e=0,i=$.timers;for(de=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||$.fx.stop(),de=void 0},$.fx.timer=function(t){$.timers.push(t),$.fx.start()},$.fx.interval=13,$.fx.start=function(){ue||(ue=!0,ve())},$.fx.stop=function(){ue=null},$.fx.speeds={slow:600,fast:200,_default:400},$.fn.delay=function(n,t){return n=$.fx&&$.fx.speeds[n]||n,t=t||"fx",this.queue(t,function(t,e){var i=C.setTimeout(t,n);e.stop=function(){C.clearTimeout(i)}})},pe=k.createElement("input"),fe=k.createElement("select").appendChild(k.createElement("option")),pe.type="checkbox",y.checkOn=""!==pe.value,y.optSelected=fe.selected,(pe=k.createElement("input")).value="t",pe.type="radio",y.radioValue="t"===pe.value;var xe,Ce=$.expr.attrHandle;$.fn.extend({attr:function(t,e){return R(this,$.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){$.removeAttr(this,t)})}}),$.extend({attr:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?$.prop(t,e,i):(1===o&&$.isXMLDoc(t)||(s=$.attrHooks[e.toLowerCase()]||($.expr.match.bool.test(e)?xe:void 0)),void 0!==i?null===i?void $.removeAttr(t,e):s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:(t.setAttribute(e,i+""),i):!(s&&"get"in s&&null!==(n=s.get(t,e)))&&null==(n=$.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!y.radioValue&&"radio"===e&&E(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n=0,s=e&&e.match(P);if(s&&1===t.nodeType)for(;i=s[n++];)t.removeAttribute(i)}}),xe={set:function(t,e,i){return!1===e?$.removeAttr(t,i):t.setAttribute(i,i),i}},$.each($.expr.match.bool.source.match(/\w+/g),function(t,e){var r=Ce[e]||$.find.attr;Ce[e]=function(t,e,i){var n,s,o=e.toLowerCase();return i||(s=Ce[o],Ce[o]=n,n=null!=r(t,e,i)?o:null,Ce[o]=s),n}});var ke=/^(?:input|select|textarea|button)$/i,$e=/^(?:a|area)$/i;function Te(t){return(t.match(P)||[]).join(" ")}function Ee(t){return t.getAttribute&&t.getAttribute("class")||""}function De(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(P)||[]}$.fn.extend({prop:function(t,e){return R(this,$.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[$.propFix[t]||t]})}}),$.extend({prop:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&$.isXMLDoc(t)||(e=$.propFix[e]||e,s=$.propHooks[e]),void 0!==i?s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:t[e]=i:s&&"get"in s&&null!==(n=s.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=$.find.attr(t,"tabindex");return e?parseInt(e,10):ke.test(t.nodeName)||$e.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||($.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),$.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){$.propFix[this.toLowerCase()]=this}),$.fn.extend({addClass:function(e){var t,i,n,s,o,r,a,l=0;if(b(e))return this.each(function(t){$(this).addClass(e.call(this,t,Ee(this)))});if((t=De(e)).length)for(;i=this[l++];)if(s=Ee(i),n=1===i.nodeType&&" "+Te(s)+" "){for(r=0;o=t[r++];)n.indexOf(" "+o+" ")<0&&(n+=o+" ");s!==(a=Te(n))&&i.setAttribute("class",a)}return this},removeClass:function(e){var t,i,n,s,o,r,a,l=0;if(b(e))return this.each(function(t){$(this).removeClass(e.call(this,t,Ee(this)))});if(!arguments.length)return this.attr("class","");if((t=De(e)).length)for(;i=this[l++];)if(s=Ee(i),n=1===i.nodeType&&" "+Te(s)+" "){for(r=0;o=t[r++];)for(;-1<n.indexOf(" "+o+" ");)n=n.replace(" "+o+" "," ");s!==(a=Te(n))&&i.setAttribute("class",a)}return this},toggleClass:function(s,e){var o=typeof s,r="string"==o||Array.isArray(s);return"boolean"==typeof e&&r?e?this.addClass(s):this.removeClass(s):b(s)?this.each(function(t){$(this).toggleClass(s.call(this,t,Ee(this),e),e)}):this.each(function(){var t,e,i,n;if(r)for(e=0,i=$(this),n=De(s);t=n[e++];)i.hasClass(t)?i.removeClass(t):i.addClass(t);else void 0!==s&&"boolean"!=o||((t=Ee(this))&&K.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",!t&&!1!==s&&K.get(this,"__className__")||""))})},hasClass:function(t){for(var e,i=0,n=" "+t+" ";e=this[i++];)if(1===e.nodeType&&-1<(" "+Te(Ee(e))+" ").indexOf(n))return!0;return!1}});var Ae=/\r/g;$.fn.extend({val:function(i){var n,t,s,e=this[0];return arguments.length?(s=b(i),this.each(function(t){var e;1===this.nodeType&&(null==(e=s?i.call(this,t,$(this).val()):i)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=$.map(e,function(t){return null==t?"":t+""})),(n=$.valHooks[this.type]||$.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):e?(n=$.valHooks[e.type]||$.valHooks[e.nodeName.toLowerCase()])&&"get"in n&&void 0!==(t=n.get(e,"value"))?t:"string"==typeof(t=e.value)?t.replace(Ae,""):null==t?"":t:void 0}}),$.extend({valHooks:{option:{get:function(t){var e=$.find.attr(t,"value");return null!=e?e:Te($.text(t))}},select:{get:function(t){for(var e,i,n=t.options,s=t.selectedIndex,o="select-one"===t.type,r=o?null:[],a=o?s+1:n.length,l=s<0?a:o?s:0;l<a;l++)if(((i=n[l]).selected||l===s)&&!i.disabled&&(!i.parentNode.disabled||!E(i.parentNode,"optgroup"))){if(e=$(i).val(),o)return e;r.push(e)}return r},set:function(t,e){for(var i,n,s=t.options,o=$.makeArray(e),r=s.length;r--;)((n=s[r]).selected=-1<$.inArray($.valHooks.option.get(n),o))&&(i=!0);return i||(t.selectedIndex=-1),o}}}}),$.each(["radio","checkbox"],function(){$.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<$.inArray($(t).val(),e)}},y.checkOn||($.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),y.focusin="onfocusin"in C;function Se(t){t.stopPropagation()}var Le=/^(?:focusinfocus|focusoutblur)$/;$.extend($.event,{trigger:function(t,e,i,n){var s,o,r,a,l,c,h,d=[i||k],u=v.call(t,"type")?t.type:t,p=v.call(t,"namespace")?t.namespace.split("."):[],f=h=o=i=i||k;if(3!==i.nodeType&&8!==i.nodeType&&!Le.test(u+$.event.triggered)&&(-1<u.indexOf(".")&&(u=(p=u.split(".")).shift(),p.sort()),a=u.indexOf(":")<0&&"on"+u,(t=t[$.expando]?t:new $.Event(u,"object"==typeof t&&t)).isTrigger=n?2:3,t.namespace=p.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),e=null==e?[t]:$.makeArray(e,[t]),c=$.event.special[u]||{},n||!c.trigger||!1!==c.trigger.apply(i,e))){if(!n&&!c.noBubble&&!m(i)){for(r=c.delegateType||u,Le.test(r+u)||(f=f.parentNode);f;f=f.parentNode)d.push(f),o=f;o===(i.ownerDocument||k)&&d.push(o.defaultView||o.parentWindow||C)}for(s=0;(f=d[s++])&&!t.isPropagationStopped();)h=f,t.type=1<s?r:c.bindType||u,(l=(K.get(f,"events")||Object.create(null))[t.type]&&K.get(f,"handle"))&&l.apply(f,e),(l=a&&f[a])&&l.apply&&V(f)&&(t.result=l.apply(f,e),!1===t.result&&t.preventDefault());return t.type=u,n||t.isDefaultPrevented()||c._default&&!1!==c._default.apply(d.pop(),e)||!V(i)||a&&b(i[u])&&!m(i)&&((o=i[a])&&(i[a]=null),$.event.triggered=u,t.isPropagationStopped()&&h.addEventListener(u,Se),i[u](),t.isPropagationStopped()&&h.removeEventListener(u,Se),$.event.triggered=void 0,o&&(i[a]=o)),t.result}},simulate:function(t,e,i){var n=$.extend(new $.Event,i,{type:t,isSimulated:!0});$.event.trigger(n,null,e)}}),$.fn.extend({trigger:function(t,e){return this.each(function(){$.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return $.event.trigger(t,e,i,!0)}}),y.focusin||$.each({focus:"focusin",blur:"focusout"},function(i,n){function s(t){$.event.simulate(n,t.target,$.event.fix(t))}$.event.special[n]={setup:function(){var t=this.ownerDocument||this.document||this,e=K.access(t,n);e||t.addEventListener(i,s,!0),K.access(t,n,(e||0)+1)},teardown:function(){var t=this.ownerDocument||this.document||this,e=K.access(t,n)-1;e?K.access(t,n,e):(t.removeEventListener(i,s,!0),K.remove(t,n))}}});var Ie=C.location,Oe={guid:Date.now()},Ne=/\?/;$.parseXML=function(t){var e;if(!t||"string"!=typeof t)return null;try{e=(new C.DOMParser).parseFromString(t,"text/xml")}catch(t){e=void 0}return e&&!e.getElementsByTagName("parsererror").length||$.error("Invalid XML: "+t),e};var Pe=/\[\]$/,Me=/\r?\n/g,Be=/^(?:submit|button|image|reset|file)$/i,je=/^(?:input|select|textarea|keygen)/i;$.param=function(t,e){function i(t,e){var i=b(e)?e():e;s[s.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==i?"":i)}var n,s=[];if(null==t)return"";if(Array.isArray(t)||t.jquery&&!$.isPlainObject(t))$.each(t,function(){i(this.name,this.value)});else for(n in t)!function i(n,t,s,o){var e;if(Array.isArray(t))$.each(t,function(t,e){s||Pe.test(n)?o(n,e):i(n+"["+("object"==typeof e&&null!=e?t:"")+"]",e,s,o)});else if(s||"object"!==_(t))o(n,t);else for(e in t)i(n+"["+e+"]",t[e],s,o)}(n,t[n],e,i);return s.join("&")},$.fn.extend({serialize:function(){return $.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=$.prop(this,"elements");return t?$.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!$(this).is(":disabled")&&je.test(this.nodeName)&&!Be.test(t)&&(this.checked||!ut.test(t))}).map(function(t,e){var i=$(this).val();return null==i?null:Array.isArray(i)?$.map(i,function(t){return{name:e.name,value:t.replace(Me,"\r\n")}}):{name:e.name,value:i.replace(Me,"\r\n")}}).get()}});var Fe=/%20/g,He=/#.*$/,Ue=/([?&])_=[^&]*/,Re=/^(.*?):[ \t]*([^\r\n]*)$/gm,ze=/^(?:GET|HEAD)$/,We=/^\/\//,qe={},Ye={},Ve="*/".concat("*"),Ge=k.createElement("a");function Ke(o){return function(t,e){"string"!=typeof t&&(e=t,t="*");var i,n=0,s=t.toLowerCase().match(P)||[];if(b(e))for(;i=s[n++];)"+"===i[0]?(i=i.slice(1)||"*",(o[i]=o[i]||[]).unshift(e)):(o[i]=o[i]||[]).push(e)}}function Qe(e,s,o,r){var a={},l=e===Ye;function c(t){var n;return a[t]=!0,$.each(e[t]||[],function(t,e){var i=e(s,o,r);return"string"!=typeof i||l||a[i]?l?!(n=i):void 0:(s.dataTypes.unshift(i),c(i),!1)}),n}return c(s.dataTypes[0])||!a["*"]&&c("*")}function Xe(t,e){var i,n,s=$.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((s[i]?t:n=n||{})[i]=e[i]);return n&&$.extend(!0,t,n),t}Ge.href=Ie.href,$.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ie.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ie.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ve,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":$.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Xe(Xe(t,$.ajaxSettings),e):Xe($.ajaxSettings,t)},ajaxPrefilter:Ke(qe),ajaxTransport:Ke(Ye),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var h,d,u,i,p,n,f,m,s,o,g=$.ajaxSetup({},e),v=g.context||g,y=g.context&&(v.nodeType||v.jquery)?$(v):$.event,b=$.Deferred(),w=$.Callbacks("once memory"),_=g.statusCode||{},r={},a={},l="canceled",x={readyState:0,getResponseHeader:function(t){var e;if(f){if(!i)for(i={};e=Re.exec(u);)i[e[1].toLowerCase()+" "]=(i[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=i[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return f?u:null},setRequestHeader:function(t,e){return null==f&&(t=a[t.toLowerCase()]=a[t.toLowerCase()]||t,r[t]=e),this},overrideMimeType:function(t){return null==f&&(g.mimeType=t),this},statusCode:function(t){var e;if(t)if(f)x.always(t[x.status]);else for(e in t)_[e]=[_[e],t[e]];return this},abort:function(t){var e=t||l;return h&&h.abort(e),c(0,e),this}};if(b.promise(x),g.url=((t||g.url||Ie.href)+"").replace(We,Ie.protocol+"//"),g.type=e.method||e.type||g.method||g.type,g.dataTypes=(g.dataType||"*").toLowerCase().match(P)||[""],null==g.crossDomain){n=k.createElement("a");try{n.href=g.url,n.href,g.crossDomain=Ge.protocol+"//"+Ge.host!=n.protocol+"//"+n.host}catch(t){g.crossDomain=!0}}if(g.data&&g.processData&&"string"!=typeof g.data&&(g.data=$.param(g.data,g.traditional)),Qe(qe,g,e,x),f)return x;for(s in(m=$.event&&g.global)&&0==$.active++&&$.event.trigger("ajaxStart"),g.type=g.type.toUpperCase(),g.hasContent=!ze.test(g.type),d=g.url.replace(He,""),g.hasContent?g.data&&g.processData&&0===(g.contentType||"").indexOf("application/x-www-form-urlencoded")&&(g.data=g.data.replace(Fe,"+")):(o=g.url.slice(d.length),g.data&&(g.processData||"string"==typeof g.data)&&(d+=(Ne.test(d)?"&":"?")+g.data,delete g.data),!1===g.cache&&(d=d.replace(Ue,"$1"),o=(Ne.test(d)?"&":"?")+"_="+Oe.guid+++o),g.url=d+o),g.ifModified&&($.lastModified[d]&&x.setRequestHeader("If-Modified-Since",$.lastModified[d]),$.etag[d]&&x.setRequestHeader("If-None-Match",$.etag[d])),(g.data&&g.hasContent&&!1!==g.contentType||e.contentType)&&x.setRequestHeader("Content-Type",g.contentType),x.setRequestHeader("Accept",g.dataTypes[0]&&g.accepts[g.dataTypes[0]]?g.accepts[g.dataTypes[0]]+("*"!==g.dataTypes[0]?", "+Ve+"; q=0.01":""):g.accepts["*"]),g.headers)x.setRequestHeader(s,g.headers[s]);if(g.beforeSend&&(!1===g.beforeSend.call(v,x,g)||f))return x.abort();if(l="abort",w.add(g.complete),x.done(g.success),x.fail(g.error),h=Qe(Ye,g,e,x)){if(x.readyState=1,m&&y.trigger("ajaxSend",[x,g]),f)return x;g.async&&0<g.timeout&&(p=C.setTimeout(function(){x.abort("timeout")},g.timeout));try{f=!1,h.send(r,c)}catch(t){if(f)throw t;c(-1,t)}}else c(-1,"No Transport");function c(t,e,i,n){var s,o,r,a,l,c=e;f||(f=!0,p&&C.clearTimeout(p),h=void 0,u=n||"",x.readyState=0<t?4:0,s=200<=t&&t<300||304===t,i&&(a=function(t,e,i){for(var n,s,o,r,a=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(s in a)if(a[s]&&a[s].test(n)){l.unshift(s);break}if(l[0]in i)o=l[0];else{for(s in i){if(!l[0]||t.converters[s+" "+l[0]]){o=s;break}r=r||s}o=o||r}if(o)return o!==l[0]&&l.unshift(o),i[o]}(g,x,i)),!s&&-1<$.inArray("script",g.dataTypes)&&(g.converters["text script"]=function(){}),a=function(t,e,i,n){var s,o,r,a,l,c={},h=t.dataTypes.slice();if(h[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(o=h.shift();o;)if(t.responseFields[o]&&(i[t.responseFields[o]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=h.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(r=c[l+" "+o]||c["* "+o]))for(s in c)if((a=s.split(" "))[1]===o&&(r=c[l+" "+a[0]]||c["* "+a[0]])){!0===r?r=c[s]:!0!==c[s]&&(o=a[0],h.unshift(a[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(g,a,x,s),s?(g.ifModified&&((l=x.getResponseHeader("Last-Modified"))&&($.lastModified[d]=l),(l=x.getResponseHeader("etag"))&&($.etag[d]=l)),204===t||"HEAD"===g.type?c="nocontent":304===t?c="notmodified":(c=a.state,o=a.data,s=!(r=a.error))):(r=c,!t&&c||(c="error",t<0&&(t=0))),x.status=t,x.statusText=(e||c)+"",s?b.resolveWith(v,[o,c,x]):b.rejectWith(v,[x,c,r]),x.statusCode(_),_=void 0,m&&y.trigger(s?"ajaxSuccess":"ajaxError",[x,g,s?o:r]),w.fireWith(v,[x,c]),m&&(y.trigger("ajaxComplete",[x,g]),--$.active||$.event.trigger("ajaxStop")))}return x},getJSON:function(t,e,i){return $.get(t,e,i,"json")},getScript:function(t,e){return $.get(t,void 0,e,"script")}}),$.each(["get","post"],function(t,s){$[s]=function(t,e,i,n){return b(e)&&(n=n||i,i=e,e=void 0),$.ajax($.extend({url:t,type:s,dataType:n,data:e,success:i},$.isPlainObject(t)&&t))}}),$.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),$._evalUrl=function(t,e,i){return $.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){$.globalEval(t,e,i)}})},$.fn.extend({wrapAll:function(t){var e;return this[0]&&(b(t)&&(t=t.call(this[0])),e=$(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(i){return b(i)?this.each(function(t){$(this).wrapInner(i.call(this,t))}):this.each(function(){var t=$(this),e=t.contents();e.length?e.wrapAll(i):t.append(i)})},wrap:function(e){var i=b(e);return this.each(function(t){$(this).wrapAll(i?e.call(this,t):e)})},unwrap:function(t){return this.parent(t).not("body").each(function(){$(this).replaceWith(this.childNodes)}),this}}),$.expr.pseudos.hidden=function(t){return!$.expr.pseudos.visible(t)},$.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},$.ajaxSettings.xhr=function(){try{return new C.XMLHttpRequest}catch(t){}};var Je={0:200,1223:204},Ze=$.ajaxSettings.xhr();y.cors=!!Ze&&"withCredentials"in Ze,y.ajax=Ze=!!Ze,$.ajaxTransport(function(s){var o,r;if(y.cors||Ze&&!s.crossDomain)return{send:function(t,e){var i,n=s.xhr();if(n.open(s.type,s.url,s.async,s.username,s.password),s.xhrFields)for(i in s.xhrFields)n[i]=s.xhrFields[i];for(i in s.mimeType&&n.overrideMimeType&&n.overrideMimeType(s.mimeType),s.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t)n.setRequestHeader(i,t[i]);o=function(t){return function(){o&&(o=r=n.onload=n.onerror=n.onabort=n.ontimeout=n.onreadystatechange=null,"abort"===t?n.abort():"error"===t?"number"!=typeof n.status?e(0,"error"):e(n.status,n.statusText):e(Je[n.status]||n.status,n.statusText,"text"!==(n.responseType||"text")||"string"!=typeof n.responseText?{binary:n.response}:{text:n.responseText},n.getAllResponseHeaders()))}},n.onload=o(),r=n.onerror=n.ontimeout=o("error"),void 0!==n.onabort?n.onabort=r:n.onreadystatechange=function(){4===n.readyState&&C.setTimeout(function(){o&&r()})},o=o("abort");try{n.send(s.hasContent&&s.data||null)}catch(t){if(o)throw t}},abort:function(){o&&o()}}}),$.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),$.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return $.globalEval(t),t}}}),$.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),$.ajaxTransport("script",function(i){var n,s;if(i.crossDomain||i.scriptAttrs)return{send:function(t,e){n=$("<script>").attr(i.scriptAttrs||{}).prop({charset:i.scriptCharset,src:i.url}).on("load error",s=function(t){n.remove(),s=null,t&&e("error"===t.type?404:200,t.type)}),k.head.appendChild(n[0])},abort:function(){s&&s()}}});var ti,ei=[],ii=/(=)\?(?=&|$)|\?\?/;$.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=ei.pop()||$.expando+"_"+Oe.guid++;return this[t]=!0,t}}),$.ajaxPrefilter("json jsonp",function(t,e,i){var n,s,o,r=!1!==t.jsonp&&(ii.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&ii.test(t.data)&&"data");if(r||"jsonp"===t.dataTypes[0])return n=t.jsonpCallback=b(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,r?t[r]=t[r].replace(ii,"$1"+n):!1!==t.jsonp&&(t.url+=(Ne.test(t.url)?"&":"?")+t.jsonp+"="+n),t.converters["script json"]=function(){return o||$.error(n+" was not called"),o[0]},t.dataTypes[0]="json",s=C[n],C[n]=function(){o=arguments},i.always(function(){void 0===s?$(C).removeProp(n):C[n]=s,t[n]&&(t.jsonpCallback=e.jsonpCallback,ei.push(n)),o&&b(s)&&s(o[0]),o=s=void 0}),"script"}),y.createHTMLDocument=((ti=k.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===ti.childNodes.length),$.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(y.createHTMLDocument?((n=(e=k.implementation.createHTMLDocument("")).createElement("base")).href=k.location.href,e.head.appendChild(n)):e=k),o=!i&&[],(s=D.exec(t))?[e.createElement(s[1])]:(s=bt([t],e,o),o&&o.length&&$(o).remove(),$.merge([],s.childNodes)));var n,s,o},$.fn.load=function(t,e,i){var n,s,o,r=this,a=t.indexOf(" ");return-1<a&&(n=Te(t.slice(a)),t=t.slice(0,a)),b(e)?(i=e,e=void 0):e&&"object"==typeof e&&(s="POST"),0<r.length&&$.ajax({url:t,type:s||"GET",dataType:"html",data:e}).done(function(t){o=arguments,r.html(n?$("<div>").append($.parseHTML(t)).find(n):t)}).always(i&&function(t,e){r.each(function(){i.apply(this,o||[t.responseText,e,t])})}),this},$.expr.pseudos.animated=function(e){return $.grep($.timers,function(t){return e===t.elem}).length},$.offset={setOffset:function(t,e,i){var n,s,o,r,a,l,c=$.css(t,"position"),h=$(t),d={};"static"===c&&(t.style.position="relative"),a=h.offset(),o=$.css(t,"top"),l=$.css(t,"left"),s=("absolute"===c||"fixed"===c)&&-1<(o+l).indexOf("auto")?(r=(n=h.position()).top,n.left):(r=parseFloat(o)||0,parseFloat(l)||0),b(e)&&(e=e.call(t,i,$.extend({},a))),null!=e.top&&(d.top=e.top-a.top+r),null!=e.left&&(d.left=e.left-a.left+s),"using"in e?e.using.call(t,d):("number"==typeof d.top&&(d.top+="px"),"number"==typeof d.left&&(d.left+="px"),h.css(d))}},$.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){$.offset.setOffset(this,e,t)});var t,i,n=this[0];return n?n.getClientRects().length?(t=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:t.top+i.pageYOffset,left:t.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],s={top:0,left:0};if("fixed"===$.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===$.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((s=$(t).offset()).top+=$.css(t,"borderTopWidth",!0),s.left+=$.css(t,"borderLeftWidth",!0))}return{top:e.top-s.top-$.css(n,"marginTop",!0),left:e.left-s.left-$.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===$.css(t,"position");)t=t.offsetParent;return t||nt})}}),$.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,s){var o="pageYOffset"===s;$.fn[e]=function(t){return R(this,function(t,e,i){var n;return m(t)?n=t:9===t.nodeType&&(n=t.defaultView),void 0===i?n?n[s]:t[e]:void(n?n.scrollTo(o?n.pageXOffset:i,o?i:n.pageYOffset):t[e]=i)},e,t,arguments.length)}}),$.each(["top","left"],function(t,i){$.cssHooks[i]=Jt(y.pixelPosition,function(t,e){if(e)return e=Xt(t,i),Yt.test(e)?$(t).position()[i]+"px":e})}),$.each({Height:"height",Width:"width"},function(r,a){$.each({padding:"inner"+r,content:a,"":"outer"+r},function(n,o){$.fn[o]=function(t,e){var i=arguments.length&&(n||"boolean"!=typeof t),s=n||(!0===t||!0===e?"margin":"border");return R(this,function(t,e,i){var n;return m(t)?0===o.indexOf("outer")?t["inner"+r]:t.document.documentElement["client"+r]:9===t.nodeType?(n=t.documentElement,Math.max(t.body["scroll"+r],n["scroll"+r],t.body["offset"+r],n["offset"+r],n["client"+r])):void 0===i?$.css(t,e,s):$.style(t,e,i,s)},a,i?t:void 0,i)}})}),$.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){$.fn[e]=function(t){return this.on(e,t)}}),$.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),$.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,i){$.fn[i]=function(t,e){return 0<arguments.length?this.on(i,null,t,e):this.trigger(i)}});var ni=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;$.proxy=function(t,e){var i,n,s;if("string"==typeof e&&(i=t[e],e=t,t=i),b(t))return n=a.call(arguments,2),(s=function(){return t.apply(e||this,n.concat(a.call(arguments)))}).guid=t.guid=t.guid||$.guid++,s},$.holdReady=function(t){t?$.readyWait++:$.ready(!0)},$.isArray=Array.isArray,$.parseJSON=JSON.parse,$.nodeName=E,$.isFunction=b,$.isWindow=m,$.camelCase=Y,$.type=_,$.now=Date.now,$.isNumeric=function(t){var e=$.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},$.trim=function(t){return null==t?"":(t+"").replace(ni,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return $});var si=C.jQuery,oi=C.$;return $.noConflict=function(t){return C.$===$&&(C.$=oi),t&&C.jQuery===$&&(C.jQuery=si),$},void 0===t&&(C.jQuery=C.$=$),$}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Popper=e()}(this,function(){"use strict";var i="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,n=function(){for(var t=["Edge","Trident","Firefox"],e=0;e<t.length;e+=1)if(i&&0<=navigator.userAgent.indexOf(t[e]))return 1;return 0}();var o=i&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then(function(){e=!1,t()}))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},n))}};function r(t){return t&&"[object Function]"==={}.toString.call(t)}function w(t,e){if(1!==t.nodeType)return[];var i=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?i[e]:i}function f(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function m(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=w(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/(auto|scroll|overlay)/.test(i+s+n)?t:m(f(t))}function g(t){return t&&t.referenceNode?t.referenceNode:t}var e=i&&!(!window.MSInputMethodContext||!document.documentMode),s=i&&/MSIE 10/.test(navigator.userAgent);function v(t){return 11===t?e:10!==t&&e||s}function b(t){if(!t)return document.documentElement;for(var e=v(10)?document.body:null,i=t.offsetParent||null;i===e&&t.nextElementSibling;)i=(t=t.nextElementSibling).offsetParent;var n=i&&i.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TH","TD","TABLE"].indexOf(i.nodeName)&&"static"===w(i,"position")?b(i):i:t?t.ownerDocument.documentElement:document.documentElement}function h(t){return null!==t.parentNode?h(t.parentNode):t}function y(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var i=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,n=i?t:e,s=i?e:t,o=document.createRange();o.setStart(n,0),o.setEnd(s,0);var r,a,l=o.commonAncestorContainer;if(t!==l&&e!==l||n.contains(s))return"BODY"===(a=(r=l).nodeName)||"HTML"!==a&&b(r.firstElementChild)!==r?b(l):l;var c=h(t);return c.host?y(c.host,e):y(t,h(e).host)}function _(t,e){var i="top"===(1<arguments.length&&void 0!==e?e:"top")?"scrollTop":"scrollLeft",n=t.nodeName;if("BODY"!==n&&"HTML"!==n)return t[i];var s=t.ownerDocument.documentElement;return(t.ownerDocument.scrollingElement||s)[i]}function d(t,e){var i="x"===e?"Left":"Top",n="Left"==i?"Right":"Bottom";return parseFloat(t["border"+i+"Width"])+parseFloat(t["border"+n+"Width"])}function a(t,e,i,n){return Math.max(e["offset"+t],e["scroll"+t],i["client"+t],i["offset"+t],i["scroll"+t],v(10)?parseInt(i["offset"+t])+parseInt(n["margin"+("Height"===t?"Top":"Left")])+parseInt(n["margin"+("Height"===t?"Bottom":"Right")]):0)}function x(t){var e=t.body,i=t.documentElement,n=v(10)&&getComputedStyle(i);return{height:a("Height",e,i,n),width:a("Width",e,i,n)}}var l=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},t=function(t,e,i){return e&&c(t.prototype,e),i&&c(t,i),t};function c(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function C(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var k=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t};function $(t){return k({},t,{right:t.left+t.width,bottom:t.top+t.height})}function T(t){var e,i,n={};try{v(10)?(n=t.getBoundingClientRect(),e=_(t,"top"),i=_(t,"left"),n.top+=e,n.left+=i,n.bottom+=e,n.right+=i):n=t.getBoundingClientRect()}catch(t){}var s,o={left:n.left,top:n.top,width:n.right-n.left,height:n.bottom-n.top},r="HTML"===t.nodeName?x(t.ownerDocument):{},a=r.width||t.clientWidth||o.width,l=r.height||t.clientHeight||o.height,c=t.offsetWidth-a,h=t.offsetHeight-l;return(c||h)&&(c-=d(s=w(t),"x"),h-=d(s,"y"),o.width-=c,o.height-=h),$(o)}function E(t,e,i){var n=2<arguments.length&&void 0!==i&&i,s=v(10),o="HTML"===e.nodeName,r=T(t),a=T(e),l=m(t),c=w(e),h=parseFloat(c.borderTopWidth),d=parseFloat(c.borderLeftWidth);n&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var u,p,f=$({top:r.top-a.top-h,left:r.left-a.left-d,width:r.width,height:r.height});return f.marginTop=0,f.marginLeft=0,!s&&o&&(u=parseFloat(c.marginTop),p=parseFloat(c.marginLeft),f.top-=h-u,f.bottom-=h-u,f.left-=d-p,f.right-=d-p,f.marginTop=u,f.marginLeft=p),(s&&!n?e.contains(l):e===l&&"BODY"!==l.nodeName)&&(f=function(t,e,i){var n=2<arguments.length&&void 0!==i&&i,s=_(e,"top"),o=_(e,"left"),r=n?-1:1;return t.top+=s*r,t.bottom+=s*r,t.left+=o*r,t.right+=o*r,t}(f,e)),f}function D(t){if(!t||!t.parentElement||v())return document.documentElement;for(var e=t.parentElement;e&&"none"===w(e,"transform");)e=e.parentElement;return e||document.documentElement}function p(t,e,i,n,s){var o,r,a,l,c,h=4<arguments.length&&void 0!==s&&s,d={top:0,left:0},u=h?D(t):y(t,g(e));"viewport"===n?d=function(t,e){var i=1<arguments.length&&void 0!==e&&e,n=t.ownerDocument.documentElement,s=E(t,n),o=Math.max(n.clientWidth,window.innerWidth||0),r=Math.max(n.clientHeight,window.innerHeight||0),a=i?0:_(n),l=i?0:_(n,"left");return $({top:a-s.top+s.marginTop,left:l-s.left+s.marginLeft,width:o,height:r})}(u,h):(o=void 0,"scrollParent"===n?"BODY"===(o=m(f(e))).nodeName&&(o=t.ownerDocument.documentElement):o="window"===n?t.ownerDocument.documentElement:n,r=E(o,u,h),"HTML"!==o.nodeName||function t(e){var i=e.nodeName;if("BODY"===i||"HTML"===i)return!1;if("fixed"===w(e,"position"))return!0;var n=f(e);return!!n&&t(n)}(u)?d=r:(l=(a=x(t.ownerDocument)).height,c=a.width,d.top+=r.top-r.marginTop,d.bottom=l+r.top,d.left+=r.left-r.marginLeft,d.right=c+r.left));var p="number"==typeof(i=i||0);return d.left+=p?i:i.left||0,d.top+=p?i:i.top||0,d.right-=p?i:i.right||0,d.bottom-=p?i:i.bottom||0,d}function u(t,e,n,i,s,o){var r=5<arguments.length&&void 0!==o?o:0;if(-1===t.indexOf("auto"))return t;var a=p(n,i,r,s),l={top:{width:a.width,height:e.top-a.top},right:{width:a.right-e.right,height:a.height},bottom:{width:a.width,height:a.bottom-e.bottom},left:{width:e.left-a.left,height:a.height}},c=Object.keys(l).map(function(t){return k({key:t},l[t],{area:(e=l[t]).width*e.height});var e}).sort(function(t,e){return e.area-t.area}),h=c.filter(function(t){var e=t.width,i=t.height;return e>=n.clientWidth&&i>=n.clientHeight}),d=0<h.length?h[0].key:c[0].key,u=t.split("-")[1];return d+(u?"-"+u:"")}function A(t,e,i,n){var s=3<arguments.length&&void 0!==n?n:null;return E(i,s?D(e):y(e,g(i)),s)}function S(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),i=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),n=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+n,height:t.offsetHeight+i}}function L(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function I(t,e,i){i=i.split("-")[0];var n=S(t),s={width:n.width,height:n.height},o=-1!==["right","left"].indexOf(i),r=o?"top":"left",a=o?"left":"top",l=o?"height":"width",c=o?"width":"height";return s[r]=e[r]+e[l]/2-n[l]/2,s[a]=i===a?e[a]-n[c]:e[L(a)],s}function O(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function N(t,i,e){return(void 0===e?t:t.slice(0,function(t,e,i){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===i});var n=O(t,function(t){return t[e]===i});return t.indexOf(n)}(t,"name",e))).forEach(function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var e=t.function||t.fn;t.enabled&&r(e)&&(i.offsets.popper=$(i.offsets.popper),i.offsets.reference=$(i.offsets.reference),i=e(i,t))}),i}function P(t,i){return t.some(function(t){var e=t.name;return t.enabled&&e===i})}function M(t){for(var e=[!1,"ms","Webkit","Moz","O"],i=t.charAt(0).toUpperCase()+t.slice(1),n=0;n<e.length;n++){var s=e[n],o=s?""+s+i:t;if(void 0!==document.body.style[o])return o}return null}function B(t){var e=t.ownerDocument;return e?e.defaultView:window}function j(t,e,i,n){i.updateBound=n,B(t).addEventListener("resize",i.updateBound,{passive:!0});var s=m(t);return function t(e,i,n,s){var o="BODY"===e.nodeName,r=o?e.ownerDocument.defaultView:e;r.addEventListener(i,n,{passive:!0}),o||t(m(r.parentNode),i,n,s),s.push(r)}(s,"scroll",i.updateBound,i.scrollParents),i.scrollElement=s,i.eventsEnabled=!0,i}function F(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,B(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function H(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function U(i,n){Object.keys(n).forEach(function(t){var e="";-1!==["width","height","top","right","bottom","left"].indexOf(t)&&H(n[t])&&(e="px"),i.style[t]=n[t]+e})}function R(t,e){function i(t){return t}var n=t.offsets,s=n.popper,o=n.reference,r=Math.round,a=Math.floor,l=r(o.width),c=r(s.width),h=-1!==["left","right"].indexOf(t.placement),d=-1!==t.placement.indexOf("-"),u=e?h||d||l%2==c%2?r:a:i,p=e?r:i;return{left:u(l%2==1&&c%2==1&&!d&&e?s.left-1:s.left),top:p(s.top),bottom:p(s.bottom),right:u(s.right)}}var z=i&&/Firefox/i.test(navigator.userAgent);function W(t,e,i){var n,s,o=O(t,function(t){return t.name===e}),r=!!o&&t.some(function(t){return t.name===i&&t.enabled&&t.order<o.order});return r||(n="`"+e+"`",s="`"+i+"`",console.warn(s+" modifier is required by "+n+" modifier in order to work, be sure to include it before "+n+"!")),r}var q=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Y=q.slice(3);function V(t,e){var i=1<arguments.length&&void 0!==e&&e,n=Y.indexOf(t),s=Y.slice(n+1).concat(Y.slice(0,n));return i?s.reverse():s}var G="flip",K="clockwise",Q="counterclockwise";function X(t,s,o,e){var r=[0,0],a=-1!==["right","left"].indexOf(e),i=t.split(/(\+|\-)/).map(function(t){return t.trim()}),n=i.indexOf(O(i,function(t){return-1!==t.search(/,|\s/)}));i[n]&&-1===i[n].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/;return(-1!==n?[i.slice(0,n).concat([i[n].split(l)[0]]),[i[n].split(l)[1]].concat(i.slice(n+1))]:[i]).map(function(t,e){var i=(1===e?!a:a)?"height":"width",n=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,n=!0,t):n?(t[t.length-1]+=e,n=!1,t):t.concat(e)},[]).map(function(t){return function(t,e,i,n){var s=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+s[1],r=s[2];if(!o)return t;if(0!==r.indexOf("%"))return"vh"!==r&&"vw"!==r?o:("vh"===r?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o;var a=void 0;switch(r){case"%p":a=i;break;case"%":case"%r":default:a=n}return $(a)[e]/100*o}(t,i,s,o)})}).forEach(function(i,n){i.forEach(function(t,e){H(t)&&(r[n]+=t*("-"===i[e-1]?-1:1))})}),r}var J={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e,i,n,s,o,r,a,l=t.placement,c=l.split("-")[0],h=l.split("-")[1];return h&&(i=(e=t.offsets).reference,n=e.popper,r=(s=-1!==["bottom","top"].indexOf(c))?"width":"height",a={start:C({},o=s?"left":"top",i[o]),end:C({},o,i[o]+i[r]-n[r])},t.offsets.popper=k({},n,a[h])),t}},offset:{order:200,enabled:!0,fn:function(t,e){var i=e.offset,n=t.placement,s=t.offsets,o=s.popper,r=s.reference,a=n.split("-")[0],l=void 0,l=H(+i)?[+i,0]:X(i,o,r,a);return"left"===a?(o.top+=l[0],o.left-=l[1]):"right"===a?(o.top+=l[0],o.left+=l[1]):"top"===a?(o.left+=l[0],o.top-=l[1]):"bottom"===a&&(o.left+=l[0],o.top+=l[1]),t.popper=o,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,n){var e=n.boundariesElement||b(t.instance.popper);t.instance.reference===e&&(e=b(e));var i=M("transform"),s=t.instance.popper.style,o=s.top,r=s.left,a=s[i];s.top="",s.left="",s[i]="";var l=p(t.instance.popper,t.instance.reference,n.padding,e,t.positionFixed);s.top=o,s.left=r,s[i]=a,n.boundaries=l;var c=n.priority,h=t.offsets.popper,d={primary:function(t){var e=h[t];return h[t]<l[t]&&!n.escapeWithReference&&(e=Math.max(h[t],l[t])),C({},t,e)},secondary:function(t){var e="right"===t?"left":"top",i=h[e];return h[t]>l[t]&&!n.escapeWithReference&&(i=Math.min(h[e],l[t]-("right"===t?h.width:h.height))),C({},e,i)}};return c.forEach(function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";h=k({},h,d[e](t))}),t.offsets.popper=h,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,i=e.popper,n=e.reference,s=t.placement.split("-")[0],o=Math.floor,r=-1!==["top","bottom"].indexOf(s),a=r?"right":"bottom",l=r?"left":"top",c=r?"width":"height";return i[a]<o(n[l])&&(t.offsets.popper[l]=o(n[l])-i[c]),i[l]>o(n[a])&&(t.offsets.popper[l]=o(n[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){var i;if(!W(t.instance.modifiers,"arrow","keepTogether"))return t;var n=e.element;if("string"==typeof n){if(!(n=t.instance.popper.querySelector(n)))return t}else if(!t.instance.popper.contains(n))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var s=t.placement.split("-")[0],o=t.offsets,r=o.popper,a=o.reference,l=-1!==["left","right"].indexOf(s),c=l?"height":"width",h=l?"Top":"Left",d=h.toLowerCase(),u=l?"left":"top",p=l?"bottom":"right",f=S(n)[c];a[p]-f<r[d]&&(t.offsets.popper[d]-=r[d]-(a[p]-f)),a[d]+f>r[p]&&(t.offsets.popper[d]+=a[d]+f-r[p]),t.offsets.popper=$(t.offsets.popper);var m=a[d]+a[c]/2-f/2,g=w(t.instance.popper),v=parseFloat(g["margin"+h]),y=parseFloat(g["border"+h+"Width"]),b=m-t.offsets.popper[d]-v-y,b=Math.max(Math.min(r[c]-f,b),0);return t.arrowElement=n,t.offsets.arrow=(C(i={},d,Math.round(b)),C(i,u,""),i),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(g,v){if(P(g.instance.modifiers,"inner"))return g;if(g.flipped&&g.placement===g.originalPlacement)return g;var y=p(g.instance.popper,g.instance.reference,v.padding,v.boundariesElement,g.positionFixed),b=g.placement.split("-")[0],w=L(b),_=g.placement.split("-")[1]||"",x=[];switch(v.behavior){case G:x=[b,w];break;case K:x=V(b);break;case Q:x=V(b,!0);break;default:x=v.behavior}return x.forEach(function(t,e){if(b!==t||x.length===e+1)return g;b=g.placement.split("-")[0],w=L(b);var i,n=g.offsets.popper,s=g.offsets.reference,o=Math.floor,r="left"===b&&o(n.right)>o(s.left)||"right"===b&&o(n.left)<o(s.right)||"top"===b&&o(n.bottom)>o(s.top)||"bottom"===b&&o(n.top)<o(s.bottom),a=o(n.left)<o(y.left),l=o(n.right)>o(y.right),c=o(n.top)<o(y.top),h=o(n.bottom)>o(y.bottom),d="left"===b&&a||"right"===b&&l||"top"===b&&c||"bottom"===b&&h,u=-1!==["top","bottom"].indexOf(b),p=!!v.flipVariations&&(u&&"start"===_&&a||u&&"end"===_&&l||!u&&"start"===_&&c||!u&&"end"===_&&h),f=!!v.flipVariationsByContent&&(u&&"start"===_&&l||u&&"end"===_&&a||!u&&"start"===_&&h||!u&&"end"===_&&c),m=p||f;(r||d||m)&&(g.flipped=!0,(r||d)&&(b=x[e+1]),m&&(_="end"===(i=_)?"start":"start"===i?"end":i),g.placement=b+(_?"-"+_:""),g.offsets.popper=k({},g.offsets.popper,I(g.instance.popper,g.offsets.reference,g.placement)),g=N(g.instance.modifiers,g,"flip"))}),g},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,i=e.split("-")[0],n=t.offsets,s=n.popper,o=n.reference,r=-1!==["left","right"].indexOf(i),a=-1===["top","left"].indexOf(i);return s[r?"left":"top"]=o[i]-(a?s[r?"width":"height"]:0),t.placement=L(e),t.offsets.popper=$(s),t}},hide:{order:800,enabled:!0,fn:function(t){if(!W(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,i=O(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<i.top||e.left>i.right||e.top>i.bottom||e.right<i.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var i=e.x,n=e.y,s=t.offsets.popper,o=O(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var r,a,l=void 0!==o?o:e.gpuAcceleration,c=b(t.instance.popper),h=T(c),d={position:s.position},u=R(t,window.devicePixelRatio<2||!z),p="bottom"===i?"top":"bottom",f="right"===n?"left":"right",m=M("transform"),g=void 0,v=void 0,v="bottom"==p?"HTML"===c.nodeName?-c.clientHeight+u.bottom:-h.height+u.bottom:u.top,g="right"==f?"HTML"===c.nodeName?-c.clientWidth+u.right:-h.width+u.right:u.left;l&&m?(d[m]="translate3d("+g+"px, "+v+"px, 0)",d[p]=0,d[f]=0,d.willChange="transform"):(r="bottom"==p?-1:1,a="right"==f?-1:1,d[p]=v*r,d[f]=g*a,d.willChange=p+", "+f);var y={"x-placement":t.placement};return t.attributes=k({},y,t.attributes),t.styles=k({},d,t.styles),t.arrowStyles=k({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,i;return U(t.instance.popper,t.styles),e=t.instance.popper,i=t.attributes,Object.keys(i).forEach(function(t){!1!==i[t]?e.setAttribute(t,i[t]):e.removeAttribute(t)}),t.arrowElement&&Object.keys(t.arrowStyles).length&&U(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,i,n,s){var o=A(s,e,t,i.positionFixed),r=u(i.placement,o,e,t,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding);return e.setAttribute("x-placement",r),U(e,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}}},Z=(t(tt,[{key:"update",value:function(){return function(){var t;this.state.isDestroyed||((t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=A(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=u(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=I(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=N(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,P(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[M("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=j(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return F.call(this)}}]),tt);function tt(t,e){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};l(this,tt),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=o(this.update.bind(this)),this.options=k({},tt.Defaults,n),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=e&&e.jquery?e[0]:e,this.options.modifiers={},Object.keys(k({},tt.Defaults.modifiers,n.modifiers)).forEach(function(t){i.options.modifiers[t]=k({},tt.Defaults.modifiers[t]||{},n.modifiers?n.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return k({name:t},i.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(t){t.enabled&&r(t.onLoad)&&t.onLoad(i.reference,i.popper,i.options,t,i.state)}),this.update();var s=this.options.eventsEnabled;s&&this.enableEventListeners(),this.state.eventsEnabled=s}return Z.Utils=("undefined"!=typeof window?window:global).PopperUtils,Z.placements=q,Z.Defaults=J,Z}),function(l,i,s,a){function c(t,e){this.settings=null,this.options=l.extend({},c.Defaults,e),this.$element=l(t),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},l.each(["onResize","onThrottledResize"],l.proxy(function(t,e){this._handlers[e]=l.proxy(this[e],this)},this)),l.each(c.Plugins,l.proxy(function(t,e){this._plugins[t.charAt(0).toLowerCase()+t.slice(1)]=new e(this)},this)),l.each(c.Workers,l.proxy(function(t,e){this._pipe.push({filter:e.filter,run:l.proxy(e.run,this)})},this)),this.setup(),this.initialize()}c.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:i,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},c.Width={Default:"default",Inner:"inner",Outer:"outer"},c.Type={Event:"event",State:"state"},c.Plugins={},c.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(t){t.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(t){var e=this.settings.margin||"",i=!this.settings.autoWidth,n=this.settings.rtl,s={width:"auto","margin-left":n?e:"","margin-right":n?"":e};i||this.$stage.children().css(s),t.css=s}},{filter:["width","items","settings"],run:function(t){var e=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,i=null,n=this._items.length,s=!this.settings.autoWidth,o=[];for(t.items={merge:!1,width:e};n--;)i=this._mergers[n],i=this.settings.mergeFit&&Math.min(i,this.settings.items)||i,t.items.merge=1<i||t.items.merge,o[n]=s?e*i:this._items[n].width();this._widths=o}},{filter:["items","settings"],run:function(){var t=[],e=this._items,i=this.settings,n=Math.max(2*i.items,4),s=2*Math.ceil(e.length/2),o=i.loop&&e.length?i.rewind?n:Math.max(n,s):0,r="",a="";for(o/=2;0<o;)t.push(this.normalize(t.length/2,!0)),r+=e[t[t.length-1]][0].outerHTML,t.push(this.normalize(e.length-1-(t.length-1)/2,!0)),a=e[t[t.length-1]][0].outerHTML+a,--o;this._clones=t,l(r).addClass("cloned").appendTo(this.$stage),l(a).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var t=this.settings.rtl?1:-1,e=this._clones.length+this._items.length,i=-1,n=0,s=0,o=[];++i<e;)n=o[i-1]||0,s=this._widths[this.relative(i)]+this.settings.margin,o.push(n+s*t);this._coordinates=o}},{filter:["width","items","settings"],run:function(){var t=this.settings.stagePadding,e=this._coordinates,i={width:Math.ceil(Math.abs(e[e.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.$stage.css(i)}},{filter:["width","items","settings"],run:function(t){var e=this._coordinates.length,i=!this.settings.autoWidth,n=this.$stage.children();if(i&&t.items.merge)for(;e--;)t.css.width=this._widths[this.relative(e)],n.eq(e).css(t.css);else i&&(t.css.width=t.items.width,n.css(t.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(t){t.current=t.current?this.$stage.children().index(t.current):0,t.current=Math.max(this.minimum(),Math.min(this.maximum(),t.current)),this.reset(t.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){for(var t,e,i=this.settings.rtl?1:-1,n=2*this.settings.stagePadding,s=this.coordinates(this.current())+n,o=s+this.width()*i,r=[],a=0,l=this._coordinates.length;a<l;a++)t=this._coordinates[a-1]||0,e=Math.abs(this._coordinates[a])+n*i,(this.op(t,"<=",s)&&this.op(t,">",o)||this.op(e,"<",s)&&this.op(e,">",o))&&r.push(a);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+r.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],c.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=l("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(l("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},c.prototype.initializeItems=function(){var t=this.$element.find(".owl-item");if(t.length)return this._items=t.get().map(function(t){return l(t)}),this._mergers=this._items.map(function(){return 1}),void this.refresh();this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass)},c.prototype.initialize=function(){var t,e,i;this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")&&(t=this.$element.find("img"),e=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:a,i=this.$element.children(e).width(),t.length&&i<=0&&this.preloadAutoWidthImages(t)),this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},c.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},c.prototype.setup=function(){var e=this.viewport(),t=this.options.responsive,i=-1,n=null;t?(l.each(t,function(t){t<=e&&i<t&&(i=Number(t))}),"function"==typeof(n=l.extend({},this.options,t[i])).stagePadding&&(n.stagePadding=n.stagePadding()),delete n.responsive,n.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):n=l.extend({},this.options),this.trigger("change",{property:{name:"settings",value:n}}),this._breakpoint=i,this.settings=n,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},c.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},c.prototype.prepare=function(t){var e=this.trigger("prepare",{content:t});return e.data||(e.data=l("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(t)),this.trigger("prepared",{content:e.data}),e.data},c.prototype.update=function(){for(var t=0,e=this._pipe.length,i=l.proxy(function(t){return this[t]},this._invalidated),n={};t<e;)(this._invalidated.all||0<l.grep(this._pipe[t].filter,i).length)&&this._pipe[t].run(n),t++;this._invalidated={},this.is("valid")||this.enter("valid")},c.prototype.width=function(t){switch(t=t||c.Width.Default){case c.Width.Inner:case c.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},c.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},c.prototype.onThrottledResize=function(){i.clearTimeout(this.resizeTimer),this.resizeTimer=i.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},c.prototype.onResize=function(){return!!this._items.length&&(this._width!==this.$element.width()&&(!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))))},c.prototype.registerEventHandlers=function(){l.support.transition&&this.$stage.on(l.support.transition.end+".owl.core",l.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(i,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1})),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",l.proxy(this.onDragEnd,this)))},c.prototype.onDragStart=function(t){var e=null;3!==t.which&&(e=l.support.transform?{x:(e=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","))[16===e.length?12:4],y:e[16===e.length?13:5]}:(e=this.$stage.position(),{x:this.settings.rtl?e.left+this.$stage.width()-this.width()+this.settings.margin:e.left,y:e.top}),this.is("animating")&&(l.support.transform?this.animate(e.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===t.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=l(t.target),this._drag.stage.start=e,this._drag.stage.current=e,this._drag.pointer=this.pointer(t),l(s).on("mouseup.owl.core touchend.owl.core",l.proxy(this.onDragEnd,this)),l(s).one("mousemove.owl.core touchmove.owl.core",l.proxy(function(t){var e=this.difference(this._drag.pointer,this.pointer(t));l(s).on("mousemove.owl.core touchmove.owl.core",l.proxy(this.onDragMove,this)),Math.abs(e.x)<Math.abs(e.y)&&this.is("valid")||(t.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))},c.prototype.onDragMove=function(t){var e=null,i=null,n=null,s=this.difference(this._drag.pointer,this.pointer(t)),o=this.difference(this._drag.stage.start,s);this.is("dragging")&&(t.preventDefault(),this.settings.loop?(e=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-e,o.x=((o.x-e)%i+i)%i+e):(e=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),n=this.settings.pullDrag?-1*s.x/5:0,o.x=Math.max(Math.min(o.x,e+n),i+n)),this._drag.stage.current=o,this.animate(o.x))},c.prototype.onDragEnd=function(t){var e=this.difference(this._drag.pointer,this.pointer(t)),i=this._drag.stage.current,n=0<e.x^this.settings.rtl?"left":"right";l(s).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==e.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(i.x,0!==e.x?n:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=n,(3<Math.abs(e.x)||300<(new Date).getTime()-this._drag.time)&&this._drag.target.one("click.owl.core",function(){return!1})),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},c.prototype.closest=function(i,n){var s=-1,o=this.width(),r=this.coordinates();return this.settings.freeDrag||l.each(r,l.proxy(function(t,e){return"left"===n&&e-30<i&&i<e+30?s=t:"right"===n&&e-o-30<i&&i<e-o+30?s=t+1:this.op(i,"<",e)&&this.op(i,">",r[t+1]!==a?r[t+1]:e-o)&&(s="left"===n?t+1:t),-1===s},this)),this.settings.loop||(this.op(i,">",r[this.minimum()])?s=i=this.minimum():this.op(i,"<",r[this.maximum()])&&(s=i=this.maximum())),s},c.prototype.animate=function(t){var e=0<this.speed();this.is("animating")&&this.onTransitionEnd(),e&&(this.enter("animating"),this.trigger("translate")),l.support.transform3d&&l.support.transition?this.$stage.css({transform:"translate3d("+t+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):e?this.$stage.animate({left:t+"px"},this.speed(),this.settings.fallbackEasing,l.proxy(this.onTransitionEnd,this)):this.$stage.css({left:t+"px"})},c.prototype.is=function(t){return this._states.current[t]&&0<this._states.current[t]},c.prototype.current=function(t){return t===a?this._current:0===this._items.length?a:(t=this.normalize(t),this._current!==t&&((e=this.trigger("change",{property:{name:"position",value:t}})).data!==a&&(t=this.normalize(e.data)),this._current=t,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}})),this._current);var e},c.prototype.invalidate=function(t){return"string"===l.type(t)&&(this._invalidated[t]=!0,this.is("valid")&&this.leave("valid")),l.map(this._invalidated,function(t,e){return e})},c.prototype.reset=function(t){(t=this.normalize(t))!==a&&(this._speed=0,this._current=t,this.suppress(["translate","translated"]),this.animate(this.coordinates(t)),this.release(["translate","translated"]))},c.prototype.normalize=function(t,e){var i=this._items.length,n=e?0:this._clones.length;return!this.isNumeric(t)||i<1?t=a:(t<0||i+n<=t)&&(t=((t-n/2)%i+i)%i+n/2),t},c.prototype.relative=function(t){return t-=this._clones.length/2,this.normalize(t,!0)},c.prototype.maximum=function(t){var e,i,n,s=this.settings,o=this._coordinates.length;if(s.loop)o=this._clones.length/2+this._items.length-1;else if(s.autoWidth||s.merge){if(e=this._items.length)for(i=this._items[--e].width(),n=this.$element.width();e--&&!(n<(i+=this._items[e].width()+this.settings.margin)););o=e+1}else o=s.center?this._items.length-1:this._items.length-s.items;return t&&(o-=this._clones.length/2),Math.max(o,0)},c.prototype.minimum=function(t){return t?0:this._clones.length/2},c.prototype.items=function(t){return t===a?this._items.slice():(t=this.normalize(t,!0),this._items[t])},c.prototype.mergers=function(t){return t===a?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])},c.prototype.clones=function(i){function n(t){return t%2==0?s+t/2:e-(t+1)/2}var e=this._clones.length/2,s=e+this._items.length;return i===a?l.map(this._clones,function(t,e){return n(e)}):l.map(this._clones,function(t,e){return t===i?n(e):null})},c.prototype.speed=function(t){return t!==a&&(this._speed=t),this._speed},c.prototype.coordinates=function(t){var e,i=1,n=t-1;return t===a?l.map(this._coordinates,l.proxy(function(t,e){return this.coordinates(e)},this)):(this.settings.center?(this.settings.rtl&&(i=-1,n=t+1),e=this._coordinates[t],e+=(this.width()-e+(this._coordinates[n]||0))/2*i):e=this._coordinates[n]||0,e=Math.ceil(e))},c.prototype.duration=function(t,e,i){return 0===i?0:Math.min(Math.max(Math.abs(e-t),1),6)*Math.abs(i||this.settings.smartSpeed)},c.prototype.to=function(t,e){var i=this.current(),n=null,s=t-this.relative(i),o=(0<s)-(s<0),r=this._items.length,a=this.minimum(),l=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(s)>r/2&&(s+=-1*o*r),(n=(((t=i+s)-a)%r+r)%r+a)!==t&&n-s<=l&&0<n-s&&(i=n-s,t=n,this.reset(i))):t=this.settings.rewind?(t%(l+=1)+l)%l:Math.max(a,Math.min(l,t)),this.speed(this.duration(i,t,e)),this.current(t),this.isVisible()&&this.update()},c.prototype.next=function(t){t=t||!1,this.to(this.relative(this.current())+1,t)},c.prototype.prev=function(t){t=t||!1,this.to(this.relative(this.current())-1,t)},c.prototype.onTransitionEnd=function(t){if(t!==a&&(t.stopPropagation(),(t.target||t.srcElement||t.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},c.prototype.viewport=function(){var t;return this.options.responsiveBaseElement!==i?t=l(this.options.responsiveBaseElement).width():i.innerWidth?t=i.innerWidth:s.documentElement&&s.documentElement.clientWidth?t=s.documentElement.clientWidth:console.warn("Can not detect viewport width."),t},c.prototype.replace=function(t){this.$stage.empty(),this._items=[],t=t&&(t instanceof jQuery?t:l(t)),this.settings.nestedItemSelector&&(t=t.find("."+this.settings.nestedItemSelector)),t.filter(function(){return 1===this.nodeType}).each(l.proxy(function(t,e){e=this.prepare(e),this.$stage.append(e),this._items.push(e),this._mergers.push(+e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},c.prototype.add=function(t,e){var i=this.relative(this._current);e=e===a?this._items.length:this.normalize(e,!0),t=t instanceof jQuery?t:l(t),this.trigger("add",{content:t,position:e}),t=this.prepare(t),0===this._items.length||e===this._items.length?(0===this._items.length&&this.$stage.append(t),0!==this._items.length&&this._items[e-1].after(t),this._items.push(t),this._mergers.push(+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[e].before(t),this._items.splice(e,0,t),this._mergers.splice(e,0,+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[i]&&this.reset(this._items[i].index()),this.invalidate("items"),this.trigger("added",{content:t,position:e})},c.prototype.remove=function(t){(t=this.normalize(t,!0))!==a&&(this.trigger("remove",{content:this._items[t],position:t}),this._items[t].remove(),this._items.splice(t,1),this._mergers.splice(t,1),this.invalidate("items"),this.trigger("removed",{content:null,position:t}))},c.prototype.preloadAutoWidthImages=function(t){t.each(l.proxy(function(t,e){this.enter("pre-loading"),e=l(e),l(new Image).one("load",l.proxy(function(t){e.attr("src",t.target.src),e.css("opacity",1),this.leave("pre-loading"),this.is("pre-loading")||this.is("initializing")||this.refresh()},this)).attr("src",e.attr("src")||e.attr("data-src")||e.attr("data-src-retina"))},this))},c.prototype.destroy=function(){for(var t in this.$element.off(".owl.core"),this.$stage.off(".owl.core"),l(s).off(".owl.core"),!1!==this.settings.responsive&&(i.clearTimeout(this.resizeTimer),this.off(i,"resize",this._handlers.onThrottledResize)),this._plugins)this._plugins[t].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},c.prototype.op=function(t,e,i){var n=this.settings.rtl;switch(e){case"<":return n?i<t:t<i;case">":return n?t<i:i<t;case">=":return n?t<=i:i<=t;case"<=":return n?i<=t:t<=i}},c.prototype.on=function(t,e,i,n){t.addEventListener?t.addEventListener(e,i,n):t.attachEvent&&t.attachEvent("on"+e,i)},c.prototype.off=function(t,e,i,n){t.removeEventListener?t.removeEventListener(e,i,n):t.detachEvent&&t.detachEvent("on"+e,i)},c.prototype.trigger=function(t,e,i,n,s){var o={item:{count:this._items.length,index:this.current()}},r=l.camelCase(l.grep(["on",t,i],function(t){return t}).join("-").toLowerCase()),a=l.Event([t,"owl",i||"carousel"].join(".").toLowerCase(),l.extend({relatedTarget:this},o,e));return this._supress[t]||(l.each(this._plugins,function(t,e){e.onTrigger&&e.onTrigger(a)}),this.register({type:c.Type.Event,name:t}),this.$element.trigger(a),this.settings&&"function"==typeof this.settings[r]&&this.settings[r].call(this,a)),a},c.prototype.enter=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]===a&&(this._states.current[e]=0),this._states.current[e]++},this))},c.prototype.leave=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]--},this))},c.prototype.register=function(i){var e;i.type===c.Type.Event?(l.event.special[i.name]||(l.event.special[i.name]={}),l.event.special[i.name].owl||(e=l.event.special[i.name]._default,l.event.special[i.name]._default=function(t){return!e||!e.apply||t.namespace&&-1!==t.namespace.indexOf("owl")?t.namespace&&-1<t.namespace.indexOf("owl"):e.apply(this,arguments)},l.event.special[i.name].owl=!0)):i.type===c.Type.State&&(this._states.tags[i.name]?this._states.tags[i.name]=this._states.tags[i.name].concat(i.tags):this._states.tags[i.name]=i.tags,this._states.tags[i.name]=l.grep(this._states.tags[i.name],l.proxy(function(t,e){return l.inArray(t,this._states.tags[i.name])===e},this)))},c.prototype.suppress=function(t){l.each(t,l.proxy(function(t,e){this._supress[e]=!0},this))},c.prototype.release=function(t){l.each(t,l.proxy(function(t,e){delete this._supress[e]},this))},c.prototype.pointer=function(t){var e={x:null,y:null};return(t=(t=t.originalEvent||t||i.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(e.x=t.pageX,e.y=t.pageY):(e.x=t.clientX,e.y=t.clientY),e},c.prototype.isNumeric=function(t){return!isNaN(parseFloat(t))},c.prototype.difference=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},l.fn.owlCarousel=function(e){var n=Array.prototype.slice.call(arguments,1);return this.each(function(){var t=l(this),i=t.data("owl.carousel");i||(i=new c(this,"object"==typeof e&&e),t.data("owl.carousel",i),l.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(t,e){i.register({type:c.Type.Event,name:e}),i.$element.on(e+".owl.carousel.core",l.proxy(function(t){t.namespace&&t.relatedTarget!==this&&(this.suppress([e]),i[e].apply(this,[].slice.call(arguments,1)),this.release([e]))},i))})),"string"==typeof e&&"_"!==e.charAt(0)&&i[e].apply(i,n)})},l.fn.owlCarousel.Constructor=c}(window.Zepto||window.jQuery,window,document),function(e,i){function n(t){this._core=t,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":e.proxy(function(t){t.namespace&&this._core.settings.autoRefresh&&this.watch()},this)},this._core.options=e.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers)}n.Defaults={autoRefresh:!0,autoRefreshInterval:500},n.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=i.setInterval(e.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},n.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible&&this._core.invalidate("width")&&this._core.refresh())},n.prototype.destroy=function(){var t,e;for(t in i.clearInterval(this._interval),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},e.fn.owlCarousel.Constructor.Plugins.AutoRefresh=n}(window.Zepto||window.jQuery,window,document),function(a,o){function e(t){this._core=t,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":a.proxy(function(t){if(t.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(t.property&&"position"==t.property.name||"initialized"==t.type)){var e=this._core.settings,i=e.center&&Math.ceil(e.items/2)||e.items,n=e.center&&-1*i||0,s=(t.property&&void 0!==t.property.value?t.property.value:this._core.current())+n,o=this._core.clones().length,r=a.proxy(function(t,e){this.load(e)},this);for(0<e.lazyLoadEager&&(i+=e.lazyLoadEager,e.loop&&(s-=e.lazyLoadEager,i++));n++<i;)this.load(o/2+this._core.relative(s)),o&&a.each(this._core.clones(this._core.relative(s)),r),s++}},this)},this._core.options=a.extend({},e.Defaults,this._core.options),this._core.$element.on(this._handlers)}e.Defaults={lazyLoad:!1,lazyLoadEager:0},e.prototype.load=function(t){var e=this._core.$stage.children().eq(t),i=e&&e.find(".owl-lazy");!i||-1<a.inArray(e.get(0),this._loaded)||(i.each(a.proxy(function(t,e){var i,n=a(e),s=1<o.devicePixelRatio&&n.attr("data-src-retina")||n.attr("data-src")||n.attr("data-srcset");this._core.trigger("load",{element:n,url:s},"lazy"),n.is("img")?n.one("load.owl.lazy",a.proxy(function(){n.css("opacity",1),this._core.trigger("loaded",{element:n,url:s},"lazy")},this)).attr("src",s):n.is("source")?n.one("load.owl.lazy",a.proxy(function(){this._core.trigger("loaded",{element:n,url:s},"lazy")},this)).attr("srcset",s):((i=new Image).onload=a.proxy(function(){n.css({"background-image":'url("'+s+'")',opacity:"1"}),this._core.trigger("loaded",{element:n,url:s},"lazy")},this),i.src=s)},this)),this._loaded.push(e.get(0)))},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this._core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},a.fn.owlCarousel.Constructor.Plugins.Lazy=e}(window.Zepto||window.jQuery,window,document),function(r,i){function n(t){this._core=t,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":r.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":r.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&"position"===t.property.name&&this.update()},this),"loaded.owl.lazy":r.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&t.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)},this._core.options=r.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var e=this;r(i).on("load",function(){e._core.settings.autoHeight&&e.update()}),r(i).resize(function(){e._core.settings.autoHeight&&(null!=e._intervalId&&clearTimeout(e._intervalId),e._intervalId=setTimeout(function(){e.update()},250))})}n.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},n.prototype.update=function(){var t=this._core._current,e=t+this._core.settings.items,i=this._core.settings.lazyLoad,n=this._core.$stage.children().toArray().slice(t,e),s=[],o=0;r.each(n,function(t,e){s.push(r(e).height())}),(o=Math.max.apply(null,s))<=1&&i&&this._previousHeight&&(o=this._previousHeight),this._previousHeight=o,this._core.$stage.parent().height(o).addClass(this._core.settings.autoHeightClass)},n.prototype.destroy=function(){var t,e;for(t in this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},r.fn.owlCarousel.Constructor.Plugins.AutoHeight=n}(window.Zepto||window.jQuery,window,document),function(h,e){function i(t){this._core=t,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":h.proxy(function(t){t.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":h.proxy(function(t){t.namespace&&this._core.settings.video&&this.isInFullScreen()&&t.preventDefault()},this),"refreshed.owl.carousel":h.proxy(function(t){t.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":h.proxy(function(t){t.namespace&&"position"===t.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":h.proxy(function(t){var e;!t.namespace||(e=h(t.content).find(".owl-video")).length&&(e.css("display","none"),this.fetch(e,h(t.content)))},this)},this._core.options=h.extend({},i.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",h.proxy(function(t){this.play(t)},this))}i.Defaults={video:!1,videoHeight:!1,videoWidth:!1},i.prototype.fetch=function(t,e){var i=t.attr("data-vimeo-id")?"vimeo":t.attr("data-vzaar-id")?"vzaar":"youtube",n=t.attr("data-vimeo-id")||t.attr("data-youtube-id")||t.attr("data-vzaar-id"),s=t.attr("data-width")||this._core.settings.videoWidth,o=t.attr("data-height")||this._core.settings.videoHeight,r=t.attr("href");if(!r)throw new Error("Missing video URL.");if(-1<(n=r.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/))[3].indexOf("youtu"))i="youtube";else if(-1<n[3].indexOf("vimeo"))i="vimeo";else{if(!(-1<n[3].indexOf("vzaar")))throw new Error("Video URL not supported.");i="vzaar"}n=n[6],this._videos[r]={type:i,id:n,width:s,height:o},e.attr("data-video",r),this.thumbnail(t,this._videos[r])},i.prototype.thumbnail=function(e,t){function i(t){n=c.lazyLoad?h("<div/>",{class:"owl-video-tn "+l,srcType:t}):h("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+t+")"}),e.after(n),e.after('<div class="owl-video-play-icon"></div>')}var n,s,o=t.width&&t.height?"width:"+t.width+"px;height:"+t.height+"px;":"",r=e.find("img"),a="src",l="",c=this._core.settings;if(e.wrap(h("<div/>",{class:"owl-video-wrapper",style:o})),this._core.settings.lazyLoad&&(a="data-src",l="owl-lazy"),r.length)return i(r.attr(a)),r.remove(),!1;"youtube"===t.type?(s="//img.youtube.com/vi/"+t.id+"/hqdefault.jpg",i(s)):"vimeo"===t.type?h.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){s=t[0].thumbnail_large,i(s)}}):"vzaar"===t.type&&h.ajax({type:"GET",url:"//vzaar.com/api/videos/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){s=t.framegrab_url,i(s)}})},i.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},i.prototype.play=function(t){var e,i=h(t.target).closest("."+this._core.settings.itemClass),n=this._videos[i.attr("data-video")],s=n.width||"100%",o=n.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),i=this._core.items(this._core.relative(i.index())),this._core.reset(i.index()),(e=h('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>')).attr("height",o),e.attr("width",s),"youtube"===n.type?e.attr("src","//www.youtube.com/embed/"+n.id+"?autoplay=1&rel=0&v="+n.id):"vimeo"===n.type?e.attr("src","//player.vimeo.com/video/"+n.id+"?autoplay=1"):"vzaar"===n.type&&e.attr("src","//view.vzaar.com/"+n.id+"/player?autoplay=true"),h(e).wrap('<div class="owl-video-frame" />').insertAfter(i.find(".owl-video")),this._playing=i.addClass("owl-video-playing"))},i.prototype.isInFullScreen=function(){var t=e.fullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement;return t&&h(t).parent().hasClass("owl-video-frame")},i.prototype.destroy=function(){var t,e;for(t in this._core.$element.off("click.owl.video"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},h.fn.owlCarousel.Constructor.Plugins.Video=i}(window.Zepto||window.jQuery,(window,document)),function(r){function e(t){this.core=t,this.core.options=r.extend({},e.Defaults,this.core.options),this.swapping=!0,this.previous=void 0,this.next=void 0,this.handlers={"change.owl.carousel":r.proxy(function(t){t.namespace&&"position"==t.property.name&&(this.previous=this.core.current(),this.next=t.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":r.proxy(function(t){t.namespace&&(this.swapping="translated"==t.type)},this),"translate.owl.carousel":r.proxy(function(t){t.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)},this.core.$element.on(this.handlers)}e.Defaults={animateOut:!1,animateIn:!1},e.prototype.swap=function(){var t,e,i,n,s,o;1===this.core.settings.items&&r.support.animation&&r.support.transition&&(this.core.speed(0),e=r.proxy(this.clear,this),i=this.core.$stage.children().eq(this.previous),n=this.core.$stage.children().eq(this.next),s=this.core.settings.animateIn,o=this.core.settings.animateOut,this.core.current()!==this.previous&&(o&&(t=this.core.coordinates(this.previous)-this.core.coordinates(this.next),i.one(r.support.animation.end,e).css({left:t+"px"}).addClass("animated owl-animated-out").addClass(o)),s&&n.one(r.support.animation.end,e).addClass("animated owl-animated-in").addClass(s)))},e.prototype.clear=function(t){r(t.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this.core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},r.fn.owlCarousel.Constructor.Plugins.Animate=e}(window.Zepto||window.jQuery,(window,document)),function(n,s,e){function i(t){this._core=t,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":n.proxy(function(t){t.namespace&&"settings"===t.property.name?this._core.settings.autoplay?this.play():this.stop():t.namespace&&"position"===t.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":n.proxy(function(t){t.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":n.proxy(function(t,e,i){t.namespace&&this.play(e,i)},this),"stop.owl.autoplay":n.proxy(function(t){t.namespace&&this.stop()},this),"mouseover.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)},this._core.$element.on(this._handlers),this._core.options=n.extend({},i.Defaults,this._core.options)}i.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},i.prototype._next=function(t){this._call=s.setTimeout(n.proxy(this._next,this,t),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||e.hidden||this._core.next(t||this._core.settings.autoplaySpeed)},i.prototype.read=function(){return(new Date).getTime()-this._time},i.prototype.play=function(t,e){var i;this._core.is("rotating")||this._core.enter("rotating"),t=t||this._core.settings.autoplayTimeout,i=Math.min(this._time%(this._timeout||t),t),this._paused?(this._time=this.read(),this._paused=!1):s.clearTimeout(this._call),this._time+=this.read()%t-i,this._timeout=t,this._call=s.setTimeout(n.proxy(this._next,this,e),t-i)},i.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,s.clearTimeout(this._call),this._core.leave("rotating"))},i.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,s.clearTimeout(this._call))},i.prototype.destroy=function(){var t,e;for(t in this.stop(),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.autoplay=i}(window.Zepto||window.jQuery,window,document),function(o){"use strict";function e(t){this._core=t,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+o(t.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")},this),"added.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,0,this._templates.pop())},this),"remove.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,1)},this),"changed.owl.carousel":o.proxy(function(t){t.namespace&&"position"==t.property.name&&this.draw()},this),"initialized.owl.carousel":o.proxy(function(t){t.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":o.proxy(function(t){t.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)},this._core.options=o.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers)}e.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},e.prototype.initialize=function(){var t,i=this._core.settings;for(t in this._controls.$relative=(i.navContainer?o(i.navContainer):o("<div>").addClass(i.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=o("<"+i.navElement+">").addClass(i.navClass[0]).html(i.navText[0]).prependTo(this._controls.$relative).on("click",o.proxy(function(t){this.prev(i.navSpeed)},this)),this._controls.$next=o("<"+i.navElement+">").addClass(i.navClass[1]).html(i.navText[1]).appendTo(this._controls.$relative).on("click",o.proxy(function(t){this.next(i.navSpeed)},this)),i.dotsData||(this._templates=[o('<button role="button">').addClass(i.dotClass).append(o("<span>")).prop("outerHTML")]),this._controls.$absolute=(i.dotsContainer?o(i.dotsContainer):o("<div>").addClass(i.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",o.proxy(function(t){var e=o(t.target).parent().is(this._controls.$absolute)?o(t.target).index():o(t.target).parent().index();t.preventDefault(),this.to(e,i.dotsSpeed)},this)),this._overrides)this._core[t]=o.proxy(this[t],this)},e.prototype.destroy=function(){var t,e,i,n,s=this._core.settings;for(t in this._handlers)this.$element.off(t,this._handlers[t]);for(e in this._controls)"$relative"===e&&s.navContainer?this._controls[e].html(""):this._controls[e].remove();for(n in this.overides)this._core[n]=this._overrides[n];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},e.prototype.update=function(){var t,e,i=this._core.clones().length/2,n=i+this._core.items().length,s=this._core.maximum(!0),o=this._core.settings,r=o.center||o.autoWidth||o.dotsData?1:o.dotsEach||o.items;if("page"!==o.slideBy&&(o.slideBy=Math.min(o.slideBy,o.items)),o.dots||"page"==o.slideBy)for(this._pages=[],t=i,e=0;t<n;t++){if(r<=e||0===e){if(this._pages.push({start:Math.min(s,t-i),end:t-i+r-1}),Math.min(s,t-i)===s)break;e=0,0}e+=this._core.mergers(this._core.relative(t))}},e.prototype.draw=function(){var t,e=this._core.settings,i=this._core.items().length<=e.items,n=this._core.relative(this._core.current()),s=e.loop||e.rewind;this._controls.$relative.toggleClass("disabled",!e.nav||i),e.nav&&(this._controls.$previous.toggleClass("disabled",!s&&n<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!s&&n>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!e.dots||i),e.dots&&(t=this._pages.length-this._controls.$absolute.children().length,e.dotsData&&0!=t?this._controls.$absolute.html(this._templates.join("")):0<t?this._controls.$absolute.append(new Array(1+t).join(this._templates[0])):t<0&&this._controls.$absolute.children().slice(t).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(o.inArray(this.current(),this._pages)).addClass("active"))},e.prototype.onTrigger=function(t){var e=this._core.settings;t.page={index:o.inArray(this.current(),this._pages),count:this._pages.length,size:e&&(e.center||e.autoWidth||e.dotsData?1:e.dotsEach||e.items)}},e.prototype.current=function(){var i=this._core.relative(this._core.current());return o.grep(this._pages,o.proxy(function(t,e){return t.start<=i&&t.end>=i},this)).pop()},e.prototype.getPosition=function(t){var e,i,n=this._core.settings;return"page"==n.slideBy?(e=o.inArray(this.current(),this._pages),i=this._pages.length,t?++e:--e,e=this._pages[(e%i+i)%i].start):(e=this._core.relative(this._core.current()),i=this._core.items().length,t?e+=n.slideBy:e-=n.slideBy),e},e.prototype.next=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!0),t)},e.prototype.prev=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!1),t)},e.prototype.to=function(t,e,i){var n;!i&&this._pages.length?(n=this._pages.length,o.proxy(this._overrides.to,this._core)(this._pages[(t%n+n)%n].start,e)):o.proxy(this._overrides.to,this._core)(t,e)},o.fn.owlCarousel.Constructor.Plugins.Navigation=e}(window.Zepto||window.jQuery,(window,document)),function(n,s){"use strict";function e(t){this._core=t,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":n.proxy(function(t){t.namespace&&"URLHash"===this._core.settings.startPosition&&n(s).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":n.proxy(function(t){if(t.namespace){var e=n(t.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");if(!e)return;this._hashes[e]=t.content}},this),"changed.owl.carousel":n.proxy(function(t){if(t.namespace&&"position"===t.property.name){var i=this._core.items(this._core.relative(this._core.current())),e=n.map(this._hashes,function(t,e){return t===i?e:null}).join();if(!e||s.location.hash.slice(1)===e)return;s.location.hash=e}},this)},this._core.options=n.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers),n(s).on("hashchange.owl.navigation",n.proxy(function(t){var e=s.location.hash.substring(1),i=this._core.$stage.children(),n=this._hashes[e]&&i.index(this._hashes[e]);void 0!==n&&n!==this._core.current()&&this._core.to(this._core.relative(n),!1,!0)},this))}e.Defaults={URLhashListener:!1},e.prototype.destroy=function(){var t,e;for(t in n(s).off("hashchange.owl.navigation"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.Hash=e}(window.Zepto||window.jQuery,window,document),function(s,o){var r=s("<support>").get(0).style,a="Webkit Moz O ms".split(" "),t={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},e=function(){return!!l("transform")},i=function(){return!!l("perspective")},n=function(){return!!l("animation")};function l(t,i){var n=!1,e=t.charAt(0).toUpperCase()+t.slice(1);return s.each((t+" "+a.join(e+" ")+e).split(" "),function(t,e){if(r[e]!==o)return n=!i||e,!1}),n}function c(t){return l(t,!0)}!function(){return!!l("transition")}()||(s.support.transition=new String(c("transition")),s.support.transition.end=t.transition.end[s.support.transition]),n()&&(s.support.animation=new String(c("animation")),s.support.animation.end=t.animation.end[s.support.animation]),e()&&(s.support.transform=new String(c("transform")),s.support.transform3d=i())}(window.Zepto||window.jQuery,(window,void document)),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e((t=t||self).bootstrap={},t.jQuery,t.Popper)}(this,function(t,f,d){"use strict";function n(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function r(t,e,i){return e&&n(t.prototype,e),i&&n(t,i),t}function e(e,t){var i,n=Object.keys(e);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(e),t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)),n}function l(s){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?e(Object(o),!0).forEach(function(t){var e,i,n;e=s,n=o[i=t],i in e?Object.defineProperty(e,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach(function(t){Object.defineProperty(s,t,Object.getOwnPropertyDescriptor(o,t))})}return s}f=f&&Object.prototype.hasOwnProperty.call(f,"default")?f.default:f,d=d&&Object.prototype.hasOwnProperty.call(d,"default")?d.default:d;var i="transitionend";function s(t){var e=this,i=!1;return f(this).one(m.TRANSITION_END,function(){i=!0}),setTimeout(function(){i||m.triggerTransitionEnd(e)},t),this}var m={TRANSITION_END:"bsTransitionEnd",getUID:function(t){for(;t+=~~(1e6*Math.random()),document.getElementById(t););return t},getSelectorFromElement:function(t){var e,i=t.getAttribute("data-target");i&&"#"!==i||(i=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(i)?i:null}catch(t){return null}},getTransitionDurationFromElement:function(t){if(!t)return 0;var e=f(t).css("transition-duration"),i=f(t).css("transition-delay"),n=parseFloat(e),s=parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(parseFloat(e)+parseFloat(i))):0},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(t){f(t).trigger(i)},supportsTransitionEnd:function(){return Boolean(i)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,i){for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n)){var s=i[n],o=e[n],r=o&&m.isElement(o)?"element":null==(a=o)?""+a:{}.toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(r))throw new Error(t.toUpperCase()+': Option "'+n+'" provided type "'+r+'" but expected type "'+s+'".')}var a},findShadowRoot:function(t){if(!document.documentElement.attachShadow)return null;if("function"!=typeof t.getRootNode)return t instanceof ShadowRoot?t:t.parentNode?m.findShadowRoot(t.parentNode):null;var e=t.getRootNode();return e instanceof ShadowRoot?e:null},jQueryDetection:function(){if(void 0===f)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var t=f.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1===t[0]&&9===t[1]&&t[2]<1||4<=t[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};m.jQueryDetection(),f.fn.emulateTransitionEnd=s,f.event.special[m.TRANSITION_END]={bindType:i,delegateType:i,handle:function(t){if(f(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}};var o="alert",a="bs.alert",c=f.fn[o],h=function(){function n(t){this._element=t}var t=n.prototype;return t.close=function(t){var e=this._element;t&&(e=this._getRootElement(t)),this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},t.dispose=function(){f.removeData(this._element,a),this._element=null},t._getRootElement=function(t){var e=m.getSelectorFromElement(t),i=!1;return e&&(i=document.querySelector(e)),i=i||f(t).closest(".alert")[0]},t._triggerCloseEvent=function(t){var e=f.Event("close.bs.alert");return f(t).trigger(e),e},t._removeElement=function(e){var t,i=this;f(e).removeClass("show"),f(e).hasClass("fade")?(t=m.getTransitionDurationFromElement(e),f(e).one(m.TRANSITION_END,function(t){return i._destroyElement(e,t)}).emulateTransitionEnd(t)):this._destroyElement(e)},t._destroyElement=function(t){f(t).detach().trigger("closed.bs.alert").remove()},n._jQueryInterface=function(i){return this.each(function(){var t=f(this),e=t.data(a);e||(e=new n(this),t.data(a,e)),"close"===i&&e[i](this)})},n._handleDismiss=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},r(n,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),n}();f(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',h._handleDismiss(new h)),f.fn[o]=h._jQueryInterface,f.fn[o].Constructor=h,f.fn[o].noConflict=function(){return f.fn[o]=c,h._jQueryInterface};var u="button",p="bs.button",g=f.fn[u],v="active",y='[data-toggle^="button"]',b='input:not([type="hidden"])',w=function(){function i(t){this._element=t}var t=i.prototype;return t.toggle=function(){var t,e,i=!0,n=!0,s=f(this._element).closest('[data-toggle="buttons"]')[0];!s||(t=this._element.querySelector(b))&&("radio"===t.type&&(t.checked&&this._element.classList.contains(v)?i=!1:(e=s.querySelector(".active"))&&f(e).removeClass(v)),i&&("checkbox"!==t.type&&"radio"!==t.type||(t.checked=!this._element.classList.contains(v)),f(t).trigger("change")),t.focus(),n=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(v)),i&&f(this._element).toggleClass(v))},t.dispose=function(){f.removeData(this._element,p),this._element=null},i._jQueryInterface=function(e){return this.each(function(){var t=f(this).data(p);t||(t=new i(this),f(this).data(p,t)),"toggle"===e&&t[e]()})},r(i,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),i}();f(document).on("click.bs.button.data-api",y,function(t){var e=t.target,i=e;if(f(e).hasClass("btn")||(e=f(e).closest(".btn")[0]),!e||e.hasAttribute("disabled")||e.classList.contains("disabled"))t.preventDefault();else{var n=e.querySelector(b);if(n&&(n.hasAttribute("disabled")||n.classList.contains("disabled")))return void t.preventDefault();"LABEL"===i.tagName&&n&&"checkbox"===n.type&&t.preventDefault(),w._jQueryInterface.call(f(e),"toggle")}}).on("focus.bs.button.data-api blur.bs.button.data-api",y,function(t){var e=f(t.target).closest(".btn")[0];f(e).toggleClass("focus",/^focus(in)?$/.test(t.type))}),f(window).on("load.bs.button.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),e=0,i=t.length;e<i;e++){var n=t[e],s=n.querySelector(b);s.checked||s.hasAttribute("checked")?n.classList.add(v):n.classList.remove(v)}for(var o=0,r=(t=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<r;o++){var a=t[o];"true"===a.getAttribute("aria-pressed")?a.classList.add(v):a.classList.remove(v)}}),f.fn[u]=w._jQueryInterface,f.fn[u].Constructor=w,f.fn[u].noConflict=function(){return f.fn[u]=g,w._jQueryInterface};var _="carousel",x="bs.carousel",C="."+x,k=f.fn[_],$={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},T={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},E="next",D="prev",A="slid"+C,S="active",L=".active.carousel-item",I={TOUCH:"touch",PEN:"pen"},O=function(){function o(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var t=o.prototype;return t.next=function(){this._isSliding||this._slide(E)},t.nextWhenVisible=function(){!document.hidden&&f(this._element).is(":visible")&&"hidden"!==f(this._element).css("visibility")&&this.next()},t.prev=function(){this._isSliding||this._slide(D)},t.pause=function(t){t||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(m.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},t.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},t.to=function(t){var e=this;this._activeElement=this._element.querySelector(L);var i=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)f(this._element).one(A,function(){return e.to(t)});else{if(i===t)return this.pause(),void this.cycle();var n=i<t?E:D;this._slide(n,this._items[t])}},t.dispose=function(){f(this._element).off(C),f.removeData(this._element,x),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},t._getConfig=function(t){return t=l(l({},$),t),m.typeCheckConfig(_,t,T),t},t._handleSwipe=function(){var t,e=Math.abs(this.touchDeltaX);e<=40||(t=e/this.touchDeltaX,(this.touchDeltaX=0)<t&&this.prev(),t<0&&this.next())},t._addEventListeners=function(){var e=this;this._config.keyboard&&f(this._element).on("keydown.bs.carousel",function(t){return e._keydown(t)}),"hover"===this._config.pause&&f(this._element).on("mouseenter.bs.carousel",function(t){return e.pause(t)}).on("mouseleave.bs.carousel",function(t){return e.cycle(t)}),this._config.touch&&this._addTouchEventListeners()},t._addTouchEventListeners=function(){var t,e,i=this;this._touchSupported&&(t=function(t){i._pointerEvent&&I[t.originalEvent.pointerType.toUpperCase()]?i.touchStartX=t.originalEvent.clientX:i._pointerEvent||(i.touchStartX=t.originalEvent.touches[0].clientX)},e=function(t){i._pointerEvent&&I[t.originalEvent.pointerType.toUpperCase()]&&(i.touchDeltaX=t.originalEvent.clientX-i.touchStartX),i._handleSwipe(),"hover"===i._config.pause&&(i.pause(),i.touchTimeout&&clearTimeout(i.touchTimeout),i.touchTimeout=setTimeout(function(t){return i.cycle(t)},500+i._config.interval))},f(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(t){return t.preventDefault()}),this._pointerEvent?(f(this._element).on("pointerdown.bs.carousel",t),f(this._element).on("pointerup.bs.carousel",e),this._element.classList.add("pointer-event")):(f(this._element).on("touchstart.bs.carousel",t),f(this._element).on("touchmove.bs.carousel",function(t){var e;(e=t).originalEvent.touches&&1<e.originalEvent.touches.length?i.touchDeltaX=0:i.touchDeltaX=e.originalEvent.touches[0].clientX-i.touchStartX}),f(this._element).on("touchend.bs.carousel",e)))},t._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},t._getItemIndex=function(t){return this._items=t&&t.parentNode?[].slice.call(t.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(t)},t._getItemByDirection=function(t,e){var i=t===E,n=t===D,s=this._getItemIndex(e),o=this._items.length-1;if((n&&0===s||i&&s===o)&&!this._config.wrap)return e;var r=(s+(t===D?-1:1))%this._items.length;return-1==r?this._items[this._items.length-1]:this._items[r]},t._triggerSlideEvent=function(t,e){var i=this._getItemIndex(t),n=this._getItemIndex(this._element.querySelector(L)),s=f.Event("slide.bs.carousel",{relatedTarget:t,direction:e,from:n,to:i});return f(this._element).trigger(s),s},t._setActiveIndicatorElement=function(t){var e,i;this._indicatorsElement&&(e=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),f(e).removeClass(S),(i=this._indicatorsElement.children[this._getItemIndex(t)])&&f(i).addClass(S))},t._slide=function(t,e){var i,n,s,o,r,a=this,l=this._element.querySelector(L),c=this._getItemIndex(l),h=e||l&&this._getItemByDirection(t,l),d=this._getItemIndex(h),u=Boolean(this._interval),p=t===E?(i="carousel-item-left",n="carousel-item-next","left"):(i="carousel-item-right",n="carousel-item-prev","right");h&&f(h).hasClass(S)?this._isSliding=!1:this._triggerSlideEvent(h,p).isDefaultPrevented()||l&&h&&(this._isSliding=!0,u&&this.pause(),this._setActiveIndicatorElement(h),s=f.Event(A,{relatedTarget:h,direction:p,from:c,to:d}),f(this._element).hasClass("slide")?(f(h).addClass(n),m.reflow(h),f(l).addClass(i),f(h).addClass(i),(o=parseInt(h.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=o):this._config.interval=this._config.defaultInterval||this._config.interval,r=m.getTransitionDurationFromElement(l),f(l).one(m.TRANSITION_END,function(){f(h).removeClass(i+" "+n).addClass(S),f(l).removeClass(S+" "+n+" "+i),a._isSliding=!1,setTimeout(function(){return f(a._element).trigger(s)},0)}).emulateTransitionEnd(r)):(f(l).removeClass(S),f(h).addClass(S),this._isSliding=!1,f(this._element).trigger(s)),u&&this.cycle())},o._jQueryInterface=function(n){return this.each(function(){var t=f(this).data(x),e=l(l({},$),f(this).data());"object"==typeof n&&(e=l(l({},e),n));var i="string"==typeof n?n:e.slide;if(t||(t=new o(this,e),f(this).data(x,t)),"number"==typeof n)t.to(n);else if("string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}else e.interval&&e.ride&&(t.pause(),t.cycle())})},o._dataApiClickHandler=function(t){var e,i,n,s=m.getSelectorFromElement(this);!s||(e=f(s)[0])&&f(e).hasClass("carousel")&&(i=l(l({},f(e).data()),f(this).data()),(n=this.getAttribute("data-slide-to"))&&(i.interval=!1),o._jQueryInterface.call(f(e),i),n&&f(e).data(x).to(n),t.preventDefault())},r(o,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return $}}]),o}();f(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",O._dataApiClickHandler),f(window).on("load.bs.carousel.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),e=0,i=t.length;e<i;e++){var n=f(t[e]);O._jQueryInterface.call(n,n.data())}}),f.fn[_]=O._jQueryInterface,f.fn[_].Constructor=O,f.fn[_].noConflict=function(){return f.fn[_]=k,O._jQueryInterface};var N="collapse",P="bs.collapse",M=f.fn[N],B={toggle:!0,parent:""},j={toggle:"boolean",parent:"(string|element)"},F="show",H="collapse",U="collapsing",R="collapsed",z='[data-toggle="collapse"]',W=function(){function a(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var i=[].slice.call(document.querySelectorAll(z)),n=0,s=i.length;n<s;n++){var o=i[n],r=m.getSelectorFromElement(o),a=[].slice.call(document.querySelectorAll(r)).filter(function(t){return t===e});null!==r&&0<a.length&&(this._selector=r,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var t=a.prototype;return t.toggle=function(){f(this._element).hasClass(F)?this.hide():this.show()},t.show=function(){var t,e,i,n,s,o,r=this;this._isTransitioning||f(this._element).hasClass(F)||(this._parent&&0===(t=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(t){return"string"==typeof r._config.parent?t.getAttribute("data-parent")===r._config.parent:t.classList.contains(H)})).length&&(t=null),t&&(e=f(t).not(this._selector).data(P))&&e._isTransitioning||(i=f.Event("show.bs.collapse"),f(this._element).trigger(i),i.isDefaultPrevented()||(t&&(a._jQueryInterface.call(f(t).not(this._selector),"hide"),e||f(t).data(P,null)),n=this._getDimension(),f(this._element).removeClass(H).addClass(U),this._element.style[n]=0,this._triggerArray.length&&f(this._triggerArray).removeClass(R).attr("aria-expanded",!0),this.setTransitioning(!0),s="scroll"+(n[0].toUpperCase()+n.slice(1)),o=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,function(){f(r._element).removeClass(U).addClass(H+" "+F),r._element.style[n]="",r.setTransitioning(!1),f(r._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(o),this._element.style[n]=this._element[s]+"px")))},t.hide=function(){var t=this;if(!this._isTransitioning&&f(this._element).hasClass(F)){var e=f.Event("hide.bs.collapse");if(f(this._element).trigger(e),!e.isDefaultPrevented()){var i=this._getDimension();this._element.style[i]=this._element.getBoundingClientRect()[i]+"px",m.reflow(this._element),f(this._element).addClass(U).removeClass(H+" "+F);var n=this._triggerArray.length;if(0<n)for(var s=0;s<n;s++){var o=this._triggerArray[s],r=m.getSelectorFromElement(o);null!==r&&(f([].slice.call(document.querySelectorAll(r))).hasClass(F)||f(o).addClass(R).attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[i]="";var a=m.getTransitionDurationFromElement(this._element);f(this._element).one(m.TRANSITION_END,function(){t.setTransitioning(!1),f(t._element).removeClass(U).addClass(H).trigger("hidden.bs.collapse")}).emulateTransitionEnd(a)}}},t.setTransitioning=function(t){this._isTransitioning=t},t.dispose=function(){f.removeData(this._element,P),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},t._getConfig=function(t){return(t=l(l({},B),t)).toggle=Boolean(t.toggle),m.typeCheckConfig(N,t,j),t},t._getDimension=function(){return f(this._element).hasClass("width")?"width":"height"},t._getParent=function(){var t,i=this;m.isElement(this._config.parent)?(t=this._config.parent,void 0!==this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);var e='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',n=[].slice.call(t.querySelectorAll(e));return f(n).each(function(t,e){i._addAriaAndCollapsedClass(a._getTargetFromElement(e),[e])}),t},t._addAriaAndCollapsedClass=function(t,e){var i=f(t).hasClass(F);e.length&&f(e).toggleClass(R,!i).attr("aria-expanded",i)},a._getTargetFromElement=function(t){var e=m.getSelectorFromElement(t);return e?document.querySelector(e):null},a._jQueryInterface=function(n){return this.each(function(){var t=f(this),e=t.data(P),i=l(l(l({},B),t.data()),"object"==typeof n&&n?n:{});if(!e&&i.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(i.toggle=!1),e||(e=new a(this,i),t.data(P,e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},r(a,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return B}}]),a}();f(document).on("click.bs.collapse.data-api",z,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();var i=f(this),e=m.getSelectorFromElement(this),n=[].slice.call(document.querySelectorAll(e));f(n).each(function(){var t=f(this),e=t.data(P)?"toggle":i.data();W._jQueryInterface.call(t,e)})}),f.fn[N]=W._jQueryInterface,f.fn[N].Constructor=W,f.fn[N].noConflict=function(){return f.fn[N]=M,W._jQueryInterface};var q="dropdown",Y="bs.dropdown",V="."+Y,G=".data-api",K=f.fn[q],Q=new RegExp("38|40|27"),X="hide"+V,J="hidden"+V,Z="click"+V+G,tt="keydown"+V+G,et="disabled",it="show",nt="dropdown-menu-right",st='[data-toggle="dropdown"]',ot=".dropdown-menu",rt={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},at={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},lt=function(){function c(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var t=c.prototype;return t.toggle=function(){var t;this._element.disabled||f(this._element).hasClass(et)||(t=f(this._menu).hasClass(it),c._clearMenus(),t||this.show(!0))},t.show=function(t){if(void 0===t&&(t=!1),!(this._element.disabled||f(this._element).hasClass(et)||f(this._menu).hasClass(it))){var e={relatedTarget:this._element},i=f.Event("show.bs.dropdown",e),n=c._getParentFromElement(this._element);if(f(n).trigger(i),!i.isDefaultPrevented()){if(!this._inNavbar&&t){if(void 0===d)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var s=this._element;"parent"===this._config.reference?s=n:m.isElement(this._config.reference)&&(s=this._config.reference,void 0!==this._config.reference.jquery&&(s=this._config.reference[0])),"scrollParent"!==this._config.boundary&&f(n).addClass("position-static"),this._popper=new d(s,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===f(n).closest(".navbar-nav").length&&f(document.body).children().on("mouseover",null,f.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),f(this._menu).toggleClass(it),f(n).toggleClass(it).trigger(f.Event("shown.bs.dropdown",e))}}},t.hide=function(){var t,e,i;this._element.disabled||f(this._element).hasClass(et)||!f(this._menu).hasClass(it)||(t={relatedTarget:this._element},e=f.Event(X,t),i=c._getParentFromElement(this._element),f(i).trigger(e),e.isDefaultPrevented()||(this._popper&&this._popper.destroy(),f(this._menu).toggleClass(it),f(i).toggleClass(it).trigger(f.Event(J,t))))},t.dispose=function(){f.removeData(this._element,Y),f(this._element).off(V),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},t.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},t._addEventListeners=function(){var e=this;f(this._element).on("click.bs.dropdown",function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},t._getConfig=function(t){return t=l(l(l({},this.constructor.Default),f(this._element).data()),t),m.typeCheckConfig(q,t,this.constructor.DefaultType),t},t._getMenuElement=function(){var t;return this._menu||(t=c._getParentFromElement(this._element))&&(this._menu=t.querySelector(ot)),this._menu},t._getPlacement=function(){var t=f(this._element.parentNode),e="bottom-start";return t.hasClass("dropup")?e=f(this._menu).hasClass(nt)?"top-end":"top-start":t.hasClass("dropright")?e="right-start":t.hasClass("dropleft")?e="left-start":f(this._menu).hasClass(nt)&&(e="bottom-end"),e},t._detectNavbar=function(){return 0<f(this._element).closest(".navbar").length},t._getOffset=function(){var e=this,t={};return"function"==typeof this._config.offset?t.fn=function(t){return t.offsets=l(l({},t.offsets),e._config.offset(t.offsets,e._element)||{}),t}:t.offset=this._config.offset,t},t._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),l(l({},t),this._config.popperConfig)},c._jQueryInterface=function(e){return this.each(function(){var t=f(this).data(Y);if(t||(t=new c(this,"object"==typeof e?e:null),f(this).data(Y,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},c._clearMenus=function(t){if(!t||3!==t.which&&("keyup"!==t.type||9===t.which))for(var e=[].slice.call(document.querySelectorAll(st)),i=0,n=e.length;i<n;i++){var s,o,r=c._getParentFromElement(e[i]),a=f(e[i]).data(Y),l={relatedTarget:e[i]};t&&"click"===t.type&&(l.clickEvent=t),a&&(s=a._menu,f(r).hasClass(it)&&(t&&("click"===t.type&&/input|textarea/i.test(t.target.tagName)||"keyup"===t.type&&9===t.which)&&f.contains(r,t.target)||(o=f.Event(X,l),f(r).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&f(document.body).children().off("mouseover",null,f.noop),e[i].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),f(s).removeClass(it),f(r).removeClass(it).trigger(f.Event(J,l))))))}},c._getParentFromElement=function(t){var e,i=m.getSelectorFromElement(t);return i&&(e=document.querySelector(i)),e||t.parentNode},c._dataApiKeydownHandler=function(t){if((/input|textarea/i.test(t.target.tagName)?!(32===t.which||27!==t.which&&(40!==t.which&&38!==t.which||f(t.target).closest(ot).length)):Q.test(t.which))&&!this.disabled&&!f(this).hasClass(et)){var e=c._getParentFromElement(this),i=f(e).hasClass(it);if(i||27!==t.which){if(t.preventDefault(),t.stopPropagation(),!i||i&&(27===t.which||32===t.which))return 27===t.which&&f(e.querySelector(st)).trigger("focus"),void f(this).trigger("click");var n,s=[].slice.call(e.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(t){return f(t).is(":visible")});0!==s.length&&(n=s.indexOf(t.target),38===t.which&&0<n&&n--,40===t.which&&n<s.length-1&&n++,n<0&&(n=0),s[n].focus())}}},r(c,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return rt}},{key:"DefaultType",get:function(){return at}}]),c}();f(document).on(tt,st,lt._dataApiKeydownHandler).on(tt,ot,lt._dataApiKeydownHandler).on(Z+" keyup.bs.dropdown.data-api",lt._clearMenus).on(Z,st,function(t){t.preventDefault(),t.stopPropagation(),lt._jQueryInterface.call(f(this),"toggle")}).on(Z,".dropdown form",function(t){t.stopPropagation()}),f.fn[q]=lt._jQueryInterface,f.fn[q].Constructor=lt,f.fn[q].noConflict=function(){return f.fn[q]=K,lt._jQueryInterface};var ct="modal",ht="bs.modal",dt="."+ht,ut=f.fn[ct],pt={backdrop:!0,keyboard:!0,focus:!0,show:!0},ft={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},mt="hidden"+dt,gt="show"+dt,vt="focusin"+dt,yt="resize"+dt,bt="click.dismiss"+dt,wt="keydown.dismiss"+dt,_t="mousedown.dismiss"+dt,xt="modal-open",Ct="fade",kt="show",$t="modal-static",Tt=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Et=".sticky-top",Dt=function(){function s(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=t.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var t=s.prototype;return t.toggle=function(t){return this._isShown?this.hide():this.show(t)},t.show=function(t){var e,i=this;this._isShown||this._isTransitioning||(f(this._element).hasClass(Ct)&&(this._isTransitioning=!0),e=f.Event(gt,{relatedTarget:t}),f(this._element).trigger(e),this._isShown||e.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),f(this._element).on(bt,'[data-dismiss="modal"]',function(t){return i.hide(t)}),f(this._dialog).on(_t,function(){f(i._element).one("mouseup.dismiss.bs.modal",function(t){f(t.target).is(i._element)&&(i._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return i._showElement(t)})))},t.hide=function(t){var e,i,n,s=this;t&&t.preventDefault(),this._isShown&&!this._isTransitioning&&(e=f.Event("hide.bs.modal"),f(this._element).trigger(e),this._isShown&&!e.isDefaultPrevented()&&(this._isShown=!1,(i=f(this._element).hasClass(Ct))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),f(document).off(vt),f(this._element).removeClass(kt),f(this._element).off(bt),f(this._dialog).off(_t),i?(n=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,function(t){return s._hideModal(t)}).emulateTransitionEnd(n)):this._hideModal()))},t.dispose=function(){[window,this._element,this._dialog].forEach(function(t){return f(t).off(dt)}),f(document).off(vt),f.removeData(this._element,ht),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},t.handleUpdate=function(){this._adjustDialog()},t._getConfig=function(t){return t=l(l({},pt),t),m.typeCheckConfig(ct,t,ft),t},t._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){var e=f.Event("hidePrevented.bs.modal");if(f(this._element).trigger(e),e.defaultPrevented)return;this._element.classList.add($t);var i=m.getTransitionDurationFromElement(this._element);f(this._element).one(m.TRANSITION_END,function(){t._element.classList.remove($t)}).emulateTransitionEnd(i),this._element.focus()}else this.hide()},t._showElement=function(t){var e=this,i=f(this._element).hasClass(Ct),n=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),f(this._dialog).hasClass("modal-dialog-scrollable")&&n?n.scrollTop=0:this._element.scrollTop=0,i&&m.reflow(this._element),f(this._element).addClass(kt),this._config.focus&&this._enforceFocus();function s(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,f(e._element).trigger(r)}var o,r=f.Event("shown.bs.modal",{relatedTarget:t});i?(o=m.getTransitionDurationFromElement(this._dialog),f(this._dialog).one(m.TRANSITION_END,s).emulateTransitionEnd(o)):s()},t._enforceFocus=function(){var e=this;f(document).off(vt).on(vt,function(t){document!==t.target&&e._element!==t.target&&0===f(e._element).has(t.target).length&&e._element.focus()})},t._setEscapeEvent=function(){var e=this;this._isShown?f(this._element).on(wt,function(t){e._config.keyboard&&27===t.which?(t.preventDefault(),e.hide()):e._config.keyboard||27!==t.which||e._triggerBackdropTransition()}):this._isShown||f(this._element).off(wt)},t._setResizeEvent=function(){var e=this;this._isShown?f(window).on(yt,function(t){return e.handleUpdate(t)}):f(window).off(yt)},t._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(function(){f(document.body).removeClass(xt),t._resetAdjustments(),t._resetScrollbar(),f(t._element).trigger(mt)})},t._removeBackdrop=function(){this._backdrop&&(f(this._backdrop).remove(),this._backdrop=null)},t._showBackdrop=function(t){var e,i,n=this,s=f(this._element).hasClass(Ct)?Ct:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",s&&this._backdrop.classList.add(s),f(this._backdrop).appendTo(document.body),f(this._element).on(bt,function(t){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:t.target===t.currentTarget&&n._triggerBackdropTransition()}),s&&m.reflow(this._backdrop),f(this._backdrop).addClass(kt),!t)return;if(!s)return void t();var o=m.getTransitionDurationFromElement(this._backdrop);f(this._backdrop).one(m.TRANSITION_END,t).emulateTransitionEnd(o)}else{!this._isShown&&this._backdrop?(f(this._backdrop).removeClass(kt),e=function(){n._removeBackdrop(),t&&t()},f(this._element).hasClass(Ct)?(i=m.getTransitionDurationFromElement(this._backdrop),f(this._backdrop).one(m.TRANSITION_END,e).emulateTransitionEnd(i)):e()):t&&t()}},t._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},t._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},t._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},t._setScrollbar=function(){var t,e,i,n,s=this;this._isBodyOverflowing&&(t=[].slice.call(document.querySelectorAll(Tt)),e=[].slice.call(document.querySelectorAll(Et)),f(t).each(function(t,e){var i=e.style.paddingRight,n=f(e).css("padding-right");f(e).data("padding-right",i).css("padding-right",parseFloat(n)+s._scrollbarWidth+"px")}),f(e).each(function(t,e){var i=e.style.marginRight,n=f(e).css("margin-right");f(e).data("margin-right",i).css("margin-right",parseFloat(n)-s._scrollbarWidth+"px")}),i=document.body.style.paddingRight,n=f(document.body).css("padding-right"),f(document.body).data("padding-right",i).css("padding-right",parseFloat(n)+this._scrollbarWidth+"px")),f(document.body).addClass(xt)},t._resetScrollbar=function(){var t=[].slice.call(document.querySelectorAll(Tt));f(t).each(function(t,e){var i=f(e).data("padding-right");f(e).removeData("padding-right"),e.style.paddingRight=i||""});var e=[].slice.call(document.querySelectorAll(Et));f(e).each(function(t,e){var i=f(e).data("margin-right");void 0!==i&&f(e).css("margin-right",i).removeData("margin-right")});var i=f(document.body).data("padding-right");f(document.body).removeData("padding-right"),document.body.style.paddingRight=i||""},t._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},s._jQueryInterface=function(i,n){return this.each(function(){var t=f(this).data(ht),e=l(l(l({},pt),f(this).data()),"object"==typeof i&&i?i:{});if(t||(t=new s(this,e),f(this).data(ht,t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i](n)}else e.show&&t.show(n)})},r(s,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return pt}}]),s}();f(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(t){var e,i=this,n=m.getSelectorFromElement(this);n&&(e=document.querySelector(n));var s=f(e).data(ht)?"toggle":l(l({},f(e).data()),f(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault();var o=f(e).one(gt,function(t){t.isDefaultPrevented()||o.one(mt,function(){f(i).is(":visible")&&i.focus()})});Dt._jQueryInterface.call(f(e),s,this)}),f.fn[ct]=Dt._jQueryInterface,f.fn[ct].Constructor=Dt,f.fn[ct].noConflict=function(){return f.fn[ct]=ut,Dt._jQueryInterface};var At=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],St={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Lt=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,It=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Ot(t,o,e){if(0===t.length)return t;if(e&&"function"==typeof e)return e(t);for(var i=(new window.DOMParser).parseFromString(t,"text/html"),r=Object.keys(o),a=[].slice.call(i.body.querySelectorAll("*")),n=function(t){var e=a[t],i=e.nodeName.toLowerCase();if(-1===r.indexOf(e.nodeName.toLowerCase()))return e.parentNode.removeChild(e),"continue";var n=[].slice.call(e.attributes),s=[].concat(o["*"]||[],o[i]||[]);n.forEach(function(t){!function(t,e){var i=t.nodeName.toLowerCase();if(-1!==e.indexOf(i))return-1===At.indexOf(i)||Boolean(t.nodeValue.match(Lt)||t.nodeValue.match(It));for(var n=e.filter(function(t){return t instanceof RegExp}),s=0,o=n.length;s<o;s++)if(i.match(n[s]))return 1}(t,s)&&e.removeAttribute(t.nodeName)})},s=0,l=a.length;s<l;s++)n(s);return i.body.innerHTML}var Nt="tooltip",Pt="bs.tooltip",Mt="."+Pt,Bt=f.fn[Nt],jt="bs-tooltip",Ft=new RegExp("(^|\\s)"+jt+"\\S+","g"),Ht=["sanitize","whiteList","sanitizeFn"],Ut={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Rt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},zt={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:St,popperConfig:null},Wt="show",qt={HIDE:"hide"+Mt,HIDDEN:"hidden"+Mt,SHOW:"show"+Mt,SHOWN:"shown"+Mt,INSERTED:"inserted"+Mt,CLICK:"click"+Mt,FOCUSIN:"focusin"+Mt,FOCUSOUT:"focusout"+Mt,MOUSEENTER:"mouseenter"+Mt,MOUSELEAVE:"mouseleave"+Mt},Yt="fade",Vt="show",Gt="hover",Kt="focus",Qt=function(){function n(t,e){if(void 0===d)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var t=n.prototype;return t.enable=function(){this._isEnabled=!0},t.disable=function(){this._isEnabled=!1},t.toggleEnabled=function(){this._isEnabled=!this._isEnabled},t.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,i=f(t.currentTarget).data(e);i||(i=new this.constructor(t.currentTarget,this._getDelegateConfig()),f(t.currentTarget).data(e,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)}else{if(f(this.getTipElement()).hasClass(Vt))return void this._leave(null,this);this._enter(null,this)}},t.dispose=function(){clearTimeout(this._timeout),f.removeData(this.element,this.constructor.DATA_KEY),f(this.element).off(this.constructor.EVENT_KEY),f(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&f(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},t.show=function(){var e=this;if("none"===f(this.element).css("display"))throw new Error("Please use show on visible elements");var t=f.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){f(this.element).trigger(t);var i=m.findShadowRoot(this.element),n=f.contains(null!==i?i:this.element.ownerDocument.documentElement,this.element);if(t.isDefaultPrevented()||!n)return;var s=this.getTipElement(),o=m.getUID(this.constructor.NAME);s.setAttribute("id",o),this.element.setAttribute("aria-describedby",o),this.setContent(),this.config.animation&&f(s).addClass(Yt);var r="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,a=this._getAttachment(r);this.addAttachmentClass(a);var l=this._getContainer();f(s).data(this.constructor.DATA_KEY,this),f.contains(this.element.ownerDocument.documentElement,this.tip)||f(s).appendTo(l),f(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new d(this.element,s,this._getPopperConfig(a)),f(s).addClass(Vt),"ontouchstart"in document.documentElement&&f(document.body).children().on("mouseover",null,f.noop);var c,h=function(){e.config.animation&&e._fixTransition();var t=e._hoverState;e._hoverState=null,f(e.element).trigger(e.constructor.Event.SHOWN),"out"===t&&e._leave(null,e)};f(this.tip).hasClass(Yt)?(c=m.getTransitionDurationFromElement(this.tip),f(this.tip).one(m.TRANSITION_END,h).emulateTransitionEnd(c)):h()}},t.hide=function(t){function e(){n._hoverState!==Wt&&s.parentNode&&s.parentNode.removeChild(s),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),f(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),t&&t()}var i,n=this,s=this.getTipElement(),o=f.Event(this.constructor.Event.HIDE);f(this.element).trigger(o),o.isDefaultPrevented()||(f(s).removeClass(Vt),"ontouchstart"in document.documentElement&&f(document.body).children().off("mouseover",null,f.noop),this._activeTrigger.click=!1,this._activeTrigger[Kt]=!1,this._activeTrigger[Gt]=!1,f(this.tip).hasClass(Yt)?(i=m.getTransitionDurationFromElement(s),f(s).one(m.TRANSITION_END,e).emulateTransitionEnd(i)):e(),this._hoverState="")},t.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},t.isWithContent=function(){return Boolean(this.getTitle())},t.addAttachmentClass=function(t){f(this.getTipElement()).addClass(jt+"-"+t)},t.getTipElement=function(){return this.tip=this.tip||f(this.config.template)[0],this.tip},t.setContent=function(){var t=this.getTipElement();this.setElementContent(f(t.querySelectorAll(".tooltip-inner")),this.getTitle()),f(t).removeClass(Yt+" "+Vt)},t.setElementContent=function(t,e){"object"!=typeof e||!e.nodeType&&!e.jquery?this.config.html?(this.config.sanitize&&(e=Ot(e,this.config.whiteList,this.config.sanitizeFn)),t.html(e)):t.text(e):this.config.html?f(e).parent().is(t)||t.empty().append(e):t.text(f(e).text())},t.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},t._getPopperConfig=function(t){var e=this;return l(l({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}}),this.config.popperConfig)},t._getOffset=function(){var e=this,t={};return"function"==typeof this.config.offset?t.fn=function(t){return t.offsets=l(l({},t.offsets),e.config.offset(t.offsets,e.element)||{}),t}:t.offset=this.config.offset,t},t._getContainer=function(){return!1===this.config.container?document.body:m.isElement(this.config.container)?f(this.config.container):f(document).find(this.config.container)},t._getAttachment=function(t){return Rt[t.toUpperCase()]},t._setListeners=function(){var n=this;this.config.trigger.split(" ").forEach(function(t){var e,i;"click"===t?f(n.element).on(n.constructor.Event.CLICK,n.config.selector,function(t){return n.toggle(t)}):"manual"!==t&&(e=t===Gt?n.constructor.Event.MOUSEENTER:n.constructor.Event.FOCUSIN,i=t===Gt?n.constructor.Event.MOUSELEAVE:n.constructor.Event.FOCUSOUT,f(n.element).on(e,n.config.selector,function(t){return n._enter(t)}).on(i,n.config.selector,function(t){return n._leave(t)}))}),this._hideModalHandler=function(){n.element&&n.hide()},f(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=l(l({},this.config),{},{trigger:"manual",selector:""}):this._fixTitle()},t._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==t||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},t._enter=function(t,e){var i=this.constructor.DATA_KEY;(e=e||f(t.currentTarget).data(i))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),f(t.currentTarget).data(i,e)),t&&(e._activeTrigger["focusin"===t.type?Kt:Gt]=!0),f(e.getTipElement()).hasClass(Vt)||e._hoverState===Wt?e._hoverState=Wt:(clearTimeout(e._timeout),e._hoverState=Wt,e.config.delay&&e.config.delay.show?e._timeout=setTimeout(function(){e._hoverState===Wt&&e.show()},e.config.delay.show):e.show())},t._leave=function(t,e){var i=this.constructor.DATA_KEY;(e=e||f(t.currentTarget).data(i))||(e=new this.constructor(t.currentTarget,this._getDelegateConfig()),f(t.currentTarget).data(i,e)),t&&(e._activeTrigger["focusout"===t.type?Kt:Gt]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e.config.delay&&e.config.delay.hide?e._timeout=setTimeout(function(){"out"===e._hoverState&&e.hide()},e.config.delay.hide):e.hide())},t._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},t._getConfig=function(t){var e=f(this.element).data();return Object.keys(e).forEach(function(t){-1!==Ht.indexOf(t)&&delete e[t]}),"number"==typeof(t=l(l(l({},this.constructor.Default),e),"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),m.typeCheckConfig(Nt,t,this.constructor.DefaultType),t.sanitize&&(t.template=Ot(t.template,t.whiteList,t.sanitizeFn)),t},t._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},t._cleanTipClass=function(){var t=f(this.getTipElement()),e=t.attr("class").match(Ft);null!==e&&e.length&&t.removeClass(e.join(""))},t._handlePopperPlacementChange=function(t){this.tip=t.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},t._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(f(t).removeClass(Yt),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},n._jQueryInterface=function(i){return this.each(function(){var t=f(this).data(Pt),e="object"==typeof i&&i;if((t||!/dispose|hide/.test(i))&&(t||(t=new n(this,e),f(this).data(Pt,t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return zt}},{key:"NAME",get:function(){return Nt}},{key:"DATA_KEY",get:function(){return Pt}},{key:"Event",get:function(){return qt}},{key:"EVENT_KEY",get:function(){return Mt}},{key:"DefaultType",get:function(){return Ut}}]),n}();f.fn[Nt]=Qt._jQueryInterface,f.fn[Nt].Constructor=Qt,f.fn[Nt].noConflict=function(){return f.fn[Nt]=Bt,Qt._jQueryInterface};var Xt="popover",Jt="bs.popover",Zt="."+Jt,te=f.fn[Xt],ee="bs-popover",ie=new RegExp("(^|\\s)"+ee+"\\S+","g"),ne=l(l({},Qt.Default),{},{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),se=l(l({},Qt.DefaultType),{},{content:"(string|element|function)"}),oe={HIDE:"hide"+Zt,HIDDEN:"hidden"+Zt,SHOW:"show"+Zt,SHOWN:"shown"+Zt,INSERTED:"inserted"+Zt,CLICK:"click"+Zt,FOCUSIN:"focusin"+Zt,FOCUSOUT:"focusout"+Zt,MOUSEENTER:"mouseenter"+Zt,MOUSELEAVE:"mouseleave"+Zt},re=function(t){var e,i;function n(){return t.apply(this,arguments)||this}i=t,(e=n).prototype=Object.create(i.prototype),(e.prototype.constructor=e).__proto__=i;var s=n.prototype;return s.isWithContent=function(){return this.getTitle()||this._getContent()},s.addAttachmentClass=function(t){f(this.getTipElement()).addClass(ee+"-"+t)},s.getTipElement=function(){return this.tip=this.tip||f(this.config.template)[0],this.tip},s.setContent=function(){var t=f(this.getTipElement());this.setElementContent(t.find(".popover-header"),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(t.find(".popover-body"),e),t.removeClass("fade show")},s._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},s._cleanTipClass=function(){var t=f(this.getTipElement()),e=t.attr("class").match(ie);null!==e&&0<e.length&&t.removeClass(e.join(""))},n._jQueryInterface=function(i){return this.each(function(){var t=f(this).data(Jt),e="object"==typeof i?i:null;if((t||!/dispose|hide/.test(i))&&(t||(t=new n(this,e),f(this).data(Jt,t)),"string"==typeof i)){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return ne}},{key:"NAME",get:function(){return Xt}},{key:"DATA_KEY",get:function(){return Jt}},{key:"Event",get:function(){return oe}},{key:"EVENT_KEY",get:function(){return Zt}},{key:"DefaultType",get:function(){return se}}]),n}(Qt);f.fn[Xt]=re._jQueryInterface,f.fn[Xt].Constructor=re,f.fn[Xt].noConflict=function(){return f.fn[Xt]=te,re._jQueryInterface};var ae="scrollspy",le="bs.scrollspy",ce="."+le,he=f.fn[ae],de={offset:10,method:"auto",target:""},ue={offset:"number",method:"string",target:"(string|element)"},pe="active",fe=".nav, .list-group",me=".nav-link",ge=".list-group-item",ve="position",ye=function(){function i(t,e){var i=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+me+","+this._config.target+" "+ge+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,f(this._scrollElement).on("scroll.bs.scrollspy",function(t){return i._process(t)}),this.refresh(),this._process()}var t=i.prototype;return t.refresh=function(){var e=this,t=this._scrollElement===this._scrollElement.window?"offset":ve,s="auto"===this._config.method?t:this._config.method,o=s===ve?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(t){var e,i=m.getSelectorFromElement(t);if(i&&(e=document.querySelector(i)),e){var n=e.getBoundingClientRect();if(n.width||n.height)return[f(e)[s]().top+o,i]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},t.dispose=function(){f.removeData(this._element,le),f(this._scrollElement).off(ce),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},t._getConfig=function(t){var e;return"string"!=typeof(t=l(l({},de),"object"==typeof t&&t?t:{})).target&&m.isElement(t.target)&&((e=f(t.target).attr("id"))||(e=m.getUID(ae),f(t.target).attr("id",e)),t.target="#"+e),m.typeCheckConfig(ae,t,ue),t},t._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},t._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},t._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},t._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),i=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),i<=t){var n=this._targets[this._targets.length-1];this._activeTarget!==n&&this._activate(n)}else{if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var s=this._offsets.length;s--;){this._activeTarget!==this._targets[s]&&t>=this._offsets[s]&&(void 0===this._offsets[s+1]||t<this._offsets[s+1])&&this._activate(this._targets[s])}}},t._activate=function(e){this._activeTarget=e,this._clear();var t=this._selector.split(",").map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'}),i=f([].slice.call(document.querySelectorAll(t.join(","))));i.hasClass("dropdown-item")?(i.closest(".dropdown").find(".dropdown-toggle").addClass(pe),i.addClass(pe)):(i.addClass(pe),i.parents(fe).prev(me+", "+ge).addClass(pe),i.parents(fe).prev(".nav-item").children(me).addClass(pe)),f(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:e})},t._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(t){return t.classList.contains(pe)}).forEach(function(t){return t.classList.remove(pe)})},i._jQueryInterface=function(e){return this.each(function(){var t=f(this).data(le);if(t||(t=new i(this,"object"==typeof e&&e),f(this).data(le,t)),"string"==typeof e){if(void 0===t[e])throw new TypeError('No method named "'+e+'"');t[e]()}})},r(i,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"Default",get:function(){return de}}]),i}();f(window).on("load.bs.scrollspy.data-api",function(){for(var t=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),e=t.length;e--;){var i=f(t[e]);ye._jQueryInterface.call(i,i.data())}}),f.fn[ae]=ye._jQueryInterface,f.fn[ae].Constructor=ye,f.fn[ae].noConflict=function(){return f.fn[ae]=he,ye._jQueryInterface};var be="bs.tab",we=f.fn.tab,_e="active",xe=".active",Ce="> li > .active",ke=function(){function n(t){this._element=t}var t=n.prototype;return t.show=function(){var t,e,i,n,s,o,r,a,l=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&f(this._element).hasClass(_e)||f(this._element).hasClass("disabled")||(e=f(this._element).closest(".nav, .list-group")[0],i=m.getSelectorFromElement(this._element),e&&(n="UL"===e.nodeName||"OL"===e.nodeName?Ce:xe,s=(s=f.makeArray(f(e).find(n)))[s.length-1]),o=f.Event("hide.bs.tab",{relatedTarget:this._element}),r=f.Event("show.bs.tab",{relatedTarget:s}),s&&f(s).trigger(o),f(this._element).trigger(r),r.isDefaultPrevented()||o.isDefaultPrevented()||(i&&(t=document.querySelector(i)),this._activate(this._element,e),a=function(){var t=f.Event("hidden.bs.tab",{relatedTarget:l._element}),e=f.Event("shown.bs.tab",{relatedTarget:s});f(s).trigger(t),f(l._element).trigger(e)},t?this._activate(t,t.parentNode,a):a()))},t.dispose=function(){f.removeData(this._element,be),this._element=null},t._activate=function(t,e,i){function n(){return o._transitionComplete(t,r,i)}var s,o=this,r=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?f(e).children(xe):f(e).find(Ce))[0],a=i&&r&&f(r).hasClass("fade");r&&a?(s=m.getTransitionDurationFromElement(r),f(r).removeClass("show").one(m.TRANSITION_END,n).emulateTransitionEnd(s)):n()},t._transitionComplete=function(t,e,i){var n,s,o;e&&(f(e).removeClass(_e),(n=f(e.parentNode).find("> .dropdown-menu .active")[0])&&f(n).removeClass(_e),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)),f(t).addClass(_e),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),m.reflow(t),t.classList.contains("fade")&&t.classList.add("show"),t.parentNode&&f(t.parentNode).hasClass("dropdown-menu")&&((s=f(t).closest(".dropdown")[0])&&(o=[].slice.call(s.querySelectorAll(".dropdown-toggle")),f(o).addClass(_e)),t.setAttribute("aria-expanded",!0)),i&&i()},n._jQueryInterface=function(i){return this.each(function(){var t=f(this),e=t.data(be);if(e||(e=new n(this),t.data(be,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i]()}})},r(n,null,[{key:"VERSION",get:function(){return"4.5.0"}}]),n}();f(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(t){t.preventDefault(),ke._jQueryInterface.call(f(this),"show")}),f.fn.tab=ke._jQueryInterface,f.fn.tab.Constructor=ke,f.fn.tab.noConflict=function(){return f.fn.tab=we,ke._jQueryInterface};var $e="toast",Te="bs.toast",Ee="."+Te,De=f.fn[$e],Ae="click.dismiss"+Ee,Se="show",Le="showing",Ie={animation:"boolean",autohide:"boolean",delay:"number"},Oe={animation:!0,autohide:!0,delay:500},Ne=function(){function n(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners()}var t=n.prototype;return t.show=function(){var t,e,i=this,n=f.Event("show.bs.toast");f(this._element).trigger(n),n.isDefaultPrevented()||(this._config.animation&&this._element.classList.add("fade"),t=function(){i._element.classList.remove(Le),i._element.classList.add(Se),f(i._element).trigger("shown.bs.toast"),i._config.autohide&&(i._timeout=setTimeout(function(){i.hide()},i._config.delay))},this._element.classList.remove("hide"),m.reflow(this._element),this._element.classList.add(Le),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t())},t.hide=function(){var t;this._element.classList.contains(Se)&&(t=f.Event("hide.bs.toast"),f(this._element).trigger(t),t.isDefaultPrevented()||this._close())},t.dispose=function(){clearTimeout(this._timeout),this._timeout=null,this._element.classList.contains(Se)&&this._element.classList.remove(Se),f(this._element).off(Ae),f.removeData(this._element,Te),this._element=null,this._config=null},t._getConfig=function(t){return t=l(l(l({},Oe),f(this._element).data()),"object"==typeof t&&t?t:{}),m.typeCheckConfig($e,t,this.constructor.DefaultType),t},t._setListeners=function(){var t=this;f(this._element).on(Ae,'[data-dismiss="toast"]',function(){return t.hide()})},t._close=function(){function t(){i._element.classList.add("hide"),f(i._element).trigger("hidden.bs.toast")}var e,i=this;this._element.classList.remove(Se),this._config.animation?(e=m.getTransitionDurationFromElement(this._element),f(this._element).one(m.TRANSITION_END,t).emulateTransitionEnd(e)):t()},n._jQueryInterface=function(i){return this.each(function(){var t=f(this),e=t.data(Te);if(e||(e=new n(this,"object"==typeof i&&i),t.data(Te,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},r(n,null,[{key:"VERSION",get:function(){return"4.5.0"}},{key:"DefaultType",get:function(){return Ie}},{key:"Default",get:function(){return Oe}}]),n}();f.fn[$e]=Ne._jQueryInterface,f.fn[$e].Constructor=Ne,f.fn[$e].noConflict=function(){return f.fn[$e]=De,Ne._jQueryInterface},t.Alert=h,t.Button=w,t.Carousel=O,t.Collapse=W,t.Dropdown=lt,t.Modal=Dt,t.Popover=re,t.Scrollspy=ye,t.Tab=ke,t.Toast=Ne,t.Tooltip=Qt,t.Util=m,Object.defineProperty(t,"__esModule",{value:!0})}),function(L){"use strict";var h=["sanitize","whiteList","sanitizeFn"],g=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],t={"*":["class","dir","id","lang","role","tabindex","style",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},v=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,y=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function j(t,e,i){if(i&&"function"==typeof i)return i(t);for(var n=Object.keys(e),s=0,o=t.length;s<o;s++)for(var r=t[s].querySelectorAll("*"),a=0,l=r.length;a<l;a++){var c=r[a],h=c.nodeName.toLowerCase();if(-1!==n.indexOf(h))for(var d=[].slice.call(c.attributes),u=[].concat(e["*"]||[],e[h]||[]),p=0,f=d.length;p<f;p++){var m=d[p];!function(t,e){var i=t.nodeName.toLowerCase();if(-1!==L.inArray(i,e))return-1===L.inArray(i,g)||Boolean(t.nodeValue.match(v)||t.nodeValue.match(y));for(var n=L(e).filter(function(t,e){return e instanceof RegExp}),s=0,o=n.length;s<o;s++)if(i.match(n[s]))return 1}(m,u)&&c.removeAttribute(m.nodeName)}else c.parentNode.removeChild(c)}}"classList"in document.createElement("_")||function(t){if("Element"in t){var e="classList",i=t.Element.prototype,n=Object,s=function(){var i=L(this);return{add:function(t){return t=Array.prototype.slice.call(arguments).join(" "),i.addClass(t)},remove:function(t){return t=Array.prototype.slice.call(arguments).join(" "),i.removeClass(t)},toggle:function(t,e){return i.toggleClass(t,e)},contains:function(t){return i.hasClass(t)}}};if(n.defineProperty){var o={get:s,enumerable:!0,configurable:!0};try{n.defineProperty(i,e,o)}catch(t){void 0!==t.number&&-2146823252!==t.number||(o.enumerable=!1,n.defineProperty(i,e,o))}}else n.prototype.__defineGetter__&&i.__defineGetter__(e,s)}}(window);var e,i,n,s,d,o=document.createElement("_");function r(t,e){if(null==this)throw new TypeError;var i=String(this);if(t&&"[object RegExp]"==d.call(t))throw new TypeError;var n=i.length,s=String(t),o=s.length,r=1<arguments.length?e:void 0,a=r?Number(r):0;a!=a&&(a=0);var l=Math.min(Math.max(a,0),n);if(n<o+l)return!1;for(var c=-1;++c<o;)if(i.charCodeAt(l+c)!=s.charCodeAt(c))return!1;return!0}function D(t,e){var i,n=t.selectedOptions,s=[];if(e){for(var o=0,r=n.length;o<r;o++)(i=n[o]).disabled||"OPTGROUP"===i.parentNode.tagName&&i.parentNode.disabled||s.push(i);return s}return n}function A(t,e){for(var i,n=[],s=e||t.selectedOptions,o=0,r=s.length;o<r;o++)(i=s[o]).disabled||"OPTGROUP"===i.parentNode.tagName&&i.parentNode.disabled||n.push(i.value);return t.multiple?n:n.length?n[0]:null}o.classList.add("c1","c2"),o.classList.contains("c2")||(e=DOMTokenList.prototype.add,i=DOMTokenList.prototype.remove,DOMTokenList.prototype.add=function(){Array.prototype.forEach.call(arguments,e.bind(this))},DOMTokenList.prototype.remove=function(){Array.prototype.forEach.call(arguments,i.bind(this))}),o.classList.toggle("c3",!1),o.classList.contains("c3")&&(n=DOMTokenList.prototype.toggle,DOMTokenList.prototype.toggle=function(t,e){return 1 in arguments&&!this.contains(t)==!e?e:n.call(this,t)}),o=null,String.prototype.startsWith||(s=function(){try{var t={},e=Object.defineProperty,i=e(t,t,t)&&e}catch(t){}return i}(),d={}.toString,s?s(String.prototype,"startsWith",{value:r,configurable:!0,writable:!0}):String.prototype.startsWith=r),Object.keys||(Object.keys=function(t,e,i){for(e in i=[],t)i.hasOwnProperty.call(t,e)&&i.push(e);return i}),HTMLSelectElement&&!HTMLSelectElement.prototype.hasOwnProperty("selectedOptions")&&Object.defineProperty(HTMLSelectElement.prototype,"selectedOptions",{get:function(){return this.querySelectorAll(":checked")}});var a={useDefault:!1,_set:L.valHooks.select.set};L.valHooks.select.set=function(t,e){return e&&!a.useDefault&&L(t).data("selected",!0),a._set.apply(this,arguments)};var S=null,l=function(){try{return new Event("change"),!0}catch(t){return!1}}();function x(t,e,i,n){for(var s=["display","subtext","tokens"],o=!1,r=0;r<s.length;r++){var a=s[r],l=t[a];if(l&&(l=l.toString(),"display"===a&&(l=l.replace(/<[^>]+>/g,"")),n&&(l=m(l)),l=l.toUpperCase(),o="contains"===i?0<=l.indexOf(e):l.startsWith(e)))break}return o}function I(t){return parseInt(t,10)||0}L.fn.triggerNative=function(t){var e,i=this[0];i.dispatchEvent?(l?e=new Event(t,{bubbles:!0}):(e=document.createEvent("Event")).initEvent(t,!0,!1),i.dispatchEvent(e)):i.fireEvent?((e=document.createEventObject()).eventType=t,i.fireEvent("on"+t,e)):this.trigger(t)};var c={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"},u=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,p=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\u1ab0-\\u1aff\\u1dc0-\\u1dff]","g");function f(t){return c[t]}function m(t){return(t=t.toString())&&t.replace(u,f).replace(p,"")}var b,w,_,C,k=(b={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"},w="(?:"+Object.keys(b).join("|")+")",_=RegExp(w),C=RegExp(w,"g"),function(t){return t=null==t?"":""+t,_.test(t)?t.replace(C,$):t});function $(t){return b[t]}var T={32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9"},E=27,O=13,N=32,P=9,M=38,B=40,F={success:!1,major:"3"};try{F.full=(L.fn.dropdown.Constructor.VERSION||"").split(" ")[0].split("."),F.major=F.full[0],F.success=!0}catch(t){}var H=0,U=".bs.select",R={DISABLED:"disabled",DIVIDER:"divider",SHOW:"open",DROPUP:"dropup",MENU:"dropdown-menu",MENURIGHT:"dropdown-menu-right",MENULEFT:"dropdown-menu-left",BUTTONCLASS:"btn-default",POPOVERHEADER:"popover-title",ICONBASE:"glyphicon",TICKICON:"glyphicon-ok"},z={MENU:"."+R.MENU},W={div:document.createElement("div"),span:document.createElement("span"),i:document.createElement("i"),subtext:document.createElement("small"),a:document.createElement("a"),li:document.createElement("li"),whitespace:document.createTextNode(" "),fragment:document.createDocumentFragment()};W.noResults=W.li.cloneNode(!1),W.noResults.className="no-results",W.a.setAttribute("role","option"),W.a.className="dropdown-item",W.subtext.className="text-muted",W.text=W.span.cloneNode(!1),W.text.className="text",W.checkMark=W.span.cloneNode(!1);var q=new RegExp(M+"|"+B),Y=new RegExp("^"+P+"$|"+E),V={li:function(t,e,i){var n=W.li.cloneNode(!1);return t&&(1===t.nodeType||11===t.nodeType?n.appendChild(t):n.innerHTML=t),void 0!==e&&""!==e&&(n.className=e),null!=i&&n.classList.add("optgroup-"+i),n},a:function(t,e,i){var n=W.a.cloneNode(!0);return t&&(11===t.nodeType?n.appendChild(t):n.insertAdjacentHTML("beforeend",t)),void 0!==e&&""!==e&&n.classList.add.apply(n.classList,e.split(/\s+/)),i&&n.setAttribute("style",i),n},text:function(t,e){var i,n,s,o=W.text.cloneNode(!1);if(t.content?o.innerHTML=t.content:(o.textContent=t.text,t.icon&&(n=W.whitespace.cloneNode(!1),(s=(!0===e?W.i:W.span).cloneNode(!1)).className=this.options.iconBase+" "+t.icon,W.fragment.appendChild(s),W.fragment.appendChild(n)),t.subtext&&((i=W.subtext.cloneNode(!1)).textContent=t.subtext,o.appendChild(i))),!0===e)for(;0<o.childNodes.length;)W.fragment.appendChild(o.childNodes[0]);else W.fragment.appendChild(o);return W.fragment},label:function(t){var e,i,n,s=W.text.cloneNode(!1);return s.innerHTML=t.display,t.icon&&(i=W.whitespace.cloneNode(!1),(n=W.span.cloneNode(!1)).className=this.options.iconBase+" "+t.icon,W.fragment.appendChild(n),W.fragment.appendChild(i)),t.subtext&&((e=W.subtext.cloneNode(!1)).textContent=t.subtext,s.appendChild(e)),W.fragment.appendChild(s),W.fragment}};function G(t,e){var i=this;a.useDefault||(L.valHooks.select.set=a._set,a.useDefault=!0),this.$element=L(t),this.$newElement=null,this.$button=null,this.$menu=null,this.options=e,this.selectpicker={main:{},search:{},current:{},view:{},isSearching:!1,keydown:{keyHistory:"",resetKeyHistory:{start:function(){return setTimeout(function(){i.selectpicker.keydown.keyHistory=""},800)}}}},this.sizeInfo={},null===this.options.title&&(this.options.title=this.$element.attr("title"));var n=this.options.windowPadding;"number"==typeof n&&(this.options.windowPadding=[n,n,n,n]),this.val=G.prototype.val,this.render=G.prototype.render,this.refresh=G.prototype.refresh,this.setStyle=G.prototype.setStyle,this.selectAll=G.prototype.selectAll,this.deselectAll=G.prototype.deselectAll,this.destroy=G.prototype.destroy,this.remove=G.prototype.remove,this.show=G.prototype.show,this.hide=G.prototype.hide,this.init()}function K(t){var a,l=arguments,c=t;if([].shift.apply(l),!F.success){try{F.full=(L.fn.dropdown.Constructor.VERSION||"").split(" ")[0].split(".")}catch(t){G.BootstrapVersion?F.full=G.BootstrapVersion.split(" ")[0].split("."):(F.full=[F.major,"0","0"],console.warn("There was an issue retrieving Bootstrap's version. Ensure Bootstrap is being loaded before bootstrap-select and there is no namespace collision. If loading Bootstrap asynchronously, the version may need to be manually specified via $.fn.selectpicker.Constructor.BootstrapVersion.",t))}F.major=F.full[0],F.success=!0}if("4"===F.major){var e=[];G.DEFAULTS.style===R.BUTTONCLASS&&e.push({name:"style",className:"BUTTONCLASS"}),G.DEFAULTS.iconBase===R.ICONBASE&&e.push({name:"iconBase",className:"ICONBASE"}),G.DEFAULTS.tickIcon===R.TICKICON&&e.push({name:"tickIcon",className:"TICKICON"}),R.DIVIDER="dropdown-divider",R.SHOW="show",R.BUTTONCLASS="btn-light",R.POPOVERHEADER="popover-header",R.ICONBASE="",R.TICKICON="bs-ok-default";for(var i=0;i<e.length;i++){t=e[i];G.DEFAULTS[t.name]=R[t.className]}}var n=this.each(function(){var t=L(this);if(t.is("select")){var e=t.data("selectpicker"),i="object"==typeof c&&c;if(e){if(i)for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e.options[n]=i[n])}else{var s=t.data();for(var o in s)Object.prototype.hasOwnProperty.call(s,o)&&-1!==L.inArray(o,h)&&delete s[o];var r=L.extend({},G.DEFAULTS,L.fn.selectpicker.defaults||{},s,i);r.template=L.extend({},G.DEFAULTS.template,L.fn.selectpicker.defaults?L.fn.selectpicker.defaults.template:{},s.template,i.template),t.data("selectpicker",e=new G(this,r))}"string"==typeof c&&(a=e[c]instanceof Function?e[c].apply(e,l):e.options[c])}});return void 0!==a?a:n}G.VERSION="1.13.17",G.DEFAULTS={noneSelectedText:"Nothing selected",noneResultsText:"No results matched {0}",countSelectedText:function(t){return 1==t?"{0} item selected":"{0} items selected"},maxOptionsText:function(t,e){return[1==t?"Limit reached ({n} item max)":"Limit reached ({n} items max)",1==e?"Group limit reached ({n} item max)":"Group limit reached ({n} items max)"]},selectAllText:"Select All",deselectAllText:"Deselect All",doneButton:!1,doneButtonText:"Close",multipleSeparator:", ",styleBase:"btn",style:R.BUTTONCLASS,size:"auto",title:null,selectedTextFormat:"values",width:!1,container:!1,hideDisabled:!1,showSubtext:!1,showIcon:!0,showContent:!0,dropupAuto:!0,header:!1,liveSearch:!1,liveSearchPlaceholder:null,liveSearchNormalize:!1,liveSearchStyle:"contains",actionsBox:!1,iconBase:R.ICONBASE,tickIcon:R.TICKICON,showTick:!1,template:{caret:'<span class="caret"></span>'},maxOptions:!1,mobile:!1,selectOnTab:!1,dropdownAlignRight:!1,windowPadding:0,virtualScroll:600,display:!1,sanitize:!0,sanitizeFn:null,whiteList:t},G.prototype={constructor:G,init:function(){var i=this,t=this.$element.attr("id");H++,this.selectId="bs-select-"+H,this.$element[0].classList.add("bs-select-hidden"),this.multiple=this.$element.prop("multiple"),this.autofocus=this.$element.prop("autofocus"),this.$element[0].classList.contains("show-tick")&&(this.options.showTick=!0),this.$newElement=this.createDropdown(),this.buildData(),this.$element.after(this.$newElement).prependTo(this.$newElement),this.$button=this.$newElement.children("button"),this.$menu=this.$newElement.children(z.MENU),this.$menuInner=this.$menu.children(".inner"),this.$searchbox=this.$menu.find("input"),this.$element[0].classList.remove("bs-select-hidden"),!0===this.options.dropdownAlignRight&&this.$menu[0].classList.add(R.MENURIGHT),void 0!==t&&this.$button.attr("data-id",t),this.checkDisabled(),this.clickListener(),this.options.liveSearch?(this.liveSearchListener(),this.focusedParent=this.$searchbox[0]):this.focusedParent=this.$menuInner[0],this.setStyle(),this.render(),this.setWidth(),this.options.container?this.selectPosition():this.$element.on("hide"+U,function(){var t,e;i.isVirtual()&&(e=(t=i.$menuInner[0]).firstChild.cloneNode(!1),t.replaceChild(e,t.firstChild),t.scrollTop=0)}),this.$menu.data("this",this),this.$newElement.data("this",this),this.options.mobile&&this.mobile(),this.$newElement.on({"hide.bs.dropdown":function(t){i.$element.trigger("hide"+U,t)},"hidden.bs.dropdown":function(t){i.$element.trigger("hidden"+U,t)},"show.bs.dropdown":function(t){i.$element.trigger("show"+U,t)},"shown.bs.dropdown":function(t){i.$element.trigger("shown"+U,t)}}),i.$element[0].hasAttribute("required")&&this.$element.on("invalid"+U,function(){i.$button[0].classList.add("bs-invalid"),i.$element.on("shown"+U+".invalid",function(){i.$element.val(i.$element.val()).off("shown"+U+".invalid")}).on("rendered"+U,function(){this.validity.valid&&i.$button[0].classList.remove("bs-invalid"),i.$element.off("rendered"+U)}),i.$button.on("blur"+U,function(){i.$element.trigger("focus").trigger("blur"),i.$button.off("blur"+U)})}),setTimeout(function(){i.buildList(),i.$element.trigger("loaded"+U)})},createDropdown:function(){var t=this.multiple||this.options.showTick?" show-tick":"",e=this.multiple?' aria-multiselectable="true"':"",i="",n=this.autofocus?" autofocus":"";F.major<4&&this.$element.parent().hasClass("input-group")&&(i=" input-group-btn");var s,o="",r="",a="",l="";return this.options.header&&(o='<div class="'+R.POPOVERHEADER+'"><button type="button" class="close" aria-hidden="true">&times;</button>'+this.options.header+"</div>"),this.options.liveSearch&&(r='<div class="bs-searchbox"><input type="search" class="form-control" autocomplete="off"'+(null===this.options.liveSearchPlaceholder?"":' placeholder="'+k(this.options.liveSearchPlaceholder)+'"')+' role="combobox" aria-label="Search" aria-controls="'+this.selectId+'" aria-autocomplete="list"></div>'),this.multiple&&this.options.actionsBox&&(a='<div class="bs-actionsbox"><div class="btn-group btn-group-sm btn-block"><button type="button" class="actions-btn bs-select-all btn '+R.BUTTONCLASS+'">'+this.options.selectAllText+'</button><button type="button" class="actions-btn bs-deselect-all btn '+R.BUTTONCLASS+'">'+this.options.deselectAllText+"</button></div></div>"),this.multiple&&this.options.doneButton&&(l='<div class="bs-donebutton"><div class="btn-group btn-block"><button type="button" class="btn btn-sm '+R.BUTTONCLASS+'">'+this.options.doneButtonText+"</button></div></div>"),s='<div class="dropdown bootstrap-select'+t+i+'"><button type="button" tabindex="-1" class="'+this.options.styleBase+' dropdown-toggle" '+("static"===this.options.display?'data-display="static"':"")+'data-toggle="dropdown"'+n+' role="combobox" aria-owns="'+this.selectId+'" aria-haspopup="listbox" aria-expanded="false"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner"></div></div> </div>'+("4"===F.major?"":'<span class="bs-caret">'+this.options.template.caret+"</span>")+'</button><div class="'+R.MENU+" "+("4"===F.major?"":R.SHOW)+'">'+o+r+a+'<div class="inner '+R.SHOW+'" role="listbox" id="'+this.selectId+'" tabindex="-1" '+e+'><ul class="'+R.MENU+" inner "+("4"===F.major?R.SHOW:"")+'" role="presentation"></ul></div>'+l+"</div></div>",L(s)},setPositionData:function(){this.selectpicker.view.canHighlight=[];for(var t=this.selectpicker.view.size=0;t<this.selectpicker.current.data.length;t++){var e=this.selectpicker.current.data[t],i=!0;"divider"===e.type?(i=!1,e.height=this.sizeInfo.dividerHeight):"optgroup-label"===e.type?(i=!1,e.height=this.sizeInfo.dropdownHeaderHeight):e.height=this.sizeInfo.liHeight,e.disabled&&(i=!1),this.selectpicker.view.canHighlight.push(i),i&&(this.selectpicker.view.size++,e.posinset=this.selectpicker.view.size),e.position=(0===t?0:this.selectpicker.current.data[t-1].position)+e.height}},isVirtual:function(){return!1!==this.options.virtualScroll&&this.selectpicker.main.elements.length>=this.options.virtualScroll||!0===this.options.virtualScroll},createView:function(O,t,e){var N,P,i,n,s,o,M=this,r=0,B=[];function a(t,e){var i,n,s,o,r,a,l,c,h=M.selectpicker.current.elements.length,d=[],u=!0,p=M.isVirtual();M.selectpicker.view.scrollTop=t,i=Math.ceil(M.sizeInfo.menuInnerHeight/M.sizeInfo.liHeight*1.5),n=Math.round(h/i)||1;for(var f,m,g,v,y=0;y<n;y++){var b=y===n-1?h:(y+1)*i;if(d[y]=[y*i+(y?1:0),b],!h)break;void 0===r&&t-1<=M.selectpicker.current.data[b-1].position-M.sizeInfo.menuInnerHeight&&(r=y)}if(void 0===r&&(r=0),a=[M.selectpicker.view.position0,M.selectpicker.view.position1],s=Math.max(0,r-1),o=Math.min(n-1,r+1),M.selectpicker.view.position0=!1!==p&&Math.max(0,d[s][0])||0,M.selectpicker.view.position1=!1===p?h:Math.min(h,d[o][1])||0,l=a[0]!==M.selectpicker.view.position0||a[1]!==M.selectpicker.view.position1,void 0!==M.activeIndex&&(P=M.selectpicker.main.elements[M.prevActiveIndex],B=M.selectpicker.main.elements[M.activeIndex],N=M.selectpicker.main.elements[M.selectedIndex],e&&(M.activeIndex!==M.selectedIndex&&M.defocusItem(B),M.activeIndex=void 0),M.activeIndex&&M.activeIndex!==M.selectedIndex&&M.defocusItem(N)),void 0!==M.prevActiveIndex&&M.prevActiveIndex!==M.activeIndex&&M.prevActiveIndex!==M.selectedIndex&&M.defocusItem(P),(e||l)&&(c=M.selectpicker.view.visibleElements?M.selectpicker.view.visibleElements.slice():[],M.selectpicker.view.visibleElements=!1===p?M.selectpicker.current.elements:M.selectpicker.current.elements.slice(M.selectpicker.view.position0,M.selectpicker.view.position1),M.setOptionStatus(),(O||!1===p&&e)&&(f=c,m=M.selectpicker.view.visibleElements,u=!(f.length===m.length&&f.every(function(t,e){return t===m[e]}))),(e||!0===p)&&u)){var w,_,x=M.$menuInner[0],C=document.createDocumentFragment(),k=x.firstChild.cloneNode(!1),$=M.selectpicker.view.visibleElements,T=[];x.replaceChild(k,x.firstChild);for(var E,D,y=0,A=$.length;y<A;y++){var S,L,I=$[y];M.options.sanitize&&(S=I.lastChild)&&(L=M.selectpicker.current.data[y+M.selectpicker.view.position0])&&L.content&&!L.sanitized&&(T.push(S),L.sanitized=!0),C.appendChild(I)}M.options.sanitize&&T.length&&j(T,M.options.whiteList,M.options.sanitizeFn),!0===p?(w=0===M.selectpicker.view.position0?0:M.selectpicker.current.data[M.selectpicker.view.position0-1].position,_=M.selectpicker.view.position1>h-1?0:M.selectpicker.current.data[h-1].position-M.selectpicker.current.data[M.selectpicker.view.position1-1].position,x.firstChild.style.marginTop=w+"px",x.firstChild.style.marginBottom=_+"px"):(x.firstChild.style.marginTop=0,x.firstChild.style.marginBottom=0),x.firstChild.appendChild(C),!0===p&&M.sizeInfo.hasScrollBar&&(E=x.firstChild.offsetWidth,e&&E<M.sizeInfo.menuInnerInnerWidth&&M.sizeInfo.totalMenuWidth>M.sizeInfo.selectWidth?x.firstChild.style.minWidth=M.sizeInfo.menuInnerInnerWidth+"px":E>M.sizeInfo.menuInnerInnerWidth&&(M.$menu[0].style.minWidth=0,(D=x.firstChild.offsetWidth)>M.sizeInfo.menuInnerInnerWidth&&(M.sizeInfo.menuInnerInnerWidth=D,x.firstChild.style.minWidth=M.sizeInfo.menuInnerInnerWidth+"px"),M.$menu[0].style.minWidth=""))}M.prevActiveIndex=M.activeIndex,M.options.liveSearch?O&&e&&(g=0,M.selectpicker.view.canHighlight[g]||(g=1+M.selectpicker.view.canHighlight.slice(1).indexOf(!0)),v=M.selectpicker.view.visibleElements[g],M.defocusItem(M.selectpicker.view.currentActive),M.activeIndex=(M.selectpicker.current.data[g]||{}).index,M.focusItem(v)):M.$menuInner.trigger("focus")}this.selectpicker.isSearching=O,this.selectpicker.current=O?this.selectpicker.search:this.selectpicker.main,this.setPositionData(),t&&(e?r=this.$menuInner[0].scrollTop:M.multiple||("number"!=typeof(n=((i=M.$element[0]).options[i.selectedIndex]||{}).liIndex)||!1===M.options.size||(o=(s=M.selectpicker.main.data[n])&&s.position)&&(r=o-(M.sizeInfo.menuInnerHeight+M.sizeInfo.liHeight)/2))),a(r,!0),this.$menuInner.off("scroll.createView").on("scroll.createView",function(t,e){M.noScroll||a(this.scrollTop,e),M.noScroll=!1}),L(window).off("resize"+U+"."+this.selectId+".createView").on("resize"+U+"."+this.selectId+".createView",function(){M.$newElement.hasClass(R.SHOW)&&a(M.$menuInner[0].scrollTop)})},focusItem:function(t,e,i){var n;t&&(e=e||this.selectpicker.main.data[this.activeIndex],(n=t.firstChild)&&(n.setAttribute("aria-setsize",this.selectpicker.view.size),n.setAttribute("aria-posinset",e.posinset),!0!==i&&(this.focusedParent.setAttribute("aria-activedescendant",n.id),t.classList.add("active"),n.classList.add("active"))))},defocusItem:function(t){t&&(t.classList.remove("active"),t.firstChild&&t.firstChild.classList.remove("active"))},setPlaceholder:function(){var t,e,i,n,s,o,r=this,a=!1;return this.options.title&&!this.multiple&&(this.selectpicker.view.titleOption||(this.selectpicker.view.titleOption=document.createElement("option")),a=!0,t=this.$element[0],e=!1,i=!this.selectpicker.view.titleOption.parentNode,n=t.selectedIndex,s=t.options[n],o=window.performance&&window.performance.getEntriesByType("navigation"),i&&(this.selectpicker.view.titleOption.className="bs-title-option",this.selectpicker.view.titleOption.value="",e=!s||0===n&&!1===s.defaultSelected&&void 0===this.$element.data("selected")),!i&&0===this.selectpicker.view.titleOption.index||t.insertBefore(this.selectpicker.view.titleOption,t.firstChild),e&&o.length&&"back_forward"!==o[0].type?t.selectedIndex=0:"complete"!==document.readyState&&window.addEventListener("pageshow",function(){r.selectpicker.view.displayedValue!==t.value&&r.render()})),a},buildData:function(){var u=':not([hidden]):not([data-hidden="true"])',p=[],f=0,m=this.setPlaceholder()?1:0;this.options.hideDisabled&&(u+=":not(:disabled)");var t=this.$element[0].querySelectorAll("select > *"+u);function g(t){var e=p[p.length-1];e&&"divider"===e.type&&(e.optID||t.optID)||((t=t||{}).type="divider",p.push(t))}function v(t,e){var i,n,s,o;(e=e||{}).divider="true"===t.getAttribute("data-divider"),e.divider?g({optID:e.optID}):(i=p.length,s=(n=t.style.cssText)?k(n):"",o=(t.className||"")+(e.optgroupClass||""),e.optID&&(o="opt "+o),e.optionClass=o.trim(),e.inlineStyle=s,e.text=t.textContent,e.content=t.getAttribute("data-content"),e.tokens=t.getAttribute("data-tokens"),e.subtext=t.getAttribute("data-subtext"),e.icon=t.getAttribute("data-icon"),t.liIndex=i,e.display=e.content||e.text,e.type="option",e.index=i,e.option=t,e.selected=!!t.selected,e.disabled=e.disabled||!!t.disabled,p.push(e))}for(var e=t.length,i=m;i<e;i++){var n=t[i];"OPTGROUP"!==n.tagName?v(n,{}):function(t,e){var i=e[t],n=!(t-1<m)&&e[t-1],s=e[t+1],o=i.querySelectorAll("option"+u);if(o.length){var r,a,l={display:k(i.label),subtext:i.getAttribute("data-subtext"),icon:i.getAttribute("data-icon"),type:"optgroup-label",optgroupClass:" "+(i.className||"")};f++,n&&g({optID:f}),l.optID=f,p.push(l);for(var c=0,h=o.length;c<h;c++){var d=o[c];0===c&&(a=(r=p.length-1)+h),v(d,{headerIndex:r,lastIndex:a,optID:l.optID,optgroupClass:l.optgroupClass,disabled:i.disabled})}s&&g({optID:f})}}(i,t)}this.selectpicker.main.data=this.selectpicker.current.data=p},buildList:function(){var n=this,t=this.selectpicker.main.data,s=[],o=0;!n.options.showTick&&!n.multiple||W.checkMark.parentNode||(W.checkMark.className=this.options.iconBase+" "+n.options.tickIcon+" check-mark",W.a.appendChild(W.checkMark));for(var e=t.length,i=0;i<e;i++){!function(t){var e,i=0;switch(t.type){case"divider":e=V.li(!1,R.DIVIDER,t.optID?t.optID+"div":void 0);break;case"option":(e=V.li(V.a(V.text.call(n,t),t.optionClass,t.inlineStyle),"",t.optID)).firstChild&&(e.firstChild.id=n.selectId+"-"+t.index);break;case"optgroup-label":e=V.li(V.label.call(n,t),"dropdown-header"+t.optgroupClass,t.optID)}t.element=e,s.push(e),t.display&&(i+=t.display.length),t.subtext&&(i+=t.subtext.length),t.icon&&(i+=1),o<i&&(o=i,n.selectpicker.view.widestOption=s[s.length-1])}(t[i])}this.selectpicker.main.elements=this.selectpicker.current.elements=s},findLis:function(){return this.$menuInner.find(".inner > li")},render:function(){var t,e,i,n=this.$element[0],s=this.setPlaceholder()&&0===n.selectedIndex,o=D(n,this.options.hideDisabled),r=o.length,a=this.$button[0],l=a.querySelector(".filter-option-inner-inner"),c=document.createTextNode(this.options.multipleSeparator),h=W.fragment.cloneNode(!1),d=!1;if(a.classList.toggle("bs-placeholder",this.multiple?!r:!A(n,o)),this.multiple||1!==o.length||(this.selectpicker.view.displayedValue=A(n,o)),"static"===this.options.selectedTextFormat)h=V.text.call(this,{text:this.options.title},!0);else if(!1===(this.multiple&&-1!==this.options.selectedTextFormat.indexOf("count")&&1<r&&(1<(t=this.options.selectedTextFormat.split(">")).length&&r>t[1]||1===t.length&&2<=r))){if(!s){for(var u=0;u<r&&u<50;u++){var p=o[u],f=this.selectpicker.main.data[p.liIndex],m={};this.multiple&&0<u&&h.appendChild(c.cloneNode(!1)),p.title?m.text=p.title:f&&(f.content&&this.options.showContent?(m.content=f.content.toString(),d=!0):(this.options.showIcon&&(m.icon=f.icon),this.options.showSubtext&&!this.multiple&&f.subtext&&(m.subtext=" "+f.subtext),m.text=p.textContent.trim())),h.appendChild(V.text.call(this,m,!0))}49<r&&h.appendChild(document.createTextNode("..."))}}else{var g=':not([hidden]):not([data-hidden="true"]):not([data-divider="true"])';this.options.hideDisabled&&(g+=":not(:disabled)");var v=this.$element[0].querySelectorAll("select > option"+g+", optgroup"+g+" option"+g).length,y="function"==typeof this.options.countSelectedText?this.options.countSelectedText(r,v):this.options.countSelectedText,h=V.text.call(this,{text:y.replace("{0}",r.toString()).replace("{1}",v.toString())},!0)}null==this.options.title&&(this.options.title=this.$element.attr("title")),h.childNodes.length||(h=V.text.call(this,{text:void 0!==this.options.title?this.options.title:this.options.noneSelectedText},!0)),a.title=h.textContent.replace(/<[^>]*>?/g,"").trim(),this.options.sanitize&&d&&j([h],this.options.whiteList,this.options.sanitizeFn),l.innerHTML="",l.appendChild(h),F.major<4&&this.$newElement[0].classList.contains("bs3-has-addon")&&(e=a.querySelector(".filter-expand"),(i=l.cloneNode(!0)).className="filter-expand",e?a.replaceChild(i,e):a.appendChild(i)),this.$element.trigger("rendered"+U)},setStyle:function(t,e){var i,n=this.$button[0],s=this.$newElement[0],o=this.options.style.trim();this.$element.attr("class")&&this.$newElement.addClass(this.$element.attr("class").replace(/selectpicker|mobile-device|bs-select-hidden|validate\[.*\]/gi,"")),F.major<4&&(s.classList.add("bs3"),s.parentNode.classList&&s.parentNode.classList.contains("input-group")&&(s.previousElementSibling||s.nextElementSibling)&&(s.previousElementSibling||s.nextElementSibling).classList.contains("input-group-addon")&&s.classList.add("bs3-has-addon")),i=t?t.trim():o,"add"==e?i&&n.classList.add.apply(n.classList,i.split(" ")):"remove"==e?i&&n.classList.remove.apply(n.classList,i.split(" ")):(o&&n.classList.remove.apply(n.classList,o.split(" ")),i&&n.classList.add.apply(n.classList,i.split(" ")))},liHeight:function(t){if(t||!1!==this.options.size&&!Object.keys(this.sizeInfo).length){var e,i,n=W.div.cloneNode(!1),s=W.div.cloneNode(!1),o=W.div.cloneNode(!1),r=document.createElement("ul"),a=W.li.cloneNode(!1),l=W.li.cloneNode(!1),c=W.a.cloneNode(!1),h=W.span.cloneNode(!1),d=this.options.header&&0<this.$menu.find("."+R.POPOVERHEADER).length?this.$menu.find("."+R.POPOVERHEADER)[0].cloneNode(!0):null,u=this.options.liveSearch?W.div.cloneNode(!1):null,p=this.options.actionsBox&&this.multiple&&0<this.$menu.find(".bs-actionsbox").length?this.$menu.find(".bs-actionsbox")[0].cloneNode(!0):null,f=this.options.doneButton&&this.multiple&&0<this.$menu.find(".bs-donebutton").length?this.$menu.find(".bs-donebutton")[0].cloneNode(!0):null,m=this.$element.find("option")[0];if(this.sizeInfo.selectWidth=this.$newElement[0].offsetWidth,h.className="text",c.className="dropdown-item "+(m?m.className:""),n.className=this.$menu[0].parentNode.className+" "+R.SHOW,n.style.width=0,"auto"===this.options.width&&(s.style.minWidth=0),s.className=R.MENU+" "+R.SHOW,o.className="inner "+R.SHOW,r.className=R.MENU+" inner "+("4"===F.major?R.SHOW:""),a.className=R.DIVIDER,l.className="dropdown-header",h.appendChild(document.createTextNode("​")),this.selectpicker.current.data.length)for(var g=0;g<this.selectpicker.current.data.length;g++){var v=this.selectpicker.current.data[g];if("option"===v.type){e=v.element;break}}else e=W.li.cloneNode(!1),c.appendChild(h),e.appendChild(c);l.appendChild(h.cloneNode(!0)),this.selectpicker.view.widestOption&&r.appendChild(this.selectpicker.view.widestOption.cloneNode(!0)),r.appendChild(e),r.appendChild(a),r.appendChild(l),d&&s.appendChild(d),u&&(i=document.createElement("input"),u.className="bs-searchbox",i.className="form-control",u.appendChild(i),s.appendChild(u)),p&&s.appendChild(p),o.appendChild(r),s.appendChild(o),f&&s.appendChild(f),n.appendChild(s),document.body.appendChild(n);var y,b=e.offsetHeight,w=l?l.offsetHeight:0,_=d?d.offsetHeight:0,x=u?u.offsetHeight:0,C=p?p.offsetHeight:0,k=f?f.offsetHeight:0,$=L(a).outerHeight(!0),T=!!window.getComputedStyle&&window.getComputedStyle(s),E=s.offsetWidth,D=T?null:L(s),A={vert:I(T?T.paddingTop:D.css("paddingTop"))+I(T?T.paddingBottom:D.css("paddingBottom"))+I(T?T.borderTopWidth:D.css("borderTopWidth"))+I(T?T.borderBottomWidth:D.css("borderBottomWidth")),horiz:I(T?T.paddingLeft:D.css("paddingLeft"))+I(T?T.paddingRight:D.css("paddingRight"))+I(T?T.borderLeftWidth:D.css("borderLeftWidth"))+I(T?T.borderRightWidth:D.css("borderRightWidth"))},S={vert:A.vert+I(T?T.marginTop:D.css("marginTop"))+I(T?T.marginBottom:D.css("marginBottom"))+2,horiz:A.horiz+I(T?T.marginLeft:D.css("marginLeft"))+I(T?T.marginRight:D.css("marginRight"))+2};o.style.overflowY="scroll",y=s.offsetWidth-E,document.body.removeChild(n),this.sizeInfo.liHeight=b,this.sizeInfo.dropdownHeaderHeight=w,this.sizeInfo.headerHeight=_,this.sizeInfo.searchHeight=x,this.sizeInfo.actionsHeight=C,this.sizeInfo.doneButtonHeight=k,this.sizeInfo.dividerHeight=$,this.sizeInfo.menuPadding=A,this.sizeInfo.menuExtras=S,this.sizeInfo.menuWidth=E,this.sizeInfo.menuInnerInnerWidth=E-A.horiz,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth,this.sizeInfo.scrollBarWidth=y,this.sizeInfo.selectHeight=this.$newElement[0].offsetHeight,this.setPositionData()}},getSelectPosition:function(){var t,e=L(window),i=this.$newElement.offset(),n=L(this.options.container);this.options.container&&n.length&&!n.is("body")?((t=n.offset()).top+=parseInt(n.css("borderTopWidth")),t.left+=parseInt(n.css("borderLeftWidth"))):t={top:0,left:0};var s=this.options.windowPadding;this.sizeInfo.selectOffsetTop=i.top-t.top-e.scrollTop(),this.sizeInfo.selectOffsetBot=e.height()-this.sizeInfo.selectOffsetTop-this.sizeInfo.selectHeight-t.top-s[2],this.sizeInfo.selectOffsetLeft=i.left-t.left-e.scrollLeft(),this.sizeInfo.selectOffsetRight=e.width()-this.sizeInfo.selectOffsetLeft-this.sizeInfo.selectWidth-t.left-s[1],this.sizeInfo.selectOffsetTop-=s[0],this.sizeInfo.selectOffsetLeft-=s[3]},setMenuSize:function(){this.getSelectPosition();var t,e,i,n,s,o,r,a,l=this.sizeInfo.selectWidth,c=this.sizeInfo.liHeight,h=this.sizeInfo.headerHeight,d=this.sizeInfo.searchHeight,u=this.sizeInfo.actionsHeight,p=this.sizeInfo.doneButtonHeight,f=this.sizeInfo.dividerHeight,m=this.sizeInfo.menuPadding,g=0;if(this.options.dropupAuto&&(r=c*this.selectpicker.current.elements.length+m.vert,a=this.sizeInfo.selectOffsetTop-this.sizeInfo.selectOffsetBot>this.sizeInfo.menuExtras.vert&&r+this.sizeInfo.menuExtras.vert+50>this.sizeInfo.selectOffsetBot,!0===this.selectpicker.isSearching&&(a=this.selectpicker.dropup),this.$newElement.toggleClass(R.DROPUP,a),this.selectpicker.dropup=a),"auto"===this.options.size)n=3<this.selectpicker.current.elements.length?3*this.sizeInfo.liHeight+this.sizeInfo.menuExtras.vert-2:0,e=this.sizeInfo.selectOffsetBot-this.sizeInfo.menuExtras.vert,i=n+h+d+u+p,o=Math.max(n-m.vert,0),this.$newElement.hasClass(R.DROPUP)&&(e=this.sizeInfo.selectOffsetTop-this.sizeInfo.menuExtras.vert),t=(s=e)-h-d-u-p-m.vert;else if(this.options.size&&"auto"!=this.options.size&&this.selectpicker.current.elements.length>this.options.size){for(var v=0;v<this.options.size;v++)"divider"===this.selectpicker.current.data[v].type&&g++;t=(e=c*this.options.size+g*f+m.vert)-m.vert,s=e+h+d+u+p,i=o=""}this.$menu.css({"max-height":s+"px",overflow:"hidden","min-height":i+"px"}),this.$menuInner.css({"max-height":t+"px","overflow-y":"auto","min-height":o+"px"}),this.sizeInfo.menuInnerHeight=Math.max(t,1),this.selectpicker.current.data.length&&this.selectpicker.current.data[this.selectpicker.current.data.length-1].position>this.sizeInfo.menuInnerHeight&&(this.sizeInfo.hasScrollBar=!0,this.sizeInfo.totalMenuWidth=this.sizeInfo.menuWidth+this.sizeInfo.scrollBarWidth),"auto"===this.options.dropdownAlignRight&&this.$menu.toggleClass(R.MENURIGHT,this.sizeInfo.selectOffsetLeft>this.sizeInfo.selectOffsetRight&&this.sizeInfo.selectOffsetRight<this.sizeInfo.totalMenuWidth-l),this.dropdown&&this.dropdown._popper&&this.dropdown._popper.update()},setSize:function(t){var e,i;this.liHeight(t),this.options.header&&this.$menu.css("padding-top",0),!1!==this.options.size&&(e=this,i=L(window),this.setMenuSize(),this.options.liveSearch&&this.$searchbox.off("input.setMenuSize propertychange.setMenuSize").on("input.setMenuSize propertychange.setMenuSize",function(){return e.setMenuSize()}),"auto"===this.options.size?i.off("resize"+U+"."+this.selectId+".setMenuSize scroll"+U+"."+this.selectId+".setMenuSize").on("resize"+U+"."+this.selectId+".setMenuSize scroll"+U+"."+this.selectId+".setMenuSize",function(){return e.setMenuSize()}):this.options.size&&"auto"!=this.options.size&&this.selectpicker.current.elements.length>this.options.size&&i.off("resize"+U+"."+this.selectId+".setMenuSize scroll"+U+"."+this.selectId+".setMenuSize")),this.createView(!1,!0,t)},setWidth:function(){var i=this;"auto"===this.options.width?requestAnimationFrame(function(){i.$menu.css("min-width","0"),i.$element.on("loaded"+U,function(){i.liHeight(),i.setMenuSize();var t=i.$newElement.clone().appendTo("body"),e=t.css("width","auto").children("button").outerWidth();t.remove(),i.sizeInfo.selectWidth=Math.max(i.sizeInfo.totalMenuWidth,e),i.$newElement.css("width",i.sizeInfo.selectWidth+"px")})}):"fit"===this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width","").addClass("fit-width")):this.options.width?(this.$menu.css("min-width",""),this.$newElement.css("width",this.options.width)):(this.$menu.css("min-width",""),this.$newElement.css("width","")),this.$newElement.hasClass("fit-width")&&"fit"!==this.options.width&&this.$newElement[0].classList.remove("fit-width")},selectPosition:function(){this.$bsContainer=L('<div class="bs-container" />');function t(t){var e={},i=r.options.display||!!L.fn.dropdown.Constructor.Default&&L.fn.dropdown.Constructor.Default.display;r.$bsContainer.addClass(t.attr("class").replace(/form-control|fit-width/gi,"")).toggleClass(R.DROPUP,t.hasClass(R.DROPUP)),n=t.offset(),a.is("body")?s={top:0,left:0}:((s=a.offset()).top+=parseInt(a.css("borderTopWidth"))-a.scrollTop(),s.left+=parseInt(a.css("borderLeftWidth"))-a.scrollLeft()),o=t.hasClass(R.DROPUP)?0:t[0].offsetHeight,(F.major<4||"static"===i)&&(e.top=n.top-s.top+o,e.left=n.left-s.left),e.width=t[0].offsetWidth,r.$bsContainer.css(e)}var n,s,o,r=this,a=L(this.options.container);this.$button.on("click.bs.dropdown.data-api",function(){r.isDisabled()||(t(r.$newElement),r.$bsContainer.appendTo(r.options.container).toggleClass(R.SHOW,!r.$button.hasClass(R.SHOW)).append(r.$menu))}),L(window).off("resize"+U+"."+this.selectId+" scroll"+U+"."+this.selectId).on("resize"+U+"."+this.selectId+" scroll"+U+"."+this.selectId,function(){r.$newElement.hasClass(R.SHOW)&&t(r.$newElement)}),this.$element.on("hide"+U,function(){r.$menu.data("height",r.$menu.height()),r.$bsContainer.detach()})},setOptionStatus:function(t){if(this.noScroll=!1,this.selectpicker.view.visibleElements&&this.selectpicker.view.visibleElements.length)for(var e=0;e<this.selectpicker.view.visibleElements.length;e++){var i=this.selectpicker.current.data[e+this.selectpicker.view.position0],n=i.option;n&&(!0!==t&&this.setDisabled(i.index,i.disabled),this.setSelected(i.index,n.selected))}},setSelected:function(t,e){var i,n,s=this.selectpicker.main.elements[t],o=this.selectpicker.main.data[t],r=void 0!==this.activeIndex,a=this.activeIndex===t||e&&!this.multiple&&!r;o.selected=e,n=s.firstChild,e&&(this.selectedIndex=t),s.classList.toggle("selected",e),a?(this.focusItem(s,o),this.selectpicker.view.currentActive=s,this.activeIndex=t):this.defocusItem(s),n&&(n.classList.toggle("selected",e),e?n.setAttribute("aria-selected",!0):this.multiple?n.setAttribute("aria-selected",!1):n.removeAttribute("aria-selected")),a||r||!e||void 0===this.prevActiveIndex||(i=this.selectpicker.main.elements[this.prevActiveIndex],this.defocusItem(i))},setDisabled:function(t,e){var i,n=this.selectpicker.main.elements[t];this.selectpicker.main.data[t].disabled=e,i=n.firstChild,n.classList.toggle(R.DISABLED,e),i&&("4"===F.major&&i.classList.toggle(R.DISABLED,e),e?(i.setAttribute("aria-disabled",e),i.setAttribute("tabindex",-1)):(i.removeAttribute("aria-disabled"),i.setAttribute("tabindex",0)))},isDisabled:function(){return this.$element[0].disabled},checkDisabled:function(){this.isDisabled()?(this.$newElement[0].classList.add(R.DISABLED),this.$button.addClass(R.DISABLED).attr("aria-disabled",!0)):this.$button[0].classList.contains(R.DISABLED)&&(this.$newElement[0].classList.remove(R.DISABLED),this.$button.removeClass(R.DISABLED).attr("aria-disabled",!1))},clickListener:function(){var E=this,e=L(document);function t(){E.options.liveSearch?E.$searchbox.trigger("focus"):E.$menuInner.trigger("focus")}function i(){E.dropdown&&E.dropdown._popper&&E.dropdown._popper.state.isCreated?t():requestAnimationFrame(i)}e.data("spaceSelect",!1),this.$button.on("keyup",function(t){/(32)/.test(t.keyCode.toString(10))&&e.data("spaceSelect")&&(t.preventDefault(),e.data("spaceSelect",!1))}),this.$newElement.on("show.bs.dropdown",function(){3<F.major&&!E.dropdown&&(E.dropdown=E.$button.data("bs.dropdown"),E.dropdown._menu=E.$menu[0])}),this.$button.on("click.bs.dropdown.data-api",function(){E.$newElement.hasClass(R.SHOW)||E.setSize()}),this.$element.on("shown"+U,function(){E.$menuInner[0].scrollTop!==E.selectpicker.view.scrollTop&&(E.$menuInner[0].scrollTop=E.selectpicker.view.scrollTop),3<F.major?requestAnimationFrame(i):t()}),this.$menuInner.on("mouseenter","li a",function(t){var e=this.parentElement,i=E.isVirtual()?E.selectpicker.view.position0:0,n=Array.prototype.indexOf.call(e.parentElement.children,e),s=E.selectpicker.current.data[n+i];E.focusItem(e,s,!0)}),this.$menuInner.on("click","li a",function(t,e){var i=L(this),n=E.$element[0],s=E.isVirtual()?E.selectpicker.view.position0:0,o=E.selectpicker.current.data[i.parent().index()+s],r=o.index,a=A(n),l=n.selectedIndex,c=n.options[l],h=!0;if(E.multiple&&1!==E.options.maxOptions&&t.stopPropagation(),t.preventDefault(),!E.isDisabled()&&!i.parent().hasClass(R.DISABLED)){var d=o.option,u=L(d),p=d.selected,f=u.parent("optgroup"),m=f.find("option"),g=E.options.maxOptions,v=f.data("maxOptions")||!1;if(r===E.activeIndex&&(e=!0),e||(E.prevActiveIndex=E.activeIndex,E.activeIndex=void 0),E.multiple){if(d.selected=!p,E.setSelected(r,!p),i.trigger("blur"),!1!==g||!1!==v){var y=g<D(n).length,b=v<f.find("option:selected").length;if(g&&y||v&&b)if(g&&1==g)n.selectedIndex=-1,d.selected=!0,E.setOptionStatus(!0);else if(v&&1==v){for(var w=0;w<m.length;w++){var _=m[w];_.selected=!1,E.setSelected(_.liIndex,!1)}d.selected=!0,E.setSelected(r,!0)}else{var x="string"==typeof E.options.maxOptionsText?[E.options.maxOptionsText,E.options.maxOptionsText]:E.options.maxOptionsText,C="function"==typeof x?x(g,v):x,k=C[0].replace("{n}",g),$=C[1].replace("{n}",v),T=L('<div class="notify"></div>');C[2]&&(k=k.replace("{var}",C[2][1<g?0:1]),$=$.replace("{var}",C[2][1<v?0:1])),d.selected=!1,E.$menu.append(T),g&&y&&(T.append(L("<div>"+k+"</div>")),h=!1,E.$element.trigger("maxReached"+U)),v&&b&&(T.append(L("<div>"+$+"</div>")),h=!1,E.$element.trigger("maxReachedGrp"+U)),setTimeout(function(){E.setSelected(r,!1)},10),T[0].classList.add("fadeOut"),setTimeout(function(){T.remove()},1050)}}}else c&&(c.selected=!1),d.selected=!0,E.setSelected(r,!0);!E.multiple||E.multiple&&1===E.options.maxOptions?E.$button.trigger("focus"):E.options.liveSearch&&E.$searchbox.trigger("focus"),h&&(!E.multiple&&l===n.selectedIndex||(S=[d.index,u.prop("selected"),a],E.$element.triggerNative("change")))}}),this.$menu.on("click","li."+R.DISABLED+" a, ."+R.POPOVERHEADER+", ."+R.POPOVERHEADER+" :not(.close)",function(t){t.currentTarget==this&&(t.preventDefault(),t.stopPropagation(),E.options.liveSearch&&!L(t.target).hasClass("close")?E.$searchbox.trigger("focus"):E.$button.trigger("focus"))}),this.$menuInner.on("click",".divider, .dropdown-header",function(t){t.preventDefault(),t.stopPropagation(),E.options.liveSearch?E.$searchbox.trigger("focus"):E.$button.trigger("focus")}),this.$menu.on("click","."+R.POPOVERHEADER+" .close",function(){E.$button.trigger("click")}),this.$searchbox.on("click",function(t){t.stopPropagation()}),this.$menu.on("click",".actions-btn",function(t){E.options.liveSearch?E.$searchbox.trigger("focus"):E.$button.trigger("focus"),t.preventDefault(),t.stopPropagation(),L(this).hasClass("bs-select-all")?E.selectAll():E.deselectAll()}),this.$button.on("focus"+U,function(t){var e=E.$element[0].getAttribute("tabindex");void 0!==e&&t.originalEvent&&t.originalEvent.isTrusted&&(this.setAttribute("tabindex",e),E.$element[0].setAttribute("tabindex",-1),E.selectpicker.view.tabindex=e)}).on("blur"+U,function(t){void 0!==E.selectpicker.view.tabindex&&t.originalEvent&&t.originalEvent.isTrusted&&(E.$element[0].setAttribute("tabindex",E.selectpicker.view.tabindex),this.setAttribute("tabindex",-1),E.selectpicker.view.tabindex=void 0)}),this.$element.on("change"+U,function(){E.render(),E.$element.trigger("changed"+U,S),S=null}).on("focus"+U,function(){E.options.mobile||E.$button.trigger("focus")})},liveSearchListener:function(){var p=this;this.$button.on("click.bs.dropdown.data-api",function(){p.$searchbox.val()&&p.$searchbox.val("")}),this.$searchbox.on("click.bs.dropdown.data-api focus.bs.dropdown.data-api touchend.bs.dropdown.data-api",function(t){t.stopPropagation()}),this.$searchbox.on("input propertychange",function(){var t=p.$searchbox.val();if(p.selectpicker.search.elements=[],p.selectpicker.search.data=[],t){var e=[],i=t.toUpperCase(),n={},s=[],o=p._searchStyle(),r=p.options.liveSearchNormalize;r&&(i=m(i));for(var a=0;a<p.selectpicker.main.data.length;a++){var l=p.selectpicker.main.data[a];n[a]||(n[a]=x(l,i,o,r)),n[a]&&void 0!==l.headerIndex&&-1===s.indexOf(l.headerIndex)&&(0<l.headerIndex&&(n[l.headerIndex-1]=!0,s.push(l.headerIndex-1)),n[l.headerIndex]=!0,s.push(l.headerIndex),n[l.lastIndex+1]=!0),n[a]&&"optgroup-label"!==l.type&&s.push(a)}for(var a=0,c=s.length;a<c;a++){var h=s[a],d=s[a-1],l=p.selectpicker.main.data[h],u=p.selectpicker.main.data[d];("divider"!==l.type||"divider"===l.type&&u&&"divider"!==u.type&&c-1!==a)&&(p.selectpicker.search.data.push(l),e.push(p.selectpicker.main.elements[h]))}p.activeIndex=void 0,p.noScroll=!0,p.$menuInner.scrollTop(0),p.selectpicker.search.elements=e,p.createView(!0),function(t,e){t.length||(W.noResults.innerHTML=this.options.noneResultsText.replace("{0}",'"'+k(e)+'"'),this.$menuInner[0].firstChild.appendChild(W.noResults))}.call(p,e,t)}else p.$menuInner.scrollTop(0),p.createView(!1)})},_searchStyle:function(){return this.options.liveSearchStyle||"contains"},val:function(t){var e=this.$element[0];if(void 0===t)return this.$element.val();var i,n=A(e);return S=[null,null,n],this.$element.val(t).trigger("changed"+U,S),this.$newElement.hasClass(R.SHOW)&&(this.multiple?this.setOptionStatus(!0):"number"==typeof(i=(e.options[e.selectedIndex]||{}).liIndex)&&(this.setSelected(this.selectedIndex,!1),this.setSelected(i,!0))),this.render(),S=null,this.$element},changeAll:function(t){if(this.multiple){void 0===t&&(t=!0);var e=this.$element[0],i=0,n=0,s=A(e);e.classList.add("bs-select-hidden");for(var o=0,r=this.selectpicker.current.data,a=r.length;o<a;o++){var l=r[o],c=l.option;c&&!l.disabled&&"divider"!==l.type&&(l.selected&&i++,!0===(c.selected=t)&&n++)}e.classList.remove("bs-select-hidden"),i!==n&&(this.setOptionStatus(),S=[null,null,s],this.$element.triggerNative("change"))}},selectAll:function(){return this.changeAll(!0)},deselectAll:function(){return this.changeAll(!1)},toggle:function(t){(t=t||window.event)&&t.stopPropagation(),this.$button.trigger("click.bs.dropdown.data-api")},keydown:function(t){var e,i,n,s,o,r=L(this),a=r.hasClass("dropdown-toggle"),l=(a?r.closest(".dropdown"):r.closest(z.MENU)).data("this"),c=l.findLis(),h=!1,d=t.which===P&&!a&&!l.options.selectOnTab,u=q.test(t.which)||d,p=l.$menuInner[0].scrollTop,f=!0===l.isVirtual()?l.selectpicker.view.position0:0;if(!(112<=t.which&&t.which<=123))if(!(i=l.$newElement.hasClass(R.SHOW))&&(u||48<=t.which&&t.which<=57||96<=t.which&&t.which<=105||65<=t.which&&t.which<=90)&&(l.$button.trigger("click.bs.dropdown.data-api"),l.options.liveSearch))l.$searchbox.trigger("focus");else{if(t.which===E&&i&&(t.preventDefault(),l.$button.trigger("click.bs.dropdown.data-api").trigger("focus")),u){if(!c.length)return;-1!==(e=(n=l.selectpicker.main.elements[l.activeIndex])?Array.prototype.indexOf.call(n.parentElement.children,n):-1)&&l.defocusItem(n),t.which===M?(-1!==e&&e--,e+f<0&&(e+=c.length),l.selectpicker.view.canHighlight[e+f]||-1===(e=l.selectpicker.view.canHighlight.slice(0,e+f).lastIndexOf(!0)-f)&&(e=c.length-1)):t.which!==B&&!d||(++e+f>=l.selectpicker.view.canHighlight.length&&(e=0),l.selectpicker.view.canHighlight[e+f]||(e=e+1+l.selectpicker.view.canHighlight.slice(e+f+1).indexOf(!0))),t.preventDefault();var m=f+e;t.which===M?0===f&&e===c.length-1?(l.$menuInner[0].scrollTop=l.$menuInner[0].scrollHeight,m=l.selectpicker.current.elements.length-1):h=(o=(s=l.selectpicker.current.data[m]).position-s.height)<p:t.which!==B&&!d||(0===e?m=l.$menuInner[0].scrollTop=0:h=p<(o=(s=l.selectpicker.current.data[m]).position-l.sizeInfo.menuInnerHeight)),n=l.selectpicker.current.elements[m],l.activeIndex=l.selectpicker.current.data[m].index,l.focusItem(n),l.selectpicker.view.currentActive=n,h&&(l.$menuInner[0].scrollTop=o),l.options.liveSearch?l.$searchbox.trigger("focus"):r.trigger("focus")}else if(!r.is("input")&&!Y.test(t.which)||t.which===N&&l.selectpicker.keydown.keyHistory){var g,v,y=[];t.preventDefault(),l.selectpicker.keydown.keyHistory+=T[t.which],l.selectpicker.keydown.resetKeyHistory.cancel&&clearTimeout(l.selectpicker.keydown.resetKeyHistory.cancel),l.selectpicker.keydown.resetKeyHistory.cancel=l.selectpicker.keydown.resetKeyHistory.start(),v=l.selectpicker.keydown.keyHistory,/^(.)\1+$/.test(v)&&(v=v.charAt(0));for(var b,w=0;w<l.selectpicker.current.data.length;w++){var _=l.selectpicker.current.data[w];x(_,v,"startsWith",!0)&&l.selectpicker.view.canHighlight[w]&&y.push(_.index)}y.length&&(b=0,c.removeClass("active").find("a").removeClass("active"),1===v.length&&(-1===(b=y.indexOf(l.activeIndex))||b===y.length-1?b=0:b++),g=y[b],h=0<p-(s=l.selectpicker.main.data[g]).position?(o=s.position-s.height,!0):(o=s.position-l.sizeInfo.menuInnerHeight,s.position>p+l.sizeInfo.menuInnerHeight),n=l.selectpicker.main.elements[g],l.activeIndex=y[b],l.focusItem(n),n&&n.firstChild.focus(),h&&(l.$menuInner[0].scrollTop=o),r.trigger("focus"))}i&&(t.which===N&&!l.selectpicker.keydown.keyHistory||t.which===O||t.which===P&&l.options.selectOnTab)&&(t.which!==N&&t.preventDefault(),l.options.liveSearch&&t.which===N||(l.$menuInner.find(".active a").trigger("click",!0),r.trigger("focus"),l.options.liveSearch||(t.preventDefault(),L(document).data("spaceSelect",!0))))}},mobile:function(){this.options.mobile=!0,this.$element[0].classList.add("mobile-device")},refresh:function(){var t=L.extend({},this.options,this.$element.data());this.options=t,this.checkDisabled(),this.buildData(),this.setStyle(),this.render(),this.buildList(),this.setWidth(),this.setSize(!0),this.$element.trigger("refreshed"+U)},hide:function(){this.$newElement.hide()},show:function(){this.$newElement.show()},remove:function(){this.$newElement.remove(),this.$element.remove()},destroy:function(){this.$newElement.before(this.$element).remove(),this.$bsContainer?this.$bsContainer.remove():this.$menu.remove(),this.$element.off(U).removeData("selectpicker").removeClass("bs-select-hidden selectpicker"),L(window).off(U+"."+this.selectId)}};var Q=L.fn.selectpicker;function X(){if(L.fn.dropdown)return(L.fn.dropdown.Constructor._dataApiKeydownHandler||L.fn.dropdown.Constructor.prototype.keydown).apply(this,arguments)}L.fn.selectpicker=K,L.fn.selectpicker.Constructor=G,L.fn.selectpicker.noConflict=function(){return L.fn.selectpicker=Q,this},L(document).off("keydown.bs.dropdown.data-api").on("keydown.bs.dropdown.data-api",':not(.bootstrap-select) > [data-toggle="dropdown"]',X).on("keydown.bs.dropdown.data-api",":not(.bootstrap-select) > .dropdown-menu",X).on("keydown"+U,'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',G.prototype.keydown).on("focusin.modal",'.bootstrap-select [data-toggle="dropdown"], .bootstrap-select [role="listbox"], .bootstrap-select .bs-searchbox input',function(t){t.stopPropagation()}),L(window).on("load"+U+".data-api",function(){L(".selectpicker").each(function(){var t=L(this);K.call(t,t.data())})})}(jQuery),jQuery.fn.selectpicker.defaults={noneSelectedText:"Nessuna selezione",noneResultsText:"Nessun risultato per {0}",countSelectedText:function(t){return 1==t?"Selezionato {0} di {1}":"Selezionati {0} di {1}"},maxOptionsText:["Limite raggiunto ({n} {var} max)","Limite del gruppo raggiunto ({n} {var} max)",["elementi","elemento"]],multipleSeparator:", ",selectAllText:"Seleziona Tutto",deselectAllText:"Deseleziona Tutto"},function(){"use strict";var y,t,b,w,_,e,i;"undefined"!=typeof window&&window.addEventListener&&(y=Object.create(null),b=function(){clearTimeout(t),t=setTimeout(e,100)},w=function(){},_="http://www.w3.org/1999/xlink",e=function(){var t,e,i,n,s,o,r,a,l,c,h,d,u,p=0;function f(){var t;0===--p&&(w(),window.addEventListener("resize",b,!1),window.addEventListener("orientationchange",b,!1),w=window.MutationObserver?((t=new MutationObserver(b)).observe(document.documentElement,{childList:!0,subtree:!0,attributes:!0}),function(){try{t.disconnect(),window.removeEventListener("resize",b,!1),window.removeEventListener("orientationchange",b,!1)}catch(t){}}):(document.documentElement.addEventListener("DOMSubtreeModified",b,!1),function(){document.documentElement.removeEventListener("DOMSubtreeModified",b,!1),window.removeEventListener("resize",b,!1),window.removeEventListener("orientationchange",b,!1)}))}function m(t){return function(){!0!==y[t.base]&&(t.useEl.setAttributeNS(_,"xlink:href","#"+t.hash),t.useEl.hasAttribute("href")&&t.useEl.setAttribute("href","#"+t.hash))}}function g(t){return function(){t.onerror=null,t.ontimeout=null,f()}}for(w(),a=document.getElementsByTagName("use"),s=0;s<a.length;s+=1){try{e=a[s].getBoundingClientRect()}catch(t){e=!1}t=(r=(n=a[s].getAttribute("href")||a[s].getAttributeNS(_,"href")||a[s].getAttribute("xlink:href"))&&n.split?n.split("#"):["",""])[0],i=r[1],o=e&&0===e.left&&0===e.right&&0===e.top&&0===e.bottom,e&&0===e.width&&0===e.height&&!o?(a[s].hasAttribute("href")&&a[s].setAttributeNS(_,"xlink:href",n),t.length&&(!0!==(l=y[t])&&setTimeout(m({useEl:a[s],base:t,hash:i}),0),void 0===l&&(c=t,u=d=h=void 0,window.XMLHttpRequest&&(h=new XMLHttpRequest,d=v(location),u=v(c),h=void 0===h.withCredentials&&""!==u&&u!==d?XDomainRequest||void 0:XMLHttpRequest),void 0!==h&&(l=new h,(y[t]=l).onload=function(n){return function(){var t,e=document.body,i=document.createElement("x");n.onload=null,i.innerHTML=n.responseText,(t=i.getElementsByTagName("svg")[0])&&(t.setAttribute("aria-hidden","true"),t.style.position="absolute",t.style.width=0,t.style.height=0,t.style.overflow="hidden",e.insertBefore(t,e.firstChild)),f()}}(l),l.onerror=g(l),l.ontimeout=g(l),l.open("GET",t),l.send(),p+=1)))):o?t.length&&y[t]&&setTimeout(m({useEl:a[s],base:t,hash:i}),0):void 0===y[t]?y[t]=!0:y[t].onload&&(y[t].abort(),delete y[t].onload,y[t]=!0)}function v(t){var e;return void 0!==t.protocol?e=t:(e=document.createElement("a")).href=t,e.protocol.replace(/:/g,"")+e.host}a="",p+=1,f()},i=function(){window.removeEventListener("load",i,!1),t=setTimeout(e,0)},"complete"!==document.readyState?window.addEventListener("load",i,!1):i())}(),Array.from||(Array.from=function(){function h(t){return"function"==typeof t||"[object Function]"===e.call(t)}function d(t){var e,i=(e=Number(t),isNaN(e)?0:0!==e&&isFinite(e)?(0<e?1:-1)*Math.floor(Math.abs(e)):e);return Math.min(Math.max(i,0),n)}var e=Object.prototype.toString,n=Math.pow(2,53)-1;return function(t,e,i){var n=Object(t);if(null==t)throw new TypeError("Array.from requires an array-like object - not null or undefined");var s,o=1<arguments.length?e:void 0;if(void 0!==o){if(!h(o))throw new TypeError("Array.from: when provided, the second argument must be a function");2<arguments.length&&(s=i)}for(var r,a=d(n.length),l=h(this)?Object(new this(a)):new Array(a),c=0;c<a;)r=n[c],l[c]=o?void 0===s?o(r,c):o.call(s,r,c):r,c+=1;return l.length=a,l}}()),function(p){function l(t){return t*Math.PI/180}function f(t,e,i){var n=p("#"+i+"canvas")[0],s=p("#"+i+"canvas"),o=n.getContext("2d"),r=n.width/2,a=n.height/2;o.beginPath(),o.arc(r,a,p(s).attr("data-radius"),0,2*Math.PI,!1),o.fillStyle="transparent",o.fill(),o.lineWidth=p(s).attr("data-width"),o.strokeStyle=p(s).attr("data-progressBarBackground"),o.stroke(),o.closePath(),o.beginPath(),o.arc(r,a,p(s).attr("data-radius"),-l(90),-l(90)+l(t/100*360),!1),o.fillStyle="transparent",o.fill(),o.lineWidth=p(s).attr("data-width"),o.strokeStyle=p(s).attr("data-stroke"),o.stroke(),o.closePath(),"true"==p(s).attr("data-text").toLocaleLowerCase()&&p("#"+i+" .clProg").val(e+("true"==p(s).attr("data-percent").toLocaleLowerCase()?"%":""))}p.fn.circularloader=function(t){var e,i,n,s,o,r,a,l,c,h,d=this[0],u=d.id;return 0==p("#"+u+"canvas").length?(e=p.extend({backgroundColor:"#ffffff",fontColor:"#000000",fontSize:"40px",radius:70,progressBarBackground:"#cdcdcd",progressBarColor:"#aaaaaa",progressBarWidth:25,progressPercent:0,progressValue:0,showText:!0,title:""},t),i=parseInt(e.radius),n=parseInt(e.progressBarWidth),h=parseInt(0<parseInt(e.progressValue)?e.progressValue:e.progressPercent),c=parseInt(e.progressPercent),s="color:"+e.fontColor+";font-size:"+parseInt(e.fontSize)+"px;width:"+2*(i+n)+"px;vertical-align:middle;position:relative;background-color:transparent;border:0 none;transform:translateY(-48%);-webkit-transform: translateY(-48%);-ms-transform: translateY(-48%);height:"+2*(i+n)+"px;margin-left:-"+2*(i+n)+"px;text-align:center;padding:0;"+(e.showText?"":"display:none;"),p('<canvas data-width="'+n+'" data-radius="'+i+'" data-stroke="'+e.progressBarColor+'" data-progressBarBackground="'+e.progressBarBackground+'" data-backgroundColor="'+e.backgroundColor+'" data-text='+e.showText+" data-percent="+(null==t.progressValue)+' id="'+u+'canvas" width='+2*(i+n)+" height="+2*(i+n)+"></canvas>").appendTo(d),p('<input class="clProg" style="'+s+'" value="'+h+(null==t.progressValue?"%":"")+'" aria-hidden="true"></input>').appendTo(d),""==e.title?p("#"+u).css("height",2*(i+n)):(p("#"+u).css("height",2*(i+n)+20),p("#"+u+"canvas").before("<div class='titleCircularLoader' style='height:19px;text-align:center;'>"+e.title+"</div>"),p(".titleCircularLoader").css("width",2*(i+n))),o=p("#"+u+"canvas")[0],r=o.getContext("2d"),a=o.width/2,l=o.height/2,p("#"+u+"canvas").offset().left,p("#"+u+"canvas").offset().top,r.beginPath(),r.arc(a,l,i,0,2*Math.PI,!1),r.fillStyle=e.backgroundColor,r.fill(),r.lineWidth=n,r.strokeStyle=e.progressBarBackground,r.stroke(),r.closePath(),0<c&&f(c,h,u)):null==t.progressPercent&&null==t.progressValue||(h=c=0,c=null==t.progressPercent?100<parseInt(t.progressValue)?100:parseInt(t.progressValue):100<parseInt(t.progressPercent)?100:parseInt(t.progressPercent),h=null==t.progressValue?100<parseInt(t.progressPercent)?100:parseInt(t.progressPercent):parseInt(t.progressValue),f(c,h,u)),this}}(jQuery),function(h){"use strict";function t(r,a){function l(t){return-1===t?"danger":-2===t?"muted":(t=t<0?0:t)<26?"danger":t<51?"warning":"success"}function c(t,e){for(var i="",n=!1,s=0;s<e.length;s++){n=!0;for(var o=0;o<t&&o+s+t<e.length;o++)n=n&&e.charAt(o+s)===e.charAt(o+s+t);o<t&&(n=!1),n?(s+=t-1,n=!1):i+=e.charAt(s)}return i}return a=h.extend({},{shortPass:"Password molto debole",badPass:"Password debole",goodPass:"Password sicura",strongPass:"Password molto sicura",enterPass:"Inserisci almeno 8 caratteri e una lettera maiuscola",showText:!0,minimumLength:4},a),function(){var s=a.showText,t=h("<div>").addClass("password-meter progress rounded-0 position-absolute");t.append('<div class="row position-absolute w-100 m-0">\n        <div class="col-3 border-left border-right border-white"></div>\n        <div class="col-3 border-left border-right border-white"></div>\n        <div class="col-3 border-left border-right border-white"></div>\n        <div class="col-3 border-left border-right border-white"></div>\n      </div>');var o=h("<div>").attr({class:"progress-bar",role:"progressbar","aria-valuenow":"0","aria-valuemin":"0","aria-valuemax":"100"}),e=h("<div>").attr({class:"password-strength-meter"}).append(t.append(o));return a.showText&&(s=h("<small>").addClass("form-text text-muted").html(a.enterPass),e.prepend(s)),r.after(e),r.keyup(function(){var t=function(t){var e=0;if(0===t.trim().length)return-2;if(t.length<a.minimumLength)return-1;e+=4*t.length,e+=c(1,t).length-t.length,e+=c(2,t).length-t.length,e+=c(3,t).length-t.length,e+=c(4,t).length-t.length,t.match(/(.*[0-9].*[0-9].*[0-9])/)&&(e+=5);var i=".*[!,@,#,$,%,^,&,*,?,_,~]",i=new RegExp("("+i+i+")");return t.match(i)&&(e+=5),t.match(/([a-z].*[A-Z])|([A-Z].*[a-z])/)&&(e+=10),t.match(/([a-zA-Z])/)&&t.match(/([0-9])/)&&(e+=15),t.match(/([!,@,#,$,%,^,&,*,?,_,~])/)&&t.match(/([0-9])/)&&(e+=15),t.match(/([!,@,#,$,%,^,&,*,?,_,~])/)&&t.match(/([a-zA-Z])/)&&(e+=15),(t.match(/^\w+$/)||t.match(/^\d+$/))&&(e-=10),100<e&&(e=100),e<0&&(e=0),e}(r.val());r.trigger("password.score",[t]);var e,i,n=t<0?0:t;o.removeClass(function(t,e){return(e.match(/(^|\s)bg-\S+/g)||[]).join(" ")}),o.addClass("bg-"+l(t)),o.css({width:n+"%"}),o.attr("aria-valuenow",n),a.showText&&(e=-1===(i=t)||(i=i<0?0:i)<26?a.shortPass:i<51?a.badPass:i<76?a.goodPass:a.strongPass,!r.val().length&&t<=0&&(e=a.enterPass),s.html()!==h("<div>").html(e).html()&&(s.html(e),s.removeClass(function(t,e){return(e.match(/(^|\s)text-\S+/g)||[]).join(" ")}),s.addClass("text-"+l(t)),r.trigger("password.text",[e,t])))}),this}.call(this)}h.fn.password=function(){return this.each(function(){new t(h(this),h(this).data())})}}(jQuery),$(function(){var i=!1,n=null;$(".input-password").on("keydown",function(t){16==(t.keyCode?t.keyCode:t.which)&&(i=!0)}).on("keyup",function(t){var e=t.keyCode?t.keyCode:t.which;16==e&&(i=!1),20==e&&(n?(n=!1,$(".password-caps").remove()):(n=!0,$("input:focus").each(function(t){showCapsLockMsg($(this))})))}).on("keypress",function(t){var e=t.keyCode?t.keyCode:t.which;65<=e&&e<=90&&!i&&(n=!0,showCapsLockMsg($(this)))}),$(".input-password-strength-meter").password(),$(".password-icon").on("click",function(t){$(this).find('[class^="password-icon"]').toggleClass("d-none");var e=$(this).siblings(".input-password"),i="password"===e.attr("type")?"text":"password";e.attr("type",i)})}),function(){"use strict";void 0===Date.dp_locales&&(Date.dp_locales={texts:{buttonTitle:"Scegli la data ...",buttonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per aprire il calendario",prevButtonLabel:"Vai al mese precedente",nextButtonLabel:"Vai al mese successivo",closeButtonTitle:"Chiudere",closeButtonLabel:"Chiudere il calendario",prevMonthButtonLabel:"Vai all'anno precedente",prevYearButtonLabel:"Vai a vent'anni precedenti",nextMonthButtonLabel:"Vai al prossimo anno",nextYearButtonLabel:"Vai ai prossimi 20 anni",changeMonthButtonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per cambiare il mese",changeYearButtonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per cambiare l'anno",changeRangeButtonLabel:"Fare clic o premere il tasto Invio o la barra spaziatrice per andare ai prossimi 20 anni",calendarHelp:"- Freccia e Freccia giù - va allo stesso giorno della settimana in settimana precedente o successiva, rispettivamente. Se viene raggiunta la fine del mese, continua nel mese precedente o successivo a seconda dei casi.\r\n- Freccia Sinistra e Freccia destra - avanza un giorno all'altro, anche in un continuum. Visivamente fuoco viene spostato da un giorno all'altro e avvolge da riga a riga nella griglia di giorni.\r\n- Control + Pagina Su - Passa alla stessa data dell'anno precedente.\r\n- Control + Pagina giù - Passa alla stessa data nel prossimo anno.\r\n- Home - Passa al primo giorno del mese in corso.\r\n- End - Passa l'ultimo giorno del mese corrente.\r\n- Pagina Su - Passa alla stessa data del mese precedente.\r\n- Pagina giù - Passa alla stessa data del mese successivo.\r\n- Invio o Espace - chiude il calendario e la data selezionata viene visualizzata nella casella di testo associato.\r\n- Escape - chiude il calendario senza alcuna azione."},directionality:"LTR",month_names:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"],month_names_abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],month_names_narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],day_names:["domenica","lunedì","martedì","mercoledì","giovedì","venerdì","sabato"],day_names_abbreviated:["dom","lun","mar","mer","gio","ven","sab"],day_names_short:["dom","lun","mar","mer","gio","ven","sab"],day_names_narrow:["D","L","M","M","G","V","S"],day_periods:{am:"AM",noon:"mezzogiorno",pm:"PM"},day_periods_abbreviated:{am:"AM",noon:"mezzogiorno",pm:"PM"},day_periods_narrow:{am:"m.",noon:"n",pm:"p."},quarter_names:["1º trimestre","2º trimestre","3º trimestre","4º trimestre"],quarter_names_abbreviated:["T1","T2","T3","T4"],quarter_names_narrow:["1","2","3","4"],era_names:["a.C.","d.C."],era_names_abbreviated:["aC","dC"],era_names_narrow:["aC","dC"],full_format:"EEEE d MMMM y",long_format:"d MMMM y",medium_format:"dd MMM y",short_format:"dd/MM/yy",firstday_of_week:1})}(),function(){"use strict";void 0===Date.dp_locales&&(Date.dp_locales={texts:{buttonTitle:"Select date ...",buttonLabel:"Click or press the Enter key or the spacebar to open the calendar",prevButtonLabel:"Go to previous month",prevMonthButtonLabel:"Go to the previous year",prevYearButtonLabel:"Go to the previous twenty years",nextButtonLabel:"Go to next month",nextMonthButtonLabel:"Go to the next year",nextYearButtonLabel:"Go to the next twenty years",changeMonthButtonLabel:"Click or press the Enter key or the spacebar to change the month",changeYearButtonLabel:"Click or press the Enter key or the spacebar to change the year",changeRangeButtonLabel:"Click or press the Enter key or the spacebar to go to the next twenty years",closeButtonTitle:"Close",closeButtonLabel:"Close the calendar",calendarHelp:"- Up Arrow and Down Arrow - goes to the same day of the week in the previous or next week respectively. If the end of the month is reached, continues into the next or previous month as appropriate.\r\n- Left Arrow and Right Arrow - advances one day to the next, also in a continuum. Visually focus is moved from day to day and wraps from row to row in the grid of days.\r\n- Control+Page Up - Moves to the same date in the previous year.\r\n- Control+Page Down - Moves to the same date in the next year.\r\n- Home - Moves to the first day of the current month.\r\n- End - Moves to the last day of the current month.\r\n- Page Up - Moves to the same date in the previous month.\r\n- Page Down - Moves to the same date in the next month.\r\n- Enter or Espace - closes the calendar, and the selected date is shown in the associated text box.\r\n- Escape - closes the calendar without any action."},directionality:"LTR",month_names:["January","February","March","April","May","June","July","August","September","October","November","December"],month_names_abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],month_names_narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],day_names:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],day_names_abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],day_names_short:["Su","Mo","Tu","We","Th","Fr","Sa"],day_names_narrow:["S","M","T","W","T","F","S"],day_periods:{am:"AM",noon:"noon",pm:"PM"},day_periods_abbreviated:{am:"AM",noon:"noon",pm:"PM"},day_periods_narrow:{am:"a",noon:"n",pm:"p"},quarter_names:["1st quarter","2nd quarter","3rd quarter","4th quarter"],quarter_names_abbreviated:["Q1","Q2","Q3","Q4"],quarter_names_narrow:["1","2","3","4"],era_names:["Before Christ","Anno Domini"],era_names_abbreviated:["BC","AD"],era_names_narrow:["B","A"],full_format:"EEEE, MMMM d, y",long_format:"MMMM d, y",medium_format:"MMM d, y",short_format:"M/d/yy",firstday_of_week:0})}(),function(t){if("function"==typeof define&&define.amd)define(["jquery"],t);else if("object"==typeof exports)t(require("jquery"));else{if("undefined"==typeof jQuery)throw new Error("Datepicker's JavaScript requires jQuery");t(jQuery)}}(function(y,d){"use strict";function o(t,e){var n=this;this.$target=y(t),this.options=y.extend({},o.DEFAULTS,e),this.locales=Date.dp_locales,this.startview(this.options.startView),"string"==typeof this.options.inputFormat&&(this.options.inputFormat=[this.options.inputFormat]),y.isArray(this.options.datesDisabled)||(this.options.datesDisabled=[this.options.datesDisabled]),y.each(this.options.datesDisabled,function(t,e){var i;"string"==typeof e?(i=n.parseDate(e),n.options.datesDisabled[t]=null===i?null:n.format(i)):e instanceof Date&&!isNaN(e.valueOf())?n.options.datesDisabled[t]=n.format(e):n.options.datesDisabled[t]=null}),null!=this.options.min?this.options.min=this.parseDate(this.options.min):this.$target.attr("min")&&(this.options.min=this.parseDate(this.$target.attr("min"))),null!=this.options.max?this.options.max=this.parseDate(this.options.max):this.$target.attr("max")&&(this.options.max=this.parseDate(this.$target.attr("max"))),"string"==typeof this.options.previous?this.options.previous=y(this.options.previous):this.options.previous instanceof jQuery||(this.options.previous=null),"string"==typeof this.options.next?this.options.next=y(this.options.next):this.options.next instanceof jQuery||(this.options.next=null),this.id=this.$target.attr("id")||"datepicker-"+Math.floor(1e5*Math.random());var i=(i=a.join("")).replace(/CALENDARID/g,this.id+"");0==this.$target.parent(".input-group").length&&this.$target.wrap('<div class="input-group"></div>'),this.$label=this.$target.parents().find("label[for="+this.id+"]"),this.$group=this.$target.parent(".input-group"),this.$target.attr("aria-autocomplete","none"),this.$target.css("min-width","7em"),this.$target.addClass("form-control"),this.$target.attr("placeholder")||this.$target.attr("placeholder",this.options.inputFormat[0]);var s=(s=r.join("")).replace(/CALENDARID/g,this.id+"");this.$button=y(s),this.$button.addClass(this.options.theme),this.$calendar=y(i),this.$calendar.addClass(this.options.theme),this.$target.after(this.$button),"static"===this.$calendar.parent().css("position")&&this.$calendar.parent().css("position","relative"),this.$calendar.find(".datepicker-bn-open-label").html(this.options.buttonLabel),this.$target.attr("id")&&this.$calendar.attr("aria-controls",this.$target.attr("id")),this.$button.find("span").attr("title",this.options.buttonTitle),this.$calendar.css("left",this.$target.parent().position().left+"px"),this.$monthObj=this.$calendar.find(".datepicker-month"),this.$prev=this.$calendar.find(".datepicker-month-prev"),this.$next=this.$calendar.find(".datepicker-month-next"),this.$fastprev=this.$calendar.find(".datepicker-month-fast-prev"),this.$fastnext=this.$calendar.find(".datepicker-month-fast-next"),this.$grid=this.$calendar.find(".datepicker-grid"),"RTL"===this.locales.directionality&&this.$grid.addClass("rtl"),this.$grid.find("th.datepicker-day abbr"),this.drawCalendarHeader(),0==this.options.inline&&1==this.options.modal?(this.$close=this.$calendar.find(".datepicker-close"),this.$close.html(this.options.closeButtonTitle).attr("title",this.options.closeButtonLabel),this.$calendar.find(".datepicker-bn-close-label").html(this.options.closeButtonLabel)):(this.hideObject(this.$calendar.find(".datepicker-close-wrap")),this.hideObject(this.$calendar.find(".datepicker-bn-close-label"))),0!=this.options.inline?(this.hideObject(this.$button),("string"==typeof this.options.inline?y("#"+this.options.inline):this.options.inline).append(this.$calendar),this.$calendar.css({position:"relative",left:"0px"}),this.initializeDate()):(this.$calendar.css({display:"none"}),this.$target.parent().after(this.$calendar),this.hide(!this.options.gainFocusOnConstruction)),this.keys={tab:9,enter:13,esc:27,space:32,pageup:33,pagedown:34,end:35,home:36,left:37,up:38,right:39,down:40},this.bindHandlers(),this.$button.click(function(t){return y(this).hasClass("disabled")||("true"===n.$calendar.attr("aria-hidden")?(n.initializeDate(),n.show()):n.hide(),n.selectGridCell(n.$grid.attr("aria-activedescendant"))),t.stopPropagation(),!1}),this.$button.keydown(function(t){var e=t||event;if(e.keyCode==n.keys.enter||e.keyCode==n.keys.space)return y(this).trigger("click"),!1}),this.$calendar.on("blur",function(t){"false"===n.$calendar.attr("aria-hidden")&&n.hide()})}var r=['<a class="datepicker-button input-group-addon btn" role="button" aria-haspopup="true" tabindex="0" aria-labelledby="datepicker-bn-open-label-CALENDARID">','\t<svg class="icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><title>it-calendar</title><g><path d="M21,9V8a3,3,0,0,0-3-3h-.55V4a1,1,0,0,0-2,0V5h-7V4a1,1,0,1,0-2,0V5H6A3,3,0,0,0,3,8V18a3,3,0,0,0,3,3H18a3,3,0,0,0,3-3V9ZM15.46,5h2V6a1,1,0,1,1-2,0Zm-9,0h2V6a1,1,0,1,1-2,0ZM20,18a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V10H20Z"/></g></svg>',"</a>"],a=['<div class="datepicker-calendar" id="datepicker-calendar-CALENDARID" aria-hidden="false">','\t<div class="datepicker-month-wrap">','\t\t<div class="datepicker-month-fast-next pull-right" role="button" aria-labelledby="datepicker-bn-fast-next-label-CALENDARID" tabindex="0"><span class="icon-right"></span><span class="icon-right"></span></div>','\t\t<div class="datepicker-month-next pull-right" role="button" aria-labelledby="datepicker-bn-next-label-CALENDARID" tabindex="0"><span class="icon-right"></span></div>','\t\t<div class="datepicker-month-fast-prev pull-left" role="button" aria-labelledby="datepicker-bn-fast-prev-label-CALENDARID" tabindex="0"><span class="icon-left"></span><span class="icon-left"></span></div>','\t\t<div class="datepicker-month-prev pull-left" role="button" aria-labelledby="datepicker-bn-prev-label-CALENDARID" tabindex="0"><span class="icon-left"></span></div>','\t\t<div id="datepicker-month-CALENDARID" class="datepicker-month" tabindex="0" role="heading" aria-live="assertive" aria-atomic="true" title="Click or press the Enter key or the spacebar to change the month">July 2015</div>',"\t</div>",'\t<table class="datepicker-grid" role="grid" aria-readonly="true" aria-activedescendant="datepicker-err-msg-CALENDARID" aria-labelledby="datepicker-month-CALENDARID" tabindex="0">',"\t\t<thead>",'\t\t\t<tr class="datepicker-weekdays" role="row">','\t\t\t\t<th scope="col" id="day0-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Sunday"><abbr title="Sunday">Su</abbr></th>','\t\t\t\t<th scope="col" id="day1-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Monday"><abbr title="Monday">Mo</abbr></th>','\t\t\t\t<th scope="col" id="day2-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Tuesday"><abbr title="Tuesday">Tu</abbr></th>','\t\t\t\t<th scope="col" id="day3-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Wednesday"><abbr title="Wednesday">We</abbr></th>','\t\t\t\t<th scope="col" id="day4-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Thursday"><abbr title="Thursday">Th</abbr></th>','\t\t\t\t<th scope="col" id="day5-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Friday"><abbr title="Friday">Fr</abbr></th>','\t\t\t\t<th scope="col" id="day6-header-CALENDARID" class="datepicker-day" role="columnheader" aria-label="Saturday"><abbr title="Saturday">Sa</abbr></th>',"\t\t\t</tr>","\t\t</thead>","\t\t<tbody>","\t\t\t<tr>",'\t\t\t\t<td id="datepicker-err-msg-CALENDARID" colspan="7"><span>Javascript must be enabled</span></td>',"\t\t\t</tr>","\t\t</tbody>","\t</table>",'\t<div class="datepicker-close-wrap">','\t\t<button class="datepicker-close" id="datepicker-close-CALENDARID" aria-labelledby="datepicker-bn-close-label-CALENDARID">Close</button>',"\t</div>",'\t<div id="datepicker-bn-open-label-CALENDARID" class="datepicker-bn-open-label offscreen">Click or press the Enter key or the spacebar to open the calendar</div>','\t<div id="datepicker-bn-prev-label-CALENDARID" class="datepicker-bn-prev-label offscreen">Go to previous month</div>','\t<div id="datepicker-bn-next-label-CALENDARID" class="datepicker-bn-next-label offscreen">Go to next month</div>','\t<div id="datepicker-bn-fast-prev-label-CALENDARID" class="datepicker-bn-fast-prev-label offscreen">Go to previous year</div>','\t<div id="datepicker-bn-fast-next-label-CALENDARID" class="datepicker-bn-fast-next-label offscreen">Go to next year</div>','\t<div id="datepicker-bn-close-label-CALENDARID" class="datepicker-bn-close-label offscreen">Close the date picker</div>',"</div>"];o.VERSION="2.1.10",o.DEFAULTS={firstDayOfWeek:Date.dp_locales.firstday_of_week,weekDayFormat:"short",startView:0,daysOfWeekDisabled:[],datesDisabled:[],isDateDisabled:null,isMonthDisabled:null,isYearDisabled:null,inputFormat:[Date.dp_locales.short_format],outputFormat:Date.dp_locales.short_format,titleFormat:Date.dp_locales.full_format,buttonTitle:Date.dp_locales.texts.buttonTitle,buttonLabel:Date.dp_locales.texts.buttonLabel,prevButtonLabel:Date.dp_locales.texts.prevButtonLabel,prevMonthButtonLabel:Date.dp_locales.texts.prevMonthButtonLabel,prevYearButtonLabel:Date.dp_locales.texts.prevYearButtonLabel,nextButtonLabel:Date.dp_locales.texts.nextButtonLabel,nextMonthButtonLabel:Date.dp_locales.texts.nextMonthButtonLabel,nextYearButtonLabel:Date.dp_locales.texts.nextYearButtonLabel,changeMonthButtonLabel:Date.dp_locales.texts.changeMonthButtonLabel,changeYearButtonLabel:Date.dp_locales.texts.changeYearButtonLabel,changeRangeButtonLabel:Date.dp_locales.texts.changeRangeButtonLabel,closeButtonTitle:Date.dp_locales.texts.closeButtonTitle,closeButtonLabel:Date.dp_locales.texts.closeButtonLabel,onUpdate:function(){},previous:null,next:null,theme:"default",modal:!1,inline:!1,gainFocusOnConstruction:!1,min:null,max:null},o.prototype.initializeDate=function(){var t=this.$target.val(),e=""===t?new Date:this.parseDate(t);this.setDate(e,!0)},o.prototype.getDate=function(){var t=this.$target.val();return""===t?new Date:this.parseDate(t)},o.prototype.setDate=function(t,e){switch(this.dateObj=t,e=void 0!==e&&e,null==this.dateObj&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=new Date,this.dateObj.setHours(0,0,0,0)),null!=this.options.min&&this.dateObj<this.options.min?(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.min):null!=this.options.max&&this.dateObj>this.options.max&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.max),e&&""==this.$target.val()||this.$target.val(this.format(this.dateObj)),this.curYear=this.dateObj.getFullYear(),this.year=this.curYear,this.curMonth=this.dateObj.getMonth(),this.month=this.curMonth,this.date=this.dateObj.getDate(),this.options.startView){case 1:this.populateMonthsCalendar(),this.$grid.attr("aria-activedescendant",this.$grid.find(".curMonth").attr("id"));break;case 2:this.populateYearsCalendar(),this.$grid.attr("aria-activedescendant",this.$grid.find(".curYear").attr("id"));break;default:this.populateDaysCalendar(),this.$grid.attr("aria-activedescendant",this.$grid.find(".curDay").attr("id"))}},o.prototype.drawCalendarHeader=function(){for(var t=this.$grid.find("th.datepicker-day"),e=this.options.firstDayOfWeek,i=0;i<7;i++)t.eq(i).attr("aria-label",this.locales.day_names[e]),t.children("abbr").eq(i).attr("title",this.locales.day_names[e]).text("short"===this.options.weekDayFormat?this.locales.day_names_short[e]:this.locales.day_names_narrow[e]),e=(e+1)%7},o.prototype.populateDaysCalendar=function(){this.$calendar.find(".datepicker-bn-prev-label").html(this.options.prevButtonLabel),this.$calendar.find(".datepicker-bn-next-label").html(this.options.nextButtonLabel),this.$calendar.find(".datepicker-bn-fast-prev-label").html(this.options.prevMonthButtonLabel),this.$calendar.find(".datepicker-bn-fast-next-label").html(this.options.nextMonthButtonLabel),null!=this.options.min&&(this.year-1<this.options.min.getFullYear()||this.year-1==this.options.min.getFullYear()&&this.month<this.options.min.getMonth())?(this.$fastprev.attr("title",""),this.$fastprev.addClass("disabled"),this.$fastprev.removeClass("enabled")):(this.$fastprev.attr("title",this.options.prevMonthButtonLabel),this.$fastprev.addClass("enabled"),this.$fastprev.removeClass("disabled"));var t=this.previousMonth(this.year,this.month);null!=this.options.min&&(t.year<this.options.min.getFullYear()||t.year==this.options.min.getFullYear()&&t.month<this.options.min.getMonth())?(this.$prev.attr("title",""),this.$prev.addClass("disabled"),this.$prev.removeClass("enabled")):(this.$prev.attr("title",this.options.prevButtonLabel),this.$prev.addClass("enabled"),this.$prev.removeClass("disabled")),this.$monthObj.attr("title",this.options.changeMonthButtonLabel);var e=this.nextMonth(this.year,this.month);null!=this.options.max&&(e.year>this.options.max.getFullYear()||e.year==this.options.max.getFullYear()&&e.month>this.options.max.getMonth())?(this.$next.attr("title",""),this.$next.addClass("disabled"),this.$next.removeClass("enabled")):(this.$next.attr("title",this.options.nextButtonLabel),this.$next.addClass("enabled"),this.$next.removeClass("disabled")),null!=this.options.max&&(this.year+1>this.options.max.getFullYear()||this.year+1==this.options.max.getFullYear()&&this.month>this.options.max.getMonth())?(this.$fastnext.attr("title",""),this.$fastnext.addClass("disabled"),this.$fastnext.removeClass("enabled")):(this.$fastnext.attr("title",this.options.nextMonthButtonLabel),this.$fastnext.addClass("enabled"),this.$fastnext.removeClass("disabled")),this.showObject(this.$fastprev),this.showObject(this.$fastnext);var i=this.getDaysInMonth(this.year,this.month),n=this.getDaysInMonth(t.year,t.month),s=new Date(this.year,this.month,1).getDay(),o=(this.options.firstDayOfWeek+6)%7,r=1,a=1;this.$monthObj.html(this.locales.month_names[this.month]+" "+this.year),this.showObject(this.$grid.find("thead"));for(var l='\t<tr id="row0-'+this.id+'" role="row">\n',c=0,h=this.options.firstDayOfWeek;h!=s;)c++,h=(h+1)%7;for(;0<c;c--)l+='\t\t<td class="empty">'+(n-c+1)+"</td>\n";for(var d=this.options.isYearDisabled&&this.options.isYearDisabled(this.year),u=this.options.isMonthDisabled&&this.options.isMonthDisabled(this.year,this.month+1),r=1;r<=i;r++){var p=new Date(this.year,this.month,r,0,0,0,0),f=this.formatDate(p,this.options.titleFormat),m=r==this.date&&this.month==this.curMonth&&this.year==this.curYear?" curDay":"";d||u||-1<y.inArray(h,this.options.daysOfWeekDisabled)||null!=this.options.min&&p<this.options.min||null!=this.options.max&&p>this.options.max||-1<y.inArray(this.format(p),this.options.datesDisabled)||this.options.isDateDisabled&&this.options.isDateDisabled(p)?l+='\t\t<td id="cell'+r+"-"+this.id+'" class="day unselectable'+m+'"':l+='\t\t<td id="cell'+r+"-"+this.id+'" class="day selectable'+m+'"',l+=' data-value="'+r+'"',l+=' title="'+f+'"',l+=' aria-label="'+f+'"',l+=' headers="day'+h+"-header-"+this.id+'" role="gridcell" tabindex="-1" aria-selected="false"><span>'+r+"</span>",l+="</td>",h==o&&r<i&&(l+='\t</tr>\n\t<tr id="row'+a+"-"+this.id+'" role="row">\n',a++),r<i&&(h=(h+1)%7)}for(;h!=o;)l+='\t\t<td class="empty">'+ ++c+"</td>\n",h=(h+1)%7;l+="\t</tr>";var g=this.$grid.find("tbody");g.empty(),g.append(l),this.gridType=0},o.prototype.populateMonthsCalendar=function(){this.$calendar.find(".datepicker-bn-prev-label").html(this.options.prevMonthButtonLabel),this.$calendar.find(".datepicker-bn-next-label").html(this.options.nextMonthButtonLabel),this.hideObject(this.$fastprev),this.hideObject(this.$fastnext),null!=this.options.min&&this.year-1<this.options.min.getFullYear()?(this.$prev.attr("title",""),this.$prev.addClass("disabled"),this.$prev.removeClass("enabled")):(this.$prev.attr("title",this.options.prevMonthButtonLabel),this.$prev.addClass("enabled"),this.$prev.removeClass("disabled")),this.$monthObj.attr("title",this.options.changeYearButtonLabel),null!=this.options.max&&this.year+1>this.options.max.getFullYear()?(this.$next.attr("title",""),this.$next.addClass("disabled"),this.$next.removeClass("enabled")):(this.$next.attr("title",this.options.nextMonthButtonLabel),this.$next.addClass("enabled"),this.$next.removeClass("disabled"));var t=0,e=1,i=this.$grid.find("tbody");this.$monthObj.html(this.year),this.hideObject(this.$grid.find("thead")),i.empty(),y("#datepicker-err-msg-"+this.id).empty();for(var n='\t<tr id="row0-'+this.id+'" role="row">\n',s=this.options.isYearDisabled&&this.options.isYearDisabled(this.year),t=0;t<12;t++)s?n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month unselectable"':t==this.month&&this.year==this.curYear?n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month curMonth selectable"':null!=this.options.min&&(this.year<this.options.min.getFullYear()||this.year==this.options.min.getFullYear()&&t<this.options.min.getMonth())||null!=this.options.max&&(this.year>this.options.max.getFullYear()||this.year==this.options.max.getFullYear()&&t>this.options.max.getMonth())||this.options.isMonthDisabled&&this.options.isMonthDisabled(this.year,t+1)?n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month unselectable"':n+='\t\t<td id="cell'+(t+1)+"-"+this.id+'" class="month selectable"',n+=' data-value="'+t+'"',n+=' title="'+this.locales.month_names[t]+" "+this.year+'"',n+=' aria-label="'+this.locales.month_names[t]+" "+this.year+'"',n+=' role="gridcell" tabindex="-1" aria-selected="false">'+this.locales.month_names_abbreviated[t],n+="</td>",3!=t&&7!=t||(n+='\t</tr>\n\t<tr id="row'+e+"-"+this.id+'" role="row">\n',e++);n+="\t</tr>",i.append(n),this.gridType=1},o.prototype.populateYearsCalendar=function(){this.$calendar.find(".datepicker-bn-prev-label").html(this.options.prevYearButtonLabel),this.$calendar.find(".datepicker-bn-next-label").html(this.options.nextYearButtonLabel),this.hideObject(this.$fastprev),this.hideObject(this.$fastnext),null!=this.options.min&&this.year-20<this.options.min.getFullYear()?(this.$prev.attr("title",""),this.$prev.addClass("disabled"),this.$prev.removeClass("enabled")):(this.$prev.attr("title",this.options.prevYearButtonLabel),this.$prev.addClass("enabled"),this.$prev.removeClass("disabled")),this.$monthObj.attr("title",this.options.changeRangeButtonLabel),null!=this.options.max&&this.year+20>this.options.max.getFullYear()?(this.$next.attr("title",""),this.$next.addClass("disabled"),this.$next.removeClass("enabled")):(this.$next.attr("title",this.options.nextYearButtonLabel),this.$next.addClass("enabled"),this.$next.removeClass("disabled"));var t=10*Math.floor(this.year/10),e=19+t,i=1,n=this.$grid.find("tbody");this.$monthObj.html(t+"-"+e),this.hideObject(this.$grid.find("thead")),n.empty(),y("#datepicker-err-msg-"+this.id).empty();for(var s='\t<tr id="row0-'+this.id+'" role="row">\n',o=t;o<=e;o++){o==this.year?s+='\t\t<td id="cell'+(o-t+1)+"-"+this.id+'" class="year curYear selectable"':null!=this.options.min&&o<this.options.min.getFullYear()||null!=this.options.max&&o>this.options.max.getFullYear()||this.options.isYearDisabled&&this.options.isYearDisabled(o)?s+='\t\t<td id="cell'+(o-t+1)+"-"+this.id+'" class="year unselectable"':s+='\t\t<td id="cell'+(o-t+1)+"-"+this.id+'" class="year selectable"',s+=' data-value="'+o+'"',s+=' title="'+o+'"',s+=' role="gridcell" tabindex="-1" aria-selected="false">'+o,s+="</td>";var r=o-t;4!=r&&9!=r&&14!=r||(s+='\t</tr>\n\t<tr id="row'+i+"-"+this.id+'" role="row">\n',i++)}s+="\t</tr>",n.append(s),this.gridType=2},o.prototype.showDaysOfPrevMonth=function(t){var e,i=this.previousMonth(this.year,this.month);return(null==this.options.min||!(i.year<this.options.min.getFullYear()||i.year==this.options.min.getFullYear()&&i.month<this.options.min.getMonth()))&&(this.month=i.month,this.year=i.year,this.populateDaysCalendar(),null!=t&&(e="cell"+(this.getDaysInMonth(this.year,this.month)-t)+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0)},o.prototype.showDaysOfMonth=function(t){if(null!=this.options.min&&(this.year<this.options.min.getFullYear()||this.year==this.options.min.getFullYear()&&t<this.options.min.getMonth()))return!1;if(null!=this.options.max&&(this.year>this.options.max.getFullYear()||this.year==this.options.max.getFullYear()&&t>this.options.max.getMonth()))return!1;this.month=t,this.date=Math.min(this.date,this.getDaysInMonth(this.year,this.month)),this.populateDaysCalendar();var e=this.$grid.find("tbody td[data-value='"+this.date+"']");return this.selectGridCell(e.attr("id")),!0},o.prototype.showMonthsOfPrevYear=function(t){return!(null!=this.options.min&&this.year-1<this.options.min.getFullYear())&&(this.year--,this.populateMonthsCalendar(),null!=t&&(e="cell"+(12-t)+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showMonthsOfYear=function(t){if(null!=this.options.min&&t<this.options.min.getFullYear())return!1;if(null!=this.options.max&&t>this.options.max.getFullYear())return!1;this.year=t,this.populateMonthsCalendar();var e=this.$grid.find("tbody td[data-value='"+this.month+"']");return this.$grid.attr("aria-activedescendant",e.attr("id")),this.selectGridCell(e.attr("id")),!0},o.prototype.showYearsOfPrevRange=function(t){return!(null!=this.options.min&&this.year-20<this.options.min.getFullYear())&&(this.year-=20,this.populateYearsCalendar(),null!=t&&(e="cell"+(20-t)+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showDaysOfNextMonth=function(t){var e,i=this.nextMonth(this.year,this.month);return(null==this.options.max||!(i.year>this.options.max.getFullYear()||i.year==this.options.max.getFullYear()&&i.month>this.options.max.getMonth()))&&(this.month=i.month,this.year=i.year,this.populateDaysCalendar(),null!=t&&(e="cell"+t+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0)},o.prototype.showMonthsOfNextYear=function(t){return!(null!=this.options.max&&this.year+1>this.options.max.getFullYear())&&(this.year++,this.populateMonthsCalendar(),null!=t&&(e="cell"+t+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showYearsOfNextRange=function(t){return!(null!=this.options.max&&this.year+20>this.options.max.getFullYear())&&(this.year+=20,this.populateYearsCalendar(),null!=t&&(e="cell"+t+"-"+this.id,this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)),!0);var e},o.prototype.showDaysOfPrevYear=function(){return(null==this.options.min||!(this.year-1<this.options.min.getFullYear()||this.year-1==this.options.min.getFullYear()&&this.month<this.options.min.getMonth()))&&(this.year--,this.populateDaysCalendar(),!0)},o.prototype.showDaysOfNextYear=function(){return(null==this.options.max||!(this.year+1>this.options.max.getFullYear()||this.year+1==this.options.max.getFullYear()&&this.month>this.options.max.getMonth()))&&(this.year++,this.populateDaysCalendar(),!0)},o.prototype.bindHandlers=function(){var i=this;this.$fastprev.click(function(t){return i.handleFastPrevClick(t)}),this.$prev.click(function(t){return i.handlePrevClick(t)}),this.$next.click(function(t){return i.handleNextClick(t)}),this.$fastnext.click(function(t){return i.handleFastNextClick(t)}),this.$monthObj.click(function(t){return i.handleMonthClick(t)}),this.$monthObj.keydown(function(t){return i.handleMonthKeyDown(t)}),this.$fastprev.keydown(function(t){return i.handleFastPrevKeyDown(t)}),this.$prev.keydown(function(t){return i.handlePrevKeyDown(t)}),this.$next.keydown(function(t){return i.handleNextKeyDown(t)}),this.$fastnext.keydown(function(t){return i.handleFastNextKeyDown(t)}),1==this.options.modal&&(this.$close.click(function(t){return i.handleCloseClick(t)}),this.$close.keydown(function(t){return i.handleCloseKeyDown(t)})),this.$grid.keydown(function(t){return i.handleGridKeyDown(t)}),this.$grid.keypress(function(t){return i.handleGridKeyPress(t)}),this.$grid.focus(function(t){return i.handleGridFocus(t)}),this.$grid.blur(function(t){return i.handleGridBlur(t)}),this.$grid.delegate("td","click",function(t){return i.handleGridClick(this,t)}),this.$target.change(function(t){var e=i.parseDate(y(this).val());i.updateLinked(e)})},o.prototype.handleFastPrevClick=function(t){var e;return this.showDaysOfPrevYear()&&(e=this.$grid.attr("aria-activedescendant"),this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e))),t.stopPropagation(),!1},o.prototype.handlePrevClick=function(t){var e=this.$grid.attr("aria-activedescendant");switch(this.gridType){case 0:var i=t.ctrlKey?this.showDaysOfPrevYear():this.showDaysOfPrevMonth();i&&(this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 1:this.showMonthsOfPrevYear()&&(this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 2:this.showYearsOfPrevRange()&&(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id))}return t.stopPropagation(),!1},o.prototype.handleMonthClick=function(t){return this.changeGrid(t),t.stopPropagation(),!1},o.prototype.handleNextClick=function(t){var e=this.$grid.attr("aria-activedescendant");switch(this.gridType){case 0:var i=t.ctrlKey?this.showDaysOfNextYear():this.showDaysOfNextMonth();i&&(this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 1:this.showMonthsOfNextYear()&&(this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e)));break;case 2:this.showYearsOfNextRange()&&(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id))}return t.stopPropagation(),!1},o.prototype.handleFastNextClick=function(t){var e;return this.showDaysOfNextYear()&&(e=this.$grid.attr("aria-activedescendant"),this.month!=this.curMonth||this.year!=this.curYear?(this.$grid.attr("aria-activedescendant","cell1-"+this.id),this.selectGridCell("cell1-"+this.id)):(this.$grid.attr("aria-activedescendant",e),this.selectGridCell(e))),t.stopPropagation(),!1},o.prototype.handleCloseClick=function(t){return this.hide(),t.stopPropagation(),!1},o.prototype.handleFastPrevKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$close.focus():this.$prev.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:return!(!t.shiftKey&&!t.ctrlKey)||(this.showDaysOfPrevYear(),t.stopPropagation(),!1);case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handlePrevKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?0==this.gridType?this.$fastprev.focus():this.$close.focus():this.$monthObj.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:if(t.shiftKey)return!0;switch(this.gridType){case 0:t.ctrlKey?this.showDaysOfPrevYear():this.showDaysOfPrevMonth();break;case 1:this.showMonthsOfPrevYear();break;case 2:this.showYearsOfPrevRange()}return t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleMonthKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$prev.focus():this.$next.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:return this.changeGrid(t),t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleNextKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$monthObj.focus():0==this.gridType?this.$fastnext.focus():this.$grid.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:switch(this.gridType){case 0:t.ctrlKey?this.showDaysOfNextYear():this.showDaysOfNextMonth();break;case 1:this.showMonthsOfNextYear();break;case 2:this.showYearsOfNextRange()}return t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleFastNextKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!(0!=this.options.modal&&!t.ctrlKey)||(t.shiftKey?this.$next.focus():this.$grid.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.space:return this.showDaysOfNextYear(),t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1}return!0},o.prototype.handleCloseKeyDown=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:return!!t.ctrlKey||(t.shiftKey?this.$grid.focus():0==this.gridType?this.$fastprev.focus():this.$prev.focus(),t.stopPropagation(),!1);case this.keys.enter:case this.keys.esc:case this.keys.space:return!(!t.shiftKey&&!t.ctrlKey)||(this.hide(),t.stopPropagation(),!1)}return!0},o.prototype.handleGridKeyDown=function(t){var e=y("#"+this.$grid.attr("aria-activedescendant")),i=this.$grid.find("td.selectable"),n=this.$grid.find("tbody tr").eq(0).find("td").length;if(t.altKey&&t.keyCode!=this.keys.pageup&&t.keyCode!=this.keys.pagedown)return!0;switch(t.keyCode){case this.keys.tab:return 1==this.options.modal?t.shiftKey?0==this.gridType?this.$fastnext.focus():this.$next.focus():this.$close.focus():(this.hide(),this.handleTabOut(t)),t.stopPropagation(),!1;case this.keys.enter:case this.keys.space:if(t.ctrlKey)return!0;switch(this.gridType){case 0:this.update(),this.hide();break;case 1:this.showDaysOfMonth(parseInt(e.attr("data-value"),10));break;case 2:this.showMonthsOfYear(parseInt(e.attr("data-value"),10))}return t.stopPropagation(),!1;case this.keys.esc:return this.hide(),t.stopPropagation(),!1;case this.keys.left:case this.keys.right:if(t.keyCode==this.keys.left&&"LTR"===this.locales.directionality||t.keyCode==this.keys.right&&"RTL"===this.locales.directionality){if(t.ctrlKey||t.shiftKey)return!0;var s=null;if(0<=(r=i.index(e)-1))s=i.eq(r),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",s.attr("id")),this.selectGridCell(s.attr("id"));else switch(this.gridType){case 0:this.showDaysOfPrevMonth(0);break;case 1:this.showMonthsOfPrevYear(0);break;case 2:this.showYearsOfPrevRange(0)}return t.stopPropagation(),!1}if(t.ctrlKey||t.shiftKey)return!0;var o=null;if((r=i.index(e)+1)<i.length)o=i.eq(r),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",o.attr("id")),this.selectGridCell(o.attr("id"));else switch(this.gridType){case 0:this.showDaysOfNextMonth(1);break;case 1:this.showMonthsOfNextYear(1);break;case 2:this.showYearsOfNextRange(1)}return t.stopPropagation(),!1;case this.keys.up:if(t.ctrlKey||t.shiftKey)return!0;s=null;if(0<=(r=i.index(e)-n))s=i.eq(r),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",s.attr("id")),this.selectGridCell(s.attr("id"));else switch(r=n-1-i.index(e),this.gridType){case 0:this.showDaysOfPrevMonth(r);break;case 1:this.showMonthsOfPrevYear(r);break;case 2:this.showYearsOfPrevRange(r)}return t.stopPropagation(),!1;case this.keys.down:if(t.ctrlKey||t.shiftKey)return!0;var r,o=null;if((r=i.index(e)+n)<i.length)o=i.eq(r),this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",o.attr("id")),this.selectGridCell(o.attr("id"));else switch(r=n+1-(i.length-i.index(e)),this.gridType){case 0:this.showDaysOfNextMonth(r);break;case 1:this.showMonthsOfNextYear(r);break;case 2:this.showYearsOfNextRange(r)}return t.stopPropagation(),!1;case this.keys.pageup:var a=this.$grid.attr("aria-activedescendant");if(t.shiftKey||t.ctrlKey)return!0;t.preventDefault();var l=!1;switch(this.gridType){case 0:l=t.altKey?(t.stopImmediatePropagation(),this.showDaysOfPrevYear()):this.showDaysOfPrevMonth();break;case 1:l=this.showMonthsOfPrevYear();break;case 2:l=this.showYearsOfPrevRange()}return l&&(y("#"+a).attr("id")==d?(h=(i=this.$grid.find("td.selectable")).eq(i.length-1),this.$grid.attr("aria-activedescendant",h.attr("id")),this.selectGridCell(h.attr("id"))):this.selectGridCell(a)),t.stopPropagation(),!1;case this.keys.pagedown:a=this.$grid.attr("aria-activedescendant");if(t.shiftKey||t.ctrlKey)return!0;t.preventDefault();l=!1;switch(this.gridType){case 0:l=t.altKey?(t.stopImmediatePropagation(),this.showDaysOfNextYear()):this.showDaysOfNextMonth();break;case 1:l=this.showMonthsOfNextYear();break;case 2:l=this.showYearsOfNextRange()}return l&&(y("#"+a).attr("id")==d?(h=(i=this.$grid.find("td.selectable")).eq(i.length-1),this.$grid.attr("aria-activedescendant",h.attr("id")),this.selectGridCell(h.attr("id"))):this.selectGridCell(a)),t.stopPropagation(),!1;case this.keys.home:if(t.ctrlKey||t.shiftKey)return!0;var c=i.eq(0);return this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",c.attr("id")),this.selectGridCell(c.attr("id")),t.stopPropagation(),!1;case this.keys.end:if(t.ctrlKey||t.shiftKey)return!0;var h=i.eq(i.length-1);return this.unSelectGridCell(e.attr("id")),this.$grid.attr("aria-activedescendant",h.attr("id")),this.selectGridCell(h.attr("id")),t.stopPropagation(),!1}return!0},o.prototype.handleGridKeyPress=function(t){if(t.altKey)return!0;switch(t.keyCode){case this.keys.tab:case this.keys.enter:case this.keys.space:case this.keys.esc:case this.keys.left:case this.keys.right:case this.keys.up:case this.keys.down:case this.keys.pageup:case this.keys.pagedown:case this.keys.home:case this.keys.end:return t.stopPropagation(),!1}return!0},o.prototype.handleGridClick=function(t,e){var i=y(t);if(i.is(".empty")||i.is(".unselectable"))return!0;switch(this.$grid.find(".focus").removeClass("focus").attr("aria-selected","false").attr("tabindex",-1),this.gridType){case 0:this.$grid.attr("aria-activedescendant",i.attr("id")),this.selectGridCell(i.attr("id")),this.update(),this.hide();break;case 1:this.showDaysOfMonth(parseInt(i.attr("data-value"),10));break;case 2:this.showMonthsOfYear(parseInt(i.attr("data-value"),10))}return e.stopPropagation(),!1},o.prototype.handleGridFocus=function(t){var e,i,n=this.$grid.attr("aria-activedescendant");return y("#"+n).attr("id")==d?(i=(e=this.$grid.find("td.selectable")).eq(e.length-1),this.$grid.attr("aria-activedescendant",i.attr("id")),this.selectGridCell(i.attr("id"))):this.selectGridCell(n),!0},o.prototype.handleGridBlur=function(t){return this.unSelectGridCell(this.$grid.attr("aria-activedescendant")),!0},o.prototype.handleTabOut=function(t){var e=y("body").find("input:visible,textarea:visible,select:visible"),i=e.index(this.$target);return-1<i&&i<e.length&&(t.shiftKey?0<i&&i--:i+1<e.length&&i++,e.eq(i).focus()),!0},o.prototype.changeGrid=function(t){switch(this.gridType){case 0:this.populateMonthsCalendar(),this.year!=this.curYear?(e=this.$grid.find("td.selectable"),this.$grid.attr("aria-activedescendant",e.eq(0).attr("id"))):this.$grid.attr("aria-activedescendant",this.$grid.find(".curMonth").attr("id")),this.selectGridCell(this.$grid.attr("aria-activedescendant"));break;case 2:t.shiftKey?this.year-=20:this.year+=20;case 1:var e;this.populateYearsCalendar(),this.year!=this.curYear?(e=this.$grid.find("td.selectable"),this.$grid.attr("aria-activedescendant",e.eq(0).attr("id"))):this.$grid.attr("aria-activedescendant",this.$grid.find(".curYear").attr("id")),this.selectGridCell(this.$grid.attr("aria-activedescendant"))}return!0},o.prototype.selectGridCell=function(t){y("#"+t).addClass("focus").attr("aria-selected","true").attr("tabindex",0).focus()},o.prototype.unSelectGridCell=function(t){y("#"+t).removeClass("focus").attr("aria-selected","false").attr("tabindex",-1)},o.prototype.update=function(){var t=y("#"+this.$grid.attr("aria-activedescendant")),e=new Date(this.year,this.month,parseInt(t.attr("data-value"),10)),i=this.formatDate(e,this.options.outputFormat);this.$target.val(i),this.$target.removeAttr("aria-invalid"),this.$target.parents(".form-group").removeClass("has-error"),this.$target.trigger("change"),this.options.onUpdate&&this.options.onUpdate(i)},o.prototype.updateLinked=function(t){var e,i;null!==this.options.previous&&""!==this.options.previous.val()&&t<this.options.previous.datepicker("getDate")&&(e=this.formatDate(t,this.options.previous.datepicker("outputFormat")),this.options.previous.val(e)),null!==this.options.next&&""!==this.options.next.val()&&this.options.next.datepicker("getDate")<t&&(i=this.formatDate(t,this.options.next.datepicker("outputFormat")),this.options.next.val(i))},o.prototype.hideObject=function(t){t.attr("aria-hidden",!0),t.fadeOut(100)},o.prototype.showObject=function(t){t.attr("aria-hidden",!1),t.fadeIn(100)},o.prototype.show=function(){var t,i=this;y(".datepicker-calendar").trigger("ab.datepicker.opening",[i.id]),1==this.options.modal?(this.modalEventHandler||(this.modalEventHandler=function(t){return i.$grid.focus(),t.stopPropagation,!1}),y(document).on("click mousedown mouseup",this.modalEventHandler),this.greyOut(!0),t=parseInt(y("#datepicker-overlay").css("z-index"),10)||40,this.$calendar.css("z-index",t+1)):(y(document).on("click",y.proxy(this.handleDocumentClick,this)),this.$calendar.on("ab.datepicker.opening",function(t,e){e!=i.id?i.hide():i.$grid.focus()})),this.$calendar.on("ab.datepicker.opened",function(t,e){e==i.id&&i.$grid.focus()});var e=Math.max(0,Math.floor(this.$group.offset().top-this.$label.offset().top)),n=Math.max(0,Math.floor(this.$group.offset().left-this.$label.offset().left)),s=parseInt(this.$calendar.parent().css("padding-left"),10),o=this.$calendar.outerHeight(),r=this.$group.offset().top,a=(this.$group.offset().left,this.$group.outerHeight(!0)),l=Math.floor(r-y(window).scrollTop()),c=Math.floor(y(window).height()-(r+a-y(window).scrollTop()));c<o&&c<l?(this.$calendar.addClass("above"),this.$calendar.css({top:e-o+"px",left:n+s+"px"})):(this.$calendar.addClass("below"),this.$calendar.css({top:a+e+"px",left:n+s+"px"})),this.$calendar.attr("aria-hidden","false"),this.$calendar.fadeIn(100),y(".datepicker-calendar").trigger("ab.datepicker.opened",[i.id])},o.prototype.refresh=function(){switch(this.drawCalendarHeader(),this.gridType){case 0:this.populateDaysCalendar();break;case 1:this.populateMonthsCalendar();break;case 2:this.populateYearsCalendar()}},o.prototype.handleDocumentClick=function(t){return 0==y(t.target).parents("#datepicker-calendar-"+this.id).length?(this.hide(),!0):(this.$grid.focus(),t.stopPropagation,!1)},o.prototype.hide=function(t){0==this.options.inline&&(1==this.options.modal?(this.modalEventHandler&&y(document).off("click mousedown mouseup",this.modalEventHandler),this.greyOut(!1)):(y(document).off("click",this.handleDocumentClick),this.$calendar.off("ab.datepicker.opening")),this.$calendar.removeClass("above below"),this.$calendar.attr("aria-hidden","true"),this.$calendar.fadeOut(100),y(".datepicker-calendar").trigger("ab.datepicker.closed",[this.id]),t||this.$target.focus())},o.prototype.greyOut=function(t){var e=y("#datepicker-overlay");0==e.length&&t&&(y("body").append('<div id="datepicker-overlay" class="datepicker-overlay"></div>'),e=y("#datepicker-overlay")),t?e.fadeIn(100):e.fadeOut(100)},o.prototype.absolutePosition=function(t){var e=0,i=0;if(t.getBoundingClientRect)var n=t.getBoundingClientRect(),s=document.body,o=document.documentElement,r=window.pageYOffset||o.scrollTop||s.scrollTop,a=window.pageXOffset||o.scrollLeft||s.scrollLeft,l=o.clientTop||s.clientTop||0,c=o.clientLeft||s.clientLeft||0,e=Math.round(n.top+r-l),i=Math.round(n.left+a-c);else for(;t;)e+=parseInt(t.offsetTop,10),i+=parseInt(t.offsetLeft,10),t=t.offsetParent;return{top:e,left:i}},o.prototype.getDaysInMonth=function(t,e){return 32-new Date(t,e,32).getDate()},o.prototype.previousMonth=function(t,e){return 0==e?(e=11,t--):e--,{year:t,month:e}},o.prototype.nextMonth=function(t,e){return 11==e?(e=0,t++):e++,{year:t,month:e}},o.prototype.formatDate=function(t,e){function i(t){return(t<0||9<t?"":"0")+t}function n(t){var e=new Date(t.getTime());return e.setHours(0),t-e}var s,o,r,a,l,c=t.getFullYear()+"",h=t.getMonth()+1,d=t.getDate(),u=(s=t,o=new Date(s.getFullYear(),0,0),Math.floor((s-o)/864e5)),p=t.getDay(),f=t.getHours(),m=t.getMinutes(),g=t.getSeconds(),v=(r=t,a=new Date(r.getFullYear(),0,1),Math.ceil(((r-a)/864e5+a.getDay()+1)/7)),y=(l=t,Math.ceil((l.getDate()-1-l.getDay())/7)),b=Math.floor(t.getDate()/7)+1,w=Math.ceil((t.getMonth()+1)/3),_=t.getFullYear()<1?0:1,x={y:""+c,yyyy:c,yy:c.substring(2,4),L:h,LL:i(h),LLL:this.locales.month_names_abbreviated[h-1],LLLL:this.locales.month_names[h-1],LLLLL:this.locales.month_names_narrow[h-1],M:h,MM:i(h),MMM:this.locales.month_names_abbreviated[h-1],MMMM:this.locales.month_names[h-1],MMMMM:this.locales.month_names_narrow[h-1],d:d,dd:i(d),D:u,DD:u,DDD:u,A:Math.round(n(t)*Math.pow(10,-2)),AA:Math.round(n(t)*Math.pow(10,-1)),AAA:Math.round(n(t)*Math.pow(10,0)),AAAA:Math.round(n(t)*Math.pow(10,1)),AAAAA:Math.round(n(t)*Math.pow(10,2)),AAAAAA:Math.round(n(t)*Math.pow(10,3)),E:this.locales.day_names_abbreviated[p],EE:this.locales.day_names_abbreviated[p],EEE:this.locales.day_names_abbreviated[p],EEEE:this.locales.day_names[p],EEEEE:this.locales.day_names_narrow[p],EEEEEE:this.locales.day_names_short[p],e:p,ee:p,eee:this.locales.day_names_abbreviated[p],eeee:this.locales.day_names[p],eeeee:this.locales.day_names_narrow[p],eeeeee:this.locales.day_names_short[p],c:p,cc:p,ccc:this.locales.day_names_abbreviated[p],cccc:this.locales.day_names[p],ccccc:this.locales.day_names_narrow[p],cccccc:this.locales.day_names_short[p],F:b,G:this.locales.era_names_abbreviated[_],GG:this.locales.era_names_abbreviated[_],GGG:this.locales.era_names_abbreviated[_],GGGG:this.locales.era_names[_],GGGGG:this.locales.era_names_narrow[_],Q:w,QQ:i(w),QQQ:this.locales.quarter_names_abbreviated[w-1],QQQQ:this.locales.quarter_names[w-1],QQQQQ:this.locales.quarter_names_narrow[w-1],q:w,qq:i(w),qqq:this.locales.quarter_names_abbreviated[w-1],qqqq:this.locales.quarter_names[w-1],qqqqq:this.locales.quarter_names_narrow[w-1],H:f,HH:i(f),h:0==f?12:12<f?f-12:f,hh:i(0==f?12:12<f?f-12:f),K:11<f?f-12:f,k:f+1,KK:i(11<f?f-12:f),kk:i(f+1),a:11<f?this.locales.day_periods.pm:this.locales.day_periods.am,m:m,mm:i(m),s:g,ss:i(g),w:v,ww:i(v),W:y};return e.replace(/('[^']+'|y{1,4}|L{1,5}|M{1,5}|c{1,6}|d{1,2}|D{1,3}|E{1,6}|e{1,6}|F{1,1}|G{1,5}|Q{1,5}|q{1,5}|H{1,2}|h{1,2}|K{1,2}|k{1,2}|m{1,2}|s{1,2}|w{1,2}|W{1,1}|A{1,6})/g,function(t){return"'"===t.charAt(0)?t.substr(1,t.length-2):x[t]||t})},o.prototype.createDateFromFormat=function(t,r){function a(t,e,i,n){for(var s=n;i<=s;s--){var o=t.substring(e,e+s);if(o.length<i)return null;if(/^\d+$/.test(o))return o}return null}function l(t,e){for(var i=0;i<t.length;i++){var n=t[i];if(r.substring(e,e+n.length).toLowerCase()==n.toLowerCase())return n.length}return 0}var c=0,e=new Date,h=e.getYear(),d=e.getMonth()+1,u=1,p=0,f=0,m=0,g="",v=this;if(y.each(t.match(/(.).*?\1*/g),function(t,e){switch(e){case"yyyy":null!=(h=a(r,c,4,4))&&(c+=h.length);break;case"yy":null!=(h=a(r,c,2,2))&&(c+=h.length);break;case"y":null!=(h=a(r,c,2,4))&&(c+=h.length);break;case"MMM":case"LLL":for(var i=d=0;i<v.locales.month_names_abbreviated.length;i++){var n=v.locales.month_names_abbreviated[i];if(r.substring(c,c+n.length).toLowerCase()==n.toLowerCase()){d=i+1,c+=n.length;break}}break;case"MMMM":case"LLLL":for(i=d=0;i<v.locales.month_names.length;i++){n=v.locales.month_names[i];if(r.substring(c,c+n.length).toLowerCase()==n.toLowerCase()){d=i+1,c+=n.length;break}}break;case"EEE":case"EE":case"E":case"eee":c+=l(v.locales.day_names_abbreviated,c);break;case"EEEE":case"eeee":case"cccc":c+=l(v.locales.day_names,c);break;case"EEEEEE":case"eeeeee":case"cccccc":c+=l(v.locales.day_names_short,c);break;case"MM":case"M":case"LL":case"L":if(null==(d=a(r,c,e.length,2))||d<1||12<d)return null;c+=d.length;break;case"dd":case"d":if(null==(u=a(r,c,e.length,2))||u<1||31<u)return null;c+=u.length;break;case"hh":case"h":if(null==(p=a(r,c,e.length,2))||p<1||12<p)return null;c+=p.length;break;case"HH":case"H":if(null==(p=a(r,c,e.length,2))||p<0||23<p)return null;c+=p.length;break;case"KK":case"K":if(null==(p=a(r,c,e.length,2))||p<0||11<p)return null;c+=p.length;break;case"kk":case"k":if(null==(p=a(r,c,e.length,2))||p<1||24<p)return null;c+=p.length,p--;break;case"mm":case"m":if(null==(f=a(r,c,e.length,2))||f<0||59<f)return null;c+=f.length;break;case"ss":case"s":if(null==(m=a(r,c,e.length,2))||m<0||59<m)return null;c+=m.length;break;case"a":var s=v.locales.day_periods.am.length,o=v.locales.day_periods.pm.length;if(r.substring(c,c+s)==v.locales.day_periods.am)g="AM",c+=s;else{if(r.substring(c,c+o)!=v.locales.day_periods.pm)return null;g="PM",c+=o}break;default:if(r.substring(c,c+e.length)!=e)return null;c+=e.length}}),c!=r.length)return null;if(null==h)return null;if(2==h.length&&(h=50<h?+h+1900:+h+2e3),d<1||12<d)return null;if(2==d)if(h%4==0&&h%100!=0||h%400==0){if(29<u)return null}else if(28<u)return null;return(4==d||6==d||9==d||11==d)&&30<u?null:(p<12&&"PM"==g?p=+p+12:11<p&&"AM"==g&&(p-=12),new Date(h,d-1,u,p,f,m))},o.prototype.parseDate=function(i){var n=null,s=this;return y.each(this.options.inputFormat,function(t,e){if(null!=(n=s.createDateFromFormat(e,i)))return!1}),null==n&&(n=s.createDateFromFormat(this.options.outputFormat,i)),n},o.prototype.min=function(t){return null!=t&&(this.options.min=t instanceof Date?t:this.parseDate(t),null!=this.options.min&&this.dateObj<this.options.min&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.min),0!=this.options.inline&&this.refresh()),this.options.min},o.prototype.max=function(t){return null!=t&&(this.options.max=t instanceof Date?t:this.parseDate(t),null!=this.options.max&&this.dateObj>this.options.max&&(this.$target.attr("aria-invalid",!0),this.$target.parents(".form-group").addClass("has-error"),this.dateObj=this.options.max),0!=this.options.inline&&this.refresh()),this.options.max},o.prototype.theme=function(t){return null!=t&&(this.$button.removeClass(this.options.theme),this.$calendar.removeClass(this.options.theme),this.options.theme=t,this.$button.addClass(this.options.theme),this.$calendar.addClass(this.options.theme)),this.options.theme},o.prototype.firstDayOfWeek=function(t){return null!=t&&(this.options.firstDayOfWeek=parseInt(t,10),0==this.options.inline?this.drawCalendarHeader():this.refresh()),this.options.firstDayOfWeek},o.prototype.daysOfWeekDisabled=function(t){var i;return null!=t&&(this.options.daysOfWeekDisabled=[],y.isArray(t)||(t=[t]),i=this,y.each(t,function(t,e){"number"==typeof e?i.options.daysOfWeekDisabled.push(e):"string"==typeof e&&i.options.daysOfWeekDisabled.push(parseInt(e,10))})),this.options.daysOfWeekDisabled},o.prototype.weekDayFormat=function(t){return null!=t&&(this.options.weekDayFormat=t,this.drawCalendarHeader()),this.options.weekDayFormat},o.prototype.inputFormat=function(t){return null!=t&&(y.isArray(t)||(t=[t]),this.$target.attr("placeholder")==this.options.inputFormat[0]&&this.$target.attr("placeholder",t[0]),this.options.inputFormat=t),this.options.inputFormat},o.prototype.outputFormat=function(t){return null!=t&&(this.options.outputFormat=t),this.options.outputFormat},o.prototype.modal=function(t){var e;return null!=t&&(this.options.modal=t,1==this.options.modal?(0==this.options.inline&&(this.showObject(this.$calendar.find(".datepicker-close-wrap")),this.showObject(this.$calendar.find(".datepicker-bn-close-label"))),this.$close=this.$calendar.find(".datepicker-close"),this.$close.html(this.options.closeButtonTitle).attr("title",this.options.closeButtonLabel),this.$calendar.find(".datepicker-bn-close-label").html(this.options.closeButtonLabel),(e=this).$close.click(function(t){return e.handleCloseClick(t)}),this.$close.keydown(function(t){return e.handleCloseKeyDown(t)})):(this.hideObject(this.$calendar.find(".datepicker-close-wrap")),this.hideObject(this.$calendar.find(".datepicker-bn-close-label")))),this.options.modal},o.prototype.inline=function(t){return null!=t&&(0!=t?(this.hideObject(this.$button),this.hideObject(this.$calendar.find(".datepicker-close-wrap")),this.hideObject(this.$calendar.find(".datepicker-bn-close-label")),("string"==typeof t?y("#"+t):t).append(this.$calendar),this.$calendar.css({position:"relative",left:"0px",top:"0px"}),this.options.inline=t,this.initializeDate(),this.showObject(this.$calendar)):(this.$target.parent().after(this.$calendar),this.showObject(this.$button),1==this.options.modal&&(this.showObject(this.$calendar.find(".datepicker-close-wrap")),this.showObject(this.$calendar.find(".datepicker-bn-close-label"))),"static"===this.$calendar.parent().css("position")&&this.$calendar.parent().css("position","relative"),this.$calendar.css({position:"absolute"}),this.options.inline=t,this.hide())),this.options.inline},o.prototype.format=function(t){return this.formatDate(t,this.options.outputFormat)},o.prototype.enable=function(){this.$button.removeClass("disabled"),this.$button.attr("aria-disabled",!1),this.$button.attr("tabindex",0)},o.prototype.disable=function(){this.hide(),this.$button.addClass("disabled"),this.$button.attr("aria-disabled",!0),this.$button.attr("tabindex",-1)},o.prototype.datesDisabled=function(t){this.options.datesDisabled=[],y.isArray(t)||(t=[t]);var n=this;y.each(t,function(t,e){var i;"string"==typeof e?null!==(i=n.parseDate(e))&&n.options.datesDisabled.push(n.format(i)):e instanceof Date&&!isNaN(e.valueOf())&&n.options.datesDisabled.push(n.format(e))})},o.prototype.startview=function(t){switch(t){case 1:case"months":this.options.startView=1;break;case 2:case"years":this.options.startView=2;break;default:this.options.startView=0}},o.prototype.setLocales=function(t){this.locales=t,this.options.inputFormat=[this.locales.short_format],this.options.outputFormat=this.locales.short_format,this.options.titleFormat=this.locales.full_format,this.options.firstDayOfWeek=this.locales.firstday_of_week,this.options.buttonTitle=this.locales.texts.buttonTitle,this.$button.find("span").attr("title",this.options.buttonTitle),this.options.buttonLabel=this.locales.texts.buttonLabel,this.options.prevButtonLabel=this.locales.texts.prevButtonLabel,this.options.prevMonthButtonLabel=this.locales.texts.prevMonthButtonLabel,this.options.prevYearButtonLabel=this.locales.texts.prevYearButtonLabel,this.options.nextButtonLabel=this.locales.texts.nextButtonLabel,this.options.nextMonthButtonLabel=this.locales.texts.nextMonthButtonLabel,this.options.nextYearButtonLabel=this.locales.texts.nextYearButtonLabel,this.options.changeMonthButtonLabel=this.locales.texts.changeMonthButtonLabel,this.options.changeYearButtonLabel=this.locales.texts.changeYearButtonLabel,this.options.changeRangeButtonLabel=this.locales.texts.changeRangeButtonLabel,this.options.closeButtonTitle=this.locales.texts.closeButtonTitle,this.options.closeButtonLabel=this.locales.texts.closeButtonLabel,this.options.calendarHelp=this.locales.texts.calendarHelp,this.drawCalendarHeader(),"RTL"===this.locales.directionality?this.$grid.addClass("rtl"):this.$grid.removeClass("rtl")};var t=y.fn.datepicker;y.fn.datepicker=function(n,s){if("string"!=typeof n||1!=y(this).length)return this.each(function(){var t=y(this),e=t.data("ab.datepicker"),i=y.extend({},o.DEFAULTS,t.data(),"object"==typeof n&&n);e||t.data("ab.datepicker",e=new o(this,i)),"string"==typeof n&&e[n](s)});var t=y(this).eq(0).data("ab.datepicker");return t?t[n](s):void 0},y.fn.datepicker.Constructor=o,y.fn.datepicker.noConflict=function(){return y.fn.datepicker=t,this}}),function(a){var t,e=["","-webkit-","-ms-","-moz-","-o-"],i=document.createElement("div"),n=!1,s=!1,b=!1,w=0,o=["56"],r=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame,l=0,_=[],c={unstick:function(){for(var t,e,i=a(this).data("sticky-id"),n=_.length-1;0<=n;n--)if(_[n].id==i){t=n;break}return void 0!==t&&(e=_.splice(t,1)),void 0!==e&&a(this).removeAttr("style").next("."+e[0].options.holderClass).remove(),this}};if(!function(){if(!window.chrome)return!1;var t=/Chrom(e|ium)\/(\d+)/.exec(navigator.appVersion);return t&&~o.indexOf(t[2])}())for(var h=0,d=e.length;h<d;h++)if(i.setAttribute("style","position:"+e[h]+"sticky"),""!==i.style.position){n=!0;break}function x(t){var e,i,n,s,o,r,a,l={top:0,left:0},c=t&&t.ownerDocument;if(c)return(i=c.body)===t?{top:i.offsetTop,left:i.offsetLeft}:(e=c.documentElement,void 0!==t.getBoundingClientRect&&(l=t.getBoundingClientRect()),n=window,s=e.clientTop||i.clientTop||0,o=e.clientLeft||i.clientLeft||0,r=n.pageYOffset||e.scrollTop,a=n.pageXOffset||e.scrollLeft,{top:l.top+r-s,left:l.left+a-o});options.debug&&console.error("i-sticky: no element.ownerDocument defined")}function u(){var t=w,e=window.pageXOffset||document.documentElement.scrollLeft,i=t+(window.innerHeight||document.documentElement.clientHeight);b=!1;for(var n=0,s=_.length;n<s;n++){var o=_[n],r=o.el.offsetHeight,a=x(o.parent),l=o.style.isStuck?x(o.holder):x(o.el),c=o.style.top?o.style.top.px:0,h=o.style.bottom?o.style.bottom.px:0,d=o.style.home,u=!0,p=o.el.className.split(" "),f=0,m=(a.top,l.top-c),g=a.top+o.parent.offsetHeight-r-c,v=a.top+r+h,y=l.top+r+h;for(f in p)p[f]===o.options.stuckClass&&p.splice(f,1);o.el.className=p.join(" "),o.style.bottom&&i<=v?d=o.style.bottom.opposite:o.style.bottom&&v<i&&i<y?d=o.style.bottom.fixed:o.style.top&&m<t&&t<g?d=o.style.top.fixed:o.style.top&&g<=t?d=o.style.top.opposite:u=!1,o.style.isStuck!==u&&(o.holder.style.display=u?"block":"none"),u&&o.options.stuckClass&&(o.el.className+=" "+o.options.stuckClass),u&&(d+="margin-left:-"+(e-o.style.margin.left)+"px;"),o.options.fixWidth?d+="width:"+(u?o.holder.offsetWidth+"px;":"auto;"):d+="min-width:"+(u?o.holder.offsetWidth+"px;":"auto;"),d!==o.style.current&&(o.el.setAttribute("style",d),o.style.isStuck=u,o.style.current=d),o.options.holderAutoHeight&&u&&r!=o.style.height&&(o.holder.style.height=r+"px",o.style.height=r)}0}function p(){_.length&&(w=document.documentElement.scrollTop||document.body.scrollTop,b||(b=!0,r?r(u):(t&&clearTimeout(t),t=setTimeout(u,15))))}function f(){s||(a(window).on("scroll",p).on("resize",p),p(),s=!0)}a.fn.iSticky=function(t){if(n){if("object"!=typeof t||!t.force)return this;f()}if("string"==typeof t&&c[t])return c[t].apply(this,Array.prototype.slice.call(arguments,1));var o=a.extend({holderClass:"i-sticky__holder",holderAutoHeight:!0,debug:!1,fixWidth:!0,stuckClass:""},t),r=this.selector;return this.each(function(){var t,e,i,n=a(this),s="sticky-"+ ++l;n.hide(),t=n.css("top"),e=n.css("bottom"),n.show(),t||e?(n.data("sticky-id",s).after('<span class="'+o.holderClass+'" style="display:none;"></span>'),i={id:s,el:this,parent:this.parentElement,holder:this.nextSibling,style:{home:"position:static;",top:void 0,bottom:void 0,current:"",height:0,isStuck:!1,margin:{left:parseInt(n.css("margin-left"),10)}},options:{holderClass:o.holderClass,holderAutoHeight:o.holderAutoHeight,fixWidth:o.fixWidth,stuckClass:o.stuckClass||""}},"auto"!==t&&(i.style.top={fixed:"position:fixed;top:"+t+";bottom:auto;",opposite:"position:absolute;bottom:0;top:auto;",px:parseInt(t,10)}),"auto"!==e&&(i.style.bottom={fixed:"position:fixed;bottom:"+e+";top:auto;",opposite:"position:absolute;top:0;bottom:auto;",px:parseInt(e,10)}),_.push(i),p()):o.debug&&console.warn("i-sticky: element `top` or `bottom` properties must be set in pixels",r,this)})},n||f()}(jQuery),function(){var t,r,p,a,e,i,n,f=document.querySelector(".it-header-sticky");f&&(t=document.querySelector(".custom-navbar-toggler"),n=!1,(e=t)&&(n="none"===(i=window.getComputedStyle(e)).display||"hidden"===i.visibility),r=!1,a=void(p=0),function(e){var t=document.querySelector(".it-header-slim-wrapper"),i=document.querySelector(".it-header-center-wrapper"),n=document.querySelector(".it-header-navbar-wrapper"),d=n&&n.offsetHeight||0,s=t&&t.offsetHeight||0,u=s;e&&d&&(u=s+i?i.offsetHeight:0);function o(t,e,i){var n,s,o,r,a,l,c,h;void 0===e&&(e=!0),t&&(n=document.querySelector(".menu-wrapper"),e?(s=document.querySelector(".it-brand-wrapper"),o=document.querySelector(".it-search-wrapper"),r=document.querySelector(".it-user-wrapper"),a=s?s.cloneNode(!0):null,l=o?o.cloneNode(!0):null,c=r?r.cloneNode(!0):null,a&&n.insertBefore(a,n.childNodes[0]).classList.add("cloned"),l&&n.appendChild(l).classList.add("cloned"),c&&n.appendChild(c).classList.add("cloned").remove("show")):((h=document.getElementsByClassName("cloned"))&&Array.from(h).forEach(function(t){t.parentElement.removeChild(t)}),"function"==typeof i&&i())),f.nextElementSibling.style.paddingTop=e?d+(t?u-p:d-p)+"px":"0px"}a=function(){var t=d;window.scrollY+p>=u&&!r?(r=!0,f.classList.add("is-sticky"),o(e,!0),t!==d&&(p=d-t)):window.scrollY+p<u&&r&&(r=!1,f.classList.remove("is-sticky"),o(e,!1))},window.addEventListener("scroll",a),a()}(n))}(),function(){var p,e,i=document.getElementsByClassName("sticky-wrapper"),n=document.querySelector(".custom-navbar-toggler"),t=o(n),s=void 0;function o(t){var e,i=!1;return t&&(i="none"===(e=window.getComputedStyle(t)).display||"hidden"===e.visibility),i}i&&i.length&&(p=!1,e=function(h){function d(t,e){return h?parseInt((window.getComputedStyle?getComputedStyle(t,null):t.currentStyle)[e]):0}function u(t,e,i){return!h&&t&&"bottom"===i?"0px":!h&&t&&"top"===i?"auto":"bottom"===i?"":e+"px"}var t=h?document.querySelector(".it-header-navbar-wrapper"):document.querySelector(".it-header-center-wrapper");s=function(){var c=t?t.offsetHeight:0;Array.from(i).forEach(function(t){!function(t){var e=t.offsetHeight,i=t.parentNode;i.style.position="relative";var n=i.offsetWidth||0,s=i.offsetHeight,o=d(i,"paddingTop"),r=d(i,"paddingRight"),a=i.getBoundingClientRect().top||0,l=c+o;a<=c?(p=!0,t.classList.add("is-sticky"),t.style.top=u(!0,l,"top"),t.style.bottom=u(!0,l,"bottom"),t.style.width=h?n-r+"px":""):p&&c<a&&(p=!1,t.classList.remove("is-sticky"),t.style.top="",t.style.bottom="",t.style.width=""),p&&h&&(a<0&&Math.abs(a)+e+o+c>s?t.classList.add("at-bottom"):t.classList.remove("at-bottom"))}(t)})},window.addEventListener("scroll",s),s()},window.addEventListener("resize",function(){var t;s&&(window.removeEventListener("scroll",s),t=o(n),e(t))}),e(t))}();var styleNode=document.createElement("style"),__PUBLIC_PATH__=window.__PUBLIC_PATH__?window.__PUBLIC_PATH__:"/bootstrap-italia/dist/fonts";styleNode.innerHTML="\n/* Titillium+Web:300,400,600,700 */\n\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 300;\n  src: local('Titillium Web Light'), local('TitilliumWeb-Light'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 300;\n  src: local('Titillium Web Light'), local('TitilliumWeb-Light'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Light.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Titillium Web Regular'), local('TitilliumWeb-Regular'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Titillium Web Regular'), local('TitilliumWeb-Regular'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Regular.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 600;\n  src: local('Titillium Web SemiBold'), local('TitilliumWeb-SemiBold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 600;\n  src: local('Titillium Web SemiBold'), local('TitilliumWeb-SemiBold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-SemiBold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Titillium Web Bold'), local('TitilliumWeb-Bold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Titillium Web';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Titillium Web Bold'), local('TitilliumWeb-Bold'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Titillium_Web/TitilliumWeb-Bold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n\n/* Lora:400,700 */\n\n/* latin-ext */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Lora Regular'), local('Lora-Regular'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Lora Regular'), local('Lora-Regular'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Regular.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Lora Bold'), local('Lora-Bold'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Lora';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Lora Bold'), local('Lora-Bold'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Lora/Lora-Bold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n\n/* Roboto+Mono:400,700 */\n\n/* latin-ext */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Roboto Mono'), local('RobotoMono-Regular'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 400;\n  src: local('Roboto Mono'), local('RobotoMono-Regular'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Regular.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Roboto Mono Bold'), local('RobotoMono-Bold'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.ttf) format('truetype');\n  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Roboto Mono';\n  font-style: normal;\n  font-weight: 700;\n  src: local('Roboto Mono Bold'), local('RobotoMono-Bold'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff2) format('woff2'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.woff) format('woff'),\n  url("+__PUBLIC_PATH__+"/Roboto_Mono/RobotoMono-Bold.ttf) format('truetype');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n",document.getElementsByTagName("head")[0].appendChild(styleNode),$(function(){$.fn.autocomplete=function(t){return t=$.extend({data:{}},t),this.each(function(){var c=$(this),h=null,d=c.data("autocomplete");d&&Object.keys(d).length&&((h=$('<ul class="autocomplete-list"></ul>')).insertAfter($(this).next()),c.on("keyup",function(t){var e=c.val();if(h.empty(),e.length)for(var i in d){var n,s=new RegExp("("+e+")","gi"),o=d[i].text.replace(s,"<mark>$1</mark>"),r=d[i].label?"<em>"+d[i].label+"</em>":"",a=d[i].icon?d[i].icon:"",l=d[i].link?d[i].link:"#";-1!==o.toLowerCase().indexOf(e.toLowerCase())&&($(this).closest(".form-group").find(".autocomplete-list").addClass("autocomplete-list-show"),n=$('<li>\n              <a href="'+l+'">\n                '+a+'\n                <span class="autocomplete-list-text">\n                  <span>'+o+"</span>\n                  "+r+"\n                </span>\n              </a>\n             </li>"),h.append(n))}else $(this).closest(".form-group").find(".autocomplete-list").removeClass("autocomplete-list-show")}))})},$(".autocomplete").autocomplete()}),$(function(){var t=$('a[data-attribute*="back-to-top"]');$(window).on("scroll",function(){t.toggleClass("back-to-top-show",t.length&&100<=$(this).scrollTop())}),t.on("click",function(){$("body,html").animate({scrollTop:0},800)})});var ComponenteBase=function(s){var t="componenteBase",o="bs.componente-base",e=s.fn[t],i={CLICK_DATA_API:"click.bs.componente-base.data-api"},n=function(){function n(t){this._element=t}return n.prototype.publicFunction=function(t){console.log(s(t).data("value"),n.VERSION)},n._jQueryInterface=function(i){return this.each(function(){var t=s(this),e=t.data(o);if(e||(e=new n(this),t.data(o,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},n._handleConsole=function(e){return function(t){t&&t.preventDefault(),e.publicFunction(this)}},_createClass2(n,null,[{key:"VERSION",get:function(){return"1.0.0"}}]),n}();return s(document).on(i.CLICK_DATA_API,".componente-base",n._handleConsole(new n)),s.fn[t]=n._jQueryInterface,s.fn[t].Constructor=n,s.fn[t].noConflict=function(){return s.fn[t]=e,n._jQueryInterface},n}($),Cookiebar=function(s){var t="cookiebar",o="bs.cookiebar",e="."+o,i=s.fn[t],r="cookies_consent",n=".cookiebar",a='[data-accept="cookiebar"]',l={CLOSE:"close"+e,CLOSED:"closed"+e,LOAD_DATA_API:"load"+e+".data-api",CLICK_DATA_API:"click"+e+".data-api"},c="cookiebar",h="show",d=function(){function n(t){this._element=t}var t=n.prototype;return t.show=function(t){s(t).addClass(h).attr("aria-hidden","false").attr("aria-live","polite")},t.close=function(t){t=t||this._element;var e=this._getRootElement(t);this._triggerCloseEvent(e).isDefaultPrevented()||(this._setCookieEU(),this._removeElement(e))},t.dispose=function(){s.removeData(this._element,o),this._element=null},t._setCookieEU=function(){var t=new Date;t.setDate(t.getDate()+30);var e=escape("true")+("; expires="+t.toUTCString());document.cookie=r+"="+e+"; path=/"},t._getSelectorFromElement=function(t){var e,i=t.getAttribute("data-target");i&&"#"!==i||(i=(e=t.getAttribute("href"))&&"#"!==e?e.trim():"");try{return document.querySelector(i)?i:null}catch(t){return null}},t._getRootElement=function(t){var e=this._getSelectorFromElement(t),i=!1;return e&&(i=s(e)[0]),i=i||s(t).closest("."+c)[0]},t._triggerCloseEvent=function(t){var e=s.Event(l.CLOSE);return s(t).trigger(e),e},t._removeElement=function(t){s(t).removeClass(h).attr("aria-hidden","true").attr("aria-live","off"),this._destroyElement(t)},t._destroyElement=function(t){s(t).detach().trigger(l.CLOSED).remove()},n._jQueryInterface=function(i){return this.each(function(){var t=s(this),e=t.data(o);if(e||(e=new n(this),t.data(o,e)),"string"==typeof i){if(void 0===e[i])throw new TypeError('No method named "'+i+'"');e[i](this)}})},n._handleAccept=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},n._handleConsent=function(e){return function(t){t&&t.preventDefault(),e.close(this)}},n._getCookieEU=function(){for(var t,e,i=document.cookie.split(";"),n=0;n<i.length;n++)if(t=i[n].substr(0,i[n].indexOf("=")),e=i[n].substr(i[n].indexOf("=")+1),(t=t.replace(/^\s+|\s+$/g,""))==r)return unescape(e)},_createClass2(n,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),n}();return s(document).on(l.CLICK_DATA_API,a,d._handleAccept(new d)),s(window).on(l.LOAD_DATA_API,function(){var t=s.makeArray(s(n));if(!d._getCookieEU())for(var e=t.length;e--;){var i=s(t[e]);d._jQueryInterface.call(i,"show")}}),s.fn[t]=d._jQueryInterface,s.fn[t].Constructor=d,s.fn[t].noConflict=function(){return s.fn[t]=i,d._jQueryInterface},d}($);function notificationShow(t,e){var i;$("#"+t).fadeIn(300),$("#"+t).hasClass("dismissable")||($("#"+t).fadeIn(300),i="number"==typeof e?e:7e3,setTimeout(function(){$("#"+t).fadeOut(100)},i))}$(function(){$(".navbar .dropdown").on("show.bs.dropdown",function(){window.matchMedia("(max-width: 992px)").matches&&$(this).find(".dropdown-menu").first().stop(!0,!0).slideDown(180)}),$(".navbar .dropdown").on("hide.bs.dropdown",function(){window.matchMedia("(max-width: 992px)").matches&&$(this).find(".dropdown-menu").first().stop(!0,!0).slideUp(180)}),$(window).resize(function(){window.matchMedia("(min-width: 993px)").matches&&$(".navbar .dropdown-menu.show").removeAttr("style")})}),$(function(){function o(t){var e,i=t.siblings("label:not(.active)");i&&i.length&&(e=i[0].offsetWidth>t[0].offsetWidth-20?t[0].offsetWidth:"auto",$(i[0]).css("width",e))}var t='input[type="text"],input[type="password"],input[type="email"],input[type="email"],input[type="url"],input[type="tel"],input[type="number"],input[type="search"],textarea',e='input[type="file"]';$(document).on("focus",t,function(t){var e=$(t.target);e.siblings("label, i").addClass("active");var i=e.siblings("label");i&&i.length&&$(i[0]).css("width","auto")}).on("blur",t,function(t){var e=$(t.target),i=!e.val(),n=!e.attr("placeholder");i&&n&&(e.siblings("label, i").removeClass("active"),o(e))}).on("change",t,function(t){var e=$(t.target);s(e),r(e)}).on("blur",e,function(t){$(t.target).siblings("label").addClass("active")}).on("change",e,function(t){var e=$(t.target),n=t.currentTarget.files.length,s="",o="";for(i=0;i<n;i++)fileSize=parseInt(t.currentTarget.files[i].size,10)/1024,filesize=Math.round(fileSize),s=s+t.currentTarget.files[i].name+" ("+filesize+"kb); ";1<n&&(o=n+" file da caricare: "),e.siblings(".form-file-name").text(o+s)});function n(){$("body").find(t).removeClass("valid invalid").each(function(t,e){var i=$(e),n=!!i.val(),s=!!i.attr("placeholder");(n||s)&&i.siblings("label, i").css("transition","none").addClass("active"),n||s||(i.siblings("label, i").removeClass("active"),o(i))})}var s=function(t){var e=t.siblings("label, i"),i=t.val().length,n=!!t.attr("placeholder");i||n?e.addClass("active"):e.removeClass("active")},r=function(t){var e,i,n,s,o;t.hasClass("validate")&&(i=!(e=t.val()).length,n=!t[0].validity.badInput,i&&n?t.removeClass("valid").removeClass("invalid"):(s=t.is(":valid"),o=Number(t.attr("length"))||0,s&&(!o||o>e.length)?t.removeClass("invalid").addClass("valid"):t.removeClass("valid").addClass("invalid")))};$(window).resize(function(){$(t).each(function(t,e){var i=$(e);o(i)})}),n(),$(document).on("changed.bs.form-control",n)}),$(function(){var e;$(document).on("keydown mousedown",function(t){e="mousedown"===t.type}).on("focusin",function(t){e&&t.target&&$(t.target).addClass("focus--mouse")}).on("focusout",function(t){t.target&&$(t.target).removeClass("focus--mouse")})}),$(function(){$('a[data-attribute*="forward"]').on("click",function(t){var e=$(this.hash);e.length&&(t.preventDefault(),$("html, body").animate({scrollTop:e.offset().top},500))})}),$(function(){var s=$(".custom-navbar-toggler"),t=$(".close-div"),o=$(".it25-close-div"),e=$(".overlay"),n=$(".it-back-button"),i=$(".navbar-collapsable a");$(s).on("click",function(t){var e=$(this).attr("data-target"),i=$(e).find(".overlay");$(this).attr("aria-expanded","true"),$(n).fadeIn(),$(e).show(),$(i).fadeIn(),$(e).addClass("expanded"),$(this).hide(),$(o).show(),$(this).hasClass("it25-btn-sidebar")&&$("body,html").animate({scrollTop:0},800)}),$(e).on("click",function(){var t=$(this).closest(".navbar-collapsable"),e=$(this).closest(".navbar").find(".custom-navbar-toggler"),i=$(t).find(".overlay");$(e).attr("aria-expanded","false"),$(t).removeClass("expanded"),$(i).fadeOut(),$(o).hide(),$(s).show(),setTimeout(function(){$(t).hide()},300)}),$(t).on("click",function(t){var e=$(this).closest(".navbar-collapsable"),i=$(this).closest(".navbar").find(".custom-navbar-toggler"),n=$(e).find(".overlay");$(i).attr("aria-expanded","false"),$(e).removeClass("expanded"),$(n).fadeOut(),$(o).hide(),$(s).show(),$(e).hide()}),$(o).on("click",function(t){var e=$(this).find(".close-menu").attr("data-target"),i=$(".it25-barra-ist").find(".custom-navbar-toggler"),n=$(e).find(".overlay");$(i).attr("aria-expanded","false"),$(e).removeClass("expanded"),$(n).fadeOut(),$(this).hide(),$(s).show(),setTimeout(function(){$(e).hide()},300)}),$(i).on("blur",function(t){closemenu=$(this).closest(".navbar-collapsable").find(".close-div .btn"),$(this).closest(".navbar-collapsable").hasClass("expanded")&&setTimeout(function(){var t=document.activeElement;0==$(t).closest(".navbar-collapsable").length&&$(closemenu).trigger("click")},50)}),$(t).on("blur",function(t){closemenu=$(this),$(this).closest(".navbar-collapsable").hasClass("expanded")&&setTimeout(function(){var t=document.activeElement;0==$(t).closest(".navbar-collapsable").length&&$(closemenu).trigger("click")},50)})}),$(function(){$(".custom-navbar-toggler"),$(".close-div");var i=$(".overlay"),t=$(".it-back-button");$(".navbar-collapsable a");$('.it-bottom-navscroll ul li a[href^="#"]').on("click",function(t){t.preventDefault();var e=this.hash;$("html, body").animate({scrollTop:$(e).offset().top},600,function(){history.pushState?history.pushState(null,null,e):location.hash=e}),$(i).trigger("click")}),$(t).click(function(t){$(i).trigger("click"),$(this).fadeOut(),t.preventDefault()}),$(window).on("scroll",function(){var t=$(".it-page-sections-container").length?$(".it-page-sections-container").offset().top:0,r=$(window).scrollTop()-t;$(".it-page-section").each(function(t){var e,i,n,s,o;$(this).position().top<=r&&($(".it-navscroll-wrapper .menu-wrapper a.active").removeClass("active"),$(".it-navscroll-wrapper .menu-wrapper a").eq(t).addClass("active"),e=$(".it-navscroll-wrapper .menu-wrapper a").eq(t).closest("ul").prev("a"),i=$(e).closest("ul").prev("a"),$(e).addClass("active"),$(i).addClass("active"),n=$(".it-navscroll-wrapper .menu-wrapper a").eq(t).find("span").text(),o=(s=$(".it-navscroll-wrapper .custom-navbar-toggler")).find("span.it-list"),s.text(n),s.prepend(o))})}).scroll()}),$(function(){$(".go-back").on("click",function(){return window.history.back(),!1})}),$(document).on("click",".notification-close",function(){$(this).closest(".notification").fadeOut(100)}),$(function(){$(".upload-dragdrop:not(.success)").on("drag dragstart dragend dragover dragenter dragleave drop",function(t){t.preventDefault(),t.stopPropagation()}).on("dragover dragenter",function(){$(this).addClass("dragover")}).on("dragleave dragend drop",function(){$(this).removeClass("dragover")}).on("drop",function(t){$(this).addClass("loading"),$(this).find(".upload-progress").circularloader({backgroundColor:"#ffffff",fontColor:"#000000",fontSize:"40px",radius:130,progressBarBackground:"transparent",progressBarColor:"#0073e6",progressBarWidth:96,progressPercent:0})})});var progressDonut=function(i){return{generate:function(t,e){i(t).circularloader({backgroundColor:"#fff",fontColor:"#000",fontSize:40,radius:130,progressBarBackground:"transparent",progressBarColor:"#0073e6",progressBarWidth:96,progressPercent:e}),i(t).next().html("Progresso "+e+"%")},update:function(t,e){i(t).circularloader({progressPercent:e})}}}($);$(function(){$(".it-has-checkbox").on("click",function(t){var e=$(this).find("input");$(e).prop("checked")?($(e).prop("checked",!1),$(this).removeClass("active")):($(e).prop("checked",!0),$(this).addClass("active")),t.preventDefault()})}),$(function(){var t=window.navigator.userAgent;/msie|Trident.*rv[ :]*11\./gi.test(t)&&$(".img-wrapper").each(function(){var t=$(this),e=t.find("img").prop("src");e&&t.css("backgroundImage","url("+e+")").addClass("custom-object-fit")})});var numbers=[48,49,50,51,52,53,54,55,56,57],timeRegEx=/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/i;$(document).ready(function(){function _(t){return[8,9,13].includes(t)}function x(t,e,i,n,s){var o;t.hasClass("is-open")&&(t.fadeOut(100).toggleClass("is-open").attr("aria-hidden","true"),i&&n&&(o=i.attr("value")+":"+n.attr("value"),e.val(o)),k(e,s))}var C={};function k(t,e){var i,n=t.val();n&&(i=t.siblings("label"),(""!=n?n.match(timeRegEx):"")?i.removeClass("error-label").html(C[e]):i.addClass("error-label").html("Formato ora non valido (hh:mm)"))}$(".it-timepicker-wrapper").each(function(s){function n(t){return t<0&&(t=0),t<10?"0"+t:t}function o(t,e){switch(h=e.closest(".spinner").find("input"),r=parseInt(h.attr("aria-valuemin")),a=parseInt(h.attr("aria-valuemax")),l=parseInt(h.attr("aria-valuenow")),c=parseInt(h.attr("bb-skip")),t){case"up":(!a||l<a)&&l++;break;case"down":(!r||r<l)&&l--}if(t&&-1<c)switch(!0){case"up"===t&&c===l:l++;break;case"down"===t&&c===l:l--}switch(!0){case e.hasClass("btnHourUp")||e.hasClass("btnHourDown"):d=n(l);break;case e.hasClass("btnMinUp")||e.hasClass("btnMinDown"):u=n(l)}h.val(n(l)),h.attr("value",n(l)),h.attr("aria-valuenow",n(l)),p.val(d+":"+u).change()}function i(t,e){var i=n(t.val());t.attr("aria-valuenow",i),o(null,e)}var r,a,l,c,h,d="00",u="00",t=$(this),p=t.find(".txtTime"),e=t.find(".btn-time"),f=t.find(".spinner-control"),m=t.find(".spinnerHour"),g=t.find(".spinnerMin"),v=t.find(".btnHourUp"),y=t.find(".btnHourDown"),b=t.find(".btnMinUp"),w=t.find(".btnMinDown");C[s]=p.siblings("label").text(),t.find(".spinner-control button").attr("aria-hidden","true").attr("tabindex","-1"),e.on("click",function(t){t.stopPropagation(),f.hasClass("is-open")?x(f,p,m,g,s):f.toggleClass("is-open").attr("aria-hidden","false").fadeIn(100)}),p.on("keyup",function(t){var e,i=t.which||t.keyCode,n=p.val();if(n.includes(":")?(e=n.split(":"),m.attr("aria-valuenow",e[0].substring(0,2)),m.attr("value",e[0].substring(0,2)),m.val(e[0].substring(0,2)),d=e[0].substring(0,2),g.attr("aria-valuenow",e[1].substring(0,2)),g.attr("value",e[1].substring(0,2)),g.val(e[1].substring(0,2)),u=e[1].substring(0,2)):(m.attr("aria-valuenow",n.substring(0,2)),m.attr("value",n.substring(0,2)),m.val(n.substring(0,2)),d=n.substring(0,2)),13===i)return k(p,s)}).on("focus",function(t){t.stopPropagation(),p.val()&&k(p,s)}).on("blur",function(t){p.val()&&k(p,s)}),v.on("click",function(t){o("up",v)}),y.on("click",function(t){o("down",y)}),b.on("click",function(t){o("up",b)}),w.on("click",function(t){o("down",w)}),m.on("keydown",function(t){var e=t.which||t.keyCode,i=numbers.includes(e);switch(!0){case 38===e:v.trigger("click");break;case 40===e:y.trigger("click");break;case _(e)||i:return!0}return!1}).on("keyup",function(t){var e=t.which||t.keyCode;numbers.includes(e)&&i(m,v)}),g.on("keydown",function(t){var e=t.which||t.keyCode,i=numbers.includes(e);switch(!0){case 38===e:b.trigger("click");break;case 40===e:w.trigger("click");break;case _(e)||i:return!0}return!1}).on("keyup",function(t){var e=t.which||t.keyCode;numbers.includes(e)&&i(g,b)}),$(document).on("click",function(t){x(f,p,m,g,s)}),f.on("click",function(t){t.stopPropagation()})})}),$(function(){function a(t){var e=t.closest(".input-number");e.hasClass("input-number-adaptive")&&(e.hasClass("input-number-percentage")||(t.css("width","calc(44px + "+t.val().length+"ch)"),isIe()&&t.css("width","calc(44px + (1.5 * "+t.val().length+"ch))")),e.hasClass("input-number-currency")&&(t.css("width","calc(40px + 44px + "+t.val().length+"ch)"),isIe()&&t.css("width","calc(40px + 44px + (1.5 * "+t.val().length+"ch))")))}$(".input-number input[type=number]").each(function(t){a($(this))}),$(".input-number button").click(function(t){t.preventDefault();var e,i,n,s,o=$(this).closest(".input-number").find("input[type=number]"),r=parseFloat(o.val());isNaN(r)||(e=0,i=parseFloat(o.attr("max")),n=parseFloat(o.attr("min")),s=(s=parseFloat(o.attr("step")))||1,$(this).hasClass("input-number-add")&&(e=!isNaN(i)&&i<=r+s?i:r+s,o.val(e)),$(this).hasClass("input-number-sub")&&(e=!isNaN(n)&&r-s<=n?n:r-s,o.val(e))),a(o)}),$(".input-number input[type=number]").change(function(t){var e,i,n=$(this),s=parseFloat(n.val());isNaN(s)||(e=parseFloat(n.attr("max")),(i=parseFloat(n.attr("min")))&&s<i&&(s=i),e&&e<s&&(s=e),n.val(s)),a(n)}),$("input[type=number]").on("keyup",function(t){var e=t&&t.target.value;this.value=e.replace(/[^0-9,.]/g,"")})}),$(function(){var t=$(".transfer-header input"),e=$(".transfer-group input"),o=$(".it-transfer-block").find("a.transfer"),r=$(".it-transfer-block").find("a.backtransfer"),n=$(".it-transfer-block").find("a.reset"),d=$(".source .transfer-group .form-check"),u=$(".target .transfer-group .form-check"),p=d.length,f=u.length;function m(t){t.removeClass("active").attr("disabled","disabled").attr("aria-disabled","true")}function a(t){t.addClass("active").removeAttr("disabled").removeAttr("aria-disabled")}function s(t,e){var i=t.find(".transfer-group input:checked"),n=i.closest(".form-check"),s=t.find(".transfer-header input"),o=t.find(".transfer-header span.num"),r=i.length,a=t.find(".transfer-group input").length,l=e.find(".transfer-group"),c=e.find(".transfer-group input").length+r,h=e.find(".transfer-header span.num"),d=e.find(".transfer-header input");n.each(function(){$(this).detach().appendTo(l).find("input").prop("checked",!1)});var u=a-r,p=c;o.text(u),h.text(p),0==u&&s.prop("disabled",!0),0<p&&d.prop("disabled",!1),s.removeClass("semi-checked").prop("checked",!1)}$(d).each(function(t){0}),$(u).each(function(t){0}),$(t).on("click",function(){var t,e,i,n,s=$(this).closest(".it-transfer-wrapper");t=s,e=$(t).find(".transfer-group input"),i=$(t).find(".transfer-group input:checked"),n=$(t).find(".transfer-header input"),o=t.closest(".it-transfer-block").find("a.transfer"),r=t.closest(".it-transfer-block").find("a.backtransfer"),0<i.length?($(e).prop("checked",!1),$(n).removeClass("semi-checked").prop("checked",!1),t.hasClass("source")?m(o):m(r)):($(e).prop("checked",!0),t.hasClass("source")?a(o):a(r))}),$(e).on("click",function(){var t,e,i,n,s=$(this).closest(".it-transfer-wrapper");t=s,e=$(t).find(".transfer-group input"),i=$(t).find(".transfer-group input:checked"),n=$(t).find(".transfer-header input"),o=t.closest(".it-transfer-block").find("a.transfer"),r=t.closest(".it-transfer-block").find("a.backtransfer"),0==i.length?(n.removeClass("semi-checked").prop("checked",!1),t.hasClass("source")?m(o):m(r)):(i.length==e.length?n.removeClass("semi-checked").prop("checked",!0):n.addClass("semi-checked").prop("checked",!1),t.hasClass("source")?a(o):a(r))}),$(o).on("click",function(t){var e=$(this).closest(".it-transfer-block").find(".source"),i=$(this).closest(".it-transfer-block").find(".target");a(n=$(this).closest(".it-transfer-block").find("a.reset")),m($(this)),s(e,i),t.preventDefault()}),$(r).on("click",function(t){var e=$(this).closest(".it-transfer-block").find(".source"),i=$(this).closest(".it-transfer-block").find(".target");a(n=$(this).closest(".it-transfer-block").find("a.reset")),m($(this)),s(i,e),t.preventDefault()}),$(n).on("click",function(t){var e,i,n,s,o,r,a,l=$(this).closest(".it-transfer-block"),c=l.find("a.transfer"),h=l.find("a.backtransfer");m(c),m(h),m($(this)),i=(e=l).find(".source .transfer-group"),n=e.find(".target .transfer-group"),s=e.find(".source .transfer-header span.num"),o=e.find(".target .transfer-header span.num"),r=e.find(".transfer-header input"),a=e.find(".transfer-group input"),$(i).find(".form-check").detach(),$(n).find(".form-check").detach(),$(i).append(d),$(n).append(u),$(s).text(p),$(o).text(f),$(r).prop("disabled",!1).removeClass("semi-checked"),$(r).prop("checked",!1),$(a).prop("checked",!1),t.preventDefault()})}),$(function(){function i(){$(".dropdown-menu li.selected").find('input[type="checkbox"]').prop("checked",!0),$(".dropdown-menu li:not(.selected)").find('input[type="checkbox"]').prop("checked",!1)}jQuery.fn.setOptionsToSelect=function(t){var e=$(this).find("select");return $(e).off("changed.bs.select").selectpicker("destroy").empty(),t.forEach(function(t){$(e).append($("<option>",{value:t.value,text:t.text}))}),$(e).selectpicker("refresh").on("changed.bs.select",i),this},$(".bootstrap-select-wrapper select").selectpicker().on("changed.bs.select",i);var t=$(".bootstrap-select-wrapper");t.find("select option.bs-title-option").text("Nessuna opzione"),t.find("select option[data-content]").text("Annulla"),t.find("button.dropdown-toggle").removeAttr("role"),t.find("div.filter-option").replaceWith(function(){return $("<span />").addClass("filter-option").append($(this).contents())}),t.find("div.filter-option-inner").replaceWith(function(){return $("<span />").addClass("filter-option-inner").append($(this).contents())}),t.find("div.filter-option-inner-inner").replaceWith(function(){return $("<span />").addClass("filter-option-inner-inner").append($(this).contents())}),t.find(".bs-searchbox input").attr("title","Cerca").attr("aria-expanded","false")}),$(function(){$(".rating.rating-label input[type=radio]").on("click",function(t){var e=$(this).val(),i=1==e?"stella":"stelle";$(this).closest(".rating-label").find("legend span:not(.sr-only)").text(e+" "+i)})}),$(function(){$.fn.dimmerShow=function(){return this.each(function(){"flex"!=$(this).css("display")&&$(this).css("display","flex").hide().fadeIn(200)})},$.fn.dimmerHide=function(t){return this.each(function(){$(this).fadeOut(200)})}}),$(function(){var t,e,i,n;$(".bd-example")[0]||(t=function(){var t=$(".it25-top-bar").outerHeight(!0)+$(".it25-barra-ist").outerHeight(!0);$(".it25-search-container").css({top:t+"px"})},e=function(){$(document).scrollTop()>i?($(".it25-top-bar").addClass("shrink"),$(".it-header-slim-wrapper-content").hide(),$(".it25-barra-ist").addClass("shrink"),$(".it25-menu-principale").addClass("header-shrinked")):$(document).scrollTop()<n&&($(".it25-top-bar").removeClass("shrink"),$(".it-header-slim-wrapper-content").show(),$(".it25-barra-ist").removeClass("shrink"),$(".it25-menu-principale").removeClass("header-shrinked")),t()},i=90,n=2,function(){if(null!=$("header").attr("class")){var t=$("header").attr("class").split("-");if("it25"==t[0])switch(t[1]){case"menu":$(".it25-hamburger-btn-wrapper").addClass("d-lg-none");break;case"sidebar":$(".it25-hamburger-btn-wrapper").addClass("d-md-none"),$(".it25-btn-menu").addClass("it25-btn-sidebar");break;case"none":$(".it25-hamburger-btn-wrapper").addClass("d-none");break;default:$(".it25-hamburger-btn-wrapper").addClass("d-lg-none")}else $(".it25-hamburger-btn-wrapper").addClass("d-lg-none")}else $(".it25-hamburger-btn-wrapper").addClass("d-lg-none")}(),0<$(".it25-barra-utente").outerHeight(!0)&&$(".it25-menu-principale").addClass("postUserBar"),e(),$(window).on("scroll",e),$(window).on("resize",e),$(".it-search-wrapper").on("show.bs.collapse",t))}),$(function(){function e(t){for(var e=0;e<t.length;e++)label=t[e],label.hasAttribute("style")&&(attrStyle=label.getAttribute("style"),pos=attrStyle.search("width: 0px;"),0<=pos&&(result=attrStyle.replace("width: 0px","width: auto"),label.setAttribute("style",result)))}$('a[data-toggle="tab"]').on("shown.bs.tab",function(t){rif=t.target.getAttribute("href").substr(1),panel=document.getElementById(rif),labels=panel.getElementsByTagName("label"),e(labels)}),$('div[role="tabpanel"]').on("shown.bs.collapse",function(t){labels=t.target.getElementsByTagName("label"),e(labels)})}),$(function(){var t,e,i,n,s,o,r,a,l,c,h,d,u,p,f,m,g,v,y,b,w,_,x,C,k,T,E,D,A,S,L,I,O,N=$(".owl-carousel.it-carousel-all");$(N).each(function(){$(this).closest(".it-carousel-wrapper").hasClass("it-carousel-landscape-abstract-three-cols")&&(n=[],s=300,o=500,r=200,h=!(c=l=a=!(e=!(t=!0))),f=!(p=!(d="page")),m=1,b=!(y=!(v=24)),w=2,_=g=40,k=!(C=!(x=24)),S=!(A=!(T=i=3)),L=3,O=I=D=E=0,$(this).hasClass("it-card-bg")&&(_=g=40,I=E=12,O=D=x=v=24),$(this).hasClass("it-img-card")&&(_=g=40,I=E=0,O=D=x=v=24),$(this).hasClass("it-img-card")&&$(this).hasClass("it-big-img")&&(e=!0,v=g=0,_=160,x=24,I=E=320,O=D=48,L=T=w=1),$(this).hasClass("it-img-card")&&$(this).hasClass("it-standard-image")&&(e=!0,g=40,m=1,w=2,E=68,I=_=48,O=D=x=v=24,L=T=3)),$(this).closest(".it-carousel-wrapper").hasClass("it-calendar-wrapper")&&(e=!(t=!0),n=[],s=300,o=500,r=200,h=!(c=l=a=!0),f=!(p=!(d="page")),m=1,b=!(y=!(g=40)),w=2,k=!(C=!(_=40)),S=!(A=!(T=i=4)),L=4,O=I=D=E=x=v=0,$(this).hasClass("it-card-bg")&&(_=g=40,O=I=D=E=x=v=0),$(this).hasClass("it-img-card")&&(_=g=40,O=I=D=E=x=v=0),$(this).hasClass("it-img-card")&&$(this).hasClass("it-big-img")&&(e=!0,_=160,I=E=320,O=D=x=v=g=0,L=T=w=1),$(this).hasClass("it-img-card")&&$(this).hasClass("it-standard-image")&&(e=!0,g=40,m=1,x=w=2,E=68,I=_=48,O=D=v=0,L=T=4)),$(this).closest(".it-carousel-wrapper").hasClass("it-carousel-landscape-abstract")&&(n=[],s=300,o=500,r=200,h=!(c=l=a=!(e=!(t=!0))),f=!(p=!(d="page")),b=!(y=!(v=24)),k=!(C=!(x=_=24)),S=!(A=!(T=w=m=i=1)),L=1,O=I=D=E=g=0),$(this).hasClass("it25-carousel-nav")&&(A=C=y=p=t=!0,n=['<span class="icon icon-primary">&ltrif;</span>','<span class="icon icon-primary">&rtrif;</span>']),$(this).hasClass("it25-carousel-autoplay")?(cautoplay=!0,cautoplayHoverPause=!0,e=!0):(cautoplay=!1,cautoplayHoverPause=!1),$(this).owlCarousel&&$(this).owlCarousel({nav:t,autoplay:cautoplay,autoplayHoverPause:cautoplayHoverPause,loop:e,margin:24,items:i,navText:n,navSpeed:s,smartSpeed:o,dotsSpeed:r,navElement:"button",dotElement:"button",controlsAriaHidden:a,mouseDrag:l,touchDrag:c,dots:h,slideBy:d,stagePadding:u,responsive:{0:{nav:p,dots:f,items:m,stagePadding:g,margin:v},768:{nav:y,dots:b,stagePadding:_,items:w,margin:x},992:{nav:C,dots:k,items:T,stagePadding:E,margin:D},1200:{nav:A,dots:S,stagePadding:I,items:L,margin:O}},onInitialized:function(t){$(t.target).find(".owl-dot").each(function(t){$(this).attr("aria-labelledby","owl-dot-"+t)})}})})});
//# sourceMappingURL=bootstrap-lombardia.bundle.min.js.map
