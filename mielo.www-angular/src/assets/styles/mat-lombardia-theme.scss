//// Custom Theming for Angular Material
//// For more information: https://material.angular.io/guide/theming
////@import "node_modules/@angular/material/theming";
//// Plus imports for other components in your app.
//// Include the common styles for Angular Material. We include this here so that you only
//// have to load a single css file for Angular Material in your app.
//// Be sure that you only ever include this mixin once!
//// Define a custom typography config that overrides the font-family as well as the
//// `headlines` and `body-1` levels.
////$my-custom-typography: mat-typography-config($font-family: '"Titillium Web", sans-serif',
////  $headline: mat-typography-level(32px, 48px, 700),
////  $body-1: mat-typography-level(16px, 24px, 500),
////);
//// Override the typography in the core CSS.
//@include mat-core($my-custom-typography);
//@import "mat-lombardia-palette";
//@import "mat-lombardia-variables";
//// import custom componenet themes
//@import "mat-lombardia-component-themes";
//
///* ======== angular material custom theme ======== */
//
//$my-custom-primary: mat-palette($mat-verdelombardia);
//$my-custom-accent: mat-palette($mat-blulombardia);
//
///*, 100, 500, A100);*/
//
//$my-custom-warn: mat-palette($mat-deep-orange);
//// Light theme
//$my-custom-theme: mat-light-theme($my-custom-primary,
//  $my-custom-accent,
//  $my-custom-warn);
//// Dark theme
////$my-custom-theme: mat-dark-theme($my-custom-primary, $my-custom-accent, $my-custom-warn);
//// Include theme styles for core and each component used in your app.
//// Alternatively, you can import and @include the theme mixins for each component
//// that you are using.
//@include angular-material-theme($my-custom-theme);
//@include mat-lombardia-components-theme($my-custom-theme);
//
//// Alternate Angular Material Theme
//.my-alternate-theme {
//  $my-alternate-primary: mat-palette($mat-blulombardia);
//  $my-alternate-accent: mat-palette($mat-verdelombardia);
//  /*, 400);*/
//  $my-alternate-warn: mat-palette($mat-grey);
//  $my-alternate-theme: mat-light-theme($my-alternate-primary,
//      $my-alternate-accent,
//      $my-alternate-warn);
//  @include angular-material-theme($my-alternate-theme);
//}
//
//.title {
//  color: $blu-dark;
//
//  h1 {
//    font-weight: bold;
//
//    &.page {
//      line-height: 48px;
//      letter-spacing: -0.5px;
//    }
//  }
//
//  p.sub-title {
//    font-size: 18px;
//  }
//}
//
//.title-bold {
//  font-weight: bold;
//  color: $blu-dark;
//}
//
//p {
//  color: $color-black-text;
//}
//
//.sub-title, .title-field {
//  color: $blu-dark;
//  font-weight: 600;
//}
//
//.sub-title-tertiary {
//  color: $tertiary;
//  font-weight: 600;
//}
//
//.sub-title-alternative {
//  color: $color-tertiary-alternative;
//  font-family: Lora;
//}
//
//.sub-title-lora {
//  color: $secondary;
//  font-family: Lora;
//}
//
//.italic {
//  font-style: italic;
//}
//
//.normal {
//  font-style: normal;
//}
//
//.header-details {
//  color: $blu-dark;
//
//  .label {
//    font-size: 16px;
//    font-weight: 600;
//    line-height: 24px;
//  }
//
//  .date {
//    font-size: 16px;
//    font-weight: 400;
//    line-height: 24px;
//  }
//}
//
//.pr-header-details {
//  padding-right: 40px;
//}
//
//.pr-container-agenda {
//  padding-right: 2.2rem !important;
//}
//
//.border-full {
//  border-top: 2px solid $neutral-1 !important;
//  padding: 10px 0;
//}
//
//.border-partial {
//  border-top: 2px solid $neutral-1 !important;
//  width: 23%;
//}
//
//.mat-card-header-text {
//  margin: 0 !important;
//}
//
//.drop-down-profile {
//  color: $blu-dark;
//}
//
///* Rules for icon. */
//.icon-check {
//  display: flex !important;
//  margin-left: -38px;
//  margin-top: -58px;
//}
//
////size
//.mat-icon.md-14 {
//  height: 14px;
//  width: 14px;
//}
//
//.mat-icon.md-18 {
//  height: 18px;
//  width: 18px;
//}
//
//.mat-icon.md-20 {
//  height: 20px;
//  width: 20px;
//}
//
//.mat-icon.md-22 {
//  height: 22px;
//  width: 22px;
//}
//
//.mat-icon.md-24 {
//  height: 24px;
//  width: 24px;
//}
//
//.mat-icon.md-26 {
//  height: 26px;
//  width: 26px;
//}
//
//.mat-icon.md-28 {
//  height: 28px;
//  width: 28px;
//}
//
//.mat-icon.md-30 {
//  height: 30px;
//  width: 30px;
//}
//
//.mat-icon.md-36 {
//  height: 36px;
//  width: 36px;
//}
//
//.mat-icon.md-40 {
//  height: 40px;
//  width: 40px;
//}
//
//.mat-icon.md-48 {
//  height: 48px;
//  width: 48px;
//}
//
//.mat-icon.md-52 {
//  height: 52px;
//  width: 52px;
//}
//
//.mat-icon.md-65 {
//  height: 65px;
//  width: 65px;
//}
//
//.mat-icon.md-60 {
//  height: 60px;
//  width: 60px;
//}
//
//.mat-icon.md-80 {
//  height: 80px;
//  width: 80px;
//}
//
//.mat-icon.md-90 {
//  height: 90px;
//  width: 90px;
//}
//
//.mat-icon.md-160 {
//  height: 160px;
//  width: 160px;
//}
//
////style
//.icon_transparent {
//  color: transparent;
//}
//
//mat-icon.icon-primary {
//  color: $primary;
//}
//
//mat-icon.icon-secondary {
//  color: $secondary;
//}
//
//mat-icon.icon-white {
//  color: white;
//}
//
//mat-icon.icon-disabled {
//  color: #767676;
//}
//
///** FINE: Rules for icon.**/
//
//
//
///** RULES for fonts */
//.font-16 {
//  font-size: 16px;
//}
//
//.font-18 {
//  font-size: 18px;
//}
//
//.font-21 {
//  font-size: 21px;
//}
//
//.font-44 {
//  font-size: 44px;
//}
//
//.span-secondary {
//  color: $secondary;
//}
//
//.span-primary {
//  color: $primary;
//}
//
//.span-tertiary {
//  color: $tertiary;
//}
//
//.span-gray1 {
//  color: $gray-1;
//}
//
//.span-red {
//  color: $triadico-3-lombardia;
//}
//
//.weight-bold {
//  font-weight: bold;
//}
//
//.weight-600 {
//  font-weight: 600;
//}
//
//.weight-normal {
//  font-weight: normal;
//}
//
///** FINE: Rules for fonts */
//
//
//
///** RULES for MAT-DRAWER e MAT-CONTAINER */
//.mat-drawer-inner-container {
//  width: 20vw !important;
//  min-width: 250px;
//  max-width: 350px;
//}
//
//.mat-drawer-content.mat-sidenav-content {
//  background-color: white;
//  overflow-x: hidden !important;
//}
//
///**FINE rules MAT-DRAWER e MAT-CONTAINER */
//
//
//
///** RULES for tooltip */
//.mat-tooltip {
//  background: $tertiary;
//  font-size: 14px;
//  word-break: keep-all;
//  white-space: normal;
//  min-width: fit-content;
//}
//
///**FINE: Rules for tooltip */
//
//
///** RULES for button */
//.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,
//.mat-button.cdk-program-focused .mat-button-focus-overlay,
//.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,
//.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,
//.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,
//.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,
//.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,
//.mat-flat-button.cdk-program-focused .mat-button-focus-overlay {
//  opacity: 0 !important;
//}
//
//.btn-action-underlined {
//  text-decoration: underline !important;
//}
//
//.btn-not-outlined {
//  outline: none !important;
//}
//
//.mat-button[disabled] {
//  opacity: 0.4;
//}
//
//.mat-button,
//.mat-raised-button,
//.mat-stroked-button {
//  height: 36px;
//  padding: 0 40px !important;
//}
//
//.mat-stroked-button {
//  border-width: 2px !important;
//}
//
//.mat-stroked-button:not([disabled]),
//.mat-raised-button:not([disabled]) {
//  border-width: $border-width-cta;
//}
//
//.mat-stroked-button:not([disabled]) {
//  border-color: $primary !important;
//  color: $primary;
//}
//
//.mat-stroked-button:not([disabled]).cta-ricerca {
//  border-color: $color-cta-ricerca;
//  color: $color-cta-ricerca;
//}
//
//.mat-stroked-button:not([disabled]).btn-action {
//  border-color: $color-btn-action;
//  color: $color-btn-action;
//  padding: 0 25px !important;
//}
//
//.mat-raised-button:not([disabled]) {
//  border-color: $primary;
//  color: white;
//  background-color: $primary;
//}
//
//.mat-raised-button:not([disabled]).cta-ricerca {
//  background-color: $btn-text-allegati;
//  color: white;
//}
//
//.mat-raised-button:not([disabled]).btn-action {
//  background-color: $color-btn-action;
//  color: white;
//  padding: 0 25px !important;
//}
//
//.mat-raised-button:disabled {
//  background-color: $color-disabled !important;
//  color: white !important;
//}
//
//.mat-raised-button:disabled.btn-action {
//  background-color: $color-gray-4 !important;
//  color: white !important;
//  padding: 0 25px !important;
//}
//
//.mat-stroked-button:disabled {
//  background: $color-disabled;
//  border: 1px solid $color-disabled;
//  color: white !important;
//}
//
//.mat-raised-button:focus {
//  outline: none;
//  box-shadow: none;
//}
//
//.floating-button.mat-raised-button:disabled {
//  background-color: $color-gray-4 !important;
//  /* floating button con matt background */
//}
//
//.squared-button {
//  border: 1.2px solid $blu-dark;
//  border-radius: 6px;
//
//  &.squared-button-disabled {
//    border-color: rgba(0, 0, 0, 0.26);
//  }
//
//  button {
//    height: 78px;
//    width: 82px;
//    font-weight: normal;
//    padding: 0 4px 0px 4px;
//    line-height: 22px;
//
//    span {
//      margin-top: 4px;
//    }
//  }
//}
//
//div.squared-button:not(:first-child) {
//  margin-left: 1rem;
//}
//
//.btn-close.mat-button {
//  height: 25px;
//  min-width: 20px !important;
//  position: absolute;
//  // right: 20px;
//  transform: translate(-10%, -210%);
//
//  mat-icon {
//    height: 28px;
//    width: 28px;
//    align-items: center;
//    display: flex;
//  }
//}
//
//.mat-button-wrapper {
//  font-size: 16px;
//  font-weight: 600;
//  display: flex;
//  justify-content: center;
//}
//
//.button-underlined {
//  text-decoration-line: underline !important;
//  color: #297A38 !important;
//  &.h-auto {
//    height: auto;
//  }
//}
//
//.button-underlined:focus {
//  outline: none;
//  box-shadow: none;
//}
//
///**FINE: Rules for button */
//
///** RULES for mat-label */
//
//mat-label {
//  font-weight: 700;
//  font-size: 15px;
//  line-height: 23px;
//  color: $color-black-text
//}
//
//.mat-select-value,
//.mat-option,
//.mat-input-element,
//.mat-option.mat-active {
//  color: #353535 !important;
//}
//
//.title-label {
//  font-size: 18px !important;
//  font-weight: 700;
//}
//
//.mat-hint {
//  color: $blu-dark;
//  font-style: italic;
//  &.warn-msg {
//    color: $warning;
//  }
//}
//
///* FINE MAT-LABEL */
//
///** RULES for MAT-INPUT e MAT-FORM-FIELD */
//
//input:disabled:-moz-placeholder {
//  /* Mozilla Firefox 4 to 18 */
//  opacity: 0 !important;
//}
//
//input:disabled::-moz-placeholder {
//  /* Mozilla Firefox 19+ */
//  opacity: 0 !important;
//}
//
//input:disabled:-ms-input-placeholder {
//  /* Internet Explorer 10+ */
//  opacity: 0 !important;
//}
//
//.mat-input-element {
//  font: inherit;
//  background: 0 0;
//  color: currentColor;
//  border: none;
//  outline: 0;
//  padding: 0;
//  margin: 0;
//  width: 100%;
//  max-width: 100%;
//  vertical-align: bottom;
//  text-align: inherit;
//  text-overflow: ellipsis;
//}
//
//textarea.mat-input-element.cdk-textarea-autosize {
//  overflow: hidden;
//}
//
//.mat-form-field-appearance-outline:not(.mat-form-field-disabled) {
//
//  /*input filed con sfondo bianco*/
//  .mat-form-field-outline {
//    background-color: white;
//    border-radius: 5px;
//    -webkit-border-radius: 5px;
//    -moz-border-radius: 5px;
//  }
//}
//
//.no-border-right {
//  .mat-form-field-outline .mat-form-field-outline-end {
//    border-radius: 0;
//    border-right: none;
//  }
//}
//
//.no-border-left .mat-form-field-outline .mat-form-field-outline-start {
//  border-radius: 0;
//}
//
//.mat-input-element:disabled,
//.mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after {
//  color: #353535;
//}
//
//.mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline {
//  background: $color-gray-3;
//  box-sizing: border-box;
//  border-radius: 4px;
//}
//
//.mat-form-field {
//  padding-left: 0;
//  padding-right: 0;
//  display: block !important;
//  margin-top: 10px;
//}
//
//.mat-form-field-appearance-outline .mat-form-field-wrapper {
//  margin: 0.4em 0;
//}
//
//.mat-form-field-appearance-outline .mat-form-field-subscript-wrapper {
//  padding: 0 0.25em !important;
//}
//
//.mat-form-field-appearance-outline .mat-form-field-flex {
//  min-height: 41px;
//}
//
//.mat-form-field-infix {
//  padding: 0.6em 0 0.6em 0 !important;
//  border-top-width: 0.2em;
//}
//
///* IE11 fix wrap flex layout da applicare al componente */
//.fix-wrap {
//  flex-basis: 100%;
//}
//
//.mat-form-field-suffix {
//  align-self: center;
//}
//
//.mat-form-field-appearance-outline .mat-form-field-outline {
//  color: #BACCD9;
//}
//
//.mat-form-field-appearance-outline {
//  .mat-form-field-outline-thick {
//    color: mat-color($my-custom-accent, 500);
//  }
//}
//
///*Gestione messaggi lunghi in mat-error: https://github.com/angular/components/issues/4580#issuecomment-551200224*/
//mat-form-field {
//  &.ng-valid {
//    .mat-form-field-wrapper {
//      padding-bottom: 1.25em;
//    }
//  }
//
//  &.ng-invalid,
//  &.mat-form-field-invalid {
//    .mat-form-field-wrapper {
//      padding-bottom: 7px;
//    }
//  }
//
//  &.ng-untouched {
//    .mat-form-field-wrapper {
//      padding-bottom: 1.25em;
//    }
//  }
//
//  .mat-form-field {
//    &-underline {
//      position: static;
//    }
//
//    &-subscript-wrapper {
//      position: static;
//    }
//  }
//}
///*Fine gestione messaggi lunghi in mat-error*/
//
//.mat-error {
//  //aggiunto alla soluzione precedente per migliorare accessibilità!
//  font-weight: bold;
//  line-height: 1.5em;
//}
//
//.mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick {
//  color: $color-text-error;
//}
//
//.mat-error {
//  color: $color-text-error !important;
//}
//
//.red {
//  color: $color-text-error;
//}
//
//
///* FINE MAT-INPUT e MAT-FORM-FIELD */
//
//// .mat-select-trigger {
////   display: flex;
////   justify-content: space-between;
//// }
//
//// .mat-select-value {
////   display: contents;
//// }
//
//// .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
////   transform: translateY(-25%);
////   min-width: 40px;
////   height: 40px;
//// }
//// .mat-select-arrow-wrapper {
////   display: flex;
////   background-image: url(../images/npia/svg_npia/arrow-down.svg) !important;
//// }
//
//
//
//// Truncate text in tables with ellipsis
//.long-and-truncated {
//  flex: 1;
//
//  &,
//  &>* {
//    white-space: nowrap;
//    overflow: hidden;
//    text-overflow: ellipsis;
//  }
//}
//
//.generic_msg {
//  color: $tertiary;
//}
//
///** MAT TABLE **/
//
//.mat-sort-header-button {
//  color: $blu-dark;
//  font-weight: bold;
//}
//
//.mat-sort-header-arrow {
//  color: $label-interna !important;
//}
//
//.mat-header-cell .mat-sort-header-container.mat-sort-header-sorted .mat-sort-header-arrow {
//  opacity: 1 !important;
//}
//
///* workaround for https://github.com/angular/material2/issues/15093 */
//
//:-ms-input-placeholder.mat-input-element {
//  -ms-user-select: text !important;
//}
//
//button.mat-button.btn-as-link>span,
//button.btn-as-link {
//  text-decoration-color: $primary;
//  -moz-text-decoration-color: $primary;
//  color: $primary;
//  text-decoration: underline;
//  text-underline-position: below;
//  background-color: transparent;
//}
//
//button.mat-button.btn-as-link.show-link>span,
//button.btn-as-link.show-link {
//  text-decoration-color: $tertiary;
//  -moz-text-decoration-color: $tertiary;
//  color: $tertiary;
//}
//
//button.mat-button.btn-as-link.secondary-link>span,
//button.btn-as-link.secondary-link {
//  text-decoration-color: $secondary;
//  -moz-text-decoration-color: $secondary;
//  color: $secondary;
//}
//
//button.btn-as-link {
//  border: none;
//  padding: 0 !important;
//
//  &:hover {
//    text-decoration: underline;
//    /*for IE*/
//    text-decoration-line: underline;
//  }
//
//  &.underline {
//    text-decoration: underline;
//    text-decoration-color: $primary;
//  }
//
//  &.underline-blue {
//    text-decoration: underline;
//    text-decoration-color: $secondary;
//  }
//}
//
//.btn-as-link-medium {
//  font-size: 16px;
//  font-weight: bold;
//}
//
//.btn-as-link-small {
//  font-size: 14px;
//  font-weight: bold;
//}
//
//.btn-link-error {
//  color: $color-message-error !important;
//  text-decoration-line: underline;
//  text-decoration-color: $color-message-error !important;
//}
//
//.btn-nuova-agenda {
//  span {
//    color: $secondary;
//    font-weight: 600;
//  }
//
//  .mat-raised-button {
//    background-color: $primary;
//    max-width: 56px;
//  }
//}
//
//.btn-chiudi-agenda {
//  text-decoration: underline;
//  color: $triadico-3-lombardia;
//  font-size: 14px;
//  font-weight: 700;
//  border: none;
//  background: #f5f6fa;
//}
//
//.mat-row {
//  height: 60px;
//  border-radius: $border-radius-tab;
//  cursor: pointer;
//  -ms-flex-align: stretch;
//
//  /*fix IE11 centering*/
//  &.selected {
//    background: rgba(92, 111, 130, 0.5) !important;
//  }
//}
//
//.mat-row:nth-child(even) {
//  //background-color: $neutral-2;
//  background-color: $color-row-tabella;
//  border-bottom: 2px solid #E6E9F2;
//}
//
//.mat-row:nth-child(odd) {
//  //background-color: rgba($neutral-1-r, $neutral-1-g, $neutral-1-b, 0.6);
//  background-color: $color-neutral-header;
//  border-bottom: 2px solid #E6E9F2;
//}
//
//tr.mat-row:hover {
//  background-color: yellow;
//}
//
//.mat-header-row,
//.mat-header-row.mat-table-sticky {
//  height: 60px;
//  background-color: $color-neutral-header;
//  //background-color: $neutral-1;
//  //border-radius:$border-radius-tab
//}
//
//.mat-header-row.mat-table-sticky {
//  top: 1em;
//}
//
//.mat-toolbar-row,
//.mat-toolbar-single-row {
//  height: auto !important;
//  /*fix IE11*/
//}
//
//// .mat-tab-label.mat-tab-label-active {
////     font-weight: bold;
////     color: $blu-dark;
////     opacity: 1;
//// }
//
//label.mat-checkbox-layout {
//  margin-bottom: 0;
//}
//
//mat-cell span.long-and-truncated {
//  padding-right: 2em;
//}
//
//.mat-cell,
//.mat-footer-cell {
//  color: $blu-dark;
//  font-size: 16px;
//}
//
//.mat-header-cell {
//  color: $blu-dark;
//  font-size: 16px;
//  font-weight: bold;
//}
//
//.mat-header-cell:not(:last-of-type) {
//  border-right: 2px solid white;
//  //height: 100%;
//}
//
//.mat-cell:not(:last-of-type) {
//  border-right: 2px solid #E6E9F2;
//  //height: 100%;
//}
//
////paginator sotto mat table
//.pagination-component {
//
//  /*nel componente paginator viene ignorato*/
//  >mat-form-field {
//    .mat-form-field-wrapper {
//      padding-bottom: 0px !important;
//    }
//  }
//}
//
//.recap_paginator {
//  font-size: 12px !important;
//  color: $tertiary;
//}
//
//.recap_tabella {
//  background-color: $neutral-1;
//  height: 67px;
//  border-radius: 8px;
//  margin-top: 1.5rem;
//  margin-bottom: 1.5rem;
//  color: $blu-dark;
//
//  p {
//    margin-bottom: 0;
//  }
//}
//
//.recap_generale {
//  background-color: $neutral-1;
//  border-radius: 8px;
//  color: $blu-dark;
//}
//
//.table-container {
//  padding: 1em 0.2em;
//}
//
//.mat-column-select {
//  overflow: initial;
//  flex: 0;
//  padding-right: 24px;
//}
//
///** FINE MAT TABLE**/
//
//
//
///** CHECKBOX **/
//.mat-checkbox-label {
//  font-weight: 400;
//  font-size: 18px;
//  line-height: 27px !important;
//  color: black;
//  white-space: normal;
//}
//
//.mat-select-disabled {
//  //opacity: 0.7;
//  color: rgba(0, 0, 0, 0.38);
//}
//
//.mat-select-disabled .mat-select-value {
//  color: $blu-dark;
//}
//
//.mat-button[disabled] {
//  opacity: 0.4;
//}
//
////background-checkbox
//.mat-checkbox-checked .mat-checkbox-background {
//  background-color: $primary !important;
//}
//
//// Stile generale checkbox
//.mat-checkbox-inner-container {
//  height: 24px !important;
//  width: 21.4px !important;
//  margin-top: 2px !important;
//  .mat-checkbox-frame {
//    border-radius: 4px;
//    background: #FFFFFF;
//    border: 1px solid $color-black-text;
//    box-sizing: border-box;
//  }
//  .mat-checkbox-label.strong {
//    font-size: 18px;
//    font-weight: bold;
//  }
//}
//
//.select-all-checkbox {
//  .mat-checkbox-inner-container {
//    height: 16px !important;
//    width: 16px !important;
//  }
//}
//
//.mat-pseudo-checkbox {
//  border-radius: 4px !important;
//  background: #FFFFFF;
//  border: 1.6px solid $color-gray-4 !important;
//  box-sizing: border-box !important;
//}
//
//.mat-pseudo-checkbox.mat-pseudo-checkbox-checked, .mat-pseudo-checkbox.mat-pseudo-checkbox-indeterminate {
//  border-color: transparent !important;
//}
//
//.no-ripple .mat-checkbox-ripple {
//  display: none !important;
//}
//
////impostazione checkbox disabilitate
//.mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background {
//  background-color: $color-disabled !important;
//}
//
//.mat-checkbox-disabled:not(.mat-checkbox-checked) {
//  .mat-checkbox-frame {
//    border-color: #706F6F;
//    margin: -1px;
//  }
//  .mat-checkbox-background {
//    background-color: $color-gray-3 !important;
//  }
//}
//
//.mat-checkbox-background {
//  display: block;
//}
//
//.mat-checkbox-disabled .mat-checkbox-label {
//  /* color: rgba(0, 0, 0, 0.54); */
//  color: black;
//}
//
//.checkDeceduti .mat-checkbox-inner-container {
//  width: 26px !important;
//  height: 26px !important;
//}
//
////background-checkbox BLUE
//.blue-back {
//  &.mat-checkbox-checked .mat-checkbox-background {
//    background-color: $blu-dark !important;
//  }
//
//  &.mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background {
//    background-color: $tertiary !important;
//  }
//
//  .mat-checkbox-inner-container {
//    height: 18px;
//    width: 18px;
//  }
//}
//
//.check {
//  margin-top: -.7em;
//  margin-bottom: 1em;
//  .mat-checkbox-label {
//    white-space: nowrap !important;
//  }
//}
//
///**FINE CHECKBOX **/
//
//
///** STILE RADIO-BUTTON **/
//
//.mat-radio-label {
//  display: flex !important;
//  align-items: flex-start !important;
//}
//
//.mat-radio-label-content {
//  font-size: 18px;
//  line-height: 27px !important;
//  color: black;
//  white-space: normal;
//}
//
//.mat-radio-container {
//  margin-top: 3px;
//}
//
//.mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
//  background-color: transparent !important;
//}
//
//.mat-radio-button.mat-accent .mat-radio-inner-circle {
//  background-color: $primary !important;
//}
//
//.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
//  border-color: $color-black-text !important;
//}
//
//.mat-radio-button.mat-radio-disabled {
//  .mat-radio-label-content {
//    color: black;
//  }
//  .mat-radio-inner-circle{
//    background-color: #B0B0B0 !important ;
//  }
//}
//
//.mat-radio-checked .mat-radio-inner-circle {
//  transform: scale(0.7) !important;
//}
//
///** FINE RADIO-BUTTON **/
//
//
///**Gestione TAB */
//.custom-tab-group-ricerca {
//  padding-bottom: 1.3rem;
//
//  .mat-tab-labels {
//    .mat-tab-label {
//      color: $color-gray-2;
//      font-weight: bold;
//      font-size: 16px;
//      opacity: 1;
//
//      &.mat-tab-label-active {
//        color: $secondary;
//      }
//
//      &.mat-tab-disabled {
//        width: auto;
//        margin-left: auto;
//        padding-right: 0;
//        align-items: end;
//        padding-bottom: 10px;
//        color: $secondary;
//        font-weight: 600;
//        line-height: 94%;
//      }
//    }
//  }
//}
//
//.custom-tab-group-scheda {
//  .mat-tab-header {
//    border-bottom: none;
//    padding-left: 1.5rem;
//    background-color: $neutral-1;
//  }
//
//  .mat-ink-bar {
//    width: 0 !important;
//  }
//
//  .mat-tab-label {
//    width: 14vw;
//    height: 59px;
//    border: 1px solid $neutral-1;
//    box-sizing: border-box;
//    box-shadow: 5px -4px 21px rgba(19, 52, 80, 0.05);
//    border-radius: 8px 8px 0 0;
//    background: $color-azzurro-chiaro;
//    opacity: 0.7;
//
//    .label {
//      color: $color-gray-2;
//      opacity: 0.9;
//      font-size: 24px;
//      font-weight: bold;
//    }
//
//    &.mat-tab-label-active {
//      opacity: 1;
//      border-bottom: none;
//
//      .label {
//        color: $secondary;
//      }
//    }
//
//    &.mat-tab-disabled {
//      .label {
//        opacity: 0.3;
//      }
//    }
//  }
//}
//
///**FINE gestione TAB*/
//
//
///**Gestione ACCORDION*/
//.parentAccordion {
//  .mat-expansion-panel-header[aria-disabled=true] {
//    opacity: 0.7;
//    background: $neutral-1 !important;
//  }
//
//  .mat-expansion-panel {
//    &:first-of-type {
//      margin-top: 1.5rem !important
//    }
//
//    &:last-of-type {
//      margin-bottom: 2.5rem !important;
//      &.less-mb {
//        margin-bottom: 2rem !important;
//      }
//    }
//
//    margin-bottom: 1rem;
//    border: 1px solid $primary;
//    box-sizing: border-box;
//    border-radius: 8px !important;
//
//    .mat-expansion-panel-header {
//      pointer-events: none;
//      height: 130px;
//      background: white;
//
//      span.mat-content {
//        font-weight: bold;
//        font-size: 18px;
//        line-height: 27px;
//        color: $secondary;
//      }
//    }
//
//    .mat-expansion-indicator {
//      display: none;
//    }
//
//    .mat-expanded .mat-expansion-indicator::after {}
//
//    .mat-expansion-panel-body {}
//  }
//}
//
//.parentAccordionReading {
//  .mat-expansion-panel-header[aria-disabled=true] {
//    opacity: 0.7;
//    background: $neutral-1 !important;
//  }
//  .mat-expansion-panel {
//    &:first-of-type {
//      margin-top: 2rem !important;
//      &.less-mt {
//        margin-top: 0.5rem !important;
//      }
//    }
//    &:last-of-type {
//      margin-bottom: 2rem !important;
//      &.less-mb {
//        margin-bottom: 1rem !important;
//      }
//    }
//    margin-bottom: 1rem;
//    border: 1px solid $primary !important;
//    box-sizing: border-box;
//    border-radius: 8px !important;
//    .mat-expansion-panel-header {
//      height: 130px;
//      background: white;
//      span.mat-content {
//        font-weight: bold;
//        font-size: 18px;
//        line-height: 27px;
//        color: $secondary;
//      }
//      &.no-indicator .mat-expansion-indicator {
//        display: none !important;
//      }
//    }
//    .mat-expansion-indicator {
//      padding: 0 1.5rem;
//      &::after {
//        width: 25px;
//        height: 25px;
//        color: $primary;
//      }
//    }
//    .mat-expanded .mat-expansion-indicator::after {
//    }
//    .mat-expansion-panel-body {
//    }
//  }
//}
//
//.childPanel {
//  .mat-expansion-panel-header[aria-disabled=true] {
//    opacity: 0.7;
//    background: $neutral-1 !important;
//  }
//  .mat-expansion-panel {
//    &:first-of-type {
//      margin-top: 0 !important
//    }
//    &:last-of-type {
//      margin-bottom: 0 !important
//    }
//    margin-bottom: 0;
//    border: none !important;
//    box-shadow: none;
//    .mat-expansion-panel-header {
//      pointer-events: visible;
//      height: 70px;
//      background: white;
//      padding: 0 0.5rem 0 0;
//      span.mat-content {
//        font-weight: bold;
//        font-size: 18px;
//        line-height: 27px;
//        color: $secondary;
//      }
//    }
//    .mat-expansion-indicator {
//      padding: 0 0.5rem;
//      display: flex;
//      &::after {
//        width: 20px;
//        height: 20px;
//        color: $primary;
//      }
//    }
//    .mat-expanded .mat-expansion-indicator::after {
//    }
//    .mat-expansion-panel-body {
//      padding: 0;
//    }
//  }
//}
//
//.filtersPanel {
//  .mat-expansion-indicator::after {
//    //padding:7px;
//    color: #033565;
//    padding: 7px;
//    border-width: 0 3px 3px 0;
//    vertical-align: inherit;
//  }
//  .mat-expansion-panel:not([class*='mat-elevation-z']){
//    box-shadow: none;
//    border: 1px solid #D8D8D8;
//  }
//  .mat-expansion-panel-header {
//    height: 55px;
//  }
//}
///*FINE gestione ACCORDION*/
//
///*Gestione MODAL CUSTOM*/
//
///* MODAL FEEDBACK-GENERICO */
//// !!! Non viene più utilizzato, aggiungere classe in dialog.service se serve
//
//// .feedback-modal-box > mat-dialog-container {
////   min-width: 26vw;
////   max-width: fit-content;
////   .mat-dialog-title {
////     font-weight: 700;
////     color: $color-dialog;
////     font-size: 32px;
////     //font-size: 1.65em;
////   }
////   mat-dialog-content {
////     margin: auto;
////     overflow-y: hidden;
////     color: $color-dialog;
////     padding: 0 0;
////     div {
////       text-align: left;
////       p {
////         line-height: 28px;
////       }
////       span {
////         margin-top: 6px;
////       }
////     }
////     .codiceId {
////       color: $primary;
////       font-weight: 700 !important;
////     }
////     .mat-radio-button {
////       margin: 15px 45px 5px 5px;
////       color: $blu-dark;
////       font-weight: 600;
////       font-size: 16px;
////       &.mat-radio-checked {
////         color: $primary !important;
////         .mat-radio-inner-circle {
////           transform: scale(0.6);
////         }
////         .mat-radio-outer-circle {
////           border-color: $primary;
////         }
////       }
////       &.mat-accent .mat-radio-inner-circle {
////         background-color: $primary;
////       }
////     }
////   }
////   mat-dialog-actions {
////     justify-content: space-between;
////     padding: 0;
////     margin-top: 1rem;
////     margin-bottom: 0;
////   }
////   mat-dialog-actions .mat-button,
////   mat-dialog-actions .mat-fab,
////   mat-dialog-actions .mat-flat-button,
////   mat-dialog-actions .mat-icon-button,
////   mat-dialog-actions .mat-mini-fab,
////   mat-dialog-actions .mat-raised-button,
////   mat-dialog-actions .mat-stroked-button {
////     border-width: $border-width-cta;
////     font-weight: 600;
////     //font-size: 16px;
////     min-width: 6rem;
////     height: $height-cta-secondaria;
////   }
////   mat-dialog-actions .mat-raised-button {
////     background-color: $primary;
////     /*medboard*/ //$color-cta;
////     color: white;
////   }
////   mat-dialog-actions .mat-stroked-button:not([disabled]) {
////     color: $primary;
////     /*medboard*/ //$color-cta;
////     border-color: $primary;
////     /*medboard*/ //$color-cta;
////   }
////   .dialog-title {
////     /*icona a sinistra del titolo e come medboard*/
////     align-items: center;
////     justify-content: center;
////   }
//// }
///* FINE MODAL FEEDBACK-GENERICO */
//
///* MODAL SECONDARI */
//.dialog-secondari>mat-dialog-container {
//  min-width: 35vw;
//  max-width: min-content;
//  padding: 24px 48px;
//
//  .mat-dialog-title {
//    font-weight: bold;
//    color: $color-black-text;
//    font-size: 18px;
//    line-height: 25px;
//    text-align: center;
//    margin: 20px 0px;
//  }
//
//  .mat-icon {
//    display: flex;
//    margin-left: auto;
//    margin-right: auto !important;
//  }
//
//  mat-dialog-content {
//    p {
//      color: $color-black-text;
//    }
//    text-align: center;
//    margin: 10px 0px;
//    font-size: 16px;
//    // line-height: 22px;
//    padding: 0;
//    &.greater {
//      font-size: 24px;
//      line-height: 36px;
//    }
//  }
//
//  mat-dialog-actions {
//    margin: 10px 0 0;
//    padding: 8px 40px 0 40px;
//    justify-content: space-around;
//  }
//
//  mat-dialog-actions .mat-button,
//  mat-dialog-actions .mat-fab,
//  mat-dialog-actions .mat-flat-button,
//  mat-dialog-actions .mat-icon-button,
//  mat-dialog-actions .mat-mini-fab,
//  mat-dialog-actions .mat-raised-button,
//  mat-dialog-actions .mat-stroked-button {
//    margin-left: 0 !important;
//  }
//}
///* FINE MODAL SECONDARI*/
//
///* MODAL HEADER */
//.dialog-modal-header>mat-dialog-container {
//  min-width: 50vw;
//  max-width: min-content;
//  max-height: 70vh;
//  padding: 0;
//
//  .mat-dialog-title {
//    background: #F9FAFA;
//    font-weight: bold;
//    font-size: 36px;
//    margin: 0px;
//    padding: 30px;
//
//    color: $blu-dark;
//  }
//
//  mat-dialog-content {
//
//    margin: 0px;
//    padding: 30px;
//    max-height: calc(70vh - 220px);
//
//    .dialog-subtitle {
//    font-weight: bold;
//    font-size: 18px;
//    padding-bottom: 15px;
//
//    color: $blu-dark;
//    }
//    ul {
//      list-style: none;
//      padding-left: 5px;
//    }
//    a {
//      display: block;
//      text-decoration: none;
//      color: #007BFF;
//      margin-bottom: 5px;
//
//      &:hover {
//        text-decoration: underline;
//      }
//    }
//  }
//
//  mat-dialog-actions {
//    margin: 0px;
//    padding: 15px 30px;
//    justify-content: flex-start;
//    box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.05);
//
//    .mat-icon {
//      display: flex;
//      align-items: center;
//
//      svg {
//        path {
//          stroke: white;
//        }
//      }
//    }
//  }
//
//  mat-dialog-actions .mat-button,
//  mat-dialog-actions .mat-fab,
//  mat-dialog-actions .mat-flat-button,
//  mat-dialog-actions .mat-icon-button,
//  mat-dialog-actions .mat-mini-fab,
//  mat-dialog-actions .mat-raised-button,
//  mat-dialog-actions .mat-stroked-button {
//    margin-left: 0 !important;
//  }
//}
///* FINE MODAL HEADER*/
//
//
///* Fine gestione MODAL CUSTOM */
//
//
///**GESTIONE CALENDARIO */
//
//.mat-datepicker-content {
//  .mat-datepicker-content-container {
//    padding: 10px 20px;
//    .mat-calendar {
//      width: 250px;
//      height: 100%;
//      .mat-calendar-header {
//        padding: 8px 8px 8px 8px;
//        .mat-calendar-controls {
//          flex-direction: column;
//          align-items: center;
//          margin-top: 0;
//          .mat-calendar-period-button {
//            width: fit-content;
//            padding: 0 5px !important;
//            .mat-button-wrapper {
//              align-items: center;
//              color: $secondary;
//              font-weight: bold;
//            }
//          }
//          .mat-calendar-spacer {
//            display: none;
//          }
//          .mat-calendar-previous-button {
//            position: absolute;
//            top: 35px;
//            left: 0;
//            margin-left: 20px;
//          }
//          .mat-calendar-next-button {
//            position: absolute;
//            top: 35px;
//            right: 0;
//            margin-right: 20px;
//          }
//        }
//      }
//    }
//    .mat-calendar-content {
//      .mat-calendar-table-header {
//        tr {
//          border-top: 1.1px solid #E6E9F2;
//          border-bottom: 1px solid #E6E9F2;
//        }
//        th {
//          padding: 4px 0;
//          font-size: 12px;
//          font-weight: 600;
//          color: $secondary;
//        }
//        .mat-calendar-table-header-divider {
//          display: none;
//        }
//      }
//      .mat-calendar-body {
//        .mat-calendar-body-label {
//          visibility: collapse;
//          padding-top: 0 !important;
//          padding-bottom: 10px !important;
//        }
//        .mat-calendar-body-cell-content {
//          border-radius: 4px;
//          font-size: 14px;
//          color: #353535;
//          &.mat-calendar-body-selected {
//            box-shadow: none;
//            color: white;
//          }
//        }
//        .mat-calendar-body-disabled{
//          opacity: 0.4;
//        }
//      }
//    }
//  }
//}
//
//.data-field {
//  .mat-form-field-outline {
//    right: 40px !important;
//  }
//  .mat-form-field-suffix {
//    position: absolute;
//    right: 0;
//  }
//  .mat-form-field-infix {
//    .mat-input-element {
//      width: 60%;
//    }
//  }
//  &.with-check {
//    .mat-form-field-subscript-wrapper {
//      margin-top: 0;
//    }
//  }
//}
