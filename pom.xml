<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>it.ariaspa</groupId>
		<artifactId>cm</artifactId>
		<version>1.0.0</version>
	</parent>

	<groupId>it.ariaspa.mielo.www</groupId>
	<artifactId>mielo.www</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<name>mielo.www</name>
	<description>Template for mielo.www</description>

	<modules>
		<module>mielo.www-angular</module>
		<module>mielo.www-release</module>
	</modules>

	<properties>
		<project.build.operatingsystem>windows</project.build.operatingsystem>	
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>17</java.version>
		<maven.compiler.target>17</maven.compiler.target>
		<maven.compiler.source>17</maven.compiler.source>
		<custom-package-plugin.version>2.0.1</custom-package-plugin.version>
		<maven-compiler-plugin.version>3.8.0</maven-compiler-plugin.version>
		<maven-resources-plugin.version>3.0.2</maven-resources-plugin.version>
		<maven-clean-plugin.version>2.5</maven-clean-plugin.version>
		<frontend-maven-plugin.version>1.15.0</frontend-maven-plugin.version>
		<node.version>v18.13.0</node.version>
		
	</properties>

</project>